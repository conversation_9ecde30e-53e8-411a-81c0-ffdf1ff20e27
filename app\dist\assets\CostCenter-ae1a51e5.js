import{r as l,i as R,l as Tl,b as Fl,cK as me,q as Ml,hs as Pl,w as y,a as s,ht as Ol,j as n,J as Il,hl as Rl,aB as fe,$ as i,hp as Ll,hq as Wl,T as d,hu as Bl,h as zt,W as ee,hv as Tt,hw as Vl,R as xe,hx as Ul,g as _l,A as Hl,gM as Yl,f as ql,gO as F,gQ as S,a3 as A,hy as w,h2 as Gl,a4 as Jl,hz as Kl,h4 as ye,hA as Ql,a1 as $,gU as Xl,gW as Zl,hB as jl,hC as ea,hD as ta,P as pe,hE as sa,hF as Ve,fI as Ue,hG as _e,hH as He,fV as Ft,h1 as Mt,hI as Pt,fX as Ot,gR as Se,gS as Ae,hJ as be,fH as It,hi as la,F as aa,gL as q,hK as f,hL as ra,gV as Rt,hM as na,hN as Ye,hO as ve,hP as O,hQ as oa,hR as ca,hS as ia,hT as da,hU as ha,hV as ua,hW as Lt,hX as Ca,hY as ga}from"./index-fdfa25a0.js";import{A as ma}from"./AttachmentUploadDialog-cc0b6643.js";const wa=()=>{var bt,vt,Dt;l.useState(!1);const[Wt,Bt]=l.useState(!1),[Vt,W]=l.useState("");R(e=>e.appSettings.Format),l.useState(!1),l.useState([]);const g=R(e=>e.AllDropDown.dropDown),u=Tl(),te=Fl(),[qe,Ge]=l.useState(0),[De,se]=l.useState(0),[$e,we]=l.useState(10),[Je,le]=l.useState(0),[Ut,_t]=l.useState(!0),[Ke,ae]=l.useState(!1),[Ht,re]=l.useState(!1),[Qe,Yt]=l.useState(!1);l.useState(!1);const[qt,Gt]=l.useState([]),Jt=(e,t)=>{se(t)};let Kt=R(e=>{var t;return(t=e.userManagement.entitiesAndActivities)==null?void 0:t["Display Material"]}),B=R(e=>e.userManagement.userData);const Qt=e=>{const t=e.target.value;we(t),se(0),le(0)},Xt=48,Zt=8,jt={PaperProps:{style:{maxHeight:Xt*4.5+Zt,width:250}}};l.useEffect(()=>{(parseInt(De)+1)*parseInt($e)>=parseInt(Je)+1e3&&(ge(Je+1e3),le(e=>e+1e3))},[De,$e]);var es=new Date,ts=new Date("December 31, 9999 01:15:00");l.useState(!1);const[ss,I]=l.useState(!1),[ls,ne]=l.useState(!1),[as,rs]=l.useState("1"),[V,Xe]=l.useState([]);me.useState(""),l.useState(!1),l.useState("");const[fa,ns]=l.useState("");l.useState(!0);const[xa,Ze]=l.useState(!1),[ya,je]=l.useState(!0);l.useState([]),l.useState([]),l.useState([]),l.useState([]),l.useState(!0),l.useState([]),l.useState([]),l.useState(!1);const[G,et]=l.useState([]),[pa,os]=l.useState([]),[v,tt]=l.useState({});l.useState([]),l.useState([]),l.useState(!1),l.useState([]);const[st,cs]=l.useState([]);l.useState([]);const[is,lt]=l.useState(!1),[ds,at]=l.useState(!1);l.useState(!0),l.useState("sm");const[hs,J]=l.useState(!1),[us,rt]=l.useState(!1),[M,U]=l.useState(""),[D,oe]=l.useState(""),[ce,nt]=l.useState(es),[Ee,ot]=l.useState(ts),[b,ie]=l.useState(""),[P,ct]=l.useState(""),[it,Cs]=l.useState(""),[L,gs]=l.useState(""),[ms,K]=l.useState(!1),[fs,de]=l.useState(!1),[dt,ht]=l.useState(!1),[ut,xs]=l.useState([]),[Ne,ke]=l.useState(!1);l.useState(!1);const Q=me.useRef(null),[ys,ps]=l.useState(0),[ze,Te]=l.useState(!1),[Fe,Me]=l.useState(!1),X=me.useRef(null),Z=me.useRef(null),[Ss,As]=l.useState(0),[bs,vs]=l.useState(0),[Sa,Ds]=l.useState(!0),[Aa,$s]=l.useState(!1);l.useState(!1);const ws=R(e=>e.costCenter.handleMassMode),Pe=["Create Multiple","Upload Template ","Download Template "],Oe=["Create Single","With Copy","Without Copy"],Ie=["Change Multiple","Upload Template","Download Template"],c=R(e=>e.commonFilter.CostCenter),he=R(e=>e.commonSearchBar.CostCenter),ue=R(e=>{var t;return(t=e==null?void 0:e.AllDropDown)==null?void 0:t.dropDown}),Es=()=>{lt(!0)},Ns=()=>{at(!0)},C={companyCode:{newCompanyCode:D},costCenterName:{newCostCenterName:M},controllingAreaData:{newControllingArea:b},controllingAreaDataCopy:{newControllingAreaCopyFrom:P},costCenter:{newCostCenter:L},validFromDate:{newValidFromDate:ce},validToDate:{newValidToDate:Ee}},ks=()=>{var m,T,E,N,k;let e=(T=(m=C==null?void 0:C.controllingAreaData)==null?void 0:m.newControllingArea)==null?void 0:T.code,t=(N=(E=C==null?void 0:C.companyCode)==null?void 0:E.newCompanyCode)==null?void 0:N.code,r=(k=C==null?void 0:C.costCenterName)==null?void 0:k.newCostCenterName;if(console.log(P,it,r,"selectedCostCenterName"),(b==null?void 0:b.code)===void 0||(b==null?void 0:b.code)===""||(D==null?void 0:D.code)===void 0||(D==null?void 0:D.code)===""||M===void 0||M===""){ae(!1),K(!0),$s(!0);return}else{if(M.length!==6){ae(!0),K(!1);return}else ae(!1);K(!1)}let a=e.concat("$$",t,r);console.log("sendNewCostCenterData",c),I(!0);const o=x=>{I(!1),x.body.length>0?ht(!0):te("/masterDataCockpit/costCenter/newSingleCostCenter",{state:C})},h=x=>{console.log(x)};y(`/${f}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${a}`,"get",o,h)},zs=()=>{var m,T,E,N,k;if(console.log(P,it,"newControllingAreaCopyFrom?.code "),(b==null?void 0:b.code)===void 0||(b==null?void 0:b.code)===""||(D==null?void 0:D.code)===void 0||(D==null?void 0:D.code)===""||M===void 0||M===""||(P==null?void 0:P.code)===void 0||(P==null?void 0:P.code)===""||(L==null?void 0:L.code)===void 0||(L==null?void 0:L.code)===""){re(!1),de(!0);return}else{if(M.length!==6){re(!0),de(!1);return}else re(!1);de(!1)}let e=(m=C==null?void 0:C.controllingAreaData)==null?void 0:m.newControllingArea.code,t=(T=C==null?void 0:C.companyCode)==null?void 0:T.newCompanyCode.code,r=(E=C==null?void 0:C.costCenterName)==null?void 0:E.newCostCenterName,a=e.concat("$$",t,r);console.log((N=C==null?void 0:C.costCenter)==null?void 0:N.newCostCenter,"sendNewCostCenterData=========="),console.log((k=C==null?void 0:C.costCenterName)==null?void 0:k.newCostCenterName,"sendNewCostCenterData=========="),I(!0);const o=x=>{var z,p;I(!1),console.log("dupli",x),x.body.length>0?ht(!0):te(`/masterDataCockpit/costCenter/displayCopyCostCenter/${(p=(z=C==null?void 0:C.costCenter)==null?void 0:z.newCostCenter)==null?void 0:p.code}`,{state:C})},h=x=>{console.log(x)};y(`/${f}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${a}`,"get",o,h)},Ts=()=>{ks()},Fs=()=>{zs()},Re=()=>{lt(!1),K(!1),_t(!0),ae(!1),Yt(!1),ie(""),oe(""),U("")},Le=()=>{K(!1),de(!1),re(!1),ie(""),oe(""),U(""),ct(""),Cs(""),at(!1)},Ms=e=>{Z.current&&Z.current.contains(e.target)||Me(t=>!t)},Ps=e=>{if(e.target.value!==null){var t=e.target.value;let r={...c,costCenterName:t};u(q({module:"CostCenter",filterData:r}))}},Os=(e,t)=>{{var r=t;let a={...c,controllingArea:r};u(q({module:"CostCenter",filterData:a})),Gs(a),qs(a),Ks(a)}},Is=(e,t)=>{{var r=t;let a={...c,companyCode:r};u(q({module:"CostCenter",filterData:a}))}},Rs=(e,t)=>{{var r=t;let a={...c,profitCenter:r};u(q({module:"CostCenter",filterData:a}))}},Ls=(e,t)=>{{var r=t;let a={...c,hierarchyArea:r};u(q({module:"CostCenter",filterData:a}))}},Ws=(e,t)=>{{var r=t;let a={...c,costCenterCategory:r};u(q({module:"CostCenter",filterData:a}))}};let Bs={"Person Responsible":`/${f}/data/getSalesOrg`,"Business Area":`/${f}/data/getBusinessArea`,"Functional Area":`/${f}/data/getFunctionalArea`};const Vs=e=>{const t=e.target.value;et(t),os([]),console.log("selected field",e.target.value),t.forEach(async r=>{const a=Bs[r];al(a)})},Us=[{title:"Person Responsible"},{title:"Business Area"},{title:"Functional Area"}],_s={"Task ID":"taskId",Status:"status",SalesOrganization:"salesOrg",Division:"division",OldMaterialNumber:"oldMaterialNumber","Lab/Office":"labOffice","Transportation Group":"transportationGroup","Batch management":"batchManagement","Person Responsible":"personResponsible","Business Area":"businessArea","Functional Area":"functionalArea"},Hs=()=>{const e=r=>{u(O({keyName:"CostCenterCategory",data:r.body}))},t=r=>{console.log(r)};y(`/${f}/data/getCostCenterCategory`,"get",e,t)},Ys=()=>{const e=r=>{u(O({keyName:"CostCenter",data:r.body}))},t=r=>{console.log(r)};y(`/${f}/data/getCostCenter`,"get",e,t)},qs=e=>{var a;const t=o=>{u(O({keyName:"ProfitCenterSearch",data:o.body}))},r=o=>{console.log(o)};y(`/${f}/data/getProfitCenterAsPerControllingArea?controllingArea=${(a=e.controllingArea)==null?void 0:a.code}`,"get",t,r)},Gs=e=>{var a;console.log("CA",e);const t=o=>{u(O({keyName:"HierarchyAreaSearch",data:o.body})),console.log("data",o)},r=o=>{console.log(o)};y(`/${f}/data/getHierarchyArea?controllingArea=${(a=e.controllingArea)==null?void 0:a.code}`,"get",t,r)},Js=()=>{const e=r=>{u(O({keyName:"ControllingArea",data:r.body}))},t=r=>{console.log(r)};y(`/${f}/data/getControllingArea`,"get",e,t)},Ks=e=>{var a;const t=o=>{u(O({keyName:"CompCodeSearch",data:o.body}))},r=o=>{console.log(o)};y(`/${ra}/data/getCompCode?contrllingArea=${(a=e==null?void 0:e.controllingArea)==null?void 0:a.code}`,"get",t,r)},Ct=e=>{const t=a=>{u(O({keyName:"CompanyCode",data:a.body}))},r=a=>{console.log(a)};y(`/${f}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${e.code}`,"get",t,r)},Qs=e=>{const t=a=>{u(O({keyName:"CompanyCode",data:a.body}))},r=a=>{console.log(a)};y(`/${f}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${e.code}`,"get",t,r)},Xs=e=>{const t=a=>{u(O({keyName:"CompCodeCopy",data:a.body}))},r=a=>{console.log(a)};y(`/${f}/data/getCostCenterBasedOnCOA?controllingArea=${e.code}`,"get",t,r)},Zs=()=>{let e="Basic Data";const t=a=>{u(oa(a.body))},r=a=>{console.log(a)};y(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},js=()=>{let e="Control";const t=a=>{u(ca(a.body))},r=a=>{console.log(a)};y(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},el=()=>{let e="Templates";const t=a=>{u(ia(a.body))},r=a=>{console.log(a)};y(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},tl=()=>{let e="Address";const t=a=>{u(da(a.body))},r=a=>{console.log(a)};y(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},sl=()=>{let e="Communication";const t=a=>{u(ha(a.body))},r=a=>{console.log(a)};y(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},ll=()=>{let e="History";const t=a=>{u(ua(a.body))},r=a=>{console.log(a)};y(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)};l.useEffect(()=>{Hs(),Zs(),js(),el(),tl(),sl(),ll(),Ys(),Js(),u(Ml({})),u(Pl())},[]);const al=e=>{const t=a=>{const o=a.body;cs(o)};console.log(st,"dinamicoptions=================="),y(e,"get",t,a=>{console.log(a)})},rl=()=>{ns("")},nl=e=>{console.log("rmSearchForm",he),ne(!0),e||(se(0),we(10),le(0));let t={costCenterName:"",costCenter:(he==null?void 0:he.number)??"",controllingArea:"",companyCode:"",profitCenter:"",heirarchyArea:"",costCenterCategory:"",createdBy:"",fromDate:"",toDate:"",personResponsible:"",businessArea:"",functionalArea:"",top:1e3,skip:e??0};const r=o=>{var E,N,k,x;var h=[];for(let z=0;z<((N=(E=o==null?void 0:o.body)==null?void 0:E.list)==null?void 0:N.length);z++){var m=(k=o==null?void 0:o.body)==null?void 0:k.list[z];{var T={id:Lt(),description:m.Description,controllingArea:m.controllingArea,companyCode:m.CompanyCode,profitCenter:m.ProfitCenter,hierarchyArea:m.HeirarchyArea,costCenterCategory:m.CCtrCategory,costCenter:m.costCenter,CostCenterName:m.CostCenterName};h.push(T)}}Xe(h),ne(!1),gt(h.length),Ge((x=o==null?void 0:o.body)==null?void 0:x.count)};let a=o=>{console.log(o)};y(`/${f}/data/getCostCentersBasedOnAdditionalParams`,"post",r,a,t)},We=new Date,Ce=new Date;Ce.setDate(Ce.getDate()-15),l.useState([Ce,We]),l.useState([Ce,We]),console.log("newcontrollingarea",b);const ge=e=>{var o,h,m,T,E,N,k;console.log("rmSearchForm",c),ne(!0),e||(se(0),we(10),le(0)),console.log(v,"filterFieldData============");let t={costCenterName:(c==null?void 0:c.costCenterName)??"",costCenter:"",controllingArea:((o=c==null?void 0:c.controllingArea)==null?void 0:o.code)??"",companyCode:((h=c==null?void 0:c.companyCode)==null?void 0:h.code)??"",profitCenter:((m=c==null?void 0:c.profitCenter)==null?void 0:m.code)??"",heirarchyArea:((T=c==null?void 0:c.hierarchyArea)==null?void 0:T.code)??"",costCenterCategory:((E=c==null?void 0:c.costCenterCategory)==null?void 0:E.code)??"",createdBy:"",fromDate:"",toDate:"",personResponsible:(v==null?void 0:v["Person Responsible"])??"",businessArea:((N=v==null?void 0:v["Business Area"])==null?void 0:N.code)??"",functionalArea:((k=v==null?void 0:v["Functional Area"])==null?void 0:k.code)??"",top:1e3,skip:e??0};const r=x=>{var wt,Et,Nt,kt;console.log("data",x.body.list);var z=[];for(let j=0;j<((Et=(wt=x==null?void 0:x.body)==null?void 0:wt.list)==null?void 0:Et.length);j++){var p=(Nt=x==null?void 0:x.body)==null?void 0:Nt.list[j];console.log("hshshsh",p);var $t={id:Lt(),description:p.Description,controllingArea:p.controllingArea,companyCode:p.CompanyCode,profitCenter:p.ProfitCenter,hierarchyArea:p.HeirarchyArea,costCenterCategory:p.CCtrCategory,costCenter:p.costCenter,CostCenterName:p==null?void 0:p.CostCenterName,businessArea:p.BusinessArea!==""?`${p.BusinessArea}`:"Not Available",functionalArea:p.FunctionalArea!==""?`${p.FunctionalArea}`:"Not Available",personResponsible:p.PersonResponsible!==""?`${p.PersonResponsible}`:"Not Available"};z.push($t)}console.log("tempobj",$t),console.log("tempObH",p),z.sort((j,zl)=>Ye(j.createdOn,"DD MMM YYYY HH:mm")-Ye(zl.createdOn,"DD MMM YYYY HH:mm")),Xe(z.reverse()),ne(!1),gt(z.length),Ge((kt=x==null?void 0:x.body)==null?void 0:kt.count)},a=x=>{console.log(x)};y(`/${f}/data/getCostCentersBasedOnAdditionalParams`,"post",r,a,t)};l.useState([]),l.useState([]),l.useState(null),l.useState(null);const[ba,gt]=l.useState(0);l.useState(!1),l.useState(!1),l.useState(!1),l.useState(!1),l.useState(!1),l.useState(!1),l.useState(""),l.useState("");const[ol,mt]=l.useState(!1),[cl,_]=l.useState(""),[ft,H]=l.useState(),Y=()=>{mt(!0)},xt=()=>{mt(!1)};l.useState(null);const il=()=>{Bt(!0)};l.useState(null),l.useState(null);const dl=()=>{u(Rt({module:"CostCenter"}))},hl=e=>{I(!0),console.log(e);const t=new FormData;if([...e].forEach(h=>t.append("files",h)),ws==="Change")var r=`/${f}/massAction/getAllCostCenterFromExcelForMassChange`;else var r=`/${f}/massAction/getAllCostCenterFromExcel`;y(r,"postformdata",h=>{var m;I(!1),console.log(h,"example"),u(Ca((m=h==null?void 0:h.body)==null?void 0:m.controllingArea)),h.statusCode===200?(J(!1),u(ga(h==null?void 0:h.body)),_("Create"),H(`${e.name} has been Uploaded Succesfully`),W("success"),je(!1),rt(!0),il(),Ze(!0),te("/masterDataCockpit/costCenter/createMultipleCostCenter")):(J(!1),_("Error"),rt(!1),H("Error Uploading Cost Center Excel"),W("danger"),je(!1),Ze(!0),Y()),ml()},h=>{console.log(h)},t)},ul=e=>{console.log("newselection",e),xs(e);let t=Be.map(o=>o.field);const r=V.filter(o=>e.includes(o.id));let a=[];r.map(o=>{console.log("sssssss",o);let h={};t.forEach(m=>{console.log("yyyyy",o[m]),o[m]!==null&&(h[m]=o[m]||"")}),a.push(h),Gt(a),console.log("requiredArrayDetails",a)})};function Cl(){u(Rt({module:"CostCenter"})),ge()}l.useState([]),l.useState([]);const[va,gl]=l.useState(!1);l.useState(null),l.useState(null),l.useState([]);const ml=()=>{gl(!1)};l.useState(null),l.useState("");const Be=[{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1},{field:"CostCenterName",headerName:"Cost Center Name",editable:!1,flex:1},{field:"description",headerName:"Description",editable:!1,flex:1},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"hierarchyArea",headerName:"Hierarchy Area",editable:!1,flex:1},{field:"costCenterCategory",headerName:"Cost Center Category",editable:!1,flex:1}],fl=G.map(e=>{const t=_s[e];return t?{field:t,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null),xl=[...Be,...fl];l.useEffect(()=>{ge()},[]);let yl=l.useRef(null);const yt={convertJsonToExcel:()=>{let e=[];Be.forEach(t=>{t.headerName.toLowerCase()!=="action"&&!t.hide&&e.push({header:t.headerName,key:t.field})}),na({fileName:`Cost Center Data-${Ye(We).format("DD-MMM-YYYY")}`,columns:e,rows:V})},button:()=>s($,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>yt.convertJsonToExcel(),children:"Download"})},pl=()=>{let e=r=>{const a=URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.setAttribute("download","Cost Center_Mass Create.xls"),document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(a),Y(),_("Success"),H("Cost Center_Mass Create.xls has been downloaded successfully"),W("success")},t=r=>{r.message&&(Y(),_("Error"),H(`${r.message}`),W("danger"))};y(`/${f}/excel/downloadExcel`,"getblobfile",e,t)},Sl=()=>{var e=qt.map(a=>({costCenter:a.costCenter,controllingArea:a.controllingArea}));console.log("downloadPayload",e);let t=a=>{I(!1);const o=URL.createObjectURL(a),h=document.createElement("a");h.href=o,h.setAttribute("download","Cost Center_Mass Change.xls"),document.body.appendChild(h),h.click(),document.body.removeChild(h),URL.revokeObjectURL(o),Y(),_("Success"),H("Cost Center_Mass Change.xls has been downloaded successfully"),W("success")},r=a=>{a.message&&(Y(),_("Error"),H(`${a.message}`),W("danger"))};y(`/${f}/excel/downloadExcelWithData`,"postandgetblob",t,r,e)},Al=()=>{Es()},bl=()=>{Ns()},vl=()=>{J(!0),ve("Create"),u(ve("Create"))},Dl=()=>{ke(e=>!e)},pt=(e,t)=>{t!==0&&(ps(t),ke(!1),t===1?vl():t===2&&(u(ve("Create")),pl()))},$l=e=>{Q.current&&Q.current.contains(e.target)||ke(!1)},wl=()=>{Te(e=>!e)},El=()=>{Me(e=>!e)},Nl=e=>{X.current&&X.current.contains(e.target)||Te(!1)},St=(e,t)=>{console.log("indexx",t),t!==0&&(As(t),Te(!1),t===1?kl():t===2&&(ut.length>0?(console.log("selectedRows",ut),I(!0),Ds(!1),Sl()):console.log("Please select at least one row to download Excel.")))},At=(e,t)=>{t!==0&&(vs(t),Me(!1),t===1?bl():t===2&&Al())},kl=()=>{J(!0),u(ve("Change"))};return console.log("costCenterName",ce),s(aa,{children:ss===!0?s(Ol,{}):n("div",{ref:yl,children:[s(Il,{dialogState:ol,openReusableDialog:Y,closeReusableDialog:xt,dialogTitle:cl,dialogMessage:ft,handleDialogConfirm:xt,dialogOkText:"OK",dialogSeverity:Vt}),us&&s(ReusableSnackBar,{openSnackBar:Wt,alertMsg:ft,handleSnackBarClose}),s("div",{style:{...Rl,backgroundColor:"#FAFCFF"},children:n(fe,{spacing:1,children:[n(i,{container:!0,sx:Ll,children:[n(i,{item:!0,md:5,sx:Wl,children:[s(d,{variant:"h3",children:s("strong",{children:"Cost Center"})}),s(d,{variant:"body2",color:"#777",children:"This view displays the list of Cost Centers"})]}),s(i,{item:!0,md:7,sx:{display:"flex"},children:n(i,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[s(Bl,{title:"Search for multiple Cost Center numbers separated by comma",handleSearchAction:()=>nl(),module:"CostCenter",keyName:"number",message:"Search Cost Center ",clearSearchBar:rl}),s(zt,{title:"Reload",children:s(ee,{sx:Tt,children:s(Vl,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:Cl})})}),s(zt,{title:"Export Table",children:s(ee,{sx:Tt,onClick:yt.convertJsonToExcel,children:s(xe,{iconName:"IosShare"})})})]})})]}),s(i,{container:!0,sx:Ul,children:s(i,{item:!0,md:12,children:n(_l,{className:"filter-accordian",children:[s(Hl,{expandIcon:s(Yl,{}),"aria-controls":"panel1a-content",id:"panel1a-header",sx:{minHeight:"2rem !important",margin:"0px !important"},children:s(d,{sx:{fontWeight:"700"},children:"Search Cost Center"})}),n(ql,{sx:{padding:"0.5rem 1rem 0.5rem"},children:[n(i,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:[n(i,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Cost Center Name"}),s(S,{size:"small",fullWidth:!0,children:s(A,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:c==null?void 0:c.costCenterName,onChange:Ps,placeholder:"Enter Cost Center Name"})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Controlling Area"}),s(S,{size:"small",fullWidth:!0,children:s(w,{sx:{height:"31px"},fullWidth:!0,size:"small",value:c==null?void 0:c.controllingArea,onChange:Os,options:(ue==null?void 0:ue.ControllingArea)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Controlling Area"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Company Code"}),s(S,{fullWidth:!0,size:"small",children:s(w,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:Is,options:(g==null?void 0:g.CompCodeSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.companyCode,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Company Code"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Profit Center"}),s(S,{fullWidth:!0,size:"small",children:s(w,{sx:{height:"31px"},fullWidth:!0,size:"small",placeholder:"Select Profit Center",value:c==null?void 0:c.profitCenter,onChange:Rs,options:ue.ProfitCenterSearch??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Profit Center"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Hierarchy Area"}),s(S,{fullWidth:!0,size:"small",children:s(w,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:Ls,options:(g==null?void 0:g.HierarchyAreaSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.hierarchyArea,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Hierarchy Area"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Cost Center Category"}),s(S,{fullWidth:!0,size:"small",children:s(w,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:Ws,options:(g==null?void 0:g.CostCenterCategory)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.costCenterCategory,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Cost Center Category"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Add New Filters"}),s(S,{children:s(Gl,{sx:{font_Small:F,height:"31px",fontSize:"12px",width:"200px"},size:"small",multiple:!0,limitTags:2,value:G,onChange:Vs,renderValue:e=>e.join(", "),MenuProps:{MenuProps:jt},endAdornment:G.length>0&&s(Jl,{position:"end",children:s(ee,{size:"small",onClick:()=>et([]),"aria-label":"Clear selections",children:s(Kl,{})})}),children:Us.map(e=>n(ye,{value:e.title,children:[s(Ql,{checked:G.indexOf(e.title)>-1}),e.title]},e.title))})}),s(i,{style:{display:"flex",justifyContent:"space-around"}})]})]}),s(i,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:G.map((e,t)=>e==="Person Responsible"?s(i,{item:!0,children:n(fe,{children:[s(d,{sx:{fontSize:"12px"},children:e}),s(A,{sx:{font_Small:F,height:"31px",fontSize:"12px",width:"200px"},size:"small",fullWidth:!0,onChange:(r,a)=>tt({...v,[e]:r.target.value}),placeholder:`Enter ${e}`,value:v[e]})]})}):s(i,{item:!0,children:n(fe,{children:[s(d,{sx:{fontSize:"12px"},children:e}),s(w,{sx:{font_Small:F,height:"31px",fontSize:"12px",width:"200px"},size:"small",options:st??[],getOptionLabel:r=>`${r.code} - ${r.desc}`,placeholder:`Enter ${e}`,value:v[e],onChange:(r,a)=>tt({...v,[e]:a}),renderInput:r=>s(A,{sx:{fontSize:"12px !important"},...r,size:"small",placeholder:`Enter ${e}`,variant:"outlined"})})]})}))})]}),s(i,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:n(i,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:[s($,{variant:"outlined",sx:Xl,onClick:dl,children:"Clear"}),s($,{variant:"contained",sx:{...Zl,...jl},onClick:()=>ge(),children:"Search"})]})})]})]})})}),s(i,{item:!0,sx:{position:"relative"},children:s(fe,{children:s(ea,{isLoading:ls,module:"CostCenter",width:"100%",title:"List of Cost Centers ("+qe+")",rows:V,columns:xl,page:De,pageSize:$e,rowCount:qe??(V==null?void 0:V.length)??0,onPageChange:Jt,onPageSizeChange:Qt,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:ul,callback_onRowSingleClick:e=>{const t=e.row.costCenter.slice(0,10);console.log("materialNumber",t),te(`/masterDataCockpit/costCenter/displayCostCenter/${t}`,{state:e.row})},stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})})}),ta(Kt,"Cost Center","CreateCC")&&(B==null?void 0:B.role)==="Super User"||(B==null?void 0:B.role)==="Finance"?s(pe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(sa,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:as,onChange:e=>{rs(e)},children:[n(Ve,{variant:"contained",ref:Z,"aria-label":"split button",children:[s($,{size:"small",variant:"contained",onClick:()=>At(Oe[0],0),children:Oe[0]}),s($,{size:"small","aria-controls":Fe?"split-button-menu":void 0,"aria-expanded":Fe?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:El,children:s(xe,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),s(Ue,{sx:{zIndex:1},open:Fe,anchorEl:Z.current,placement:"top-end",children:s(pe,{style:{width:(bt=Z.current)==null?void 0:bt.clientWidth},children:s(_e,{onClickAway:Ms,children:s(He,{id:"split-button-menu",autoFocusItem:!0,children:Oe.slice(1).map((e,t)=>s(ye,{selected:t===bs-1,onClick:()=>At(e,t+1),children:e},e))})})})}),n(Ve,{variant:"contained",ref:Q,"aria-label":"split button",children:[s($,{size:"small",onClick:()=>pt(Pe[0],0),sx:{cursor:"default"},children:Pe[0]}),s($,{size:"small","aria-controls":Ne?"split-button-menu":void 0,"aria-expanded":Ne?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:Dl,children:s(xe,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),s(Ue,{sx:{zIndex:1},open:Ne,anchorEl:Q.current,placement:"top-end",children:s(pe,{style:{width:(vt=Q.current)==null?void 0:vt.clientWidth},children:s(_e,{onClickAway:$l,children:s(He,{id:"split-button-menu",autoFocusItem:!0,children:Pe.slice(1).map((e,t)=>s(ye,{selected:t===ys-1,onClick:()=>pt(e,t+1),children:e},e))})})})}),n(Ve,{variant:"contained",ref:X,"aria-label":"split button",children:[s($,{size:"small",onClick:()=>St(Ie[0],0),sx:{cursor:"default"},children:Ie[0]}),s($,{size:"small","aria-controls":ze?"split-button-menu":void 0,"aria-expanded":ze?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:wl,children:s(xe,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),s(Ue,{sx:{zIndex:1},open:ze,anchorEl:X.current,placement:"top-end",children:s(pe,{style:{width:(Dt=X.current)==null?void 0:Dt.clientWidth},children:s(_e,{onClickAway:Nl,children:s(He,{id:"split-button-menu",autoFocusItem:!0,children:Ie.slice(1).map((e,t)=>s(ye,{selected:t===Ss-1,onClick:()=>St(e,t+1),children:e},e))})})})}),n(Ft,{open:is,onClose:Re,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Mt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[s(d,{variant:"h6",children:"New Cost Center"}),s(ee,{sx:{width:"max-content"},onClick:Re,children:s(Pt,{})})]}),n(Ot,{sx:{padding:".5rem 1rem"},children:[n(i,{container:!0,spacing:3,children:[n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Controlling Area",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(w,{sx:{height:"42px"},required:"true",value:b,size:"small",onChange:(e,t)=>{ie(t),Ct(t)},options:(g==null?void 0:g.ControllingArea)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA"})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Cost Center",s("span",{style:{color:"red"},children:"*"})]}),n(S,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[n(i,{md:5,children:[s(w,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{oe(t)},options:(g==null?void 0:g.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE",error:Qe})}),Qe&&s(d,{variant:"caption",color:"error",children:"Please Select Any value"})]}),n(i,{md:7,children:[s(A,{sx:{fontSize:"12px !important",height:"40px"},fullWidth:!0,size:"small",value:M,onChange:e=>{const t=e.target.value;if(/^[a-zA-Z0-9\-/'#&]*$/.test(t))if(t.length>0&&t[0]===" ")U(t.trimStart());else{let r=t.toUpperCase();U(r)}},inputProps:{length:6,maxLength:6,style:{textTransform:"uppercase"}},placeholder:"Enter Cost Center",error:Ke,required:!0}),Ke&&s(d,{variant:"caption",color:"error",children:"Cost Center must be 10 digits"})]})]})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Valid From",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(Se,{dateAdapter:Ae,children:s(be,{slotProps:{textField:{size:"small"}},value:ce,onChange:e=>nt(e)})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Valid To",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(Se,{dateAdapter:Ae,children:s(be,{slotProps:{textField:{size:"small"}},value:Ee,onChange:e=>ot(e),maxDate:new Date(9999,12,31)})})})]})]}),ms&&s(i,{children:s(d,{style:{color:"red"},children:"Please Enter Mandatory Fields"})}),dt&&s(i,{children:s(d,{style:{color:"red"},children:"*The Cost Center with Controlling Area already exists. Please enter different Cost Center or Controlling Area"})})]}),n(It,{sx:{display:"flex",justifyContent:"end"},children:[s($,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Re,children:"Cancel"}),s($,{className:"button_primary--normal",type:"save",onClick:Ts,variant:"contained",disabled:!Ut,children:"Proceed"})]})]}),n(Ft,{open:ds,onClose:Le,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Mt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[s(d,{variant:"h6",children:"New Cost Center"}),s(ee,{sx:{width:"max-content"},onClick:Le,children:s(Pt,{})})]}),n(Ot,{sx:{padding:".5rem 1rem"},children:[n(i,{container:!0,spacing:3,children:[n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Controlling Area",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(w,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{ie(t),Ct(t),Qs(t)},options:(g==null?void 0:g.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA"})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Cost Center",s("span",{style:{color:"red"},children:"*"})]}),n(S,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[s(i,{md:5,children:s(w,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{oe(t)},options:(g==null?void 0:g.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})}),n(i,{md:7,children:[s(A,{sx:{fontSize:"12px !important",height:"40px"},fullWidth:!0,size:"small",value:M,onChange:e=>{const t=e.target.value;if(t.length>0&&t[0]===" ")U(t.trimStart());else{let r=t.toUpperCase();U(r)}},inputProps:{length:6,maxLength:6,style:{textTransform:"uppercase"}},placeholder:"Enter Cost Center",required:!0}),Ht&&s(d,{variant:"caption",color:"error",children:"Cost Center must be 10 digits"})]})]})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Valid From",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(Se,{dateAdapter:Ae,children:s(be,{slotProps:{textField:{size:"small"}},value:ce,onChange:e=>nt(e)})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Valid To",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(Se,{dateAdapter:Ae,children:s(be,{slotProps:{textField:{size:"small"}},value:Ee,onChange:e=>ot(e),maxDate:new Date(9999,12,31)})})})]}),s(la,{sx:{width:"100%",marginLeft:"2%"},children:s("b",{children:"Copy From"})}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Controlling Area",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(w,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{ct(t),Xs(t)},options:(g==null?void 0:g.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA"})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Cost Center",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:s(i,{md:12,children:s(w,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{gs(t)},options:(g==null?void 0:g.CompCodeCopy)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(A,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COST CENTER"})})})})]})]}),fs&&s(i,{children:s(d,{style:{color:"red"},children:"Please Enter Mandatory Fields"})}),dt&&s(i,{children:s(d,{style:{color:"red"},children:"*The Cost Center with Controlling Area already exists. Please enter different Cost Center or Controlling Area"})})]}),n(It,{sx:{display:"flex",justifyContent:"end"},children:[s($,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Le,children:"Cancel"}),s($,{className:"button_primary--normal",type:"save",onClick:Fs,variant:"contained",children:"Proceed"})]})]}),hs&&s(ma,{artifactId:"",artifactName:"",setOpen:J,handleUpload:hl})]})}):""]})})]})})};export{wa as default};
