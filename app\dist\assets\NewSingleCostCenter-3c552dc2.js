import{l as Ht,i as w,r as i,ir as Lr,a as t,j as l,T as D,a3 as te,sI as z,g_ as $,$ as h,hy as Vr,hA as qr,gR as Or,gS as Mr,hJ as Hr,w as S,hK as A,hP as q,h_ as X,B as M,F as H,u as jr,b as Jr,hs as Wr,hZ as zr,sJ as Ur,ht as _r,J as Kr,i7 as Ft,fV as Et,h1 as Rt,W as le,hI as It,fX as Bt,gQ as $t,fH as Lt,a1 as B,ho as Xr,h8 as Qr,hp as Vt,hv as Gr,i8 as Yr,hd as qt,ic as Zr,id as yr,ie as Cr,P as Pr,hE as en,gW as K,ih as Ot,hn as tn,i6 as rn,hQ as nn,hR as on,hS as cn,hT as sn,hU as an,hV as dn,ik as Mt}from"./index-fdfa25a0.js";import"./AutoCompleteType-3a9c9c9d.js";import"./dayjs.min-774e293a.js";import"./useChangeLogUpdate-3699f77c.js";function Q(r){var G,F,U,k;const m=Ht();var b=r.field.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").replaceAll("%","").split(" ").join("");let o=w(u=>u.costCenter.errorFields);const a=w(u=>u.costCenter.singleCCPayload);console.log("valuesfrompayload",a.Description);const f=u=>{const x=g=>{console.log("value",g),m(q({keyName:"Currency",data:g.body}))},E=g=>{console.log(g,"error in dojax")};S(`/${A}/data/getCurrency?companyCode=${u==null?void 0:u.code}`,"get",x,E)},p=u=>{const x=g=>{console.log("value",g),m(q({keyName:"Region",data:g.body}))},E=g=>{console.log(g,"error in dojax")};S(`/${A}/data/getRegionBasedOnCountry?country=${u==null?void 0:u.code}`,"get",x,E)};i.useEffect(()=>{var u,x;(((u=r==null?void 0:r.field)==null?void 0:u.visibility)==="0"||((x=r==null?void 0:r.field)==null?void 0:x.visibility)==="Required")&&m(Lr(b))},[]),console.log("test",Object.keys(a).length);const N=w(u=>u.AllDropDown.dropDown);if(((G=r.field)==null?void 0:G.fieldType)==="Input")return t(h,{item:!0,md:2,children:r.field.visibility==="Hidden"?null:l($,{children:[l(D,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(te,{size:"small",type:r.field.dataType==="QUAN"?"number":"",placeholder:`Enter ${r.field.fieldName}`,inputProps:{maxLength:r.field.maxLength},value:a[b],onChange:(u,x)=>{const E=u.target.value;Object.keys(a).length>0?(console.log("0"),E.length>0&&E[0]===" "?(console.log("1"),m(z({keyName:b,data:E.trimStart()}))):(console.log("2"),m(z({keyName:b,data:E.toUpperCase()})))):(console.log("3"),m(z({keyName:b,data:E.trimStart()})))},required:r.field.visibility==="Required"||r.field.visibility==="0",error:o.includes(b)})]})});if(((F=r.field)==null?void 0:F.fieldType)==="Drop Down")return t(h,{item:!0,md:2,children:r.field.visibility==="Hidden"?null:l($,{children:[l(D,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Vr,{sx:{height:"31px"},fullWidth:!0,size:"small",value:a[b],onChange:(u,x)=>{r.field.fieldName==="Company Code"&&f(x),r.field.fieldName==="Country/Reg"&&p(x),m(z({keyName:b,data:x}))},options:N[b]??[],required:r.field.visibility==="0"||r.field.visibility==="Required",getOptionLabel:u=>`${u==null?void 0:u.code} - ${u==null?void 0:u.desc}`,renderOption:(u,x)=>t("li",{...u,children:l(D,{style:{fontSize:12},children:[x==null?void 0:x.code," - ",x==null?void 0:x.desc]})}),renderInput:u=>t(te,{...u,variant:"outlined",placeholder:`Select ${r.field.fieldName}`,error:o.includes(b)})})]})});if(((U=r.field)==null?void 0:U.fieldType)==="Radio Button")return l(h,{item:!0,md:2,children:[l(D,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(qr,{sx:{padding:0},checked:a[b]==!0,onChange:u=>{m(z({keyName:b,data:u.target.checked}))}})]});if(((k=r.field)==null?void 0:k.fieldType)==="Calendar")return t(h,{item:!0,md:2,children:l($,{children:[l(D,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Or,{dateAdapter:Mr,children:t(Hr,{slotProps:{textField:{size:"small"}},value:a[b],onChange:u=>m(z({keyName:b,data:"/Date("+Date.parse(u)+")/"})),required:r.field.visibility==="0"||r.field.visibility==="Required"})})]})})}const ln=r=>{let m=Object==null?void 0:Object.entries(r==null?void 0:r.basicDataTabDetails);const[b,o]=i.useState([]);return i.useEffect(()=>{o(m==null?void 0:m.map(a=>l(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(h,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(h,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,p)=>f.sequenceNo-p.sequenceNo).map(f=>t(Q,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.basicDataTabDetails]),t(H,{children:b})},un=r=>{let m=Object==null?void 0:Object.entries(r==null?void 0:r.controlTabDetails);console.log("basic",m);const[b,o]=i.useState([]);return i.useEffect(()=>{o(m==null?void 0:m.map(a=>l(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(h,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(h,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,p)=>f.sequenceNo-p.sequenceNo).map(f=>t(Q,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.basicDataTabDetails]),t(H,{children:b})},hn=r=>{let m=Object==null?void 0:Object.entries(r==null?void 0:r.templatesTabDetails);console.log("basic",m);const[b,o]=i.useState([]);return i.useEffect(()=>{o(m==null?void 0:m.map(a=>l(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(h,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(h,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,p)=>f.sequenceNo-p.sequenceNo).map(f=>t(Q,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.templatesTabDetails]),t(H,{children:b})},mn=r=>{let m=Object==null?void 0:Object.entries(r==null?void 0:r.addressTabDetails);console.log("basic",m);const[b,o]=i.useState([]);return i.useEffect(()=>{o(m==null?void 0:m.map(a=>l(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(h,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(h,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,p)=>f.sequenceNo-p.sequenceNo).map(f=>t(Q,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.addressTabDetails]),t(H,{children:b})},fn=r=>{let m=Object==null?void 0:Object.entries(r==null?void 0:r.communicationTabDetails);console.log("basic",m);const[b,o]=i.useState([]);return i.useEffect(()=>{o(m==null?void 0:m.map(a=>l(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(h,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(h,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,p)=>f.sequenceNo-p.sequenceNo).map(f=>t(Q,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.communicationTabDetails]),t(H,{children:b})},gn=()=>{var we,pe,Ne,ke,Fe,Ee,Re,Ie,Be,$e,Le,Ve,qe,Oe,Me,He,je,Je,We,ze,Ue,_e,Ke,Xe,Qe,Ge,Ye,Ze,ye,Ce,Pe,et,tt,rt,nt,ot,ct,st,it,at,dt,lt,ut,ht,mt,ft,xt,bt,Dt,St,At,gt,Tt,vt,wt,pt;i.useState(0);const[r,m]=i.useState({}),o=jr().state,a=Ht(),f=Jr(),[p,N]=i.useState(!1),[G,F]=i.useState(!1),[U,k]=i.useState(""),[u,x]=i.useState(!1),[E,g]=i.useState(!0),[xn,O]=i.useState(!1),[jt,j]=i.useState(!1),[Jt,Y]=i.useState(!1),[Wt,re]=i.useState(!1),[zt,ue]=i.useState(!1),[Ut,ne]=i.useState(!1),[_t,Kt]=i.useState(!1),[Xt,oe]=i.useState(!0),[Z,he]=i.useState(""),[ce,Qt]=i.useState("");i.useState("");const[me,L]=i.useState(!1),[Gt,Yt]=i.useState(""),[y,Zt]=i.useState([]),[yt,fe]=i.useState(!1),[Ct,se]=i.useState(!0),[Pt,xe]=i.useState(!1),J=w(n=>n.appSettings),e=w(n=>n.costCenter.singleCCPayload),er=w(n=>n.costCenter.costCenterBasicData),tr=w(n=>n.costCenter.costCenterControl),rr=w(n=>n.costCenter.costCenterTemplate),nr=w(n=>n.costCenter.costCenterAddress),or=w(n=>n.costCenter.costCenterCommunication);w(n=>n.costCenter.costCenterHistory);const cr=w(n=>n.costCenter.requiredFields);console.log("controlTabDetails",e.RecordQuantity);let I=w(n=>n.userManagement.userData);const[C,be]=i.useState(0),sr=w(n=>n.costCenter.singleCCPayload),De=["BASIC DATA","CONTROL","TEMPLATES","ADDRESS","COMMUNICATION","ATTACHMENTS & COMMENTS"],ir=()=>{Te()?be(s=>s+1):ge()},ar=()=>{oe(!0),Te()?be(s=>s-1):ge()},P=()=>{se(!0),Kt(!1)},dr=()=>{let n="Basic Data";const s=d=>{a(nn(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${n}`,"get",s,c)},lr=()=>{let n="Control";const s=d=>{a(on(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${n}`,"get",s,c)},ur=()=>{let n="Templates";const s=d=>{a(cn(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${n}`,"get",s,c)},hr=()=>{let n="Address";const s=d=>{a(sn(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${n}`,"get",s,c)},mr=()=>{let n="Communication";const s=d=>{a(an(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${n}`,"get",s,c)},fr=()=>{let n="History";const s=d=>{a(dn(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${n}`,"get",s,c)},xr=()=>{const n=c=>{a(q({keyName:"UserResponsible",data:c.body}))},s=c=>{console.log(c)};S(`/${A}/data/getUserResponsible`,"get",n,s)},br=()=>{const n=c=>{a(q({keyName:"CostCenterCategory",data:c.body}))},s=c=>{console.log(c)};S(`/${A}/data/getCostCenterCategory`,"get",n,s)},Dr=()=>{const n=c=>{a(q({keyName:"CostCenter",data:c.body}))},s=c=>{console.log(c)};S(`/${A}/data/getCostCenter`,"get",n,s)},Se=(n,s)=>{const c=n.target.value;if(c.length>0&&c[0]===" ")he(c.trimStart());else{let d=c.toUpperCase();he(d)}};var ie={CostCenterHeaderID:"",ControllingArea:(pe=(we=o==null?void 0:o.controllingAreaData)==null?void 0:we.newControllingArea)!=null&&pe.code?(ke=(Ne=o==null?void 0:o.controllingAreaData)==null?void 0:Ne.newControllingArea)==null?void 0:ke.code:"",Testrun:Ct,Action:"I",Remarks:Z||"",ReqCreatedBy:I==null?void 0:I.user_id,ReqCreatedOn:"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",MassRequestStatus:"",Toitem:[{CostCenterID:"",Costcenter:`${(Fe=o==null?void 0:o.companyCode)==null?void 0:Fe.newCompanyCode.code}${(Ee=o==null?void 0:o.costCenterName)==null?void 0:Ee.newCostCenterName}`?`${(Re=o==null?void 0:o.companyCode)==null?void 0:Re.newCompanyCode.code}${(Ie=o==null?void 0:o.costCenterName)==null?void 0:Ie.newCostCenterName}`:"",ValidFrom:(Be=o==null?void 0:o.validFromDate)!=null&&Be.newValidFromDate?"/Date("+Date.parse(($e=o==null?void 0:o.validFromDate)==null?void 0:$e.newValidFromDate)+")/":"",ValidTo:(Le=o==null?void 0:o.validToDate)!=null&&Le.newValidToDate?"/Date("+Date.parse((Ve=o==null?void 0:o.validToDate)==null?void 0:Ve.newValidToDate)+")/":"",PersonInCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",CostcenterType:(qe=e==null?void 0:e.CostCenterCategory)!=null&&qe.code?(Oe=e==null?void 0:e.CostCenterCategory)==null?void 0:Oe.code:"",CostctrHierGrp:(Me=e==null?void 0:e.HierarchyArea)!=null&&Me.code?(He=e==null?void 0:e.HierarchyArea)==null?void 0:He.code:"",BusArea:(je=e==null?void 0:e.BusinessArea)!=null&&je.code?(Je=e==null?void 0:e.BusinessArea)==null?void 0:Je.code:"",CompCode:(We=e==null?void 0:e.CompanyCode)!=null&&We.code?(ze=e==null?void 0:e.CompanyCode)==null?void 0:ze.code:"",Currency:(Ue=e==null?void 0:e.Currency)!=null&&Ue.code?(_e=e==null?void 0:e.Currency)==null?void 0:_e.code:"",ProfitCtr:(Ke=e==null?void 0:e.ProfitCenter)!=null&&Ke.code?(Xe=e==null?void 0:e.ProfitCenter)==null?void 0:Xe.code:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Descript:e!=null&&e.Description?e==null?void 0:e.Description:"",PersonInChargeUser:(Qe=e==null?void 0:e.UserResponsible)!=null&&Qe.code?(Ge=e==null?void 0:e.UserResponsible)==null?void 0:Ge.code:"",RecordQuantity:(e==null?void 0:e.RecordQuantity)===!0?"X":"",LockIndActualPrimaryCosts:(e==null?void 0:e.ActualPrimaryCosts)===!0?"X":"",LockIndPlanPrimaryCosts:(e==null?void 0:e.PlanPrimaryCosts)===!0?"X":"",LockIndActSecondaryCosts:(e==null?void 0:e.ActsecondaryCosts)===!0?"X":"",LockIndPlanSecondaryCosts:(e==null?void 0:e.PlanSecondaryCosts)===!0?"X":"",LockIndActualRevenues:(e==null?void 0:e.ActualRevenue)===!0?"X":"",LockIndPlanRevenues:(e==null?void 0:e.PlanRevenue)===!0?"X":"",LockIndCommitmentUpdate:(e==null?void 0:e.CommitmentUpdate)===!0?"X":"",ConditionTableUsage:"",Application:"",CstgSheet:(Ye=e==null?void 0:e.CostingSheet)!=null&&Ye.code?(Ze=e==null?void 0:e.CostingSheet)==null?void 0:Ze.code:"",ActyIndepTemplate:(ye=e==null?void 0:e.ActyIndepFromPlngTemp)!=null&&ye.code?(Ce=e==null?void 0:e.ActyIndepFromPlngTemp)==null?void 0:Ce.code:"",ActyDepTemplate:(Pe=e==null?void 0:e.ActyDepFromPlngTemp)!=null&&Pe.code?(et=e==null?void 0:e.ActyDepFromPlngTemp)==null?void 0:et.code:"",AddrTitle:e!=null&&e.Title?e==null?void 0:e.Title:"",AddrName1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",AddrName2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",AddrName3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",AddrName4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",AddrStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",AddrCity:e!=null&&e.Location?e==null?void 0:e.Location:"",AddrDistrict:e!=null&&e.District?e==null?void 0:e.District:"",AddrCountry:(tt=e==null?void 0:e.CountryReg)!=null&&tt.code?(rt=e==null?void 0:e.CountryReg)==null?void 0:rt.code:"",AddrCountryIso:"",AddrTaxjurcode:(nt=e==null?void 0:e.Jurisdiction)!=null&&nt.code?(ot=e==null?void 0:e.Jurisdiction)==null?void 0:ot.code:"",AddrPoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",AddrPostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",AddrPobxPcd:e!=null&&e.POBoxPostCod?e==null?void 0:e.POBoxPostCod:"",AddrRegion:(ct=e==null?void 0:e.Region)!=null&&ct.code?(st=e==null?void 0:e.Region)==null?void 0:st.code:"",TelcoLangu:"",TelcoLanguIso:(it=e==null?void 0:e.LanguageKey)!=null&&it.code?(at=e==null?void 0:e.LanguageKey)==null?void 0:at.code:"",TelcoTelephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",TelcoTelephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",TelcoTelebox:e!=null&&e.TeleboxNumber?e==null?void 0:e.TeleboxNumber:"",TelcoTelex:e!=null&&e.TelexNumber?e==null?void 0:e.TelexNumber:"",TelcoFaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",TelcoTeletex:e!=null&&e.TeletexNumber?e==null?void 0:e.TeletexNumber:"",TelcoPrinter:e!=null&&e.PrinterDestination?e==null?void 0:e.PrinterDestination:"",TelcoDataLine:e!=null&&e.DataLine?e==null?void 0:e.DataLine:"",ActyDepTemplateAllocCc:(dt=e==null?void 0:e.ActyDepAllocTemp)!=null&&dt.code?(lt=e==null?void 0:e.ActyDepAllocTemp)==null?void 0:lt.code:"",ActyDepTemplateSk:(ut=e==null?void 0:e.TempActStatKeyFigure)!=null&&ut.code?(ht=e==null?void 0:e.TempActStatKeyFigure)==null?void 0:ht.code:"",ActyIndepTemplateAllocCc:(mt=e==null?void 0:e.ActyIndepAllocTemp)!=null&&mt.code?(ft=e==null?void 0:e.ActyIndepAllocTemp)==null?void 0:ft.code:"",ActyIndepTemplateSk:(xt=e==null?void 0:e.TempActStatKeyFigure)!=null&&xt.code?(bt=e==null?void 0:e.TempActStatKeyFigure)==null?void 0:bt.code:"",AvcActive:!1,AvcProfile:"",BudgetCarryingCostCtr:"",CurrencyIso:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",FuncArea:(Dt=e==null?void 0:e.FunctionalArea)!=null&&Dt.code?(St=e==null?void 0:e.FunctionalArea)==null?void 0:St.code:"",FuncAreaFixAssigned:"",FuncAreaLong:"",Fund:"",FundFixAssigned:"",GrantFixAssigned:"",GrantId:"",JvEquityTyp:"",JvJibcl:"",JvJibsa:"",JvOtype:"",JvRecInd:"",JvVenture:"",Logsystem:""}]};const[Ae,Sr]=i.useState(0),Ar=(n,s)=>{const c=T=>{a(q({keyName:n,data:T.body})),Sr(V=>V+1)},d=T=>{console.log(T)};S(`/${A}/data/${s}`,"get",c,d)},gr=()=>{var n,s;(s=(n=Ot)==null?void 0:n.costCenter)==null||s.map(c=>{Ar(c==null?void 0:c.keyName,c==null?void 0:c.endPoint)})},Tr=()=>{var n,s;Ae==((s=(n=Ot)==null?void 0:n.costCenter)==null?void 0:s.length)?j(!1):j(!0)};i.useEffect(()=>{Tr()},[Ae]),i.useEffect(()=>{gr(),dr(),lr(),ur(),hr(),mr(),xr(),br(),Dr(),fr(),vr(),wr(),a(Wr())},[]),i.useEffect(()=>{Qt(zr("CC"))},[]);const vr=()=>{var c,d;const n=T=>{a(q({keyName:"HierarchyArea",data:T.body}))},s=T=>{console.log(T)};S(`/${A}/data/getHierarchyArea?controllingArea=${(d=(c=o==null?void 0:o.controllingAreaData)==null?void 0:c.newControllingArea)==null?void 0:d.code}`,"get",n,s)},wr=()=>{var c,d;const n=T=>{a(q({keyName:"ProfitCenter",data:T.body}))},s=T=>{console.log(T)};S(`/${A}/data/getProfitCenterAsPerControllingArea?controllingArea=${(d=(c=o==null?void 0:o.controllingAreaData)==null?void 0:c.newControllingArea)==null?void 0:d.code}`,"get",n,s)},ae=()=>{ne(!0)},W=()=>{re(!0)},de=()=>{re(!1)},pr=()=>{zt?(ne(!1),ue(!1)):(ne(!1),f("/masterDataCockpit/costCenter"))},ge=()=>{fe(!0)},Te=()=>tn(sr,cr,Zt);i.useEffect(()=>{a(Ur(y))},[y]);const Nr=()=>{j(!0);const n=c=>{if(j(!1),c.statusCode===200){N("Create"),k(`Cost Center has been Submitted for review NCS${c.body}`),L(!1),x("success"),g(!1),F(!0),ae(),O(!0),P();const d={artifactId:ce,createdBy:I==null?void 0:I.emailId,artifactType:"CostCenter",requestId:`NCS${c==null?void 0:c.body}`},T=R=>{console.log("Second API success",R)},V=R=>{console.error("Second API error",R)};S(`/${Mt}/documentManagement/updateDocRequestId`,"post",T,V,d)}else N("Create"),F(!1),k("Creation Failed"),L(!1),x("danger"),g(!1),O(!0),W(),_();handleClose()},s=c=>{console.log(c)};S(`/${A}/alter/costCenterSubmitForReview`,"post",n,s,ie)},kr=()=>{x(!1),W(),N("Confirm"),k("Do You Want to Save as Draft ?"),L(!0),Yt("proceed")};console.log(me,"setHandleExtrabutton");const Fr=()=>{j(!0);const n=c=>{if(de(),j(!1),c.statusCode===200){console.log("success"),N("Create"),k(`Cost Center has been Saved with ID NCS${c.body}`),L(!1),x("success"),g(!1),F(!0),ae(),O(!0);const d={artifactId:ce,createdBy:I==null?void 0:I.emailId,artifactType:"CostCenter",requestId:`NCS${c==null?void 0:c.body}`},T=R=>{console.log("Second API success",R)},V=R=>{console.error("Second API error",R)};S(`/${Mt}/documentManagement/updateDocRequestId`,"post",T,V,d)}else N("Save"),F(!1),k("Failed Saving the Data"),L(!1),x("danger"),g(!1),O(!0),W();handleClose()},s=c=>{console.log(c)};S(`/${A}/alter/costCenterAsDraft`,"post",n,s,ie)},Er=()=>{var V,R;Y(!0);const n={coArea:((R=(V=o==null?void 0:o.controllingAreaData)==null?void 0:V.newControllingArea)==null?void 0:R.code)||"",name:e!=null&&e.Name?e==null?void 0:e.Name.toUpperCase():""},s=v=>{var ee,Nt,kt;v.statusCode===201?(N("Create"),N("Create"),k("All Data has been Validated. Cost Center can be Sent for Review"),L(!1),x("success"),g(!1),F(!0),ae(),O(!0),ue(!0),(n.coArea!==""||n.name!=="")&&S(`/${A}/alter/fetchCCDescriptionDupliChk`,"post",c,d,n)):(N("Error"),F(!1),k(`${(ee=v==null?void 0:v.body)!=null&&ee.message[0]?(Nt=v==null?void 0:v.body)==null?void 0:Nt.message[0]:(kt=v==null?void 0:v.body)==null?void 0:kt.value}`),L(!1),x("danger"),g(!1),O(!0),W(),Y(!1))},c=v=>{v.body.length===0||!v.body.some(ee=>ee.toUpperCase()===n.name)?(Y(!1),oe(!1)):(Y(!1),N("Duplicate Check"),F(!1),k("There is a direct match for the Cost Center name. Please change the name."),L(!1),x("danger"),g(!1),O(!0),W(),oe(!0))},d=v=>{console.log(v)},T=v=>{console.log(v)};S(`/${A}/alter/validateCostCenter`,"post",s,T,ie)},ve=()=>{_(),Nr()},Rr=()=>{se(!1),xe(!0)},Ir=()=>{fe(!1)},Br=n=>{switch(n){case 0:return t(ln,{basicDataTabDetails:er,dropDownData:r});case 1:return t(un,{controlTabDetails:tr,dropDownData:r});case 2:return t(hn,{templatesTabDetails:rr,dropDownData:r});case 3:return t(mn,{addressTabDetails:nr,dropDownData:r});case 4:return t(fn,{communicationTabDetails:or,dropDownData:r});case 5:return t(rn,{title:"CostCenter",useMetaData:!1,artifactId:ce,artifactName:"CostCenter"});default:return"Unknown step"}},$r=()=>{re(!1)},_=()=>{se(!0),xe(!1)};return t(H,{children:jt===!0?t(_r,{}):l("div",{children:[t(Kr,{dialogState:Wt,openReusableDialog:W,closeReusableDialog:de,dialogTitle:p,dialogMessage:U,handleDialogConfirm:de,dialogOkText:"OK",showExtraButton:me,showCancelButton:!0,dialogSeverity:u,handleDialogReject:$r,handleExtraText:Gt,handleExtraButton:Fr}),G&&t(Ft,{openSnackBar:Ut,alertMsg:U,handleSnackBarClose:pr}),y.length!=0&&t(Ft,{openSnackBar:yt,alertMsg:"Please fill the following Field: "+y.join(", "),handleSnackBarClose:Ir}),l(Et,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:_t,onClose:P,children:[l(Rt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(D,{variant:"h6",children:"Remarks"}),t(le,{sx:{width:"max-content"},onClick:P,children:t(It,{})})]}),t(Bt,{sx:{padding:".5rem 1rem"},children:t($,{children:t(M,{sx:{minWidth:400},children:t($t,{sx:{height:"auto"},fullWidth:!0,children:t(te,{sx:{backgroundColor:"#F5F5F5"},value:Z,onChange:Se,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(Lt,{sx:{display:"flex",justifyContent:"end"},children:[t(B,{sx:{width:"max-content",textTransform:"capitalize"},onClick:P,children:"Cancel"}),t(B,{className:"button_primary--normal",type:"save",onClick:ve,variant:"contained",children:"Submit"})]})]}),l(Et,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Pt,onClose:_,children:[l(Rt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(D,{variant:"h6",children:"Remarks"}),t(le,{sx:{width:"max-content"},onClick:_,children:t(It,{})})]}),t(Bt,{sx:{padding:".5rem 1rem"},children:t($,{children:t(M,{sx:{minWidth:400},children:t($t,{sx:{height:"auto"},fullWidth:!0,children:t(te,{sx:{backgroundColor:"#F5F5F5"},value:Z,onChange:Se,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(Lt,{sx:{display:"flex",justifyContent:"end"},children:[t(B,{sx:{width:"max-content",textTransform:"capitalize"},onClick:_,children:"Cancel"}),t(B,{className:"button_primary--normal",type:"save",onClick:ve,variant:"contained",children:"Submit"})]})]}),t(Xr,{sx:{color:"#fff",zIndex:n=>n.zIndex.drawer+1},open:Jt,children:t(Qr,{color:"inherit"})}),t(h,{container:!0,style:{...Vt,backgroundColor:"#FAFCFF"},children:l(h,{sx:{width:"inherit"},children:[t(h,{item:!0,md:7,style:{padding:"16px",display:"flex"},children:l(h,{item:!0,md:5,sx:{display:"flex"},children:[t(h,{children:t(le,{color:"primary","aria-label":"upload picture",component:"label",sx:Gr,children:t(Yr,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{f("/masterDataCockpit/costCenter"),a(clearPayload()),a(clearOrgData())}})})}),l(h,{children:[t(D,{variant:"h3",children:t("strong",{children:"Create Cost Center"})}),t(D,{variant:"body2",color:"#777",children:"This view creates a new Cost Center"})]})]})}),t(h,{container:!0,style:{padding:"0 1rem 0 1rem"},children:l(h,{container:!0,sx:Vt,children:[l(h,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[l(h,{item:!0,md:10,sx:{marginLeft:"40px",marginBottom:"20px"},children:[t(h,{item:!0,sx:{paddingTop:"2px !important"},children:l($,{flexDirection:"row",children:[t("div",{style:{width:"12%"},children:t(D,{variant:"body2",color:"#777",children:"Cost Center"})}),l(D,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",(At=o==null?void 0:o.companyCode)==null?void 0:At.newCompanyCode.code,(gt=o==null?void 0:o.costCenterName)==null?void 0:gt.newCostCenterName]})]})}),t(h,{item:!0,sx:{paddingTop:"2px !important"},children:l($,{flexDirection:"row",children:[t("div",{style:{width:"12%"},children:t(D,{variant:"body2",color:"#777",children:"Controlling Area"})}),l(D,{variant:"body2",fontWeight:"bold",children:[":"," ",(vt=(Tt=o==null?void 0:o.controllingAreaData)==null?void 0:Tt.newControllingArea)==null?void 0:vt.code]})]})})]}),l(h,{item:!0,md:2,sx:{marginLeft:"40px",marginBottom:"20px"},children:[t(h,{item:!0,sx:{paddingTop:"2px !important"},children:l($,{flexDirection:"row",children:[t("div",{style:{width:"50%"},children:t(D,{variant:"body2",color:"#777",children:"Valid From"})}),l(D,{variant:"body2",fontWeight:"bold",children:[":"," ",qt((wt=o==null?void 0:o.validFromDate)==null?void 0:wt.newValidFromDate).format(J==null?void 0:J.dateFormat)]})]})}),t(h,{item:!0,sx:{paddingTop:"2px !important"},children:l($,{flexDirection:"row",children:[t("div",{style:{width:"50%"},children:t(D,{variant:"body2",color:"#777",children:"Valid To"})}),l(D,{variant:"body2",fontWeight:"bold",children:[":"," ",qt((pt=o==null?void 0:o.validToDate)==null?void 0:pt.newValidToDate).format(J==null?void 0:J.dateFormat)]})]})})]})]}),t(h,{container:!0,children:t(Zr,{activeStep:C,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},children:De.map((n,s)=>t(yr,{children:t(Cr,{sx:{fontWeight:"700"},children:n})},n))})}),t(h,{container:!0,children:Br(C)})]})})]})}),t(Pr,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(en,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:kr,children:"Save As Draft"}),t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:ar,disabled:C===0,children:"Back"}),C===De.length-1?l(H,{children:[t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:Er,children:"Validate"}),t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:Rr,disabled:Xt,children:"Submit For Review"})]}):t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:ir,children:"Next"})]})})]})})};export{gn as default};
