import React from 'react';
import { SvgIcon, useTheme } from '@mui/material';

const CustomDashboardIcon = (props) => {
  const theme = useTheme();
  
  return (
    <SvgIcon {...props} viewBox="0 0 24 24">
      <defs>
        <linearGradient id="dashboardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={theme.palette.primary.main} />
          <stop offset="100%" stopColor={theme.palette.primary.dark} />
        </linearGradient>
      </defs>
      <path 
        d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" 
        fill="url(#dashboardGradient)"
      />
    </SvgIcon>
  );
};

export default CustomDashboardIcon;
