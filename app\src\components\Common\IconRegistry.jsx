import React from 'react';
import {
  Home as HomeIcon,
  Dashboard as DashboardIcon,
  GroupWork as GroupWorkIcon,
  PendingActions as PendingActionsIcon,
  Task as TaskIcon,
  AssignmentInd as AssignmentIndIcon,
  HowToReg as HowToRegIcon,
  Dataset as DatasetIcon,
  ShoppingCart as ShoppingCartIcon,
  TrendingDown as TrendingDownIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  Schema as SchemaIcon,
  MergeType as MergeTypeIcon,
  AccountTree as AccountTreeIcon,
  FolderOpen as FolderOpenIcon,
  Settings as SettingsIcon,
  Article as ArticleIcon,
  Person as PersonIcon,
  AccountBalance as AccountBalanceIcon,
  AccessAlarm as AccessAlarmIcon,
  MailOutline as MailOutlineIcon,
  AppSettingsAlt as AppSettingsAltIcon,
} from '@mui/icons-material';

// Import your custom SVG icons
import HomeIconActive from '../Icons/HomeIconActive';
import HomeIconPassive from '../Icons/HomeIconPassive';

// Icon registry mapping string names to components
export const iconRegistry = {
  // MUI Icons
  'Home': HomeIcon,
  'Dashboard': DashboardIcon,
  'GroupWork': GroupWorkIcon,
  'PendingActions': PendingActionsIcon,
  'Task': TaskIcon,
  'AssignmentInd': AssignmentIndIcon,
  'HowToReg': HowToRegIcon,
  'Dataset': DatasetIcon,
  'ShoppingCart': ShoppingCartIcon,
  'TrendingDown': TrendingDownIcon,
  'TrendingUp': TrendingUpIcon,
  'Assignment': AssignmentIcon,
  'Schema': SchemaIcon,
  'MergeType': MergeTypeIcon,
  'AccountTree': AccountTreeIcon,
  'FolderOpen': FolderOpenIcon,
  'Settings': SettingsIcon,
  'Article': ArticleIcon,
  'Person': PersonIcon,
  'AccountBalance': AccountBalanceIcon,
  'AccessAlarm': AccessAlarmIcon,
  'MailOutline': MailOutlineIcon,
  'AppSettingsAlt': AppSettingsAltIcon,
  
  // Custom SVG Icons
  'HomeIconActive': HomeIconActive,
  'HomeIconPassive': HomeIconPassive,
};

// Component to render icons by name
export const IconRenderer = ({ 
  iconName, 
  sx = {}, 
  color = 'inherit', 
  fontSize = 'medium',
  ...props 
}) => {
  const IconComponent = iconRegistry[iconName];
  
  if (!IconComponent) {
    console.warn(`Icon "${iconName}" not found in registry`);
    return null;
  }
  
  return (
    <IconComponent 
      sx={sx} 
      color={color} 
      fontSize={fontSize}
      {...props}
    />
  );
};

export default IconRenderer;
