import React from 'react';
import { ListItem, ListItemIcon, ListItemText } from '@mui/material';
import { IconRenderer } from './IconRegistry';

const NavigationItem = ({ item, isActive = false }) => {
  const iconName = isActive ? item.hoverIcon : item.icon;
  
  return (
    <ListItem button>
      <ListItemIcon>
        <IconRenderer 
          iconName={iconName}
          color={isActive ? 'primary' : 'inherit'}
          sx={{ 
            transition: 'color 0.2s ease',
            color: isActive ? 'primary.main' : 'text.secondary'
          }}
        />
      </ListItemIcon>
      <ListItemText 
        primary={item.displayName}
        secondary={item.description}
      />
    </ListItem>
  );
};

export default NavigationItem;
