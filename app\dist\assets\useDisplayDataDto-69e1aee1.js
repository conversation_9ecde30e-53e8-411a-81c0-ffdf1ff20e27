import{l as ie,r as o,k as Ee,m as Ce,k0 as R,jZ as Te,j4 as e,w as ne,H as Ae,G as Ne,y as ce,k1 as Ie,k2 as v,k3 as ue,k4 as re,jL as _e,t as le,k5 as oe,jn as Re,j7 as Se,E as K}from"./index-fdfa25a0.js";import{u as de,a as te,b as Ge}from"./useFinanceCostingRows-2aab0ea4.js";const Oe=()=>{const n=ie(),[z,u]=o.useState(!1),[Y,r]=o.useState(null),{getChangeTemplate:$}=de(),{fetchDisplayDataRows:Q}=te(),{createFCRows:V}=Ge(),{customError:S}=Ee(),{showSnackbar:Z}=Ce();return{getDisplayData:o.useCallback(async(J,I,D,C,E)=>new Promise((_,d)=>{u(!0),r(null),n(R(!0));const A=J,l=Te(Ae.CURRENT_TASK,!0,{}),s=I||(C==null?void 0:C.ATTRIBUTE_2)||(l==null?void 0:l.ATTRIBUTE_2);let k=D?{massCreationId:E!=null&&E.isBifurcated?"":s===e.CREATE||s===e.CREATE_WITH_UPLOAD?A.slice(3):"",massChildCreationId:E!=null&&E.isBifurcated&&(s===e.CREATE||s===e.CREATE_WITH_UPLOAD)?A.slice(3):"",massChangeId:E!=null&&E.isBifurcated?"":s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD?A.slice(3):"",massExtendId:E!=null&&E.isBifurcated?"":s===e.EXTEND||s===e.EXTEND_WITH_UPLOAD?A.slice(3):"",massSchedulingId:E!=null&&E.isBifurcated?"":s===e.FINANCE_COSTING?A.slice(3):"",screenName:s===e.FINANCE_COSTING?"":s,dtName:s===e.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:s===e.FINANCE_COSTING?"":"v2",page:0,size:s===e.FINANCE_COSTING?100:s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:C==null?void 0:C.ATTRIBUTE_5,Region:"",massChildSchedulingId:E!=null&&E.isBifurcated&&s===e.FINANCE_COSTING?A.slice(3):"",massChildExtendId:E!=null&&E.isBifurcated&&(s===e.EXTEND||s===e.EXTEND_WITH_UPLOAD)?A.slice(3):"",massChildChangeId:E!=null&&E.isBifurcated&&(s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD)?A.slice(3):""}:{massCreationId:"",massChangeId:"",massSchedulingId:s===e.FINANCE_COSTING||s==="Finance Costing"?A.slice(3):"",massExtendId:"",screenName:s==="MASS_CREATE"||s==="Mass Create"||s===e.CREATE?e.CREATE:s===e.FINANCE_COSTING?"":e.CHANGE,dtName:s===e.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:s===e.FINANCE_COSTING?"":"v2",page:0,size:s===e.FINANCE_COSTING||I===e.FINANCE_COSTING?100:I===e.CHANGE||I===e.CHANGE_WITH_UPLOAD||s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:C==null?void 0:C.ATTRIBUTE_5,Region:"",massChildCreationId:s==="MASS_CREATE"||s==="Mass Create"||s===e.CREATE||s===e.CREATE_WITH_UPLOAD?A.slice(3):"",massChildSchedulingId:"",massChildExtendId:s===e.EXTEND||s===e.EXTEND_WITH_UPLOAD?A.slice(3):"",massChildChangeId:s==="MASS_CHANGE"||s==="Mass Change"||s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD?A.slice(3):""};const w=async i=>{var t,G,O,g,h,m,p,H,L,U,F,f,P,q,y,M,B,W,x,b,j;try{if((i==null?void 0:i.statusCode)===ce.STATUS_200){n(R(!1)),u(!1);const T=i.body;if(n(Ie(i==null?void 0:i.totalElements)),(i==null?void 0:i.totalPages)===1||(i==null?void 0:i.currentPage)+1===(i==null?void 0:i.totalPages)?(n(v(i==null?void 0:i.totalElements)),n(ue(!0))):n(v(((i==null?void 0:i.currentPage)+1)*(i==null?void 0:i.pageSize))),(C==null?void 0:C.ATTRIBUTE_2)===e.CHANGE||(C==null?void 0:C.ATTRIBUTE_2)===e.CHANGE_WITH_UPLOAD||I===e.CHANGE_WITH_UPLOAD||I===e.CHANGE){n(re({keyName:"requestHeaderData",data:(t=T[0])==null?void 0:t.Torequestheaderdata})),$(((G=T[0])==null?void 0:G.Torequestheaderdata)||"",T[0]||{}),Q(T),_(i);return}if(I===e.FINANCE_COSTING||(C==null?void 0:C.ATTRIBUTE_2)===e.FINANCE_COSTING){const c={ReqCreatedBy:(g=(O=T[0])==null?void 0:O.Torequestheaderdata)==null?void 0:g.ReqCreatedBy,RequestStatus:(m=(h=T[0])==null?void 0:h.Torequestheaderdata)==null?void 0:m.RequestStatus,Region:(H=(p=T[0])==null?void 0:p.Torequestheaderdata)==null?void 0:H.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(U=(L=T[0])==null?void 0:L.Torequestheaderdata)==null?void 0:U.RequestType,RequestDesc:(f=(F=T[0])==null?void 0:F.Torequestheaderdata)==null?void 0:f.RequestDesc,RequestPriority:(q=(P=T[0])==null?void 0:P.Torequestheaderdata)==null?void 0:q.RequestPriority,LeadingCat:(M=(y=T[0])==null?void 0:y.Torequestheaderdata)==null?void 0:M.LeadingCat,RequestId:(W=(B=T[0])==null?void 0:B.Torequestheaderdata)==null?void 0:W.RequestId,TemplateName:(b=(x=T[0])==null?void 0:x.Torequestheaderdata)==null?void 0:b.TemplateName};n(_e({data:c}));const se=await V(T);n(le(se)),_(i);return}const N=await oe(T);await n(Re({data:N==null?void 0:N.payload}));const ee=Object.keys(N==null?void 0:N.payload).filter(c=>!isNaN(Number(c))),X={};ee.forEach(c=>{X[c]=N==null?void 0:N.payload[c]}),n(Se((j=Object.values(X))==null?void 0:j.map(c=>c.headerData))),_(i)}else Z(i==null?void 0:i.message,"error")}catch(T){S(K.ERROR_GET_DISPLAY_DATA),r(T),u(!1),d(T)}},a=i=>{S(K.ERROR_FETCHING_DATA),r(i),u(!1),n(R(!1)),d(i)};ne(`/${Ne}/data/displayMassMaterialDTO`,"post",w,a,k)}),[n]),loading:z,error:Y,clearError:()=>r(null)}},pe=Oe;export{pe as u};
