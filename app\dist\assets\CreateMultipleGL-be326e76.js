import{r,l as ht,b as mt,i as O,u as ft,hZ as Dt,a as t,ht as yt,j as h,fV as J,h1 as Q,T as D,W as q,hI as Fe,fX as Z,g_ as Y,B as Pe,gQ as vt,a3 as St,fH as x,a1 as G,J as wt,i7 as At,hl as kt,$ as y,hp as bt,hv as Lt,i8 as It,h as Rt,kf as Tt,i6 as Mt,hC as Ne,P as Gt,hE as Bt,gW as ee,ho as Ft,h8 as Pt,F as Nt,ih as Oe,w as A,io as E,hP as Ot,ik as Ee}from"./index-fdfa25a0.js";const zt=()=>{var De;const[z,te]=r.useState(!1),[_e,Et]=r.useState("1"),[j,$e]=r.useState([]),[ae,k]=r.useState(""),[Ve,K]=r.useState(!1),[_t,b]=r.useState(!1),[qe,S]=r.useState(!1),[ze,L]=r.useState(!1),[$t,I]=r.useState(!0),[je,R]=r.useState(!1),[Ke,oe]=r.useState(!1),[ne,se]=r.useState(!0),[Ue,m]=r.useState(!1),[le,B]=r.useState(!1),[We,re]=r.useState(!1),[U,ie]=r.useState(""),[_,Xe]=r.useState([]),[He,ce]=r.useState(!1),[Je,de]=r.useState(!1),[W,Qe]=r.useState(""),[Ze,ue]=r.useState(!1),[Ye,xe]=r.useState([]),et=ht(),X=mt();O(e=>e.generalLedger.MultipleGLData),ft();const F=O(e=>e.generalLedger.MultipleGLData);O(e=>e.appSettings);let w=O(e=>e.generalLedger.handleMassMode);console.log("massHandleType",w);let u=O(e=>e.userManagement.userData);const[ge,tt]=r.useState(0),at=(e,o)=>{const a=g=>{et(Ot({keyName:e,data:g.body})),tt(n=>n+1)},i=g=>{console.log(g)};A(`/${E}/data/${o}`,"get",a,i)},ot=()=>{var e,o;(o=(e=Oe)==null?void 0:e.generalLedger)==null||o.map(a=>{at(a==null?void 0:a.keyName,a==null?void 0:a.endPoint)})},nt=()=>{var e,o;ge==((o=(e=Oe)==null?void 0:e.generalLedger)==null?void 0:o.length)?te(!1):te(!0)};r.useEffect(()=>{nt()},[ge]),r.useEffect(()=>{ot()},[]),r.useEffect(()=>{Qe(Dt("GL"))},[]);const T=F==null?void 0:F.map((e,o)=>{var g,n,C,f,s,d,p,v,P,N;const a=e,i=(e==null?void 0:e.viewData)||{};return{id:o,chartOfAccount:(a==null?void 0:a.ChartOfAccount)||"",companyCode:(a==null?void 0:a.CompCode)||"",glAccount:(a==null?void 0:a.GLAccount)||"",accountType:((n=(g=i["Type/Description"]["Control in COA"])==null?void 0:g.find(c=>(c==null?void 0:c.fieldName)==="Account Type"))==null?void 0:n.value)||"",accountGroup:((f=(C=i["Type/Description"]["Control in COA"])==null?void 0:C.find(c=>(c==null?void 0:c.fieldName)==="Account Group"))==null?void 0:f.value)||"",shortText:((d=(s=i["Type/Description"].Description)==null?void 0:s.find(c=>(c==null?void 0:c.fieldName)==="Short Text"))==null?void 0:d.value)||"",longText:((v=(p=i["Type/Description"].Description)==null?void 0:p.find(c=>(c==null?void 0:c.fieldName)==="Long Text"))==null?void 0:v.value)||"",accountCurrency:((N=(P=i["Control Data"]["Account Control in Company Code"])==null?void 0:P.find(c=>(c==null?void 0:c.fieldName)==="Account Currency"))==null?void 0:N.value)||""}}),st=[{field:"glAccount",headerName:"GL Account",editable:!1,flex:1,renderCell:e=>{const o=_.find(a=>a.generalLedger===e.value);return console.log(o,"isDirectMatch"),console.log(e,"params"),o&&o.code===400?t(D,{sx:{fontSize:"12px",color:"red"},children:e.value}):t(D,{sx:{fontSize:"12px"},children:e.value})}},{field:"chartOfAccount",headerName:"Chart Of Account",editable:!1,flex:1},{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"accountType",headerName:"Account Type",editable:!1,flex:1},{field:"accountGroup",headerName:"Account Group",editable:!1,flex:1},{field:"shortText",headerName:"Short Text",editable:!1,flex:1,renderCell:e=>{const o=Ye.includes(e.row.profitCenterName);return t(D,{sx:{fontSize:"12px",color:o?"red":"inherit"},children:e.value})}},{field:"longText",headerName:"Long Text",editable:!1,flex:1},{field:"accountCurrency",headerName:"Account Currency",editable:!1,flex:1}],l=(e,o)=>{const a=e==null?void 0:e.find(i=>(i==null?void 0:i.fieldName)===o);return a?a.value:""};var M=F.map(e=>{var o,a,i,g,n,C,f,s,d,p,v,P,N,c,ye,ve,Se,we,Ae,ke,be,Le,Ie,Re,Te,Me,Ge,Be;return{GeneralLedgerID:"",Action:w==="Create"?"I":"U",RequestID:"",TaskStatus:"",TaskId:"",Remarks:U||"",Info:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:w==="Create"?"Mass Create":"Mass Change",ReqCreatedBy:u==null?void 0:u.user_id,ReqCreatedOn:u!=null&&u.createdOn?"/Date("+(u==null?void 0:u.createdOn)+")/":"",ReqUpdatedOn:"",RequestStatus:"",Testrun:le,COA:e==null?void 0:e.ChartOfAccount,CompanyCode:e==null?void 0:e.CompCode,CoCodeToExtend:"",GLAccount:e==null?void 0:e.GLAccount,Accounttype:l((o=e==null?void 0:e.viewData["Type/Description"])==null?void 0:o["Control in COA"],"Account Type"),AccountGroup:l((a=e==null?void 0:e.viewData["Type/Description"])==null?void 0:a["Control in COA"],"Account Group"),GLname:l((i=e==null?void 0:e.viewData["Type/Description"])==null?void 0:i.Description,"Short Text"),Description:l((g=e==null?void 0:e.viewData["Type/Description"])==null?void 0:g.Description,"Long Text"),TradingPartner:l((n=e==null?void 0:e.viewData["Type/Description"])==null?void 0:n["Consolidation Data in COA"],"Trading Partner"),GroupAccNo:l((C=e==null?void 0:e.viewData["Type/Description"])==null?void 0:C["Consolidation Data in COA"],"Group Account Number"),AccountCurrency:l((f=e==null?void 0:e.viewData["Control Data"])==null?void 0:f["Account Control in Company Code"],"Account Currency"),Exchangerate:l((s=e==null?void 0:e.viewData["Control Data"])==null?void 0:s["Account Control in Company Code"],"Exchange Rate Difference Key"),Balanceinlocrcy:l((d=e==null?void 0:e.viewData["Control Data"])==null?void 0:d["Account Control in Company Code"],"Only Balance In Local Currency")===!0?"X":"",Taxcategory:l((p=e==null?void 0:e.viewData["Control Data"])==null?void 0:p["Account Control in Company Code"],"Tax Category"),Pstnwotax:l((v=e==null?void 0:e.viewData["Control Data"])==null?void 0:v["Account Control in Company Code"],"Posting Without Tax Allowed")===!0?"X":"",ReconAcc:l((P=e==null?void 0:e.viewData["Control Data"])==null?void 0:P["Account Control in Company Code"],"Recon. Account For Account Type"),Valuationgrp:l((N=e==null?void 0:e.viewData["Control Data"])==null?void 0:N["Account Control in Company Code"],"Valuation Group"),AlterAccno:l((c=e==null?void 0:e.viewData["Control Data"])==null?void 0:c["Account Control in Company Code"],"Alternative Account Number"),Openitmmanage:l((ye=e==null?void 0:e.viewData["Control Data"])==null?void 0:ye["Account Management in Company Code"],"Open Item Management")===!0?"X":"",Sortkey:l((ve=e==null?void 0:e.viewData["Control Data"])==null?void 0:ve["Account Management in Company Code"],"Sort Key"),CostEleCategory:l((Se=e==null?void 0:e.viewData["Control Data"])==null?void 0:Se["Account Management in Company Code"],"Sort Key"),FieldStsGrp:l((we=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:we["Control of Document creation in Company Code"],"Field Status Group"),PostAuto:l((Ae=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Ae["Control of Document creation in Company Code"],"Post Automatically Only")===!0?"X":"",Supplementautopost:l((ke=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:ke["Control of Document creation in Company Code"],"Supplement Auto Postings")===!0?"X":"",Planninglevel:l((be=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:be["Bank/Financial Details in Company Code"],"Planning Level"),Relvnttocashflow:l((Le=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Le["Bank/Financial Details in Company Code"],"Relevant To Cash Flows")===!0?"X":"",HouseBank:l((Ie=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Ie["Bank/Financial Details in Company Code"],"House Bank"),AccountId:l((Re=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Re["Bank/Financial Details in Company Code"],"Account ID"),Interestindicator:l((Te=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Te["Interest Calculation Information in Company Code"],"Interest Indicator"),ICfrequency:l((Me=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Me["Interest Calculation Information in Company Code"],"Interest Calculation Frequency"),KeydateofLIC:l((Ge=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Ge["Interest Calculation Information in Company Code"],"Key Date Of Last Interest Calculation"),LastIntrstundate:l((Be=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Be["Interest Calculation Information in Company Code"],"Date Of Last Interest Run"),AccmngExistsys:"",Infationkey:"",Tolerancegrp:"",AuthGroup:"",AccountClerk:"",ReconAccReady:"",PostingBlocked:"",PlanningBlocked:""}});const lt=e=>{e.length>0?(B(!0),console.log("selectedIds1",e)):B(!1),console.log("selectedIds",e),$e(e)},H=()=>{K(!0)},rt=()=>{Je?(K(!1),de(!1)):(K(!1),X("/masterDataCockpit/generalLedger"))},it=()=>{ue(!0)},Ce=()=>{ue(!1)},$=()=>{oe(!0)},pe=()=>{oe(!1)},ct=()=>{},dt=()=>{const e=T.filter((n,C)=>j.includes(C));console.log("selectedData",e);const o=e.map(n=>({...M[n==null?void 0:n.id]}));let a=M;a=o,console.log("selectedProfitCenterRows",o);const i=n=>{if(n.statusCode===200){console.log("success"),S("Create"),k(`Mass General Ledger Sent for Review with ID NLM${n.body}`),L("success"),I(!1),R(!0),H(),b(!0),m(!1);const C={artifactId:W,createdBy:u==null?void 0:u.emailId,artifactType:"GeneralLedger",requestId:`NLM${n==null?void 0:n.body}`},f=d=>{console.log("Second API success",d)},s=d=>{console.error("Second API error",d)};A(`/${Ee}/documentManagement/updateDocRequestId`,"post",f,s,C)}else S("Error"),R(!1),k("Failed Submitting the General Ledger for Review "),L("danger"),I(!1),b(!0),$(),m(!1);handleClose(),m(!1)},g=n=>{console.log(n)};A(`/${E}/massAction/generalLedgersSubmitForReview`,"post",i,g,a)},ut=()=>{const e=T.filter((n,C)=>j.includes(C));console.log("selectedData",e);const o=e.map(n=>({...M[n==null?void 0:n.id]}));let a=M;a=o,console.log("selectedProfitCenterRows",o);const i=n=>{if(n.statusCode===200){console.log("success"),S("Create"),k(`Mass General Ledger Sent for Review with ID CGM${n.body}`),L("success"),I(!1),R(!0),H(),b(!0),m(!1);const C={artifactId:W,createdBy:u==null?void 0:u.emailId,artifactType:"GeneralLedger",requestId:`CGM${n==null?void 0:n.body}`},f=d=>{console.log("Second API success",d)},s=d=>{console.error("Second API error",d)};A(`/${Ee}/documentManagement/updateDocRequestId`,"post",f,s,C)}else S("Error"),R(!1),k("Failed Submitting the General Ledger for Review "),L("danger"),I(!1),b(!0),$(),m(!1);handleClose(),m(!1)},g=n=>{console.log(n)};A(`/${E}/massAction/changeGeneralLedgersSubmitForReview`,"post",i,g,a)},gt=()=>{m(!0);const e=T.filter((s,d)=>j.includes(d));console.log("selectedData",e);const o=e.map(s=>({...M[s==null?void 0:s.id]}));console.log("selectedProfitCenterRows",o);const a=[];o.map(s=>{var p;var d={glName:(p=s==null?void 0:s.GLname)==null?void 0:p.toUpperCase(),compCode:s==null?void 0:s.CompanyCode};a.push(d)}),console.log("duplicateCheckPayload",a);let i=M;i=o;const g=s=>{s.statusCode===400?(Xe(s.body),ce(!0),m(!1)):(S("Create"),console.log("success"),S("Create"),k("All Data has been Validated.General Ledger can be Send for Review"),L("success"),I(!1),R(!0),H(),b(!0),de(!0),(a.glName!==""||a.compCode!=="")&&A(`/${E}/alter/fetchGlNameNCompCodeDupliChkMass`,"post",n,C,a))},n=s=>{if(console.log("dataaaa",s),s.body.length===0||!s.body.some(d=>a.some(p=>{var v;return((v=p==null?void 0:p.glName)==null?void 0:v.toUpperCase())===d.matches[0]})))m(!1),se(!1),B(!0);else{const d=s.body.map(p=>p.matches[0]);m(!1),S("Duplicate Check"),R(!1),k("There is a direct match for the General Ledger name."),L("danger"),I(!1),b(!0),$(),se(!0),xe(d)}},C=s=>{console.log(s)},f=s=>{console.log(s)};A(`/${E}/massAction/validateMassGeneralLedger`,"post",g,f,i)},Ct=[{field:"generalLedger",headerName:"General Ledger",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}],he=(De=_==null?void 0:_.filter(e=>(e==null?void 0:e.code)===400))==null?void 0:De.map((e,o)=>{var a;if(e.code===400)return{id:o,generalLedger:e==null?void 0:e.generalLedger,error:(a=e==null?void 0:e.status)==null?void 0:a.message}});console.log("validationRows",he);const V=()=>{B(!0),re(!1)},me=()=>{B(!1),re(!0)},pt=(e,o)=>{const a=e.target.value;if(a.length>0&&a[0]===" ")ie(a.trimStart());else{let i=a.toUpperCase();ie(i)}},fe=()=>{ce(!1)};return t(Nt,{children:z===!0?t(yt,{}):h("div",{children:[h(J,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:We,onClose:V,children:[h(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(D,{variant:"h6",children:"Remarks"}),t(q,{sx:{width:"max-content"},onClick:V,children:t(Fe,{})})]}),t(Z,{sx:{padding:".5rem 1rem"},children:t(Y,{children:t(Pe,{sx:{minWidth:400},children:t(vt,{sx:{height:"auto"},fullWidth:!0,children:t(St,{sx:{backgroundColor:"#F5F5F5"},value:U,onChange:pt,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),h(x,{sx:{display:"flex",justifyContent:"end"},children:[t(G,{sx:{width:"max-content",textTransform:"capitalize"},onClick:V,children:"Cancel"}),t(G,{className:"button_primary--normal",type:"save",onClick:()=>{m(!0),V(),w==="Create"?dt():ut()},variant:"contained",children:"Submit"})]})]}),t(wt,{dialogState:Ke,openReusableDialog:$,closeReusableDialog:pe,dialogTitle:qe,dialogMessage:ae,handleDialogConfirm:pe,dialogOkText:"OK",handleExtraButton:ct,dialogSeverity:ze}),je&&t(At,{openSnackBar:Ve,alertMsg:ae,handleSnackBarClose:rt}),h("div",{style:{...kt,backgroundColor:"#FAFCFF"},children:[t(y,{container:!0,sx:bt,children:h(y,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[h(y,{item:!0,md:11,sx:{display:"flex"},children:[t(y,{children:t(q,{color:"primary","aria-label":"upload picture",component:"label",sx:Lt,children:t(It,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{X("/masterDataCockpit/generalLedger")}})})}),t(y,{children:w==="Create"?h(y,{item:!0,md:12,children:[t(D,{variant:"h3",children:t("strong",{children:"Create Multiple General Ledger"})}),t(D,{variant:"body2",color:"#777",children:"This view creates multiple General Ledger"})]}):h(y,{item:!0,md:12,children:[t(D,{variant:"h3",children:t("strong",{children:"Change Multiple General Ledger"})}),t(D,{variant:"body2",color:"#777",children:"This view changes multiple General Ledger"})]})})]}),t(y,{item:!0,md:1,sx:{display:"flex"},children:t(Rt,{title:"Upload documents if any",arrow:!0,children:t(q,{onClick:it,children:t(Tt,{})})})}),h(J,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Ze,onClose:Ce,children:[t(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:t(D,{variant:"h6",children:"Add Attachment"})}),t(Z,{sx:{padding:".5rem 1rem"},children:t(Y,{children:t(Pe,{sx:{minWidth:400},children:t(Mt,{title:"GeneralLedger",useMetaData:!1,artifactId:W,artifactName:"GeneralLedger"})})})}),t(x,{children:t(G,{onClick:Ce,children:"Close"})})]})]})}),t(y,{item:!0,sx:{position:"relative"},children:t(Y,{children:t(Ne,{isLoading:z,width:"100%",title:"General Ledger Master List ("+T.length+")",rows:T,columns:st,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:lt,callback_onRowSingleClick:e=>{console.log("paramss",e);const o=e.row.glAccount,a=F.find(i=>i.GLAccount===o);console.log(a,"pppp"),X(`/masterDataCockpit/generalLedger/createMultipleGL/editMultipleGL/${o}`,{state:{rowViewData:a,selectedRow:e.row}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})})]}),t(Gt,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(Bt,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:_e,children:[t(G,{variant:"contained",size:"small",sx:{...ee,mr:1},onClick:gt,disabled:!le,children:"Validate"}),w==="Create"?t(G,{variant:"contained",size:"small",sx:{...ee},onClick:me,disabled:ne,children:"Submit for Review"}):w==="Change"?t(G,{variant:"contained",size:"small",sx:{...ee},onClick:me,disabled:ne,children:"Submit for Review"}):""]})}),h(J,{open:He,fullWidth:!0,onClose:fe,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[h(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(D,{variant:"h6",color:"red",children:"Errors"}),t(q,{sx:{width:"max-content"},onClick:fe,children:t(Fe,{})})]}),t(Z,{sx:{padding:".5rem 1rem"},children:t(Ne,{isLoading:z,width:"100%",title:"General Ledger Master List ("+T.length+")",rows:he,columns:Ct,pageSize:10,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),t(x,{sx:{display:"flex",justifyContent:"end"}})]}),t(Ft,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+1},open:Ue,children:t(Pt,{color:"inherit"})})]})})};export{zt as default};
