@charset "UTF-8";.styleBoxShadow.MuiButton-contained,.styleBoxShadow.MuiButton-contained:hover{box-shadow:none}.styleCheckBoxLabel .MuiTypography-body{font-size:.875rem}.styleNavBack .MuiSvgIcon-root{width:1rem;height:1rem}.styleOrchestrationCheckbox .MuiSvgIcon-root{width:1.3rem;height:1.3rem}.styleOrchestrationCheckbox .MuiFormControlLabel-label{font-size:.875rem!important}.styleManageButton.MuiButton-root{left:0%;right:0%;top:0%;bottom:0%;background:#007AD4;border-radius:4px;color:#fff!important}.styleButton.MuiButton-root{padding:.2rem;text-transform:none!important;color:#757575}.styleButton .MuiButton-iconSizeMedium>*:first-child{font-size:14px}.MuiFormControlLabel-root .MuiFormControlLabel-label{color:#757575}.scrollbar::-webkit-scrollbar{width:.75rem;height:.5rem;padding:.5rem}.infinite-scroll-component__outerdiv{height:calc(100% - 9.5rem)}[data-testid=virtuoso-item-list]{padding-right:.3rem}.styleVerticalDivider.MuiDivider-root{background-color:#089b13}.styleActionsRow{display:flex;flex-direction:row;align-items:center;padding:.5rem 5px .5rem .7rem;border-radius:4px;background-color:#fff1cc}.styleBlockNameinput.MuiFormControl-root{width:100%}.styleBlockNameinput .MuiInputAdornment-root{height:0}.styleBlockNameinput .MuiOutlinedInput-input{padding:.3rem}.styleBlockNameinput .MuiInputBase-input{height:1rem}.AddConditionsStyle,.ConditionGroupStyle{color:#9e9e9e}.styleConditionRow{display:flex;flex-direction:row;align-items:flex-start;padding:.5rem 5px .5rem .7rem;background:#D6E6FF;border-radius:4px}.styleActionInputs .MuiInputBase-root.Mui-disabled{background:#f5f5f5}.styleActionInputs .MuiOutlinedInput-root{background-color:#fff;padding:0}.styleActionInputs.MuiFormControl-root{width:23%}.styleActionInputs .MuiInputAdornment-root{height:0}.styleActionInputs .MuiOutlinedInput-input{padding:.3rem}.styleActionInputs .MuiInputBase-input{height:1rem}.styleActionInputs .MuiInputBase-root{font-size:.875rem}.styleDividerCondition.MuiDivider-root{width:1rem;background-color:#089b13;height:.063rem}.styleOperatorSelect .MuiSelect-select.MuiSelect-select{background:white;border:1px solid #E0E0E0;border-radius:5px;padding:2px 5px 3px!important;font-size:.875rem}.styleOperatorSelect .MuiSelect-select.MuiSelect-select.Mui-disabled{background:#f5f5f5}.styleOperatorSelect.MuiInput-root{width:4rem}.styleOperatorSelect .MuiSelect-icon{top:0s}.styleOperatorSelect.MuiInputBase-root{align-items:flex-start;line-height:inherit;width:4rem}.styleOperatorSelect.MuiInput-underline:before{border-bottom:none}.styleOperatorSelect.MuiInput-underline:hover:not(.Mui-disabled):before{border-bottom:none!important}.styleTypeSelection.MuiInputBase-root{width:5rem}.styleTypeSelection .MuiSelect-select.MuiSelect-select{background:white;border:1px solid #E0E0E0;border-radius:5px;padding:0 5px!important;font-size:.625rem!important;font-style:"italic"!important;display:flex;justify-content:flex-start!important;align-items:center;height:1.4rem}.styleTypeSelection .MuiSelect-select.MuiSelect-select.Mui-disabled{background:#f5f5f5!important}.styleTypeSelection.MuiInput-root{width:5rem}.styleTypeSelection .MuiSelect-icon{top:0}.styleTypeSelection.MuiInputBase-root{align-items:flex-start;width:5rem;line-height:1.1rem}.styleTypeSelection.MuiInput-underline:before{border-bottom:none}.styleTypeSelection.MuiInput-underline:hover:not(.Mui-disabled):before{border-bottom:none!important}.styleConditionInputs.MuiFormControl-root{width:28%}.styleConditionInputs .MuiInputAdornment-root{height:0}.styleConditionInputs .MuiOutlinedInput-input{padding:.3rem}.styleConditionInputs .MuiInputBase-input{height:1rem}.styleConditionInputs .MuiInputBase-root{font-size:.875rem}.styleConditionInputs .MuiOutlinedInput-root{background-color:#fff;padding:0}.styleConditionInputs .MuiInputBase-root.Mui-disabled{background:#f5f5f5}.styleConditionValue.MuiFormControl-root{width:73%!important}.styleConditionValueArray.MuiFormControl-root{width:54%!important}.styleTypeSelectionMenuItem.MuiButtonBase-root{display:block;padding:.5rem}.styleLookupTablePadding.TableRow:nth-child(odd){background-color:#d31616}.styleGridTable.MuiDataGrid-root{height:calc(80% - 2.5rem)}.styleGridTable .MuiDataGrid-columnHeaders{max-height:2.5rem!important;min-height:2.5rem!important;background-color:#fafcff}.styleGridTable .MuiDataGrid-main{border-radius:14px}.styleGridTable .MuiDataGrid-columnHeaderTitle{font-size:.875rem!important;font-weight:500;color:#424242}.styleGridTable .MuiDataGrid-cell{max-height:2.5rem!important;min-height:2.5rem!important}.styleGridTable .MuiDataGrid-row{background-color:#fff;max-height:2.5rem!important;min-height:2.5rem!important}.cwOutlineIcon{display:flex}.cwFilledIcon{display:none}.cwIconContainer{align-items:center}.cwIconContainer:hover .cwOutlineIcon{display:none}.cwIconContainer:hover .cwFilledIcon{display:flex}.customInputpackage .MuiInputBase-input{padding:.25rem!important}.Autocomp .MuiInputBase-root{padding:.15rem!important}.styleInputFields .MuiInputBase-root-MuiOutlinedInput-root{height:1.5rem!important}.styleInputFields .MuiOutlinedInput-input{padding:4px 1rem 5px 5px;font-size:.875rem}.styleDTname .MuiOutlinedInput-root{padding:.5rem!important;height:2rem;font-size:.8rem}.styleDTname.multiline .MuiInputBase-root.MuiOutlinedInput-root{padding:.5rem!important;font-size:.8rem;height:auto}.styleHeaderCellList .MuiTableCell-root{padding:.5rem!important;background-color:#fafafa!important;white-space:nowrap;font-weight:600}.noCaps{text-transform:none!important}.MuiFormControlLabel-asterisk{display:none}.styleWTTab{text-transform:none!important}.valuehelpdialog.muidialogcontent-root{background-color:"#F1F5FE"}.styleInputField .MuiOutlinedInput-input{padding:5px;height:1rem;font-size:1rem}.styleInputField .MuiFormHelperText-contained{margin:0}.styleInputField .PrivateNotchedOutline-legend-2,.styleSelectFields .PrivateNotchedOutline-legend-2{height:0!important}.styleSelectFields .MuiSelect-select.MuiSelect-select{padding:5px;font-size:.8rem}.styleSelectFields .MuiSelect-select:focus{background-color:#fff}.styleSelectFieldsDisabled .MuiSelect-select{background-color:#b2b2b212!important}.styleSelectFields{width:9rem}.styledWTPrimaryButton.MuiButton-root{position:static;left:0%;right:0%;top:0%;bottom:0%;background:#ffffff;border-radius:4px;border:none;color:#007ad4;font-weight:600}.styledWTPrimaryButton.MuiButton-root:hover{background:#ffffff}.styledWTPrimaryButton .MuiButton-label{color:#007ad4;font-weight:600}.styleWTPrimaryContainedButton.MuiButton-root{left:0%;right:0%;top:0%;bottom:0%;background:#007ad4;border-radius:4px;color:#fff}.styleWTPrimaryContainedButton.MuiButton-root:hover{background:#007ad4}.styleWTPrimaryContainedButton .MuiButton-label{color:#fff}.styledWTOutlinedButton.MuiButton-root{position:static;left:0%;right:0%;top:0%;bottom:0%;background:#ffffff;border-radius:4px;border:.5px solid #007ad4;color:#007ad4;font-weight:600}.styledWTOutlinedButton.MuiButton-root:hover{background:#ffffff}.styledWTOutlinedButton .MuiButton-label{color:#007ad4;font-weight:600}.styleWTRedButton.MuiIconButton-root{color:#b71c1c}.styleAutoComplete .MuiOutlinedInput-root.MuiInputBase-sizeSmall{padding:0!important}.styleBusyLoader.MuiBackdrop-root{z-index:1000;background-color:transparent}.gfc-chips-stack{--gfc-chip-bgcolor: #F1F5FE;--gfc-chip-color: #00518D}.SearchField .MuiOutlinedInput-root{padding:.5rem 1rem;height:1.9rem;background-color:#e9e7e7;border-radius:.5rem;width:11.5rem}.SearchField .MuiOutlinedInput-input{padding:8px 0}.CustomTextField .MuiOutlinedInput-input{padding:4px 1rem 5px 5px;font-size:.875rem}.CustomTextField .MuiInputBase-input{height:1.5rem!important}.Editor.rdw-editor-wrapper{height:70%}.Editor.rdw-editor-main{padding:.5rem;background-color:#fffefe;border-radius:5px}.CustomAutoComplete .MuiOutlinedInput-root{padding:0px 3.3rem 0px 0px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.CustomAutoComplete .MuiAutocomplete-input{padding:4.5px 4px 4.5px 6px!important}.styleMessageBox .MuiDialogTitle-root{height:2rem;align-items:center;display:flex;justify-content:flex-start;box-sizing:border-box;padding:0}.customhover .MuiIconButton-root:hover{background-color:#1e1684}.mentions__mention{position:relative;z-index:1;color:#1236ff;text-decoration:underline;pointer-events:none}.card .MuiCardContent-root{padding:10px!important}.styleOperatorsTreeItem .MuiTreeItem-content{padding:.3rem;background-color:#fff}.styleOperatorsTreeItem .MuiTreeItem-content:hover{background-color:#f0f8ff}.styleOperatorsTreeItem .MuiTreeItem-label{font-size:.875rem!important}.styleOperatorsTreeItem.MuiTreeItem-label:hover{background-color:#f0f8ff!important}.styleOperatorsTreeItem.MuiTreeItem-root.Mui-selected>.MuiTreeItem-content .MuiTreeItem-label{background-color:#f0f8ff}.styleOperatorsTreeItem .MuiTreeItem-root.Mui-selected>.MuiTreeItem-content .MuiTreeItem-label:hover{background-color:#f0f8ff}.styleOperatorsTreeItem .MuiTreeItem-iconContainer{display:none}.styleMessageBox .MuiDialogTitle-root,.styleErrorMessageBox .MuiDialogTitle-root{height:2rem;align-items:center;display:flex;justify-content:flex-start;box-sizing:border-box;padding:0}.styleMessageBox .MuiButtonBase-root{top:0}.styleMessageBox .MuiPaper-root{border:1px solid #007ad4;border-radius:4px;width:16rem}.styleMessageBox .MuiDialogContent-root{border-bottom:1px solid #88ccff;border-top:none;padding:.5rem}.styleWarningMessageBox .MuiPaper-root{border:1px solid #ff8f00;border-radius:4px}.styleWarningMessageBox .MuiDialogContent-root{border-bottom:1px solid #ffe082;border-top:none;padding:.5rem}.styleErrorMessageBox .MuiPaper-root{border:1.2px solid #ecafaf;border-radius:4px;width:max-content!important}.styleErrorMessageBox .MuiDialogContent-root{border-bottom:1px solid #b71c1c;border-top:none;padding:.5rem}.mentions{margin:1em 0}.mentions--singleLine .mentions__control{display:inline-block;width:130px}.mentions--singleLine .mentions__highlighter{padding:1px;border:2px inset transparent}.mentions--singleLine .mentions__input{padding:1px;border:2px inset}.mentions--multiLine .mentions__highlighter{border:1px solid transparent;padding:9px;min-height:63px}.mentions--multiLine .mentions__input{border:1px solid silver;padding:9px;outline:0}.mentions__suggestions__list{background-color:#fff;height:10rem;overflow-y:scroll}.mentions__suggestions__item{border-bottom:1px solid rgba(0,0,0,.15)}.mentions__suggestions__item--focused{background-color:#cee4e5}.mentions__mention{position:relative;z-index:1;color:#00f;text-shadow:1px 1px 1px white,1px -1px 1px white,-1px 1px 1px white,-1px -1px 1px white;text-decoration:underline;pointer-events:none}.editor{box-sizing:border-box;border:1px solid #ddd;cursor:text;padding:16px;border-radius:2px;margin-bottom:2em;box-shadow:inset 0 1px 8px -3px #ababab;background:#fefefe}.editor :global(.public-DraftEditor-content){min-height:140px}.mention{color:#4a85bb;text-decoration:none}.mentionSuggestions{border-top:1px solid #eee;background:#fff;border-radius:2px;cursor:pointer;padding-top:8px;padding-bottom:8px;display:flex;flex-direction:column;box-sizing:border-box;transform-origin:50% 0%;transform:scaleY(0);margin:-16px}.mentionSuggestionsEntryContainer{display:table;width:100%}.mentionSuggestionsEntryContainerLeft,.mentionSuggestionsEntryContainerRight{display:table-cell;vertical-align:middle}.mentionSuggestionsEntryContainerRight{width:100%;padding-left:8px}.mentionSuggestionsEntry{padding:7px 10px 3px;transition:background-color .4s cubic-bezier(.27,1.27,.48,.56)}.mentionSuggestionsEntry:active{background-color:#cce7ff}.mentionSuggestionsEntryFocused{composes:mentionSuggestionsEntry;background-color:#e6f3ff}.mentionSuggestionsEntryText,.mentionSuggestionsEntryTitle{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mentionSuggestionsEntryTitle{font-size:80%;color:#a7a7a7}.mentionSuggestionsEntryAvatar{display:block;width:30px;height:30px;border-radius:50%}.styleDefaultTab.MuiButton-root{text-transform:none!important;color:#757575}.styleActiveTab.MuiButton-root{text-transform:none!important;color:#007ad4;border-bottom:2px solid #007AD4;border-radius:0;font-weight:600}.styleSearch{width:12.375rem}.styleSearch .MuiInputBase-input{height:1rem}.StyleTab.MuiButton-textPrimary:hover{background-color:transparent!important}.styleBusyLoader.MuiBackdrop-root{z-index:99999!important;background-color:transparent}.styleActionTypeSelect .MuiSelect-select.MuiSelect-select{background:white;border:1px solid #E0E0E0;border-radius:5px;padding:2px 5px 3px;font-size:.875rem;display:flex;justify-content:flex-start!important}.styleActionTypeSelect.MuiInput-root{width:24%}.styleActionTypeSelect .MuiSelect-icon{top:0}.styleActionTypeSelect.MuiInputBase-root{align-items:flex-start;width:24%;line-height:1.1rem;height:fit-content}.styleActionTypeSelect.MuiInput-underline:before{border-bottom:none}.styleActionTypeSelect.MuiInput-underline.Mui-disabled:before{border-bottom-style:none}.styleActionTypeSelect .MuiSelect-select.MuiSelect-select.Mui-disabled{background:#f5f5f5}.styleActionTypeSelect.MuiInput-underline:hover:not(.Mui-disabled):before{border-bottom:none!important}.styleActionObjectInputs .MuiOutlinedInput-root{background-color:#fff}.styleActionObjectInputs .MuiOutlinedInput-input{padding:5px}.styleActionObjectInputs .MuiInputBase-input{height:1rem;font-size:.875rem}.styleActionObjectInputs.MuiFormControl-root{width:48.7%}.styleActionValue.MuiFormControl-root{width:21.5%}.iconStyleDoc{width:"15px";height:"15px"}.headerDoc{width:"100%";box-shadow:"0px 2px 4px rgba(192, 192, 192, 0.25)";background:white}.headerItemsDoc{display:flex;align-items:center;flex-direction:row;padding:.75rem}.headerTextDoc{padding-left:"0.5rem";color:"007AD4"}.styleModal-wr.makeStyles-paper-95{padding:0;width:80%;height:100%}.styletable-wr .MuiTableCell-root{padding:10px}.classesModal-paper-changeLog{position:absolute;width:100%;background-color:#e5e5e5;box-shadow:0 3px 5px -1px #0003,0 5px 8px #00000024,0 1px 14px #0000001f}.changeLog-table-container{margin:1rem;width:calc(100% - 2rem)!important;height:calc(100% - 5rem)!important}.changeLog-tableCell{padding:.5rem!important;font-size:.75rem!important}.styleActivityLog-wr{display:flex;flex-direction:column;background-color:#fff;margin-bottom:.6rem!important;flex-wrap:"wrap"!important;font-size:.9rem;height:4.8rem}.styleActivityLogText-wr{width:74px;height:16px;font-family:Roboto;font-style:normal;font-weight:600;font-size:1.25rem;line-height:16px;color:#282828;display:contents}.styleClockIcon-wr .MuiIconButton-edgeEnd{margin-right:0}.styleClockIcon-wr.MuiIconButton-root{padding:0}.customAppBar.MuiToolbar-gutters{padding-bottom:0rem}.customAppBar.MuiToolbar-regular{min-height:0}.styleNavIcon{min-width:20px}.styleListItem.MuiListItem-root{padding-top:0;padding-bottom:0}.styleNavIcon .MuiListItemIcon-root{min-width:none!important}.styleListItem .MuiTypography-body1{font-size:.75rem}.styleListItem.MuiListItem-gutters :hover{background-color:#c3e6ff59}.ValidateToolbar{display:flex;justify-content:space-between;flex-wrap:wrap;align-items:center}.styleFlex{display:flex;width:100%;height:100%}.styleFlex1{width:23rem}.StyleMenu{padding:0}.styleButton.MuiButton-label{font-size:.85rem!important}.styleButton .MuiButton-iconSizeMedium>*:first-child{font-size:18px}.styleButton2{border-radius:0!important;padding:.2rem!important}.styleLink{font-size:.75rem}.styleSearch{width:12.375rem;color:#424242}.styleSearch .MuiOutlinedInput-root{background-color:"#ffffff"!important}.styleSearch .MuiOutlinedInput-input{padding:.5rem}.styleSearch .MuiInputBase-input{height:.55rem!important;font-size:.8rem}.styleSearch .MuiIconButton-root{padding:0}.styleLineHeight.MuiButton-root{line-height:0}.footer{position:"fixed";left:"0";bottom:"0";z-index:10;padding:"0.5rem";width:"100%";display:"inline-flex";justify-content:"flex-end";background-color:"red"}.styleRoundPadding.MuiIconButton-root{padding:.3rem!important}.styledPrimaryButton.MuiButton-root{position:static;left:0%;right:0%;top:0%;bottom:0%;background:transparent;border-radius:4px;border:transparent;color:#007ad4;font-weight:600}.styledPrimaryButton.MuiButton-root:disabled{color:#ccc!important}.styledPrimaryButton.MuiButton-root:hover{background:transparent}.styledPrimaryButton .MuiButton-label{color:#007ad4;font-weight:600}.stylePaginationCount{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;margin-left:1rem}.styleToolbar.MuiToolbar-regular{color:#212121}.styleAppBar.MuiAppBar-colorPrimary{background-color:#fff}.styleAppBar{background-color:#fff;height:3rem;position:relative;box-shadow:0 2px 1px -1px #0003,0 1px 1px #00000024,0 1px 3px #0000001f;border-radius:8px 8px 0 0}.inputStyle.MuiInputBase-input{padding:0!important}.styleAppBar .MuiToolbar-regular{min-height:0rem}.styleButton.MuiButton-root{padding:.2rem .5rem;text-transform:none!important}.styleActive.MuiButton-root{background:#C3E6FF;box-shadow:0 3px 8px #0000001f,0 3px 1px #0000000a;border-radius:10px}.styleActive.MuiButton-root:hover{background:#C3E6FF}.styleManageButton.MuiButton-root{left:0%;right:0%;top:0%;bottom:0%;background:#007AD4;border-radius:4px}.styleManageButton.MuiButton-root:hover{background:#007AD4}.styleManageButton .MuiButton-label{color:#fff!important}.styletable-wr .MuiTableCell-root{padding:4px!important;color:#424242}.styletable-wr .MuiTableCell-stickyHeader{background-color:#fff}.styletable-wr .MuiTableCell-head{font-weight:600}.styleSortDiv{position:static;height:18.75rem;left:0;right:0;top:calc(50% - 271.5px);background:#FFFFFF;flex:none;order:0;align-self:stretch;flex-grow:1;margin:.5rem}.styleSortInput.MuiInput-root{padding-left:.4rem;border-bottom:none;position:relative;box-sizing:border-box;border-radius:5px;margin-left:1rem;color:#424242}.styletable-wr .MuiTableCell-root,.styleVariantTable .MuiTableCell-root{padding:10px!important}.styleVariantTable .MuiTableBody-root{height:1rem!important}.stack-2d-table{padding:3px}.container-stack{margin:0 1rem;width:calc(100% - 1rem)}.condition-table{border-top:.5px solid #E0E0E0;width:100%;height:100%;overflow:scroll}.table-head-variant .MuiTableCell-root{font-weight:700;background-color:#fff;padding:.5rem 0 .5rem 1rem}.conditions-table-row .MuiTableCell-root{padding:0 0 0 1rem}.row-and-columns-table .MuiTableCell-root{padding:.5rem 0 .5rem 1rem}.variant-management-paper{position:absolute;width:80%;background-color:#fff;padding:0}.variant-management-table-container{max-height:19rem;width:98%!important;margin:.5rem}.variant-management-footer{top:auto!important;bottom:0;background-color:#fff;color:#000;min-height:0;padding-right:0!important}.ac-option-div{cursor:pointer}.ac-option-div:hover{background-color:#e9e7e7}.variantCreation-popup-paper{position:absolute;width:50%;background-color:#fff;box-shadow:0 3px 5px -1px #0003,0 5px 8px #00000024,0 1px 14px #0000001f;border-radius:8px}.variantCreation-popup-appbar{top:auto;bottom:0;background-color:#fff!important;color:#000;min-height:0;position:relative}.styleInput .MuiInputBase-input{height:1rem;color:#424242}.styleInput .MuiInputBase-input{font-size:.75rem}.styleInput.MuiFormControl-root.MuiTextField-root .Mui-disabled{background-color:#f5f5f5!important}.styleInput .MuiOutlinedInput-input{padding:.3rem;color:#424242}.styleInput .MuiInputBase-input{height:1rem}.styleInput .MuiFormHelperText-root.Mui-error,.styleMultiInput .MuiFormHelperText-root.Mui-error{font-size:.6rem}.date-picker-custom{width:6.2rem!important;padding:.3rem!important;border-radius:.25rem!important;box-shadow:none;background-color:#fff;border:.1px solid rgba(90,88,88,.407)!important;font-size:.75rem;color:#424242}.date-picker-custom-disabled{width:6.2rem!important;height:1rem!important;background-color:#f5f5f5;padding:.3rem!important;border-radius:.25rem!important;box-shadow:none;color:#4242428f;border:.1px solid rgba(90,88,88,.407)!important;font-size:.75rem}.date-picker-custom .react-datepicker__input-container,.date-picker-custom-disabled .react-datepicker__input-container{position:relative;width:10rem!important;background-color:transparent;height:1.5rem;display:inline-block;width:100%;border:0 transparent!important}.react-datepicker__year-read-view--down-arrow,.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow,.react-datepicker__navigation-icon:before{border-color:#ccc;border-style:solid;border-width:3px 3px 0 0;content:"";display:block;height:9px;position:absolute;top:6px;width:9px}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle,.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle{margin-left:-4px;position:absolute;width:0}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before,.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before,.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after,.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after{box-sizing:content-box;position:absolute;border:8px solid transparent;height:0;width:1px;content:"";z-index:-1;border-width:8px;left:-8px}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before,.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before{border-bottom-color:#aeaeae}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle{top:0;margin-top:-8px}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before,.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after{border-top:none;border-bottom-color:#f0f0f0}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after{top:0}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before{top:-1px;border-bottom-color:#aeaeae}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle{bottom:0;margin-bottom:-8px}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before,.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after{border-bottom:none;border-top-color:#fff}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after{bottom:0}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before{bottom:-1px;border-top-color:#aeaeae}.react-datepicker-wrapper{display:inline-block;padding:0;border:0}.react-datepicker{font-family:Helvetica Neue,helvetica,arial,sans-serif;font-size:.8rem;background-color:#fff;color:#000;border:1px solid #aeaeae;border-radius:.3rem;display:inline-block;position:relative}.react-datepicker--time-only .react-datepicker__triangle{left:35px}.react-datepicker--time-only .react-datepicker__time-container{border-left:0}.react-datepicker--time-only .react-datepicker__time,.react-datepicker--time-only .react-datepicker__time-box{border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem}.react-datepicker__triangle{position:absolute;left:50px}.react-datepicker-popper{z-index:5}.react-datepicker-popper[data-placement^=bottom]{padding-top:10px}.react-datepicker-popper[data-placement=bottom-end] .react-datepicker__triangle,.react-datepicker-popper[data-placement=top-end] .react-datepicker__triangle{left:auto;right:50px}.react-datepicker-popper[data-placement^=top]{padding-bottom:10px}.react-datepicker-popper[data-placement^=right]{padding-left:8px}.react-datepicker-popper[data-placement^=right] .react-datepicker__triangle{left:auto;right:42px}.react-datepicker-popper[data-placement^=left]{padding-right:8px}.react-datepicker-popper[data-placement^=left] .react-datepicker__triangle{left:42px;right:auto}.react-datepicker__header{text-align:center;background-color:#f0f0f0;border-bottom:1px solid #aeaeae;border-top-left-radius:.3rem;padding:8px 0;position:relative}.react-datepicker__header--time{padding-bottom:8px;padding-left:5px;padding-right:5px}.react-datepicker__header--time:not(.react-datepicker__header--time--only){border-top-left-radius:0}.react-datepicker__header:not(.react-datepicker__header--has-time-select){border-top-right-radius:.3rem}.react-datepicker__year-dropdown-container--select,.react-datepicker__month-dropdown-container--select,.react-datepicker__month-year-dropdown-container--select,.react-datepicker__year-dropdown-container--scroll,.react-datepicker__month-dropdown-container--scroll,.react-datepicker__month-year-dropdown-container--scroll{display:inline-block;margin:0 15px}.react-datepicker__current-month,.react-datepicker-time__header,.react-datepicker-year-header{margin-top:0;color:#000;font-weight:700;font-size:.944rem}.react-datepicker-time__header{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.react-datepicker__navigation{align-items:center;background:none;display:flex;justify-content:center;text-align:center;cursor:pointer;position:absolute;top:2px;padding:0;border:none;z-index:1;height:32px;width:32px;text-indent:-999em;overflow:hidden}.react-datepicker__navigation--previous{left:2px}.react-datepicker__navigation--next{right:2px}.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button){right:85px}.react-datepicker__navigation--years{position:relative;top:0;display:block;margin-left:auto;margin-right:auto}.react-datepicker__navigation--years-previous{top:4px}.react-datepicker__navigation--years-upcoming{top:-4px}.react-datepicker__navigation:hover *:before{border-color:#a6a6a6}.react-datepicker__navigation-icon{position:relative;top:-1px;font-size:20px;width:0}.react-datepicker__navigation-icon--next{left:-2px}.react-datepicker__navigation-icon--next:before{transform:rotate(45deg);left:-7px}.react-datepicker__navigation-icon--previous{right:-2px}.react-datepicker__navigation-icon--previous:before{transform:rotate(225deg);right:-7px}.react-datepicker__month-container{float:left}.react-datepicker__year{margin:.4rem;text-align:center}.react-datepicker__year-wrapper{display:flex;flex-wrap:wrap;max-width:180px}.react-datepicker__year .react-datepicker__year-text{display:inline-block;width:4rem;margin:2px}.react-datepicker__month{margin:.4rem;text-align:center}.react-datepicker__month .react-datepicker__month-text,.react-datepicker__month .react-datepicker__quarter-text{display:inline-block;width:4rem;margin:2px}.react-datepicker__input-time-container{clear:both;width:100%;float:left;margin:5px 0 10px 15px;text-align:left}.react-datepicker__input-time-container .react-datepicker-time__caption,.react-datepicker__input-time-container .react-datepicker-time__input-container{display:inline-block}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input{display:inline-block;margin-left:10px}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input{width:auto}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-inner-spin-button,.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]{-moz-appearance:textfield}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__delimiter{margin-left:5px;display:inline-block}.react-datepicker__time-container{float:right;border-left:1px solid #aeaeae;width:85px}.react-datepicker__time-container--with-today-button{display:inline;border:1px solid #aeaeae;border-radius:.3rem;position:absolute;right:-87px;top:0}.react-datepicker__time-container .react-datepicker__time{position:relative;background:white;border-bottom-right-radius:.3rem}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box{width:85px;overflow-x:hidden;margin:0 auto;text-align:center;border-bottom-right-radius:.3rem}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list{list-style:none;margin:0;height:calc(195px + .85rem);overflow-y:scroll;padding-right:0;padding-left:0;width:100%;box-sizing:content-box}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item{height:30px;padding:5px 10px;white-space:nowrap}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover{cursor:pointer;background-color:#f0f0f0}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected{background-color:#216ba5;color:#fff;font-weight:700}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover{background-color:#216ba5}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled{color:#ccc}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover{cursor:default;background-color:transparent}.react-datepicker__week-number{color:#ccc;display:inline-block;width:1.7rem;line-height:1.7rem;text-align:center;margin:.166rem}.react-datepicker__week-number.react-datepicker__week-number--clickable{cursor:pointer}.react-datepicker__week-number.react-datepicker__week-number--clickable:hover{border-radius:.3rem;background-color:#f0f0f0}.react-datepicker__day-names,.react-datepicker__week{white-space:nowrap}.react-datepicker__day-names{margin-bottom:-8px}.react-datepicker__day-name,.react-datepicker__day,.react-datepicker__time-name{color:#000;display:inline-block;width:1.7rem;line-height:1.7rem;text-align:center;margin:.166rem}.react-datepicker__day,.react-datepicker__month-text,.react-datepicker__quarter-text,.react-datepicker__year-text{cursor:pointer}.react-datepicker__day:hover,.react-datepicker__month-text:hover,.react-datepicker__quarter-text:hover,.react-datepicker__year-text:hover{border-radius:.3rem;background-color:#f0f0f0}.react-datepicker__day--today,.react-datepicker__month-text--today,.react-datepicker__quarter-text--today,.react-datepicker__year-text--today{font-weight:700}.react-datepicker__day--highlighted,.react-datepicker__month-text--highlighted,.react-datepicker__quarter-text--highlighted,.react-datepicker__year-text--highlighted{border-radius:.3rem;background-color:#3dcc4a;color:#fff}.react-datepicker__day--highlighted:hover,.react-datepicker__month-text--highlighted:hover,.react-datepicker__quarter-text--highlighted:hover,.react-datepicker__year-text--highlighted:hover{background-color:#32be3f}.react-datepicker__day--highlighted-custom-1,.react-datepicker__month-text--highlighted-custom-1,.react-datepicker__quarter-text--highlighted-custom-1,.react-datepicker__year-text--highlighted-custom-1{color:#f0f}.react-datepicker__day--highlighted-custom-2,.react-datepicker__month-text--highlighted-custom-2,.react-datepicker__quarter-text--highlighted-custom-2,.react-datepicker__year-text--highlighted-custom-2{color:green}.react-datepicker__day--holidays,.react-datepicker__month-text--holidays,.react-datepicker__quarter-text--holidays,.react-datepicker__year-text--holidays{position:relative;border-radius:.3rem;background-color:#ff6803;color:#fff}.react-datepicker__day--holidays .holiday-overlay,.react-datepicker__month-text--holidays .holiday-overlay,.react-datepicker__quarter-text--holidays .holiday-overlay,.react-datepicker__year-text--holidays .holiday-overlay{position:absolute;bottom:100%;left:50%;transform:translate(-50%);background-color:#333;color:#fff;padding:4px;border-radius:4px;white-space:nowrap;visibility:hidden;opacity:0;transition:visibility 0s,opacity .3s ease-in-out}.react-datepicker__day--holidays:hover,.react-datepicker__month-text--holidays:hover,.react-datepicker__quarter-text--holidays:hover,.react-datepicker__year-text--holidays:hover{background-color:#cf5300}.react-datepicker__day--holidays:hover .holiday-overlay,.react-datepicker__month-text--holidays:hover .holiday-overlay,.react-datepicker__quarter-text--holidays:hover .holiday-overlay,.react-datepicker__year-text--holidays:hover .holiday-overlay{visibility:visible;opacity:1}.react-datepicker__day--selected,.react-datepicker__day--in-selecting-range,.react-datepicker__day--in-range,.react-datepicker__month-text--selected,.react-datepicker__month-text--in-selecting-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--selected,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--selected,.react-datepicker__year-text--in-selecting-range,.react-datepicker__year-text--in-range{border-radius:.3rem;background-color:#216ba5;color:#fff}.react-datepicker__day--selected:hover,.react-datepicker__day--in-selecting-range:hover,.react-datepicker__day--in-range:hover,.react-datepicker__month-text--selected:hover,.react-datepicker__month-text--in-selecting-range:hover,.react-datepicker__month-text--in-range:hover,.react-datepicker__quarter-text--selected:hover,.react-datepicker__quarter-text--in-selecting-range:hover,.react-datepicker__quarter-text--in-range:hover,.react-datepicker__year-text--selected:hover,.react-datepicker__year-text--in-selecting-range:hover,.react-datepicker__year-text--in-range:hover{background-color:#1d5d90}.react-datepicker__day--keyboard-selected,.react-datepicker__month-text--keyboard-selected,.react-datepicker__quarter-text--keyboard-selected,.react-datepicker__year-text--keyboard-selected{border-radius:.3rem;background-color:#bad9f1;color:#000}.react-datepicker__day--keyboard-selected:hover,.react-datepicker__month-text--keyboard-selected:hover,.react-datepicker__quarter-text--keyboard-selected:hover,.react-datepicker__year-text--keyboard-selected:hover{background-color:#1d5d90}.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range){background-color:#216ba580}.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range){background-color:#f0f0f0;color:#000}.react-datepicker__day--disabled,.react-datepicker__month-text--disabled,.react-datepicker__quarter-text--disabled,.react-datepicker__year-text--disabled{cursor:default;color:#ccc}.react-datepicker__day--disabled:hover,.react-datepicker__month-text--disabled:hover,.react-datepicker__quarter-text--disabled:hover,.react-datepicker__year-text--disabled:hover{background-color:transparent}.react-datepicker__input-container{position:relative;width:10rem;background-color:transparent;height:1.5rem;display:inline-block;width:100%;border:0 transparent}.react-datepicker__input-container input{margin:0}.react-datepicker__input-container .react-datepicker__calendar-icon{position:absolute;padding:.5rem}.react-datepicker__view-calendar-icon input{padding:0}.react-datepicker__year-read-view,.react-datepicker__month-read-view,.react-datepicker__month-year-read-view{border:1px solid transparent;border-radius:.3rem;position:relative}.react-datepicker__year-read-view:hover,.react-datepicker__month-read-view:hover,.react-datepicker__month-year-read-view:hover{cursor:pointer}.react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__month-read-view--down-arrow{border-top-color:#b3b3b3}.react-datepicker__year-read-view--down-arrow,.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow{transform:rotate(135deg);right:-16px;top:0}.react-datepicker__year-dropdown,.react-datepicker__month-dropdown,.react-datepicker__month-year-dropdown{background-color:#f0f0f0;position:absolute;width:50%;left:25%;top:30px;z-index:1;text-align:center;border-radius:.3rem;border:1px solid #aeaeae}.react-datepicker__year-dropdown:hover,.react-datepicker__month-dropdown:hover,.react-datepicker__month-year-dropdown:hover{cursor:pointer}.react-datepicker__year-dropdown--scrollable,.react-datepicker__month-dropdown--scrollable,.react-datepicker__month-year-dropdown--scrollable{height:150px;overflow-y:scroll}.react-datepicker__year-option,.react-datepicker__month-option,.react-datepicker__month-year-option{line-height:20px;width:100%;display:block;margin-left:auto;margin-right:auto}.react-datepicker__year-option:first-of-type,.react-datepicker__month-option:first-of-type,.react-datepicker__month-year-option:first-of-type{border-top-left-radius:.3rem;border-top-right-radius:.3rem}.react-datepicker__year-option:last-of-type,.react-datepicker__month-option:last-of-type,.react-datepicker__month-year-option:last-of-type{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem}.react-datepicker__year-option:hover,.react-datepicker__month-option:hover,.react-datepicker__month-year-option:hover{background-color:#ccc}.react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-upcoming{border-bottom-color:#b3b3b3}.react-datepicker__year-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__month-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-previous{border-top-color:#b3b3b3}.react-datepicker__year-option--selected,.react-datepicker__month-option--selected,.react-datepicker__month-year-option--selected{position:absolute;left:15px}.react-datepicker__close-icon{cursor:pointer;background-color:transparent;border:0;outline:0;padding:0 6px 0 0;position:absolute;top:0;right:0;height:100%;display:table-cell;vertical-align:middle}.react-datepicker__close-icon:after{cursor:pointer;background-color:#216ba5;color:#fff;border-radius:50%;height:16px;width:16px;padding:2px;font-size:12px;line-height:1;text-align:center;display:table-cell;vertical-align:middle;content:"×"}.react-datepicker__today-button{background:#f0f0f0;border-top:1px solid #aeaeae;cursor:pointer;text-align:center;font-weight:700;padding:5px 0;clear:left}.react-datepicker__portal{position:fixed;width:100vw;height:100vh;background-color:#000c;left:0;top:0;justify-content:center;align-items:center;display:flex;z-index:2147483647}.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__time-name{width:3rem;line-height:3rem}@media (max-width: 400px),(max-height: 550px){.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__time-name{width:2rem;line-height:2rem}}.react-datepicker__portal .react-datepicker__current-month,.react-datepicker__portal .react-datepicker-time__header{font-size:1.44rem}.react-datepicker__children-container{width:13.8rem;margin:.4rem;padding-right:.2rem;padding-left:.2rem;height:auto}.react-datepicker__aria-live{position:absolute;clip-path:circle(0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;width:1px;white-space:nowrap}.react-datepicker__calendar-icon{width:1em;height:1em;vertical-align:-.125em}.styleHorizontal{display:flex;justify-content:space-between}.styleFooter{background-color:#fff;display:flex;justify-content:end;margin-top:2px}.range-operator-container{display:flex;flex-direction:column;gap:1rem;padding:1rem}.range-input-group{display:flex;align-items:center;gap:.5rem;width:100%}.range-input-label{width:3rem;font-size:1rem;text-align:right}.range-bracket-select{width:9rem!important}.range-input-field{flex:1;min-width:0}.MuiInput-root{padding-top:0!important}.MuiInputLabel-standard{transform:translateY(-1.5px) scale(.75)!important}.MuiSelect-select{padding:6px 32px 6px 12px!important}.styleVAlueHelpClose .MuiTypography-root{display:flex;font-weight:none;color:#fff}.styleVAlueHelpClose.MuiDialogTitle-root{padding:0rem 0rem 0rem .8rem;background-color:#3f51b5}.styleVAlueHelpClose .MuiSvgIcon-root{color:whhite}.styleMultiInput .MuiOutlinedInput-input{padding:.3rem}.styleMultiInput.MuiFormControl-root.MuiTextField-root .Mui-disabled{background-color:#f5f5f5!important}.styleMultiInput .MuiInputBase-input{height:1rem;width:"16rem";overflow-x:scroll!important}.multiinputstart::-webkit-scrollbar{display:none!important;height:1rem}.css-1e6alsu-MuiButtonBase-root-MuiChip-root{height:auto!important}.styleMultiInput .MuiInputBase-input{font-size:.75rem;height:1rem;color:#424242}.buttonStyle{padding:0!important}.multiinputcell-wr{width:10rem;margin-left:.5rem}.multiinputcell::-webkit-scrollbar{display:none}.dropdown-button .MuiInputBase-root.MuiOutlinedInput-root{padding-right:0!important}.rgt-container::-webkit-scrollbar-thumb{background:#888;scrollbar-width:thin!important;border-radius:10%}.rgt-container::-webkit-scrollbar{width:.7rem!important;height:.7rem!important}.styletable-wr .MuiDataGrid-columnHeader{line-height:0rem;overflow-wrap:break-word}.styletable-wr .MuiDataGrid-columnHeader.DTtableheader .MuiDataGrid-columnSeparator{visibility:hidden}.styletable-wr .rgt-container .rgt-cell-header-inner{padding-left:0;padding-right:1px}.styletable-wr .rgt-cell{height:auto;min-height:2.3rem!important}.styletable-wr.rgt-wrapper *{box-sizing:content-box}.styleSelect.MuiInputBase-root{width:10rem}.PinIcon{opacity:.15}.PinIcon:hover{opacity:1}.SortIconASC{opacity:.15}.SortIconASC:hover{opacity:1}.SortIconDESC{opacity:.15}.SortIconDESC:hover{opacity:1}.FilterIcon{opacity:.15}.FilterIcon:hover{opacity:1}.filterOperator .MuiOutlinedInput-root{height:2rem!important}.styletable-wr{background:#ffffff}.styletable-wr .rgt-cell-header{min-height:3rem!important}.styletable-wr.MuiDataGrid-root .MuiDataGrid-cell:focus-within{outline:none!important}.styletable-wr.css-30pzqk-MuiDataGrid-root{border:none}.styletable-wr .css-okt5j6-MuiDataGrid-columnHeaders{border-bottom:none;background:#ffffff}.styletable-wr .css-i4bv87-MuiSvgIcon-root{width:1.25rem;height:1.25rem}.styletable-wr .MuiDataGrid-cell--withRenderer MuiDataGrid-cell MuiDataGrid-cell--textLeft{border-bottom:"none";outline:"none"}.styleIcon .css-ptiqhd-MuiSvgIcon-root{width:1rem;height:1rem}.styleIcon.MuiIconButton-root{padding:0}.DTtableheader{display:flex;align-items:center}.grouped-table-2d{padding:.7rem;height:calc(100% - 1rem)}.grouped-table-2d th,.grouped-table-2d td{border:1px solid #1a192b29;padding:.4rem}.popover-textfield .MuiInputBase-input{padding:.3rem}.classesModal-Paper-renderDT{position:absolute;width:"100%";background-color:#fff;padding:.5rem}.operations-buttons{padding:1rem!important}.date-picker-for-filter{width:8.75rem!important;padding:.5rem!important;margin-left:2rem!important;border-radius:.25rem!important;box-shadow:none;background-color:#fff;border:.1px solid rgba(90,88,88,.407)!important;font-size:.75rem;color:#424242}.dataCell{border:0;margin:0 .4rem}.rgt-cell-inner .dataCell{display:flex!important}.dataCell:hover .expand-icon{color:#1976d2;opacity:1}.dataCell:hover{color:#1976d2}.expand-icon{opacity:0}.disabled{opacity:.5;color:#424242}.faded{opacity:.5}.loadInputCell{margin:0 .3rem;display:flex!important;flex-direction:row;justify-content:space-between;align-items:center}div.tooltip-div{position:relative}div.tooltip-div:hover:after{position:absolute;content:attr(data-title);background-color:#616161eb;color:#fff;border-radius:.25rem;padding:.25rem .5rem;margin:.125rem;width:max-content;font-weight:500;font-size:.6875rem;left:50%;transform:translate(-50%);z-index:20;max-width:30rem;word-break:break-all}.comment::-webkit-scrollbar{display:none}.styleCommentBox .MuiOutlinedInput-input{padding:.3rem}.styleCommentBox .MuiOutlinedInput-root{padding-right:0}.styleCommentBox .MuiInputBase-input{height:1rem;font-size:.875rem}.styleCell{border:1px solid #EEEEEE;padding:0 .5rem;display:table-cell!important;align-items:center}.min-width{min-width:7rem}.header-cell.MuiTableCell-root{text-align:center}.header-cell-bold.MuiTableCell-root{font-weight:600}.action-value-textfield .MuiInputBase-input{height:1.4rem;font-size:.9rem;padding:.2rem;font-family:inherit}.icon-1rem{width:.7rem;height:.7rem;color:inherit;font-weight:400}.without-value{width:max-content;color:#9e9e9e}.styleDTname .MuiFormHelperText-root.Mui-error{font-size:.6rem}.previwTable::muidatagrid-columnheadertitle css-1jbbcbn-MuiDataGrid-columnHeaderTitle{width:3rem}.MuiDataGrid-columnHeaderTitleContainerContent{font-weight:400}.action.styleInput.MuiTextField-root.MuiFormControl-root{width:"15rem"!important}.styleToolbar.MuiToolbar-regular{min-height:3rem}.styleActionsTable .MuiDataGrid-columnSeparator{display:none!important}.styleActionsTable .MuiDataGrid-columnHeaders{max-height:2.5rem!important;min-height:2.5rem!important;background-color:#fff!important}.styleActionsTable .MuiDataGrid-main{border-radius:14px}.styleActionsTable .MuiDataGrid-columnHeaderTitleContainer{height:2.5rem!important;overflow:hidden;color:#757575}.styleActionsTable .MuiDataGrid-columnHeaderTitle{font-size:.875rem!important;color:#757575}.styleActionsTable .MuiDataGrid-cell{max-height:2rem!important}.styleActionsTable .MuiDataGrid-row{background-color:#fff;max-height:2rem!important}.styleActionsTable .MuiDataGrid-virtualScroller{margin-top:2.7rem!important}.mass-add-fialog-footer{top:auto!important;bottom:0!important;background-color:#fff!important;color:#000!important;min-height:0;border-radius:0 0 8px 8px}.mass-add-paper{position:absolute;width:80%;background-color:#fff;box-shadow:0 3px 5px -1px #0003,0 5px 8px #00000024,0 1px 14px #0000001f;padding:0}.styleValidateTable .css-204u17-MuiDataGrid-main{height:10rem}.styleHeader{background-color:#eee}.styleConditionHeader{background-color:#dfeafb}.styleActionHeader{background-color:#fff7e2}.styleValidityPeriodHeader,.styleStatusHeader{background-color:#eee}.styleVariantManagementTable .MuiDataGrid-columnSeparator{display:none!important}.styleVariantManagementTable .MuiDataGrid-columnHeaders{max-height:2.5rem!important;min-height:2.5rem!important;background-color:#edf7ff!important}.styleVariantManagementTable .MuiDataGrid-columnHeaderTitleContainer{height:2.5rem!important;overflow:hidden}.styleVariantManagementTable .MuiDataGrid-columnHeaderTitle{font-size:.875rem!important;font-weight:400}.styleVariantManagementTable .MuiDataGrid-cell{max-height:2.5rem!important;min-height:2.5rem!important}.styleVariantManagementTable .MuiDataGrid-row{background-color:#fff;max-height:2.5rem!important;min-height:2.5rem!important}.styleVariantManagementTable.MuiDataGrid-root{height:calc(100% - 2.5rem)}.styleVariantManagementTable .MuiDataGrid-virtualScroller{overflow-y:scroll;overflow-x:scroll;margin-top:2.5rem!important}.classesModalPaper{position:absolute!important;width:100%;background-color:#fff!important;box-shadow:0 3px 5px -1px #0003,0 5px 8px #00000024,0 1px 14px #0000001f!important}.classesAppBarPaper{padding-bottom:50!important;box-shadow:none!important;border:none!important}.classesAppBarAppBar{top:auto!important;bottom:0!important;background-color:#fff!important;color:#000!important;min-height:3rem!important}.StaticDetails::-webkit-scrollbar{display:none}.styleButton{font-family:Roboto;font-style:normal;font-weight:500;font-size:14px;line-height:16px;display:flex;align-items:center;letter-spacing:.0125em;flex:none;flex-grow:0;margin:0 12px}.styleStaticFormButtons.MuiButton-root{color:#757575!important;padding:0!important}.styleCommentsPopover .MuiPaper-root{right:0!important;top:7.5rem!important;left:auto!important}.styleDTcreationType .MuiFormControlLabel-label{font-size:.875rem}.styleReferenceFieldsSelect .MuiSelect-select.MuiSelect-select{padding:5px;font-size:.8rem}.styleReferenceFieldsSelect .MuiSelect-select:focus{background-color:#fff}.styleReferenceFieldsSelectDisabled .MuiSelect-select{background-color:#b2b2b212!important}.styleDTname .MuiOutlinedInput-input{padding:5px;height:1rem;font-size:.8rem}.styleApplicationSelect .MuiSelect-select.MuiSelect-select{padding:4px;font-size:.8rem;height:1rem}.styleApplicationSelect .MuiSelect-select:focus{background-color:#fff}.styleApplicationSelectDisabled .MuiSelect-select{background-color:#b2b2b212!important}.adornment-rms .MuiInputBase-root{height:1.65rem}.styleLookupTablePadding .MuiTableCell-root{padding:.5rem}.styleLookupTablePadding.MuiTableRow-root{background-color:#fff}.styleGridTable.MuiDataGrid-root{height:calc(100% - 2.5rem)}.styleGridTable .MuiDataGrid-virtualScroller{overflow-y:scroll;overflow-x:scroll;margin-top:2.5rem!important}.styleLookupTable .MuiTableCell-stickyHeader{background-color:#fafcff}.styleLookupTable .MuiTableCell-root{border-bottom:1px solid #ccc!important}.styleGridTable .MuiDataGrid-columnSeparator{display:none!important}.styleGridTable .MuiDataGrid-columnHeaders{max-height:2.5rem!important;min-height:2.5rem!important;background-color:#edf7ff}.styleGridTable .MuiDataGrid-virtualScrollerContent{width:auto;height:auto;min-height:auto}.styleGridTable .MuiDataGrid-columnHeaderTitleContainer{height:2.5rem!important;overflow:hidden}.styleGridTable .MuiDataGrid-columnHeaderTitle{font-size:.75rem!important;font-weight:600}.styleGridTable .MuiDataGrid-cell{max-height:38rem!important;min-height:2.5rem!important;font-size:.75rem!important}.styleGridTable .MuiDataGrid-row{background-color:#fff;max-height:38rem!important;min-height:2.5rem!important}.styleEndAdornmentIcon .MuiSvgIcon-root{height:1.2rem;width:1.2rem}.styleChip .MuiChip-deleteIcon{height:17px;width:17px}.styleChip .MuiChip-label{font-size:12px}.styleChip.MuiChip-outlined{max-width:5rem;height:1.2rem!important;margin-left:3px!important}.styleDisabledInputs .MuiOutlinedInput-root{background-color:#b2b2b212!important;padding:0}.styleHeaderCell{background-color:#f1f5fe;padding:"0 0.5rem 0 0.5rem"!important}.styleHeaderCell .MuiTableCell-root{padding:.5rem!important;background-color:#f1f5fe;font-weight:600;white-space:nowrap;font-size:.75rem!important}.styleDataCell .MuiTableCell-root{padding:.5rem!important;font-size:.75rem!important}.styletableCheckbox.MuiCheckbox-root{padding:0;margin:0}.styleRMStableContainer.MuiTableContainer-root{height:calc(100% - 4.8rem)}.styleRMStableContainer2.MuiTableContainer-root{height:calc(100% - 2.8rem)}.styleRMStable.MuiTable-root{height:100%}.styleRMStable2{word-break:break-all}.styleDateRangePicker.mantine-DateRangePicker-input{padding-top:.3rem;padding-bottom:.3rem;height:1.5rem}.styleCreateManageRuleChainCard-wr{width:98%;background:#FFFFFF;border-radius:8px}.styledPrimaryButton .MuiButton-label{color:#007ad4!important;font-weight:600}.styleButton.MuiButton-root{padding:.2rem;text-transform:none!important}.styleButton.MuiButton-label{font-size:.85rem}.styleButton.MuiIconButton-root{padding:0rem .2rem!important;text-transform:none!important}.styleButtonGroup.MuiButtonGroup-root{height:1.8rem}.styleButtonGroup .MuiButtonGroup-grouped{min-width:2rem}.styleActiveButton.MuiIconButton-root{background:#007AD4;border-radius:4px;color:#fff;padding:3px 4px}.styleActiveButton.MuiButtonBase-root:hover{background:#007AD4;color:#fff;padding:3px 4px}.styleDefaultButton.MuiIconButton-root{border-radius:4px;background:#ffffff;color:#757575;padding:3px 4px}.styleDefaultButton.MuiButtonBase-root:hover{background:#ffffff;color:#757575;padding:3px 4px}.attribute-and-action-tables::-webkit-scrollbar-thumb{background:#888;scrollbar-width:thin!important;border-radius:10%}.attribute-and-action-tables::-webkit-scrollbar{width:.7rem!important;height:.7rem!important}.reviewTableCell{min-width:6rem}.styleTile:hover{background-color:#f5f5f5!important}.styleTile{width:17.3%;background:rgb(255,255,255);border-radius:8px;padding:.5rem;margin:1rem .5rem;height:6rem;display:flex;flex-direction:column;justify-content:space-between;box-shadow:#bbd2e3 2px 5px 12px -3px;cursor:pointer}.styleAccordionSummary .MuiSvgIcon-root{width:1rem;height:1rem}.styleAutoComplete.MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon.MuiAutocomplete-root .MuiOutlinedInput-root{padding:0}.styleAutoComplete.MuiChip-root{max-width:calc(100% - 6px);font-size:.8rem;height:1.3rem;padding:0}.styleAutoComplete .MuiButtonBase-root{height:1.2rem}.styleAutoComplete .MuiOutlinedInput-root{padding:0;height:1.7rem}.styleChipAFC .MuiChip-deleteIcon{height:17px;width:17px}.styleChipAFC .MuiChip-label{font-size:12px}.styleChipAFC.MuiChip-outlined{max-width:9rem;background:rgb(0 0 0 / 9%);height:1.2rem!important;border-radius:1rem}*{margin:0}.admin-console-icons{display:flex;justify-content:space-between;align-items:center}.Applicationtable .MuiPaper-root{height:calc(100% - 3rem)}.tableTextField .MuiOutlinedInput-input{padding:0 0 0 .25rem!important}.styleHeaderCellList .MuiTableCell-root{padding:5px!important;background-color:#fafafa!important;white-space:nowrap;font-weight:600;font-size:.75rem!important}.styleDataCellList .MuiTableCell-root{padding:5px!important;font-size:.75rem!important}.styleRMStableContainerList.MuiTableContainer-root{height:calc(100% - 6rem)}.styleDataCellList:hover{background-color:#e5e5e5!important}.styleActivePage.PageMuiButtonBase-root.MuiPaginationItem-root.Mui-selected,.styleActivePage.MuiButtonBase-root.Mui-selected{background-color:#007ad4}.styleRMStableContainerFieldCatalogue.MuiTableContainer-root{height:calc(100% - 6.8rem)}::-webkit-scrollbar{width:5px;height:5px}::-webkit-scrollbar-track{background:#f1f1f1}::-webkit-scrollbar-thumb{background:#888}::-webkit-scrollbar-thumb:hover{background:#555}.styleTabName{font-size:.875rem;color:#007ad4;font-weight:600;border-bottom:2px solid #007Ad4;margin-right:1rem;padding-bottom:.3rem}.styleDefaultTabName{font-size:.875rem;color:#757575;font-weight:600;border-bottom:none;margin-right:1rem;padding-bottom:.3rem}.hierarchy-tree{width:max-content;border:1px solid rgba(0,0,0,.411);border-radius:7px;box-shadow:3px 3px 2px #b4b3b3;padding-right:1.5%;height:calc(100vh - 25rem);overflow-y:scroll}.entity-name{margin:.5rem;font-size:.8rem;font-weight:500}.styletreelabels.MuiFormControlLabel-root{font-size:.8rem}.accordionTableContainer{width:"98%";border-radius:"0.5rem 0.5rem 0.5rem 0.5rem"}.accordionTableCell{padding:0 6px!important;height:1.5rem;margin-left:.25rem;font-size:.75rem;width:33.3%}.accordionTableRow{background-color:#fff;padding:0}.accordionTableRow:hover{background-color:#e5e5e5!important}.tableTextField .MuiOutlinedInput-root{font-size:.75rem}.date-picker-event-log .react-datepicker-wrapper,.date-picker-event-log .react-datepicker__input-container,.date-picker-event-log .react-datepicker__input-container input{display:block;width:100%;height:1.5rem;color:#000000de;margin-right:16px}.table-row-event-log{background:#ffffff!important}.audit-log-app-bar .MuiAppBar-colorPrimary{color:#000;background-color:#fff}.reactflow-pane{height:100%;width:100%;margin-left:9rem!important;margin-right:auto}.condition_node{border:1px solid #AAA6FF;padding:5px;border-radius:4px;background-color:#eae9ff;display:flex;flex-direction:column;justify-content:center;align-items:center;height:5rem;width:6rem}.decision_node{border:1px solid #B0A067;padding:5px;border-radius:4px;background-color:#fff8f1;display:flex;flex-direction:column;justify-content:center;align-items:center;height:5rem;width:6rem}.action_node{border:1px solid #1872EB;padding:5px;border-radius:4px;background-color:#dbe9ff;display:flex;flex-direction:column;justify-content:center;align-items:center;height:5rem;width:6rem}.action_field_node{border:1px solid #C2E7D3;padding:5px;border-radius:4px;background-color:#dbfff4;display:flex;flex-direction:column;justify-content:center;align-items:center;height:5rem;width:6rem}.sidebar1{position:absolute;margin-left:1.563rem;width:6.6rem;height:22rem;background-color:#fff;padding:1rem;top:9.688rem;display:flex;flex-direction:column;align-items:center;justify-content:space-around}.decision-model{position:relative;margin-top:7rem;height:35.63rem;padding-left:2rem;font-family:Roboto;font-size:16px;font-weight:400;line-height:24px;letter-spacing:.005em;text-align:left;padding-left:0;padding-right:0;background:white;width:26.5rem;float:right}.react-flow-section1{height:calc(100% - 3rem);overflow:scroll}.styleAdminConsole .MuiButton-iconSizeMedium>*:first-child{font-size:.9rem}.styleAdminConsole .MuiButton-startIcon{margin-right:3px}.styleAdminConsole .MuiButton-label{font-size:.8rem}.styleListItem.MuiListItem-gutters{padding-left:.5rem}.styleExecutionPolicyRadioBtn .MuiFormControlLabel-root,.styleCompositeCheckBox.MuiFormControlLabel-root{height:2rem}.styleActiveListItem.MuiListItem-gutters{background-color:#c3e6ff;padding-left:.5rem}.styleDialogButton .MuiButtonBase-root{padding:0}.restore-icon{cursor:pointer}.styledOutlinedButton.MuiButton-root{position:static;left:0%;right:0%;top:0%;bottom:0%;background:#FFFFFF;border-radius:4px;border:.5px solid #007AD4;color:#007ad4;font-weight:600}.styledOutlinedButton.MuiButton-root:hover{background:#FFFFFF}.styledOutlinedButton .MuiButton-label{color:#007ad4;font-weight:600}.styleDateRangePicker .mantine-DateRangePicker-wrapper{width:11.3rem}.gridTable .rgt-cell:not(.rgt-row-edit):not(.rgt-row-not-selectable){cursor:pointer}.gridTable .rgt-row-selected,.gridTable.rgt-row-hover{background:#FAFAFA}.gridTable.rgt-cell-header{font-family:Roboto,sans-serif;font-weight:500;font-size:14px}.gridTable.rgt-cell{font-family:Roboto,sans-serif;font-style:normal;font-weight:400;font-size:14px;min-height:44px!important}.gridTable .rgt-cell-header-inner{color:#424242!important;font-weight:500}.gridTable .rgt-cell-inner{color:#757575!important}.gridTable.rgt-wrapper{min-height:0}.column-header{padding:.5rem}.gridTable .MuiDataGrid-columnHeader.DTtableheader .MuiDataGrid-columnSeparator{visibility:hidden}.gridTable .rgt-container .rgt-cell-header-inner{padding-left:0;padding-right:1px}.rule-chain-sidebar{position:absolute;margin-left:1.563rem;width:4.5rem;height:18rem;background-color:#fff;top:9.688rem;border-radius:4px;box-shadow:2px 2px 5px #a5a3a5bd}.reactflow-pane-DMN{height:calc(100% - 5rem);width:100%;margin-left:7rem;margin-right:auto}.condition_node{border:1px solid #AAA6FF;padding:5px;border-radius:4px;background-color:#eae9ff;display:flex;flex-direction:column;justify-content:center;align-items:center;height:5rem;width:6rem;overflow-wrap:anywhere}.decision_node{border:1px solid #B0A067;padding:5px;border-radius:4px;background-color:#fff8f1;display:flex;flex-direction:column;justify-content:center;align-items:center;height:5rem;width:6rem;overflow-wrap:anywhere}.decision_node_error{border:1px solid #ff0000;padding:5px;border-radius:4px;background-color:#f85c5c;display:flex;flex-direction:column;justify-content:center;align-items:center;height:5rem;width:6rem;overflow-wrap:anywhere}.action_node{border:1px solid #1872EB;padding:5px;border-radius:4px;background-color:#dbe9ff;display:flex;flex-direction:column;justify-content:center;align-items:center;height:5rem;width:6rem;overflow-wrap:anywhere}.action_field_node{border:1px solid #C2E7D3;padding:5px;border-radius:4px;background-color:#dbfff4;display:flex;flex-direction:column;justify-content:center;align-items:center;height:5rem;width:6rem;overflow-wrap:anywhere}.decision{border:1px solid black;padding:5px;background-color:green}.action_type{border:1px solid black;padding:5px;background-color:red}.condition_side{width:4.313rem;height:3.563rem;font-family:Roboto;font-style:normal;font-weight:400;font-size:1rem;line-height:1.19rem;text-align:center;color:#5b5959;flex:none;order:0;flex-grow:0}.stop{border:1px solid #eee;padding:5px;border-radius:50%;background-color:red}.stop_side{width:3rem;height:2rem;border:1px solid #eee;padding:5px;border-radius:50%;background-color:red}.inprocess_side{width:5rem;height:2rem;border:1px solid #eee;background-color:#40e0d0;transform:rotate(67.5deg) skew(45deg) scaleY(cos(45deg))}.txt{text-align:center}.inprocess{width:50px;height:50px;border:1px solid #eee;background-color:#40e0d0;transform:rotate(67.5deg) skew(45deg) scaleY(cos(45deg))}.btn-add{position:absolute;z-index:10;top:10px;left:10px}.remove-btn{position:absolute;visibility:hidden;height:.8rem;top:-.5rem;width:.5rem;font-size:.4rem;right:0rem;background-color:#fff;cursor:pointer}.remove-btnv{position:absolute;height:.8rem;top:-.5rem;width:.5rem;font-size:.4rem;right:0rem;background-color:#fff;cursor:pointer}.dndflow{flex-direction:column;display:flex;flex-grow:1;height:100%}.dndflow aside{border-right:1px solid #eee;padding:15px 10px;font-size:12px;background:#fcfcfc}.dndflow .dndnode{height:20px;padding:4px;border:1px solid #1a192b;border-radius:2px;margin-bottom:10px;display:flex;justify-content:center;align-items:center;cursor:grab}.dndflow .dndnode.input{border-color:#0041d0}.dndflow .dndnode.output{border-color:#ff0072}.dndflow .reactflow-wrapper{flex-grow:1;height:100%}.dndflow .selectall{margin-top:10px}@media screen and (min-width: 768px){.dndflow{flex-direction:row}.dndflow aside{width:20%;max-width:250px}}.sidebar{position:absolute;margin-left:1.563rem;width:6.6rem;height:28rem;background-color:#fff;top:9.688rem}.side-text{font-family:Roboto;font-size:1rem;font-weight:400;line-height:1.2rem;letter-spacing:0em;text-align:center;color:#000;text-transform:capitalize}.condition-model{position:relative;margin-top:7rem;height:35.63rem;padding-left:2rem;font-family:Roboto;font-size:16px;font-weight:400;line-height:24px;letter-spacing:.005em;text-align:left;overflow-y:scroll;width:26.5rem;float:right;background-color:#fff}.input-fields-condition{width:22rem;height:.5rem!important;margin-bottom:1rem;padding-bottom:2rem}.input-fields-label{margin:2.5rem 3rem 1rem 0}.decision-model1{position:relative;margin-top:7rem;height:35.63rem;font-family:Roboto;font-size:16px;font-weight:400;line-height:24px;letter-spacing:.005em;text-align:left;padding-left:0;padding-right:0;background:white;width:26.5rem;float:right}.decision-model{background:#FFFFFF;height:calc(100% - 1rem);width:20rem;right:0;position:absolute;margin:.5rem;border-radius:4px}.styleConditionButton.MuiButton-text{color:#564dc3;font-size:.8rem;font-weight:600}.styleDecisionButton.MuiButton-text{color:#8a4200;font-size:.8rem;font-weight:600}.styleActionButton.MuiButton-text{color:#1872eb;font-size:.8rem;font-weight:600}.styleAFButton.MuiButton-text{color:#00a07a;font-size:.8rem;font-weight:600}.regular-font{font-size:.875rem}.react-flow-section{height:calc(100% - 1rem);overflow:scroll}.each-section{display:flex;flex-direction:column;align-items:flex-start;width:100%}.MuiTab-wrapper{display:contents!important}.MuiTab-labelIcon .MuiTab-wrapper>*:first-child{margin-bottom:0rem!important}.MuiTab-labelIcon{min-height:3rem!important}.MuiTabs-root{margin-left:1rem}.viewDtTab.MuiTabPanel-root{padding:.5rem!important;background-color:#fafcff!important;height:100%!important}.container{width:"98%";margin-top:"0.5rem";border-radius:.5rem!important;font-family:Roboto}.tableCell{border-bottom:.5px solid #D1D5DB!important}.tablecell-header{font-weight:600!important;padding:.3rem .1rem!important;height:2.5rem!important;font-size:.875rem!important;align-items:center!important;background:#FFF;line-height:1.5rem}.tableRow{font-family:Roboto;font-weight:400!important;padding:.1rem!important;font-size:.75rem!important;align-items:center!important;line-height:1rem;color:#1d1d11;letter-spacing:.00188rem}.tableRows{background-color:#fff!important}.tableRows:hover{background-color:#e5e5e5!important}
