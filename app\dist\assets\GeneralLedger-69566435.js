import{r,i as R,l as Qs,b as Os,cK as ee,q as js,ip as ec,j as n,fV as Re,h1 as Be,$ as f,a,a8 as tc,hA as Rt,fX as We,fH as Ue,hl as ac,aB as _e,hp as sc,hq as cc,T as d,hu as rc,h as Ye,W as B,hv as Bt,hw as nc,sA as lc,hx as oc,g as dc,A as ic,gM as uc,f as hc,gO as k,gQ as N,hy as G,a3 as L,h2 as gc,a4 as fc,hz as Cc,h4 as ge,a1 as p,gU as mc,gW as yc,hB as xc,hC as Sc,hD as Ac,P as fe,hE as Lc,hF as Ve,R as He,fI as qe,hG as Ke,hH as Je,hI as Xe,hi as Nc,w as u,io as C,gL as w,hK as te,gV as Gc,l9 as pc,sY as Ze,hM as Ec,hN as ae,J as $c,sx as kc,sZ as Dc,s_ as Tc,s$ as zc,t0 as Ic,t1 as Mc,hP as y,hW as Wt,t2 as Fc,t3 as Ut,G as Qe}from"./index-fdfa25a0.js";import{A as wc}from"./AttachmentUploadDialog-cc0b6643.js";const Jc=()=>{var Ft,wt,bt;r.useState(!1);const[bc,_t]=r.useState(!1),[Yt,W]=r.useState(""),[b,Oe]=r.useState(""),[A,je]=r.useState(""),[$,se]=r.useState("");R(e=>e.appSettings.Format);const g=R(e=>e.AllDropDown.dropDown),[et,D]=r.useState(!1),[Vt,T]=r.useState(!1),[tt,at]=r.useState(!1),[Ht,ce]=r.useState(!1),[z,qt]=r.useState(""),[x,Kt]=r.useState(""),[vc,Jt]=r.useState(!1),[Xt,U]=r.useState(!1),[Ce,re]=r.useState(0),[me,ye]=r.useState(10),[st,ne]=r.useState(0),[I,Zt]=r.useState(""),[Qt,Ot]=r.useState([]),[jt,ct]=r.useState(!1),rt=R(e=>e.generalLedger.handleMassMode);let ea=R(e=>{var t;return(t=e.userManagement.entitiesAndActivities)==null?void 0:t["Display Material"]}),_=R(e=>e.userManagement.userData);console.log("handleMassModePC",rt);const l=Qs(),K=Os(),ta=48,aa=8,sa={PaperProps:{style:{maxHeight:ta*4.5+aa,width:250}}};r.useState(!1);const[Pc,M]=r.useState(!0),[ca,ra]=r.useState(null),[Y,nt]=r.useState([]);ee.useState(""),r.useState(!1),r.useState("");const[Rc,na]=r.useState("");r.useState(!0);const[Bc,lt]=r.useState(!1),[Wc,ot]=r.useState(!0);r.useState([]),r.useState([]),r.useState([]),r.useState([]),r.useState(!0),r.useState([]),r.useState([]),r.useState(!1);const[J,dt]=r.useState([]),[Uc,la]=r.useState([]),[it,oa]=r.useState({});r.useState([]),r.useState([]),r.useState(!1),r.useState([]);const[ut,da]=r.useState([]);r.useState([]);const[ia,ht]=r.useState(!1);r.useState(!0),r.useState("sm");const[ua,ha]=r.useState(0),[ga,fa]=r.useState(0),[Ca,X]=r.useState(!1),[xe,Se]=r.useState(!1),[_c,gt]=r.useState(!1),[ma,ft]=r.useState(!1);r.useState(!1);const[v,ya]=r.useState(""),Z=ee.useRef(null),Q=ee.useRef(null),[Ae,Le]=r.useState(!1),[xa,le]=r.useState(!1),[Yc,Sa]=r.useState([]),[Ct,mt]=r.useState(0),[yt,xt]=r.useState([]),i=R(e=>e.commonFilter.GeneralLedger),Ne=R(e=>e.commonSearchBar.GeneralLedger),St={chartOfAccounts:{newChartOfAccount:b},companyCode:{newCompanyCode:A},accountType:{newAccountType:z},accountGroup:{newAccountGroup:x},newGLAccount:$,copyFromCompCode:{newCompanyCodeToCopyFrom:I},copyFromGlAccount:{newGLAccountCopyFrom:v}},Aa=()=>{ht(!0)};console.log("newGLAccount",typeof $,typeof(x==null?void 0:x.FromAcct));const At=()=>{let e=$.concat("$$",A==null?void 0:A.code);M(!0);const t=c=>{M(!1),c.body.length>0?at(!0):(l(Ut({keyName:"AccountType",data:z})),l(Ut({keyName:"AccountGroup",data:{code:x==null?void 0:x.AccountGroup,desc:x==null?void 0:x.Description}})),K("/masterDataCockpit/generalLedger/newSingleGeneralLedger",{state:St}))},s=c=>{console.log(c)};u(`/${C}/alter/fetchGlAccountNCompCodeDupliChk?glAccountNCompCode=${e}`,"get",t,s)},La=()=>{if(b.code===void 0||b.code===""||(A==null?void 0:A.code)===void 0||(A==null?void 0:A.code)===""||(z==null?void 0:z.code)===void 0||(z==null?void 0:z.code)===""||(x==null?void 0:x.AccountGroup)===void 0||(x==null?void 0:x.AccountGroup)===""||$===void 0||$===""){D(!1),T(!0),U(!1);return}else if($.length!==10){U(!1),D(!0),T(!1);return}else{let e=Number($),t=Number(x==null?void 0:x.FromAcct),s=Number(x==null?void 0:x.ToAcct);console.log("check",t,s,isNaN(s)),t==0||isNaN(t)||s==0||isNaN(s)?(console.log("inside if condition",Number(t),Number(s)),U(!1),T(!1),D(!1),At()):(e>=t&&e<=s?(console.log("inside else then if for number check"),U(!1),T(!1),D(!1),At()):(console.log("inside else condition"),U(!0),T(!1),D(!1)),D(!1)),T(!1)}},Na=()=>{La()},Ge=()=>{T(!1),le(!1),Jt(!1),ft(!1)},pe=()=>{ht(!1),U(!1),T(!1),D(!1)},Ga=()=>{Le(e=>!e)},pa=()=>{Se(e=>!e)},Ea=e=>{Z.current&&Z.current.contains(e.target)||Le(!1)},$a=e=>{Q.current&&Q.current.contains(e.target)||Se(!1)},Ee=["Create Multiple","Upload Template ","Download Template "],$e=["Change Multiple","Upload Template ","Download Template "],[ka,Da]=r.useState(0),ke=["Create Single","With Copy","Without Copy"],O=ee.useRef(null),[De,Te]=r.useState(!1),Ta=()=>{Te(e=>!e)},za=()=>{Ia()},Ia=()=>{if(b.code===void 0||b.code===""||(A==null?void 0:A.code)===void 0||(A==null?void 0:A.code)===""||$===void 0||$===""||(I==null?void 0:I.code)===void 0||(I==null?void 0:I.code)===""||(v==null?void 0:v.code)===void 0||(v==null?void 0:v.code)===""){D(!1),le(!0);return}else{if($.length!==10){D(!0),le(!1);return}else D(!1);le(!1)}let e=$.concat("$$",A==null?void 0:A.code);M(!0);const t=c=>{M(!1),c.body.length>0?at(!0):K("/masterDataCockpit/generalLedger/displayCopyGeneralLedger",{state:St})},s=c=>{console.log(c)};u(`/${C}/alter/fetchGlAccountNCompCodeDupliChk?glAccountNCompCode=${e}`,"get",t,s)},Ma=()=>{ft(!0)},Fa=e=>{O.current&&O.current.contains(e.target)||Te(t=>!t)},Lt=(e,t)=>{t!==0&&(Da(t),Te(!1),t===1?ba():t===2&&wa())},wa=()=>{Aa()},ba=()=>{Ma()},va=()=>{_t(!0)},Pa=e=>{const t=c=>{l(y({keyName:"AccountGroupSearch",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getAccountGroupCodeDesc?chartAccount=${e}`,"get",t,s)},Ra=e=>{const t=c=>{l(y({keyName:"CompanyCodeSearch",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getCompanyCode?chartAccount=${e}`,"get",t,s)},Ba=e=>{const t=c=>{l(y({keyName:"GroupAccountNumberSearch",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getGroupAccountNumber?chartAccount=${e}`,"get",t,s)},Wa=e=>{const t=c=>{l(y({keyName:"GLAccountForSearch",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getGLBasedOnCompanyCode?companyCode=${e}`,"get",t,s)},Ua=(e,t)=>{{var s=t;let c={...i,chartOfAccount:s};l(w({module:"GeneralLedger",filterData:c}))}Pa(t==null?void 0:t.code),Ra(t==null?void 0:t.code),Ba(t==null?void 0:t.code)},_a=(e,t)=>{{var s=t;let c={...i,companyCode:s};l(w({module:"GeneralLedger",filterData:c}))}Wa(t==null?void 0:t.code)},Ya=(e,t)=>{{var s=t;let c={...i,glAccount:s};l(w({module:"GeneralLedger",filterData:c}))}},Va=(e,t)=>{{var s=t;let c={...i,glAccountType:s};l(w({module:"GeneralLedger",filterData:c}))}},Ha=e=>{if(e.target.value!==null){var t=e.target.value;let s={...i,shortText:t};l(w({module:"GeneralLedger",filterData:s}))}},qa=(e,t)=>{{var s=t;let c={...i,accountGroup:s};l(w({module:"GeneralLedger",filterData:c}))}},Ka=(e,t)=>{{var s=t;let c={...i,groupAccountNumber:s};l(w({module:"GeneralLedger",filterData:c}))}},Ja=(e,t)=>{{var s=t;let c={...i,createdBy:s};l(w({module:"GeneralLedger",filterData:c}))}},Xa=e=>{const t=new FormData;if([...e].forEach(S=>t.append("files",S)),rt==="Change")var s=`/${C}/massAction/getAllGeneralLedgerFromExcelForMassChange`;else var s=`/${C}/massAction/getAllGeneralLedgerFromExcel`;u(s,"postformdata",S=>{M(),S.statusCode===200?(X(!1),l(kc(S==null?void 0:S.body)),V("Create"),H(`${e.name} has been Uploaded Succesfully`),W("success"),ot(!1),gt(!0),va(),lt(!0),M(!1),K("/masterDataCockpit/generalLedger/createMultipleGL")):(X(!1),V("Create"),gt(!1),H("Creation Failed"),W("danger"),ot(!1),lt(!0),q(),M(!1)),ws()},S=>{console.log(S)},t)};let Za={"Person Responsible":`/${Qe}/data/getSalesOrg`,"Business Area ":`/${Qe}/data/getDivision`,"Functional Area":`/${Qe}/data/getLaboratoryDesignOffice`};const Qa=e=>{const t=e.target.value;dt(t),la([]),t.forEach(async s=>{const c=Za[s];Es(c)})},Oa=e=>{console.log("newselection",e),Sa(e);let t=Ie.map(m=>m.field);const s=Y.filter(m=>e.includes(m.id));let c=[];s.map(m=>{console.log("sssssss",m);let S={};t.forEach(o=>{console.log("yyyyy",m[o]),m[o]!==null&&(S[o]=m[o]||"")}),c.push(S),Ot(c),console.log("requiredArrayDetails",c)})},ja=[{title:"Field Status Group"},{title:"Group Chart Of Accounts"},{title:"Chart Of Accounts"},{title:"Controlling Area"}],es={"Task ID":"taskId",Status:"status",SalesOrganization:"salesOrg",Division:"division",OldMaterialNumber:"oldMaterialNumber","Lab/Office":"labOffice","Transportation Group":"transportationGroup","Batch management":"batchManagement"},ts=()=>{let e="Control Data";const t=c=>{l(Dc(c.body))},s=c=>{console.log(c)};u(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,s)},as=()=>{let e="Create/Bank/Interest";const t=c=>{l(Tc(c.body))},s=c=>{console.log(c)};u(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,s)},ss=()=>{let e="Type/Description";const t=c=>{l(zc(c.body))},s=c=>{console.log(c)};u(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,s)},cs=()=>{let e="Information";const t=c=>{l(Ic(c.body))},s=c=>{console.log(c)};u(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,s)},rs=()=>{let e="Keyword/Translation";const t=c=>{l(Mc(c.body))},s=c=>{console.log(c)};u(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,s)},ns=()=>{const e=s=>{l(y({keyName:"AccountType",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getGLAccountType`,"get",e,t)},ls=()=>{const e=s=>{l(y({keyName:"TradingPartner",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getTradingPartner`,"get",e,t)},os=()=>{const e=s=>{l(y({keyName:"BusinessArea",data:s.body}))},t=s=>{console.log(s)};u(`/${te}/data/getBusinessArea`,"get",e,t)},ds=()=>{const e=s=>{l(y({keyName:"FunctionalArea",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getFunctionalArea`,"get",e,t)},is=()=>{const e=s=>{l(y({keyName:"ProfitCenter",data:s.body}))},t=s=>{console.log(s)};u(`/${te}/data/getProfitCenter`,"get",e,t)},us=()=>{const e=s=>{l(y({keyName:"CostingSheet",data:s.body}))},t=s=>{console.log(s)};u(`/${te}/data/getCostingSheet`,"get",e,t)},hs=()=>{u("get",s=>{l(y({keyName:"CountryReg",data:s.body}))},s=>{console.log(s)})},gs=()=>{const e=s=>{l(y({keyName:"Jurisdiction",data:s.body}))},t=s=>{console.log(s)};u(`/${te}/data/getJurisdiction`,"get",e,t)},fs=()=>{const e=s=>{l(y({keyName:"Region",data:s.body}))},t=s=>{console.log(s)};u(`/${te}/data/getRegion`,"get",e,t)},Cs=()=>{const e=s=>{l(y({keyName:"LanguageKey",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getLanguageKey`,"get",e,t)},ms=()=>{const e=s=>{l(y({keyName:"ChartOfAccounts",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getChartOfAccounts`,"get",e,t)},ys=()=>{const e=s=>{l(y({keyName:"ReconAccountForAccountType",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getReconAccountForAccountType`,"get",e,t)},xs=()=>{const e=s=>{l(y({keyName:"SortKey",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getSortKey`,"get",e,t)},Ss=()=>{const e=s=>{l(y({keyName:"PlanningLevel",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getPlanningLevel`,"get",e,t)},As=()=>{const e=s=>{l(y({keyName:"InternalUOM",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getInternalUOM`,"get",e,t)},Ls=()=>{const e=s=>{l(y({keyName:"Language",data:s.body}))},t=s=>{console.log(s)};u(`/${C}/data/getLanguage`,"get",e,t)},Ns=e=>{const t=c=>{l(y({keyName:"InterestIndicator",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getInterestIndicator`,"get",t,s)},Gs=e=>{const t=c=>{l(y({keyName:"InterestCalculationFrequency",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getInterestCalculationFreq`,"get",t,s)},ps=e=>{const t=c=>{l(y({keyName:"GlAccountCompCode",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getGLBasedOnCompanyCode?companyCode=${e==null?void 0:e.code}`,"get",t,s)};r.useEffect(()=>{os(),ds(),is(),us(),hs(),gs(),fs(),Cs(),ts(),as(),ss(),cs(),rs(),ns(),ls(),ms(),ys(),xs(),Ss(),As(),Ls(),Ns(),Gs(),l(js({})),l(ec())},[]);const Es=e=>{u(e,"get",c=>{da([...ut,c.body])},c=>{console.log(c)})},$s=()=>{na("")},ks=e=>{ce(!0),e||(re(0),ye(10),ne(0));let t={companyCode:"",glAccount:(Ne==null?void 0:Ne.number)??"",accountType:"",createdBy:"",glName:"",accountGroup:"",skip:0,top:1e3};const s=m=>{var E,P,h,ue;var S=[];for(let F=0;F<((P=(E=m==null?void 0:m.body)==null?void 0:E.list)==null?void 0:P.length);F++){var o=(h=m==null?void 0:m.body)==null?void 0:h.list[F],ie={id:Wt(),chartOfAccount:(o==null?void 0:o.COA)!==""?o==null?void 0:o.COA:"NA",glAccountType:(o==null?void 0:o.Accounttype)!==""?o==null?void 0:o.Accounttype:"NA",accountGroup:(o==null?void 0:o.AccountGroup)!==""?o==null?void 0:o.AccountGroup:"NA",compCode:(o==null?void 0:o.CompanyCode)!==""?o==null?void 0:o.CompanyCode:"NA",glAccount:(o==null?void 0:o.GLAccount)!==""?o==null?void 0:o.GLAccount:"NA",groupAccountNumber:(o==null?void 0:o.GroupAccNo)!==""?o==null?void 0:o.GroupAccNo:"NA",createdBy:(o==null?void 0:o.CreatedBy)!==""?o==null?void 0:o.CreatedBy:"NA"};S.push(ie)}S.sort((F,he)=>ae(F.createdOn,"DD MMM YYYY HH:mm")-ae(he.createdOn,"DD MMM YYYY HH:mm")),nt(S.reverse()),ce(!1),Nt(S.length),mt((ue=m==null?void 0:m.body)==null?void 0:ue.count)},c=m=>{console.log(m)};u(`/${C}/data/getGeneralLedgersBasedOnAdditionalParams`,"post",s,c,t)},ze=new Date,oe=new Date;oe.setDate(oe.getDate()-15),r.useState([oe,ze]),r.useState([oe,ze]);const de=e=>{var m,S,o,ie;ce(!0),e||(re(0),ye(10),ne(0));let t={companyCode:((m=i==null?void 0:i.companyCode)==null?void 0:m.code)??"",glAccount:((S=i==null?void 0:i.glAccount)==null?void 0:S.code)??"",accountType:((o=i==null?void 0:i.glAccountType)==null?void 0:o.code)??"",createdBy:"",glName:(i==null?void 0:i.shortText)??"",accountGroup:((ie=i==null?void 0:i.accountGroup)==null?void 0:ie.code)??"",skip:0,top:1e3};const s=E=>{var F,he,vt,Pt;var P=[];for(let j=0;j<((he=(F=E==null?void 0:E.body)==null?void 0:F.list)==null?void 0:he.length);j++){var h=(vt=E==null?void 0:E.body)==null?void 0:vt.list[j],ue={id:Wt(),chartOfAccount:(h==null?void 0:h.COA)!==""?h==null?void 0:h.COA:"NA",glAccountType:(h==null?void 0:h.Accounttype)!==""?h==null?void 0:h.Accounttype:"NA",accountGroup:(h==null?void 0:h.AccountGroup)!==""?h==null?void 0:h.AccountGroup:"NA",compCode:(h==null?void 0:h.CompanyCode)!==""?h==null?void 0:h.CompanyCode:"NA",glAccount:(h==null?void 0:h.GLAccount)!==""?h==null?void 0:h.GLAccount:"NA",groupAccountNumber:(h==null?void 0:h.GroupAccNo)!==""?h==null?void 0:h.GroupAccNo:"NA",createdBy:(h==null?void 0:h.CreatedBy)!==""?h==null?void 0:h.CreatedBy:"NA"};P.push(ue)}P.sort((j,Zs)=>ae(j.createdOn,"DD MMM YYYY HH:mm")-ae(Zs.createdOn,"DD MMM YYYY HH:mm")),nt(P.reverse()),ce(!1),Nt(P.length),mt((Pt=E==null?void 0:E.body)==null?void 0:Pt.count)},c=E=>{console.log(E)};u(`/${C}/data/getGeneralLedgersBasedOnAdditionalParams`,"post",s,c,t)};r.useEffect(()=>{(parseInt(Ce)+1)*parseInt(me)>=parseInt(st)+1e3&&(de(st+1e3),ne(e=>e+1e3))},[Ce,me]),r.useState([]),r.useState([]),r.useState(null),r.useState(null);const[Vc,Nt]=r.useState(0);r.useState(!1),r.useState(!1),r.useState(!1),r.useState(!1),r.useState(!1),r.useState(!1),r.useState(""),r.useState("");const[Ds,Gt]=r.useState(!1),[Ts,V]=r.useState(""),[zs,H]=r.useState(),q=()=>{Gt(!0)},pt=()=>{Gt(!1)};r.useState(null),r.useState(null),r.useState(null);const Is=()=>{l(Gc({module:"GeneralLedger"}))};function Ms(){de()}r.useState([]),r.useState([]);const[Hc,Fs]=r.useState(!1);r.useState(null),r.useState(null),r.useState([]);const ws=()=>{Fs(!1)};r.useState(null),r.useState("");const Ie=[{field:"chartOfAccount",headerName:"Chart Of Account",editable:!1,flex:1},{field:"compCode",headerName:"Company Code",editable:!1,flex:1},{field:"glAccount",headerName:"G/L Account",editable:!1,flex:1},{field:"glAccountType",headerName:"G/L Account Type",editable:!1,flex:1},{field:"accountGroup",headerName:"Account Group",editable:!1,flex:1},{field:"groupAccountNumber",headerName:"Group Account Number",editable:!1,flex:1},{field:"createdBy",headerName:"Created By",editable:!1,flex:1},{field:"actions",align:"center",flex:1,headerAlign:"center",headerName:"Actions",sortable:!1,renderCell:e=>a("div",{children:a(Ye,{title:"Extend",children:a(B,{"aria-label":"View Metadata",onClick:()=>{var t;console.log("paramsaction",e.row),Ys((t=e==null?void 0:e.row)==null?void 0:t.chartOfAccount),Js(e.row)},children:a(pc,{})})})})}],bs=J.map(e=>{const t=es[e];return t?{field:t,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null),vs=[...Ie,...bs];r.useEffect(()=>{de()},[]);let Ps=r.useRef(null);const Rs=async()=>{var e=Qt.map(c=>({compCode:c.compCode,glAccount:c.glAccount}));console.log("downloadPayload",e);let t=c=>{M(!1);const m=URL.createObjectURL(c),S=document.createElement("a");S.href=m,S.setAttribute("download","GeneraL Ledger_Mass Change.xls"),document.body.appendChild(S),S.click(),document.body.removeChild(S),URL.revokeObjectURL(m),q(),V("Success"),H("GeneraL Ledger_Mass Change.xls has been downloaded successfully"),W("success")},s=c=>{c.message&&(q(),V("Error"),H(`${c.message}`),W("danger"))};u(`/${C}/excel/downloadExcelWithData`,"postandgetblob",t,s,e)},Bs=()=>{X(!0),Ze("Create"),l(Ze("Create"))},Et=(e,t)=>{t!==0&&(ha(t),Le(!1),t===1?Bs():t===2&&Us())},$t=(e,t)=>{t!==0&&(fa(t),Se(!1),t===1?Ws():t===2&&Rs())},Ws=()=>{X(!0),l(Ze("Change"))},Us=async()=>{let e=s=>{const c=URL.createObjectURL(s),m=document.createElement("a");m.href=c,m.setAttribute("download","GeneraL Ledger_Mass Create"),document.body.appendChild(m),m.click(),document.body.removeChild(m),URL.revokeObjectURL(c),q(),V("Success"),H("General Ledger_Mass Create.xls has been downloaded successfully"),W("success")},t=s=>{s.message&&(q(),V("Error"),H(`${s.message}`),W("danger"))};u(`/${C}/excel/downloadExcel`,"getblobfile",e,t)},kt=e=>{const t=c=>{l(y({keyName:"CompanyCode",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getCompanyCode?chartAccount=${e==null?void 0:e.code}`,"get",t,s)},Dt=e=>{const t=c=>{l(y({keyName:"GroupAccountNumber",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getGroupAccountNumber?chartAccount=${e==null?void 0:e.code}`,"get",t,s)},Me=e=>{const t=c=>{l(y({keyName:"AccountCurrency",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getAccountCurrency?companyCode=${e==null?void 0:e.code}`,"get",t,s)},Fe=e=>{const t=c=>{l(y({keyName:"TaxCategory",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getTaxCategory?companyCode=${e==null?void 0:e.code}`,"get",t,s)},Tt=e=>{const t=c=>{l(y({keyName:"AlternativeAccountNumber",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getAlternativeAccountNumber?chartAccount=${e==null?void 0:e.code}`,"get",t,s)},we=e=>{const t=c=>{l(y({keyName:"FieldStatusGroup",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getFieldStatusGroup?companyCode=${e==null?void 0:e.code}`,"get",t,s)},zt=e=>{const t=c=>{l(y({keyName:"AccountGroupDialog",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getAccountGroup?chartAccount=${e==null?void 0:e.code}`,"get",t,s)},be=e=>{const t=c=>{l(y({keyName:"HouseBank",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getHouseBank?companyCode=${e==null?void 0:e.code}`,"get",t,s)},_s=e=>{const t=c=>{l(y({keyName:"CostElementCategory",data:c.body}))},s=c=>{console.log(c,"error in dojax")};u(`/${C}/data/getCostElementCategory?accountType=${e==null?void 0:e.code}`,"get",t,s)},Ys=e=>{const t=c=>{l(y({keyName:"CompanyCodeForExtend",data:c.body}))},s=c=>{console.log(c)};u(`/${C}/data/getCompanyCode?chartAccount=${e}`,"get",t,s)},Vs=[{code:"0001",desc:"Incture Company Code"},{code:"1000",desc:"Incture Company Code"},{code:"1010",desc:"Company Code 1010"},{code:"1710",desc:"Company Code 1710"},{code:"2500",desc:"Incture orissa cocd"},{code:"AE01",desc:"AE01 & CO"},{code:"BBSR",desc:"BBSR & co."},{code:"HN10",desc:"Incture Company Code"},{code:"I00X",desc:"Industry X - template CC"},{code:"JIN1",desc:"Jindal India Pvt Ltd"},{code:"PA10",desc:"Panama Digital PVT LTD"},{code:"SDS$",desc:"XYZ Chemicals"},{code:"SDS4",desc:"XYZC chmicals"},{code:"TAAJ",desc:"TAAJ Hotels"}],[ve,It]=ee.useState([]),Hs=e=>{const t=e.target.value;e.target.checked?It(s=>[...s,t]):It(s=>s.filter(c=>c!==t))};console.log("selectedcodes",ve);const qs=e=>{const t=e.target.value;ye(t),re(0),ne(0)},Ks=(e,t)=>{re(t)},Mt={convertJsonToExcel:()=>{let e=[];Ie.forEach(t=>{t.headerName.toLowerCase()!=="action"&&!t.hide&&e.push({header:t.headerName,key:t.field})}),Ec({fileName:`General Ledger Data-${ae(ze).format("DD-MMM-YYYY")}`,columns:e,rows:Y})},button:()=>a(p,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>Mt.convertJsonToExcel(),children:"Download"})},Js=e=>{ct(!0),xt(e),xt(t=>({...t,requestType:"Extend"}))},Pe=()=>{ct(!1)},Xs=()=>{l(Fc(ve)),K("/masterDataCockpit/generalLedger/displayGeneralLedger/1212",{state:yt}),console.log(yt,"tableRows")};return n("div",{ref:Ps,children:[n(Re,{open:jt,onClose:Pe,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Be,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(f,{sx:{display:"block"},children:[a(d,{variant:"h6",children:"Select Company Code(s)"}),a(d,{variant:"body2",color:"#777",children:"To extend this General Ledger"})]}),a(B,{sx:{width:"max-content"},onClick:Pe,children:a(Xe,{})})]}),a(We,{sx:{padding:".5rem 1rem",maxHeight:400,maxWidth:400,overflowY:"auto"},children:a(f,{container:!0,children:Vs.map(e=>a(tc,{control:a(Rt,{checked:ve.includes(e.code),onChange:Hs,value:e.code}),label:`${e.desc} (${e.code})`},e.code))})}),n(Ue,{sx:{display:"flex",justifyContent:"end"},children:[a(p,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Pe,children:"Cancel"}),a(p,{className:"button_primary--normal",type:"save",onClick:Xs,variant:"contained",children:"Apply"})]})]}),a($c,{dialogState:Ds,openReusableDialog:q,closeReusableDialog:pt,dialogTitle:Ts,dialogMessage:zs,handleDialogConfirm:pt,dialogOkText:"OK",dialogSeverity:Yt}),a("div",{style:{...ac,backgroundColor:"#FAFCFF"},children:n(_e,{spacing:1,children:[n(f,{container:!0,sx:sc,children:[n(f,{item:!0,md:5,sx:cc,children:[a(d,{variant:"h3",children:a("strong",{children:"General Ledger"})}),a(d,{variant:"body2",color:"#777",children:"This view displays the list of General Ledgers"})]}),a(f,{item:!0,md:7,sx:{display:"flex"},children:n(f,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[a(rc,{title:"Search for multiple General Ledger numbers separated by comma",handleSearchAction:ks,module:"GeneralLedger",keyName:"number",message:"Search General Ledger ",clearSearchBar:$s}),a(Ye,{title:"Reload",children:a(B,{sx:Bt,children:a(nc,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:Ms})})}),a(Ye,{title:"Export",children:a(B,{sx:Bt,children:a(lc,{onClick:Mt.convertJsonToExcel})})})]})})]}),a(f,{container:!0,sx:oc,children:a(f,{item:!0,md:12,children:n(dc,{className:"filter-accordian",children:[a(ic,{expandIcon:a(uc,{}),"aria-controls":"panel1a-content",id:"panel1a-header",sx:{minHeight:"2rem !important",margin:"0px !important"},children:a(d,{sx:{fontWeight:"700"},children:"Search General Ledger"})}),n(hc,{sx:{padding:"0.5rem 1rem 0.5rem"},children:[n(f,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:[n(f,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[n(f,{item:!0,md:2,children:[a(d,{sx:k,children:"Chart Of Account"}),a(N,{size:"small",fullWidth:!0,children:a(G,{sx:{height:"31px"},fullWidth:!0,size:"small",value:i==null?void 0:i.chartOfAccount,onChange:Ua,options:(g==null?void 0:g.ChartOfAccounts)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>a("li",{...e,children:a(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Chart Of Account"})})})]}),n(f,{item:!0,md:2,children:[a(d,{sx:k,children:"Company Code"}),a(N,{fullWidth:!0,size:"small",children:a(G,{sx:{height:"31px"},fullWidth:!0,size:"small",value:i==null?void 0:i.companyCode,onChange:_a,options:(g==null?void 0:g.CompanyCodeSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>a("li",{...e,children:a(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Company Code"})})})]}),n(f,{item:!0,md:2,children:[a(d,{sx:k,children:"G/L Account"}),a(N,{size:"small",fullWidth:!0,children:a(G,{sx:{height:"31px"},fullWidth:!0,size:"small",value:i==null?void 0:i.glAccount,onChange:Ya,options:(g==null?void 0:g.GLAccountForSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>a("li",{...e,children:a(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Chart Of Account"})})})]}),n(f,{item:!0,md:2,children:[a(d,{sx:k,children:"G/L Account Type"}),a(N,{size:"small",fullWidth:!0,children:a(G,{sx:{height:"31px"},fullWidth:!0,size:"small",value:i==null?void 0:i.glAccountType,onChange:Va,options:(g==null?void 0:g.AccountType)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>a("li",{...e,children:a(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select G/L Account Type"})})})]}),n(f,{item:!0,md:2,children:[a(d,{sx:k,children:"Account Group"}),a(N,{size:"small",fullWidth:!0,children:a(G,{sx:{height:"31px"},fullWidth:!0,size:"small",value:i==null?void 0:i.accountGroup,onChange:qa,options:(g==null?void 0:g.AccountGroupSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>a("li",{...e,children:a(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Account Group"})})})]}),n(f,{item:!0,md:2,children:[a(d,{sx:k,children:"Short Text"}),a(N,{fullWidth:!0,size:"small",children:a(L,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:Ha,placeholder:"Enter Short Text",value:i==null?void 0:i.shortText})})]}),n(f,{item:!0,md:2,children:[a(d,{sx:k,children:"Group Account Number"}),a(N,{fullWidth:!0,size:"small",children:a(G,{sx:{height:"31px"},fullWidth:!0,size:"small",value:i==null?void 0:i.groupAccountNumber,onChange:Ka,options:(g==null?void 0:g.GroupAccountNumberSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>a("li",{...e,children:a(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Group Account Number"})})})]}),n(f,{item:!0,md:2,children:[a(d,{sx:k,children:"Created By"}),a(L,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:i==null?void 0:i.createdBy,onChange:Ja,placeholder:"Enter Created By"})]}),n(f,{item:!0,md:2,children:[a(d,{sx:k,children:"Add New Filters"}),a(N,{children:a(gc,{sx:{font_Small:k,height:"31px",fontSize:"12px",width:"200px"},size:"small",multiple:!0,limitTags:2,value:J,onChange:Qa,renderValue:e=>e.join(", "),MenuProps:{MenuProps:sa},endAdornment:J.length>0&&a(fc,{position:"end",children:a(B,{size:"small",onClick:()=>dt([]),"aria-label":"Clear selections",children:a(Cc,{})})}),children:ja.map(e=>n(ge,{value:e.title,children:[a(Rt,{checked:J.indexOf(e.title)>-1}),e.title]},e.title))})}),a(f,{style:{display:"flex",justifyContent:"space-around"}})]})]}),a(f,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:J.map((e,t)=>a(f,{item:!0,children:n(_e,{children:[a(d,{sx:{fontSize:"12px"},children:e}),a(G,{sx:k,size:"small",options:ut??[],getOptionLabel:(s,c)=>{var m,S;return`${(m=s[c])==null?void 0:m.code} - ${(S=s[c])==null?void 0:S.desc}`},placeholder:`Enter ${e}`,value:it[e],onChange:(s,c)=>oa({...it,[e]:c}),renderInput:s=>a(L,{sx:{fontSize:"12px !important"},...s,size:"small",placeholder:`Enter ${e}`,variant:"outlined"})},e[t])]})}))})]}),a(f,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:n(f,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:[a(p,{variant:"outlined",sx:mc,onClick:Is,children:"Clear"}),a(p,{variant:"contained",sx:{...yc,...xc},onClick:de,children:"Search"})]})})]})]})})}),a(f,{item:!0,sx:{position:"relative"},children:a(_e,{children:a(Sc,{isLoading:Ht,module:"GeneralLedger",width:"100%",title:"List of General Ledgers ("+Ct+")",rows:Y,columns:vs,page:Ce,pageSize:me,rowCount:Ct??(Y==null?void 0:Y.length)??0,onPageChange:Ks,onPageSizeChange:qs,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Oa,callback_onRowDoubleClick:e=>{const t=e.row.glAccount;K(`/masterDataCockpit/generalLedger/displayGeneralLedger/${t}`,{state:e.row})},stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})})}),Ac(ea,"General Ledger","CreateGL")&&(_==null?void 0:_.role)==="Super User"||(_==null?void 0:_.role)==="Finance"?a(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(Lc,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:ca,onChange:e=>{ra(e)},children:[n(Ve,{variant:"contained",ref:O,"aria-label":"split button",children:[a(p,{size:"small",variant:"contained",onClick:()=>Lt(ke[0],0),children:ke[0]}),a(p,{size:"small","aria-controls":De?"split-button-menu":void 0,"aria-expanded":De?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:Ta,children:a(He,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),a(qe,{sx:{zIndex:1},open:De,anchorEl:O.current,placement:"top-end",children:a(fe,{style:{width:(Ft=O.current)==null?void 0:Ft.clientWidth},children:a(Ke,{onClickAway:Fa,children:a(Je,{id:"split-button-menu",autoFocusItem:!0,children:ke.slice(1).map((e,t)=>a(ge,{selected:t===ka-1,onClick:()=>Lt(e,t+1),children:e},e))})})})}),n(Re,{open:ia,onClose:pe,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Be,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[a(d,{variant:"h6",children:"New General Ledger"}),a(B,{sx:{width:"max-content"},onClick:pe,children:a(Xe,{})})]}),n(We,{sx:{padding:".5rem 1rem"},children:[n(f,{container:!0,spacing:3,children:[n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Chart Of Accounts",a("span",{style:{color:"red"},children:"*"})]}),a(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:a(G,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{Oe(t),kt(t),Dt(t),Tt(t),zt(t)},options:(g==null?void 0:g.ChartOfAccounts)??[],getOptionLabel:e=>`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,t)=>a("li",{...e,children:n(d,{style:{fontSize:12},children:[t==null?void 0:t.code," - ",t==null?void 0:t.desc]})}),error:b==="",renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CHART OF ACCOUNTS"})})})]}),n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Company Code",a("span",{style:{color:"red"},children:"*"})]}),a(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:a(G,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{je(t),Me(t),Fe(t),we(t),be(t)},options:(g==null?void 0:g.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,t)=>a("li",{...e,children:n(d,{style:{fontSize:12},children:[t==null?void 0:t.code," - ",t==null?void 0:t.desc]})}),error:A==="",renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})})]}),n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Account Type",a("span",{style:{color:"red"},children:"*"})]}),a(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:a(G,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{qt(t),_s(t)},options:(g==null?void 0:g.AccountType)??[],getOptionLabel:e=>`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,t)=>a("li",{...e,children:n(d,{style:{fontSize:12},children:[t==null?void 0:t.code," - ",t==null?void 0:t.desc]})}),error:x==="",renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT ACCOUNT TYPE"})})})]}),n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Account Group",a("span",{style:{color:"red"},children:"*"})]}),a(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:a(G,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{Kt(t)},options:(g==null?void 0:g.AccountGroupDialog)??[],getOptionLabel:e=>`${e==null?void 0:e.AccountGroup} - ${e==null?void 0:e.Description}`,renderOption:(e,t)=>a("li",{...e,children:n(d,{style:{fontSize:12},children:[t==null?void 0:t.AccountGroup," - ",t==null?void 0:t.Description]})}),error:x==="",renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT ACCOUNT GROUP"})})})]}),n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem",marginRight:"5rem"},children:[n(d,{children:["GL Account",a("span",{style:{color:"red"},children:"*"})]}),n(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:[a(L,{sx:{fontSize:"12px !important",height:"40px"},fullWidth:!0,size:"small",value:$,onChange:e=>{let t=e.target.value;if(t.length>0&&t[0]===" ")se(t.trimStart());else{let s=t.toUpperCase();se(s)}},inputProps:{maxLength:10,style:{textTransform:"uppercase"}},placeholder:`${x?x==null?void 0:x.FromAcct:"Enter GL Account"} - ${x?x==null?void 0:x.ToAcct:""}`,required:!0}),et&&a(d,{color:"red",children:"*General Ledger must be 10 digits"}),Xt&&a(d,{color:"red",children:"*Please Enter GL Account Number within Assigned Range"})]})]})]}),Vt&&a(f,{children:a(d,{style:{color:"red"},children:"*Please Enter Mandatory Fields"})}),tt&&a(f,{children:a(d,{style:{color:"red"},children:"*The GL Account with Company Code already exists. Please enter different GL Account or Company Code"})})]}),n(Ue,{sx:{display:"flex",justifyContent:"end"},children:[a(p,{sx:{width:"max-content",textTransform:"capitalize"},onClick:pe,children:"Cancel"}),a(p,{className:"button_primary--normal",type:"save",onClick:Na,variant:"contained",children:"Proceed"})]})]}),n(Re,{open:ma,onClose:Ge,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Be,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[a(d,{variant:"h6",children:"New General Ledger"}),a(B,{sx:{width:"max-content"},onClick:Ge,children:a(Xe,{})})]}),n(We,{sx:{padding:".5rem 1rem"},children:[n(f,{container:!0,spacing:3,children:[n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Chart Of Accounts",a("span",{style:{color:"red"},children:"*"})]}),a(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:a(G,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{Oe(t),kt(t),Dt(t),Tt(t),zt(t)},options:(g==null?void 0:g.ChartOfAccounts)??[],getOptionLabel:e=>`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,t)=>a("li",{...e,children:n(d,{style:{fontSize:12},children:[t==null?void 0:t.code," - ",t==null?void 0:t.desc]})}),error:b==="",renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CHART OF ACCOUNTS"})})})]}),n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Company Code",a("span",{style:{color:"red"},children:"*"})]}),a(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:a(G,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{je(t),Me(t),Fe(t),we(t),be(t)},options:(g==null?void 0:g.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,t)=>a("li",{...e,children:n(d,{style:{fontSize:12},children:[t==null?void 0:t.code," - ",t==null?void 0:t.desc]})}),error:A==="",renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})})]}),n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem",marginRight:"5rem"},children:[n(d,{children:["GL Account",a("span",{style:{color:"red"},children:"*"})]}),n(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:[a(L,{sx:{fontSize:"12px !important",height:"40px"},fullWidth:!0,size:"small",value:$,onChange:e=>{let t=e.target.value;if(t.length>0&&t[0]===" ")se(t.trimStart());else{let s=t.toUpperCase();se(s)}},inputProps:{maxLength:10,style:{textTransform:"uppercase"}},placeholder:"ENTER GL ACCOUNT",required:!0}),et&&a(d,{color:"red",children:"*General Ledger must be 10 digits"})]})]}),a(Nc,{sx:{width:"100%",marginLeft:"2%"},children:a("b",{children:"Copy From"})}),n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Company Code",a("span",{style:{color:"red"},children:"*"})]}),a(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:a(G,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{Zt(t),ps(t),Me(t),Fe(t),we(t),be(t)},options:(g==null?void 0:g.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,t)=>a("li",{...e,children:n(d,{style:{fontSize:12},children:[t==null?void 0:t.code," - ",t==null?void 0:t.desc]})}),error:I==="",renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})})]}),n(f,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["GL Account",a("span",{style:{color:"red"},children:"*"})]}),a(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:a(f,{md:12,children:a(G,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{ya(t)},options:(g==null?void 0:g.GlAccountCompCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>a("li",{...e,children:a(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>a(L,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT GL ACCOUNT"})})})})]})]}),xa&&a(f,{children:a(d,{style:{color:"red"},children:"*Please Enter Mandatory Fields"})}),tt&&a(f,{children:a(d,{style:{color:"red"},children:"*The GL Account with Company Code already exists. Please enter different GL Account or Company Code"})})]}),n(Ue,{sx:{display:"flex",justifyContent:"end"},children:[a(p,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Ge,children:"Cancel"}),a(p,{className:"button_primary--normal",type:"save",onClick:za,variant:"contained",children:"Proceed"})]})]}),n(Ve,{variant:"contained",ref:Z,"aria-label":"split button",children:[a(p,{size:"small",onClick:()=>Et(Ee[0],0),sx:{cursor:"default"},children:Ee[0]}),a(p,{size:"small","aria-controls":Ae?"split-button-menu":void 0,"aria-expanded":Ae?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:Ga,children:a(He,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),a(qe,{sx:{zIndex:1},open:Ae,anchorEl:Z.current,placement:"top-end",children:a(fe,{style:{width:(wt=Z.current)==null?void 0:wt.clientWidth},children:a(Ke,{onClickAway:Ea,children:a(Je,{id:"split-button-menu",autoFocusItem:!0,children:Ee.slice(1).map((e,t)=>a(ge,{selected:t===ua-1,onClick:()=>Et(e,t+1),children:e},e))})})})}),n(Ve,{variant:"contained",ref:Q,"aria-label":"split button",children:[a(p,{size:"small",onClick:()=>$t($e[0],0),sx:{cursor:"default"},children:$e[0]}),a(p,{size:"small","aria-controls":xe?"split-button-menu":void 0,"aria-expanded":xe?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:pa,children:a(He,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),a(qe,{sx:{zIndex:1},open:xe,anchorEl:Q.current,placement:"top-end",children:a(fe,{style:{width:(bt=Q.current)==null?void 0:bt.clientWidth},children:a(Ke,{onClickAway:$a,children:a(Je,{id:"split-button-menu",autoFocusItem:!0,children:$e.slice(1).map((e,t)=>a(ge,{selected:t===ga-1,onClick:()=>$t(e,t+1),children:e},e))})})})}),Ca&&a(wc,{artifactId:"",artifactName:"",setOpen:X,handleUpload:Xa})]})}):""]})})]})};export{Jc as default};
