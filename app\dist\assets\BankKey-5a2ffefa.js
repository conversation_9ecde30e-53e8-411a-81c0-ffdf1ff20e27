import{r as t,i as p,l as kn,b as Cn,cK as Q,w as k,a as n,ht as Bn,j as u,ho as Sn,h8 as xn,J as vn,hl as mn,aB as de,$ as h,hp as Dn,hq as wn,T as g,hu as Nn,h as Ue,W as Z,hv as We,hw as Kn,R as ue,hx as pn,g as Mn,A as En,gM as In,f as An,gO as D,gQ as O,hy as he,a3 as w,h2 as Tn,a4 as zn,hz as Rn,a1 as C,gU as Fn,gW as $n,hB as bn,hC as Pn,hD as Ln,P as ge,hE as Un,fV as Wn,h1 as _n,hI as Yn,fX as Vn,fH as Hn,hF as _e,fI as Ye,hG as Ve,hH as He,h4 as Ge,F as Gn,gL as R,hM as Jn,hN as _,iu as B,gV as qn,sU as Je,hP as Xn,sV as Qn,sW as Zn,hW as qe,sX as On,G as fe}from"./index-fdfa25a0.js";import{A as jn}from"./AttachmentUploadDialog-cc0b6643.js";const ht=()=>{var Pe,Le;t.useState(!1);const[et,Xe]=t.useState(!1),[Qe,S]=t.useState("");p(e=>e.appSettings.Format);const[Y,Ze]=t.useState(""),[M,Oe]=t.useState("");t.useState(""),t.useState("");const[ye,je]=t.useState(!1);t.useState(!1);const[at,ke]=t.useState(!1),[ea,V]=t.useState(!1),[aa,Ce]=t.useState(!1),y=kn(),j=Cn(),[na,Be]=t.useState(0),[ee,H]=t.useState(0),[Se,ae]=t.useState(10),[xe,G]=t.useState(0),ta=(e,a)=>{H(a)},sa=e=>{const a=e.target.value;ae(a),H(0),G(0)},la=48,oa=8,ra={PaperProps:{style:{maxHeight:la*4.5+oa,width:250}}},[ia,F]=t.useState(!1),[ca,da]=t.useState(null),[A,ve]=t.useState([]);Q.useState(""),t.useState(!1),t.useState("");const[nt,ua]=t.useState("");t.useState(!0);const[tt,me]=t.useState(!1),[st,De]=t.useState(!0);t.useState([]),t.useState([]),t.useState([]),t.useState([]);const[lt,$]=t.useState(!0),[ot,ha]=t.useState([]),[ga,fa]=t.useState([]);t.useState(!1);const[J,we]=t.useState([]),[rt,ya]=t.useState([]),[Ne,ka]=t.useState({});t.useState([]),t.useState([]),t.useState(!1),t.useState([]);const[Ke,Ca]=t.useState([]);t.useState([]);const[Ba,pe]=t.useState(!1);t.useState(!1),t.useState(!0),t.useState("sm");const[Sa,b]=t.useState(!1);t.useState("");const[xa,Me]=t.useState(!1),[va,ma]=t.useState(!1),[ne,te]=t.useState(!1);Q.useRef(null);const P=Q.useRef(null),[Da,wa]=t.useState(0);t.useState(!1);const[se,le]=t.useState(!1),L=Q.useRef(null);t.useState(0);const[Na,Ka]=t.useState(0),pa=p(e=>e.AllDropDown.dropDown),[it,Ee]=t.useState(!1);console.log("dropdownData",pa);const oe=["Create Multiple","Upload Template ","Download Template "],re=["Change Multiple","Upload Template ","Download Template "],i=p(e=>e.commonFilter.BankKey),E=p(e=>e.commonSearchBar.BankKey);console.log("formcontroller_SearchBar",E),console.log("rmSearchForm",i);const T=p(e=>{var a;return(a=e==null?void 0:e.AllDropDown)==null?void 0:a.dropDown});let Ma=p(e=>{var a;return(a=e.userManagement.entitiesAndActivities)==null?void 0:a["Bank Key"]}),z=p(e=>e.userManagement.userData);const Ie=p(e=>e.bankKey.handleMassMode),Ea=()=>{pe(!0)},Ia=()=>{if((M==null?void 0:M.code)===void 0||(M==null?void 0:M.code)===""||Y===void 0||Y===""){Ee(!1),Me(!0);return}else Ee(!1),ba(),Me(!1)},ie=()=>{pe(!1),je(!1)},Aa=e=>{if(e.target.value!==null){var a=e.target.value;let o={...i,bankName:a};y(R({module:"BankKey",filterData:o}))}},Ta=e=>{if(e.target.value!==null){var a=e.target.value;let o={...i,bankBranch:a};y(R({module:"BankKey",filterData:o}))}},za=e=>{if(e.target.value!==null){var a=e.target.value;let o={...i,swiftBic:a};y(R({module:"BankKey",filterData:o}))}},Ra=e=>{if(e.target.value!==null){var a=e.target.value;let o={...i,bankNumber:a};y(R({module:"BankKey",filterData:o}))}},Fa=(e,a)=>{{var o=a;let r={...i,bankCtrRegion:o};y(R({module:"BankKey",filterData:r}))}},$a=(e,a)=>{{var o=e.target.value;let r={...i,createdBy:o};y(R({module:"BankKey",filterData:r}))}},N={bankKey:{newBankKey:Y},bankCtryReg:{newBankCtryReg:M}},Ae={convertJsonToExcel:()=>{let e=[];Fe.forEach(a=>{a.headerName.toLowerCase()!=="action"&&!a.hide&&e.push({header:a.headerName,key:a.field})}),Jn({fileName:`Bank Key Data-${_(ce).format("DD-MMM-YYYY")}`,columns:e,rows:A})},button:()=>n(C,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>Ae.convertJsonToExcel(),children:"Download"})},ba=()=>{var c,s,l;Ce(!0);let e=(c=N==null?void 0:N.bankKey)==null?void 0:c.newBankKey,a=(l=(s=N==null?void 0:N.bankCtryReg)==null?void 0:s.newBankCtryReg)==null?void 0:l.code,o=e.concat("$$",a);console.log("sendNewBankKeyData",N);const r=f=>{console.log("dupli",f),f.body.length>0?(Ce(!1),ma(!0)):j("/masterDataCockpit/bankKey/newSingleBankKey",{state:N})},d=f=>{console.log(f)};k(`/${B}/alter/fetchBankKeyCountryDupliChk?bankKeyCountryToCheck=${o}`,"get",r,d)},Pa=()=>{const e=o=>{y(Xn({keyName:"CountryReg",data:o.body}))},a=o=>{console.log(o)};k(`/${B}/data/getCountry`,"get",e,a)};t.useEffect(()=>{(parseInt(ee)+1)*parseInt(Se)>=parseInt(xe)+1e3&&(X(xe+1e3),G(e=>e+1e3))},[ee,Se]),t.useEffect(()=>{Pa()},[]);let La={"Person Responsible":`/${fe}/data/getSalesOrg`,"Business Area ":`/${fe}/data/getDivision`,"Functional Area":`/${fe}/data/getLaboratoryDesignOffice`};const Ua=e=>{const a=e.target.value;we(a),ya([]),console.log("selected field",e.target.value),a.forEach(async o=>{const r=La[o];Va(r)})},Wa={"Task ID":"taskId",Status:"status",SalesOrganization:"salesOrg",Division:"division",OldMaterialNumber:"oldMaterialNumber","Lab/Office":"labOffice","Transportation Group":"transportationGroup","Batch management":"batchManagement"},_a=()=>{let e="Bank Details";const a=r=>{y(Qn(r.body))},o=r=>{console.log(r)};k(`/${B}/data/getViewFieldDetails?viewName=${e}`,"get",a,o)},Ya=()=>{let e="Address Details";const a=r=>{y(Zn(r.body))},o=r=>{console.log(r)};k(`/${B}/data/getViewFieldDetails?viewName=${e}`,"get",a,o)};t.useEffect(()=>{_a(),Ya()},[]);const Va=e=>{k(e,"get",r=>{console.log("dataaaaaaaa",r.body),Ca([...Ke,r.body])},r=>{console.log(r)})},Ha=()=>{ua("")},Ga=e=>{console.log("hhhfhfhfhf"),V(!0),e||(H(0),ae(10),G(0));let a={bankCountry:"",bankKey:(E==null?void 0:E.number)??"",bankName:"",swiftCode:"",bankNumber:"",region:"",branch:"",createdBy:"",top:1e3,skip:e??0};console.log("payload",a);const o=d=>{var f,I,U;console.log("data",d.body.list);var c=[];for(let K=0;K<((I=(f=d==null?void 0:d.body)==null?void 0:f.list)==null?void 0:I.length);K++){var s=d==null?void 0:d.body.list[K];console.log("hshshsh",s);var l={id:qe(),bankCtryReg:(s==null?void 0:s.BankCountry)!==""?s==null?void 0:s.BankCountry:"Not Available",bankKey:(s==null?void 0:s.BankInternalID)!==""?s==null?void 0:s.BankInternalID:"Not Available",bankName:(s==null?void 0:s.BankName)!==""?s==null?void 0:s.BankName:"Not Available",bankBranch:(s==null?void 0:s.Branch)!==""?s==null?void 0:s.Branch:"Not Available",swiftBic:(s==null?void 0:s.SWIFTCode)!==""?s==null?void 0:s.SWIFTCode:"Not Available",bankNumber:(s==null?void 0:s.BankNumber)!==""?s==null?void 0:s.BankNumber:"Not Available",createdBy:(s==null?void 0:s.CreatedByUser)!==""?s==null?void 0:s.CreatedByUser:"Not Available",changedBy:(s==null?void 0:s.changedBy)!==""?s==null?void 0:s.changedBy:"Not Available",createdOn:_(s.CreationDate).format("DD MMM YYYY")};c.push(l)}console.log("tempobj",l),console.log("tempObH",s),c.sort((K,W)=>_(K.createdOn,"DD MMM YYYY HH:mm")-_(W.createdOn,"DD MMM YYYY HH:mm")),ve(c),V(!1),Te(c.length),Be((U=d==null?void 0:d.body)==null?void 0:U.count)};let r=d=>{console.log(d)};k(`/${B}/data/getBankKeysBasedOnAdditionalParams`,"post",o,r,a)},ce=new Date,q=new Date;q.setDate(q.getDate()-15),t.useState([q,ce]),t.useState([q,ce]);const X=e=>{var d;console.log("called"),V(!0),e||(H(0),ae(10),G(0));let a={bankCountry:((d=i==null?void 0:i.bankCtrRegion)==null?void 0:d.code)??"",bankKey:(E==null?void 0:E.number)??"",bankName:(i==null?void 0:i.bankName)??"",swiftCode:(i==null?void 0:i.swiftBic)??"",bankNumber:(i==null?void 0:i.bankNumber)??"",region:"",branch:(i==null?void 0:i.bankBranch)??"",createdBy:(i==null?void 0:i.createdBy)??"",top:1e3,skip:e??0};const o=c=>{var I,U,K;console.log("data",c.body.list);var s=[];for(let W=0;W<((U=(I=c==null?void 0:c.body)==null?void 0:I.list)==null?void 0:U.length);W++){var l=c==null?void 0:c.body.list[W];console.log("hshshsh",l);var f={id:qe(),bankCtryReg:(l==null?void 0:l.BankCountry)!==""?l==null?void 0:l.BankCountry:"Not Available",bankKey:(l==null?void 0:l.BankInternalID)!==""?l==null?void 0:l.BankInternalID:"Not Available",bankName:(l==null?void 0:l.BankName)!==""?l==null?void 0:l.BankName:"Not Available",bankBranch:(l==null?void 0:l.Branch)!==""?l==null?void 0:l.Branch:"Not Available",swiftBic:(l==null?void 0:l.SWIFTCode)!==""?l==null?void 0:l.SWIFTCode:"Not Available",bankNumber:(l==null?void 0:l.BankNumber)!==""?l==null?void 0:l.BankNumber:"Not Available",createdBy:(l==null?void 0:l.CreatedByUser)!==""?l==null?void 0:l.CreatedByUser:"Not Available",changedBy:(l==null?void 0:l.changedBy)!==""?l==null?void 0:l.changedBy:"Not Available",createdOn:_(l.CreationDate).format("DD MMM YYYY")};s.push(f)}console.log("tempobj",f),console.log("tempObH",l),ve(s),V(!1),Te(s.length),Be((K=c==null?void 0:c.body)==null?void 0:K.count)},r=c=>{console.log(c)};k(`/${B}/data/getBankKeysBasedOnAdditionalParams`,"post",o,r,a)};t.useState([]),t.useState([]),t.useState(null),t.useState(null);const[Ja,Te]=t.useState(0);t.useState(!1),t.useState(!1),t.useState(!1),t.useState(!1),t.useState(!1),t.useState(!1),t.useState(""),t.useState("");const[qa,ze]=t.useState(!1),[Xa,x]=t.useState(""),[Qa,v]=t.useState(),m=()=>{ze(!0)},Re=()=>{ze(!1)};t.useState(null),t.useState(null),t.useState(null);const Za=()=>{y(qn({module:"BankKey"}))},Oa=e=>{const a=e.map(f=>A.find(I=>I.id===f));var o=a.map(f=>f.company),r=new Set(o),d=a.map(f=>f.vendor),c=new Set(d),s=a.map(f=>f.paymentTerm),l=new Set(s);a.length>0?r.size===1?c.size===1?l.size!==1?($(!0),x("Error"),v("Invoice cannot be generated for vendors with different payment terms"),S("danger"),m()):$(!1):($(!0),x("Error"),v("Invoice cannot be generated for multiple suppliers"),S("danger"),m()):($(!0),x("Error"),v("Invoice cannot be generated for multiple companies"),S("danger"),m()):$(!0),ha(e),fa(a)};function ja(){X()}t.useState([]),t.useState([]);const[ct,en]=t.useState(!1);t.useState(null),t.useState(null),t.useState([]);const an=()=>{en(!1)};t.useState(null),t.useState("");const Fe=[{field:"bankCtryReg",headerName:"Bank Country",editable:!1,flex:1},{field:"bankKey",headerName:"Bank key",editable:!1,flex:1},{field:"bankName",headerName:"Bank Name",editable:!1,flex:1},{field:"bankBranch",headerName:"Bank Branch",editable:!1,flex:1},{field:"swiftBic",headerName:"SWIFT/BIC",editable:!1,flex:1},{field:"bankNumber",headerName:"Bank Number",editable:!1,flex:1},{field:"createdBy",headerName:"Created By",editable:!1,flex:1},{field:"createdOn",headerName:"Created On",editable:!1,flex:1}],nn=J.map(e=>{const a=Wa[e];return a?{field:a,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null),tn=[...Fe,...nn],sn=()=>{Xe(!0)},ln=e=>{F(!0),console.log(e);const a=new FormData;if([...e].forEach(c=>a.append("files",c)),console.log(Ie,"handleMassModeBK"),Ie==="Change")var o=`/${B}/massAction/getAllBankKeyFromExcelForMassChange`;else var o=`/${B}/massAction/getAllBankKeyFromExcel`;k(o,"postformdata",c=>{console.log(c,"example"),F(!1),c.statusCode===200?(b(!1),y(On(c==null?void 0:c.body)),x("Create"),v(`${e.name} has been Uploaded Succesfully`),S("success"),De(!1),ke(!0),sn(),me(!0),F(!1),j("/masterDataCockpit/bankKey/createMultipleBankKey")):(b(!1),x("Create"),ke(!1),v("Creation Failed"),S("danger"),De(!1),me(!0),m(),F(!1)),an()},c=>{console.log(c)},a)};t.useEffect(()=>{X()},[]);let on=t.useRef(null);const rn=async()=>{let e=o=>{const r=URL.createObjectURL(o),d=document.createElement("a");d.href=r,d.setAttribute("download","Bank Key_Mass Create.xls"),document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(r),m(),x("Success"),v("Bank Key_Mass Create.xls has been downloaded successfully"),S("success")},a=o=>{o.message&&(m(),x("Error"),v(`${o.message}`),S("danger"))};k(`/${B}/excel/downloadExcel`,"getblobfile",e,a)},cn=async()=>{var e=ga.map(r=>({bankCtry:(r==null?void 0:r.bankCtryReg)??"",bankKey:(r==null?void 0:r.bankKey)??""}));console.log("downloadPayload",e);let a=r=>{F(!1);const d=URL.createObjectURL(r),c=document.createElement("a");c.href=d,c.setAttribute("download","Bank Key_Mass Change.xls"),document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(d),m(),x("Success"),v("Bank Key_Mass Change.xls has been downloaded successfully"),S("success")},o=r=>{r.message&&(m(),x("Error"),v(`${r.message}`),S("danger"))};k(`/${B}/excel/downloadExcelWithData`,"postandgetblob",a,o,e)},dn=()=>{b(!0),y(Je("Create"))},un=()=>{te(e=>!e)},$e=(e,a)=>{a!==0&&(wa(a),te(!1),a===1?dn():a===2&&rn())},hn=e=>{P.current&&P.current.contains(e.target)||te(!1)},gn=()=>{le(e=>!e)},fn=e=>{L.current&&L.current.contains(e.target)||le(!1)},be=(e,a)=>{a!==0&&(Ka(a),le(!1),a===1?yn():a===2&&cn())},yn=()=>{b(!0),y(Je("Change"))};return n(Gn,{children:ia===!0?n(Bn,{}):u("div",{ref:on,children:[n(Sn,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+999999},open:aa,children:n(xn,{color:"inherit"})}),n(vn,{dialogState:qa,openReusableDialog:m,closeReusableDialog:Re,dialogTitle:Xa,dialogMessage:Qa,handleDialogConfirm:Re,dialogOkText:"OK",dialogSeverity:Qe}),n("div",{style:{...mn,backgroundColor:"#FAFCFF"},children:u(de,{spacing:1,children:[u(h,{container:!0,sx:Dn,children:[u(h,{item:!0,md:5,sx:wn,children:[n(g,{variant:"h3",children:n("strong",{children:"Bank Key"})}),n(g,{variant:"body2",color:"#777",children:"This view displays the list of Bank Keys"})]}),n(h,{item:!0,md:7,sx:{display:"flex"},children:u(h,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[n(Nn,{title:"Search for multiple Bank Key numbers separated by comma",handleSearchAction:()=>Ga(),module:"BankKey",keyName:"number",message:"Search Bank Key ",clearSearchBar:Ha}),n(Ue,{title:"Reload",children:n(Z,{sx:We,children:n(Kn,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:ja})})}),n(Ue,{title:"Export Table",children:n(Z,{sx:We,onClick:Ae.convertJsonToExcel,children:n(ue,{iconName:"IosShare"})})})]})})]}),n(h,{container:!0,sx:pn,children:n(h,{item:!0,md:12,children:u(Mn,{className:"filter-accordian",children:[n(En,{expandIcon:n(In,{}),"aria-controls":"panel1a-content",id:"panel1a-header",sx:{minHeight:"2rem !important",margin:"0px !important"},children:n(g,{sx:{fontWeight:"700"},children:"Search Bank Key"})}),u(An,{sx:{padding:"0.5rem 1rem 0.5rem"},children:[u(h,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:[u(h,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Bank Country"}),n(O,{size:"small",fullWidth:!0,children:n(he,{sx:{height:"31px"},fullWidth:!0,size:"small",value:i==null?void 0:i.bankCtrRegion,onChange:Fa,options:(T==null?void 0:T.CountryReg)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,a)=>n("li",{...e,children:n(g,{style:{fontSize:12},children:`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`})}),renderInput:e=>n(w,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Country"})})})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Bank Name"}),n(w,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:Aa,placeholder:"Enter Bank Name",value:i==null?void 0:i.bankName})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Bank Branch"}),n(w,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:Ta,placeholder:"Enter Bank Branch",value:i==null?void 0:i.bankBranch})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"SWIFT/BIC"}),n(w,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:za,placeholder:"Enter Swift/BIC",value:i==null?void 0:i.swiftBic})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Bank Number"}),n(w,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:Ra,placeholder:"Enter Bank Number",value:i==null?void 0:i.bankNumber})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Created By"}),n(w,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:i==null?void 0:i.createdBy,onChange:$a,placeholder:"Enter Created By"})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Add New Filters"}),n(O,{children:n(Tn,{sx:{font_Small:D,height:"31px",fontSize:"12px",width:"200px"},size:"small",multiple:!0,limitTags:2,value:J,onChange:Ua,renderValue:e=>e.join(", "),MenuProps:{MenuProps:ra},endAdornment:J.length>0&&n(zn,{position:"end",children:n(Z,{size:"small",onClick:()=>we([]),"aria-label":"Clear selections",children:n(Rn,{})})})})}),n(h,{style:{display:"flex",justifyContent:"space-around"}})]})]}),n(h,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:J.map((e,a)=>n(h,{item:!0,children:u(de,{children:[n(g,{sx:{fontSize:"12px"},children:e}),n(he,{sx:D,size:"small",options:Ke??[],getOptionLabel:(o,r)=>{var d,c;return`${(d=o[r])==null?void 0:d.code} - ${(c=o[r])==null?void 0:c.desc}`},placeholder:`Enter ${e}`,value:Ne[e],onChange:(o,r)=>ka({...Ne,[e]:r}),renderInput:o=>n(w,{sx:{fontSize:"12px !important"},...o,size:"small",placeholder:`Enter ${e}`,variant:"outlined"})},e[a])]})}))})]}),n(h,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:u(h,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:[n(C,{variant:"outlined",sx:Fn,onClick:Za,children:"Clear"}),n(C,{variant:"contained",sx:{...$n,...bn},onClick:()=>X(),children:"Search"})]})})]})]})})}),n(h,{item:!0,sx:{position:"relative"},children:n(de,{children:n(Pn,{isLoading:ea,module:"BankKey",width:"100%",title:"List of Bank Keys ("+Ja+")",rows:A,columns:tn,page:ee,pageSize:10,rowCount:na??(A==null?void 0:A.length)??0,onPageChange:ta,onPageSizeChange:sa,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Oa,callback_onRowSingleClick:e=>{const a=e.row.bankKey;j(`/masterDataCockpit/bankKey/displayBankKey/${a}`,{state:e.row})},stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})})}),Ln(Ma,"Bank Key","CreateBK")&&(z==null?void 0:z.role)==="Super User"||(z==null?void 0:z.role)==="Finance"?n(ge,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(Un,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:ca,onChange:e=>{da(e)},children:[n(C,{size:"small",variant:"contained",onClick:Ea,children:"Create Single"}),u(Wn,{open:Ba,onClose:ie,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[u(_n,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(g,{variant:"h6",children:"New Bank Key"}),n(Z,{sx:{width:"max-content"},onClick:ie,children:n(Yn,{})})]}),u(Vn,{sx:{padding:".5rem 1rem"},children:[u(h,{container:!0,children:[u(h,{item:!0,md:5,sx:{width:"100%",marginTop:".5rem",marginRight:"5rem"},children:[u(g,{children:["Bank Country",n("span",{style:{color:"red"},children:"*"})]}),n(O,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:n(he,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:(e,a)=>{Oe(a)},options:(T==null?void 0:T.CountryReg)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,a)=>n("li",{...e,children:n(g,{style:{fontSize:12},children:`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`})}),renderInput:e=>n(w,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT BANK COUNTRY",error:ye})})}),ye&&n(g,{variant:"caption",color:"error",children:"Please Select a Country."})]}),u(h,{item:!0,md:5,sx:{width:"100%",marginTop:".5rem"},children:[u(g,{children:["Bank Key",n("span",{style:{color:"red"},children:"*"})]}),n(O,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:n(w,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",value:Y,onChange:e=>{let o=e.target.value.toUpperCase();Ze(o)},inputProps:{maxLength:15,style:{textTransform:"uppercase"}},placeholder:"Enter Bank Key",required:!0})})]})]}),xa&&n(h,{children:n(g,{style:{color:"red"},children:"Please Enter Mandatory Fields"})}),va&&n(h,{children:n(g,{style:{color:"red"},children:"*The Bank Key with this Country already exist. Please enter different Bank Key or Country"})})]}),u(Hn,{sx:{display:"flex",justifyContent:"end"},children:[n(C,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ie,children:"Cancel"}),n(C,{className:"button_primary--normal",type:"save",onClick:Ia,variant:"contained",children:"Proceed"})]})]}),u(_e,{variant:"contained",ref:P,"aria-label":"split button",children:[n(C,{size:"small",onClick:()=>$e(oe[0],0),sx:{cursor:"default"},children:oe[0]}),n(C,{size:"small","aria-controls":ne?"split-button-menu":void 0,"aria-expanded":ne?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:un,children:n(ue,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),n(Ye,{sx:{zIndex:1},open:ne,anchorEl:P.current,placement:"top-end",children:n(ge,{style:{width:(Pe=P.current)==null?void 0:Pe.clientWidth},children:n(Ve,{onClickAway:hn,children:n(He,{id:"split-button-menu",autoFocusItem:!0,children:oe.slice(1).map((e,a)=>n(Ge,{selected:a===Da-1,onClick:()=>$e(e,a+1),children:e},e))})})})}),u(_e,{variant:"contained",ref:L,"aria-label":"split button",children:[n(C,{size:"small",onClick:()=>be(re[0],0),sx:{cursor:"default"},children:re[0]}),n(C,{size:"small","aria-controls":se?"split-button-menu":void 0,"aria-expanded":se?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:gn,children:n(ue,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),n(Ye,{sx:{zIndex:1},open:se,anchorEl:L.current,placement:"top-end",children:n(ge,{style:{width:(Le=L.current)==null?void 0:Le.clientWidth},children:n(Ve,{onClickAway:fn,children:n(He,{id:"split-button-menu",autoFocusItem:!0,children:re.slice(1).map((e,a)=>n(Ge,{selected:a===Na-1,onClick:()=>be(e,a+1),children:e},e))})})})}),Sa&&n(jn,{artifactId:"",artifactName:"",setOpen:b,handleUpload:ln})]})}):""]})})]})})};export{ht as default};
