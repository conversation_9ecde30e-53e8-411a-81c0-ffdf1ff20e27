import{k as Ka,i as U,jZ as va,H as Ya,l as ja,u as Qa,m3 as la,iX as Xa,l8 as Ja,j4 as za,w as v,hj as qa,hg as Y,y as aa,hP as ta,jF as Ba,lx as Za,ly as Ha,lz as pa,r as Va,kT as k,o as $a,p as ka,jC as Wa,k4 as b,lk as ba,jL as at,hW as F,jM as xa,jN as tt,kU as et,G as oa,jX as lt}from"./index-fdfa25a0.js";const st=()=>{const{customError:g}=Ka(),u=U(M=>M.payload.payloadData),w=U(M=>M.applicationConfig),_=va(Ya.CURRENT_TASK);let P=null;P=typeof _=="string"?JSON.parse(_):_;let T=P==null?void 0:P.ATTRIBUTE_5;const N=ja(),q=Qa(),G=new URLSearchParams(q.search),V=U(M=>M.userManagement.taskData),O=G.get("reqBench"),x=G.get("RequestId");return{getChangeTemplate:(M,S)=>{var z,Z,H;N(la(!0));let $={decisionTableId:null,decisionTableName:"MDG_MAT_CHANGE_TEMPLATE",version:"v6",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_TEMPLATE":(u==null?void 0:u.TemplateName)||(M==null?void 0:M.TemplateName),"MDG_CONDITIONS.MDG_MAT_REGION":(u==null?void 0:u.Region)||(M==null?void 0:M.Region)||((z=Xa)==null?void 0:z.US),"MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":x&&!O?(V==null?void 0:V.ATTRIBUTE_5)||T:(Z=Ja)==null?void 0:Z.REQ_INITIATE,"MDG_CONDITIONS.MDG_MAT_SCENARIO":(u==null?void 0:u.RequestType)||(M==null?void 0:M.RequestType)||((H=za)==null?void 0:H.CHANGE)}],systemFilters:null,systemOrders:null,filterString:null};const X=B=>{var a,l,c,d,t,e,o,i,s,h,I,y,C;if(B.statusCode===((a=aa)==null?void 0:a.STATUS_200)){N(la(!1));let f=(c=(l=B==null?void 0:B.data)==null?void 0:l.result[0])==null?void 0:c.MDG_MAT_CHANGE_TEMPLATE,L=(u==null?void 0:u.TemplateName)||(M==null?void 0:M.TemplateName)||"";const D=(t=(d=f==null?void 0:f.filter(n=>n.MDG_MAT_VIEW_NAME!=="Header"))==null?void 0:d.sort((n,r)=>n.MDG_MAT_FIELD_SEQUENCE-r.MDG_MAT_FIELD_SEQUENCE))==null?void 0:t.map(n=>({fieldName:n.MDG_MAT_UI_FIELD_NAME,viewName:n.MDG_MAT_VIEW_NAME,sequenceNo:n.MDG_MAT_FIELD_SEQUENCE,fieldType:n.MDG_MAT_FIELD_TYPE,maxLength:n.MDG_MAT_MAX_LENGTH,value:n.MDG_MAT_DEFAULT_VALUE,visibility:n.MDG_MAT_VISIBILITY,jsonName:n.MDG_MAT_JSON_FIELD_NAME,templateVisibility:n.MDG_MAT_TEMPLATE_SELECTIVITY}));let m=[];const K=D==null?void 0:D.reduce((n,r)=>{r.fieldName!=="Material"&&r.fieldName!=="Plant"&&r.fieldName!=="Warehouse"&&r.fieldName!=="Sales Org"&&r.fieldName!=="Distribution Channel"&&r.fieldName!=="MRP Controller"&&m.push({code:r.fieldName,desc:""});const W=r.viewName;return n[W]||(n[W]=[]),n[W].push(r),n},{}),p=(o=(e=Object==null?void 0:Object.keys(K))==null?void 0:e.sort())==null?void 0:o.reduce((n,r)=>(n[r]=K[r],n),{}),j=[];D==null||D.forEach(n=>{(n==null?void 0:n.visibility)==="Mandatory"&&j.push({jsonName:n==null?void 0:n.jsonName,fieldName:n==null?void 0:n.fieldName})});const A={[L]:D,"Config Data":p,"Field Selectivity":(i=D[0])==null?void 0:i.templateVisibility,FieldNames:m,"Mandatory Fields":[{jsonName:"Material",fieldName:"Material"},...j]};if(N(ta({keyName:"FieldName",data:A==null?void 0:A.FieldNames})),N(Ba(A)),(Object==null?void 0:Object.keys(S).length)>0){const n=Za(A==null?void 0:A["Config Data"],(h=(s=S==null?void 0:S.Torequestheaderdata)==null?void 0:s.FieldName)==null?void 0:h.split("$^$"),["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);N(Ba({...A,"Config Data":n}));const r=Ha(A==null?void 0:A[L||((I=S==null?void 0:S.Torequestheaderdata)==null?void 0:I.TemplateName)],(C=(y=S==null?void 0:S.Torequestheaderdata)==null?void 0:y.FieldName)==null?void 0:C.split("$^$"));N(pa([...r]))}}else N(la(!1)),g("Failed to fetch data")},J=B=>{g(B)};w.environment==="localhost"?v(`/${qa}${Y.INVOKE_RULES.LOCAL}`,"post",X,J,$):v(`/${qa}${Y.INVOKE_RULES.PROD}`,"post",X,J,$)}}},dt=()=>{const g=ja(),u=U(a=>a.userManagement.taskData),w=U(a=>a.paginationData),_=U(a=>a.payload.changeFieldRows),P=U(a=>a.payload.changeFieldRowsDisplay),T=U(a=>a.payload.dynamicKeyValues),N=U(a=>a.payload.selectedRows),q=U(a=>a.payload.whseList),G=U(a=>a.payload.plantList),V=U(a=>a.payload.matNoList),{customError:O}=Ka(),x=a=>{var o,i,s,h,I,y,C,f,L,D,m,K,p,j,A,n,r,W,sa,da,ca,na,ia,ua,ha,Ta,ga,Na,Ma,ra,Ia,Ca,Ea,Da,ya,fa,_a,Sa,La,ma,Aa,Pa,Oa,Fa,Ua,Ga;const l=((o=a[0])==null?void 0:o.TemplateName)===((i=k)==null?void 0:i.LOGISTIC)?Q(a):((s=a[0])==null?void 0:s.TemplateName)===((h=k)==null?void 0:h.ITEM_CAT)?M(a):((I=a[0])==null?void 0:I.TemplateName)===((y=k)==null?void 0:y.MRP)?S(a):((C=a[0])==null?void 0:C.TemplateName)===((f=k)==null?void 0:f.UPD_DESC)?J(a):((L=a[0])==null?void 0:L.TemplateName)===((D=k)==null?void 0:D.WARE_VIEW_2)?z(a):((m=a[0])==null?void 0:m.TemplateName)===((K=k)==null?void 0:K.CHG_STAT)?$(a):((p=a[0])==null?void 0:p.TemplateName)===((j=k)==null?void 0:j.SET_DNU)?X(a):[];if(Array.isArray(l))g($a([..._,...l])),g(ka({...P,[w==null?void 0:w.page]:l}));else if(typeof l=="object"&&l!==null){const E={..._};(A=Object==null?void 0:Object.keys(l))==null||A.forEach(R=>{E[R]=[...E[R]||[],...l[R]]}),g($a(E)),g(ka({...P,[w==null?void 0:w.page]:l}))}let c;if(Array.isArray(l))c=l.map(E=>E==null?void 0:E.id),g(Wa([...N,...c]));else if(typeof l=="object"&&l!==null){c=Object.keys(l).reduce((R,Ra)=>{var wa;return R[Ra]=((wa=l[Ra])==null?void 0:wa.map(ea=>ea==null?void 0:ea.id))||[],R},{});const E={...N};(n=Object==null?void 0:Object.keys(c))==null||n.forEach(R=>{E[R]=[...E[R]||[],...c[R]]}),g(Wa(E))}g(b({keyName:"requestHeaderData",data:(r=a[0])==null?void 0:r.Torequestheaderdata})),g(b({keyName:"childRequestHeaderData",data:(W=a[0])==null?void 0:W.Tochildrequestheaderdata})),g(b({keyName:"changeLogData",data:(sa=a[0])==null?void 0:sa.changeLogData})),g(b({keyName:"templateName",data:(da=a[0])==null?void 0:da.TemplateName}));const d={};a==null||a.forEach(E=>{d[E==null?void 0:E.Material]=E==null?void 0:E.Tomaterialerrordata}),g(b({keyName:"errorData",data:{...(T==null?void 0:T.errorData)||{},...d}}));const t={};t.IntermediateTaskCount=(ca=a[0])==null?void 0:ca.IntermediateTaskCount,t.TotalIntermediateTasks=(na=a[0])==null?void 0:na.TotalIntermediateTasks,t.MassEditId=(ia=a[0])==null?void 0:ia.MassEditId,t.MassChildEditId=(ua=a[0])==null?void 0:ua.MassChildEditId,t.Comments=((ha=a[0])==null?void 0:ha.Comments)||"",t.TaskId=u==null?void 0:u.taskId,t.TaskName=u==null?void 0:u.taskDesc,t.CreationTime=u!=null&&u.createdOn?ba(u==null?void 0:u.createdOn):null,t.DueDate=u!=null&&u.criticalDeadline?ba(u==null?void 0:u.criticalDeadline):null,g(b({keyName:"otherPayloadData",data:t}));const e={ReqCreatedBy:(ga=(Ta=a[0])==null?void 0:Ta.Torequestheaderdata)==null?void 0:ga.ReqCreatedBy,RequestStatus:(Ma=(Na=a[0])==null?void 0:Na.Torequestheaderdata)==null?void 0:Ma.RequestStatus,Region:(Ia=(ra=a[0])==null?void 0:ra.Torequestheaderdata)==null?void 0:Ia.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(Ea=(Ca=a[0])==null?void 0:Ca.Torequestheaderdata)==null?void 0:Ea.RequestType,RequestDesc:(ya=(Da=a[0])==null?void 0:Da.Torequestheaderdata)==null?void 0:ya.RequestDesc,RequestPriority:(_a=(fa=a[0])==null?void 0:fa.Torequestheaderdata)==null?void 0:_a.RequestPriority,LeadingCat:(La=(Sa=a[0])==null?void 0:Sa.Torequestheaderdata)==null?void 0:La.LeadingCat,RequestId:(Aa=(ma=a[0])==null?void 0:ma.Torequestheaderdata)==null?void 0:Aa.RequestId,TemplateName:(Oa=(Pa=a[0])==null?void 0:Pa.Torequestheaderdata)==null?void 0:Oa.TemplateName,FieldName:(Ga=(Ua=(Fa=a[0])==null?void 0:Fa.Torequestheaderdata)==null?void 0:Ua.FieldName)==null?void 0:Ga.split("$^$")};g(at({data:e}))},Q=a=>{const l=[];let c=1;const d=new Set;return a.forEach(t=>{t.Touomdata.forEach(e=>{var i,s;d.add(e.Material);const o={...e,Material:t==null?void 0:t.Material,MaterialId:t==null?void 0:t.MaterialId,ClientId:(i=t==null?void 0:t.Toclientdata)==null?void 0:i.ClientId,id:F(),slNo:c++,MatlType:(t==null?void 0:t.MatlType)||"",ChangeLogId:((s=t==null?void 0:t.changeLogData)==null?void 0:s.ChangeLogId)??null};l.push(o)})}),g(xa([...V,...d])),l},M=a=>{const l=[];let c=1;return a.forEach(d=>{d.Tosalesdata.forEach(t=>{var o,i;const e={...t,Material:d==null?void 0:d.Material,MaterialId:d==null?void 0:d.MaterialId,ClientId:(o=d==null?void 0:d.Toclientdata)==null?void 0:o.ClientId,id:F(),slNo:c++,MatlType:(d==null?void 0:d.MatlType)||"",ChangeLogId:((i=d==null?void 0:d.changeLogData)==null?void 0:i.ChangeLogId)??null};l.push(e)})}),l},S=a=>{const l={"Basic Data":[],"Plant Data":[]};let c=1,d=1;const t=new Set;return a.forEach(e=>{var y;const{Toplantdata:o,Toclientdata:i,Material:s,MaterialId:h,MatlType:I}=e;l["Basic Data"].push({...i,id:F(),slNo:c++,type:"Basic Data",Material:s,MaterialId:h,Function:"UPD",MatlType:I,ChangeLogId:((y=e==null?void 0:e.changeLogData)==null?void 0:y.ChangeLogId)??null}),o==null||o.forEach(C=>{var f;t.add(C==null?void 0:C.Plant),l["Plant Data"].push({...C,id:F(),Material:s,slNo:d++,type:"Plant Data",MaterialId:h,Function:"UPD",ChangeLogId:((f=e==null?void 0:e.changeLogData)==null?void 0:f.ChangeLogId)??null})})}),g(tt([...G,...t])),l},$=a=>{const l={"Basic Data":[],"Plant Data":[],"Sales Data":[]};let c=1,d=1,t=1;return a.forEach(e=>{var C;const{Toplantdata:o,Toclientdata:i,Tosalesdata:s,Material:h,MaterialId:I,MatlType:y}=e;l["Basic Data"].push({...i,id:F(),slNo:c++,type:"Basic Data",Material:h,MaterialId:I,Function:"UPD",MatlType:y,ChangeLogId:((C=e==null?void 0:e.changeLogData)==null?void 0:C.ChangeLogId)??null}),o==null||o.forEach(f=>{var L;l["Plant Data"].push({...f,id:F(),Material:h,slNo:d++,type:"Plant Data",MaterialId:I,Function:"UPD",ChangeLogId:((L=e==null?void 0:e.changeLogData)==null?void 0:L.ChangeLogId)??null})}),s==null||s.forEach(f=>{var L;l["Sales Data"].push({...f,id:F(),Material:h,slNo:t++,type:"Sales Data",MaterialId:I,Function:"UPD",ChangeLogId:((L=e==null?void 0:e.changeLogData)==null?void 0:L.ChangeLogId)??null})})}),l},X=a=>{const l={"Basic Data":[],"Plant Data":[],"Sales Data":[],Description:[]};let c=1,d=1,t=1,e=1;return a.forEach(o=>{var L;const{Toplantdata:i,Toclientdata:s,Tosalesdata:h,Tomaterialdescription:I,Material:y,MaterialId:C,MatlType:f}=o;l["Basic Data"].push({...s,id:F(),slNo:c++,type:"Basic Data",Material:y,MaterialId:C,Function:"UPD",MatlType:f,ChangeLogId:((L=o==null?void 0:o.changeLogData)==null?void 0:L.ChangeLogId)??null}),i==null||i.forEach(D=>{var m;l["Plant Data"].push({...D,id:F(),Material:y,slNo:d++,type:"Plant Data",MaterialId:C,Function:"UPD",ChangeLogId:((m=o==null?void 0:o.changeLogData)==null?void 0:m.ChangeLogId)??null})}),h==null||h.forEach(D=>{var m;l["Sales Data"].push({...D,id:F(),Material:y,slNo:t++,type:"Sales Data",MaterialId:C,Function:"UPD",ChangeLogId:((m=o==null?void 0:o.changeLogData)==null?void 0:m.ChangeLogId)??null})}),I==null||I.forEach(D=>{var m;l.Description.push({...D,id:F(),Material:y,slNo:e++,type:"Description",MaterialId:C,Function:"UPD",ChangeLogId:((m=o==null?void 0:o.changeLogData)==null?void 0:m.ChangeLogId)??null})})}),l},J=a=>{const l=[];let c=1;const d=new Set;return a.forEach(t=>{t.Tomaterialdescription.forEach(e=>{var i,s;d.add(e.Material);const o={...e,Material:t==null?void 0:t.Material,MaterialId:t==null?void 0:t.MaterialId,ClientId:(i=t==null?void 0:t.Toclientdata)==null?void 0:i.ClientId,id:F(),slNo:c++,MatlType:(t==null?void 0:t.MatlType)||"",ChangeLogId:((s=t==null?void 0:t.changeLogData)==null?void 0:s.ChangeLogId)??null};l.push(o)})}),g(xa([...V,...d])),l},z=a=>{const l=[],c=new Set;let d=1;a.forEach(e=>{e.Towarehousedata.forEach(o=>{var s,h;c.add(o.WhseNo);const i={...o,Material:e==null?void 0:e.Material,MaterialId:e==null?void 0:e.MaterialId,ClientId:(s=e==null?void 0:e.Toclientdata)==null?void 0:s.ClientId,id:F(),slNo:d++,MatlType:(e==null?void 0:e.MatlType)||"",ChangeLogId:((h=e==null?void 0:e.changeLogData)==null?void 0:h.ChangeLogId)??null};l.push(i)})});const t=[...c];return g(et(t)),l};Va.useEffect(()=>{(async()=>{if((q==null?void 0:q.length)>0){const l=await Z(q);g(ta({keyName:"Unittype1",data:l}))}})()},[q]);const Z=async a=>{const l={};for(const c of a){let d={whseNo:c};try{const t=await new Promise(e=>{var o,i;v(`/${oa}${(i=(o=Y)==null?void 0:o.DEPENDENT_LOOKUPS)==null?void 0:i.UNITTYPE}`,"post",s=>{var h,I;s.statusCode===((h=aa)==null?void 0:h.STATUS_200)?e(s==null?void 0:s.body):(O((I=lt)==null?void 0:I.ERROR_MSG),e([]))},s=>{O(s),e([])},d)});l[c]=t}catch(t){O(t),l[c]=[]}}return l};Va.useEffect(()=>{(async()=>{if((G==null?void 0:G.length)>0){const l=await H(G);g(ta({keyName:"Spproctype",data:l}));const c=await B(G);g(ta({keyName:"MrpCtrler",data:c}))}})()},[G]);const H=async a=>{const l={};for(const c of a){let d={plant:c};try{const t=await new Promise(e=>{var o,i;v(`/${oa}${(i=(o=Y)==null?void 0:o.DATA)==null?void 0:i.GET_SPPROC_TYPE}`,"post",s=>{var h;s.statusCode===((h=aa)==null?void 0:h.STATUS_200)?e(s==null?void 0:s.body):(O("Failed to fetch data"),e([]))},s=>{O(s),e([])},d)});l[c]=t}catch(t){O(t),l[c]=[]}}return l},B=async a=>{const l={};for(const c of a){let d={plant:c};try{const t=await new Promise(e=>{var o,i;v(`/${oa}${(i=(o=Y)==null?void 0:o.DATA)==null?void 0:i.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",s=>{var h;s.statusCode===((h=aa)==null?void 0:h.STATUS_200)?e(s==null?void 0:s.body):(O("Failed to fetch data"),e([]))},s=>{O(s),e([])},d)});l[c]=t}catch(t){O(t),l[c]=[]}}return l};return{fetchDisplayDataRows:x}},ct=()=>{const g=U(_=>_.payload.unselectedRows||[]);return{createFCRows:_=>_==null?void 0:_.flatMap(T=>T.Toplantdata.map(N=>({...N,FinanceCostingId:T.FinanceCostingId,MassSchedulingId:T.MassSchedulingId,RequestType:T.RequestType,RequestId:T.RequestId,Requester:T.Requester,CreatedOn:T.CreatedOn,Material:T.Material,MatlType:T.MatlType,IntlPoPrice:T.IntlPoPrice,PryVendor:T.PryVendor,FlagForBOM:T.FlagForBOM,VolInEA:T.VolInEA,VolInCA:T.VolInCA,VolInCAR:T.VolInCAR,NoOfUnitForCA:T.NoOfUnitForCA,NoOfUnitForCT:T.NoOfUnitForCT,Torequestheaderdata:T.Torequestheaderdata,Tomaterialerrordata:T.Tomaterialerrordata,id:N==null?void 0:N.FinancePlantId}))),createFCPayload:()=>{const _=g,P=new Map;return _.forEach(N=>{const{FinancePlantId:q,Material:G,IsDeleted:V,Plant:O,FPurStatus:x,FStdPrice:Q,id:M,...S}=N,$={FinancePlantId:q,Material:G,IsDeleted:!0,Plant:O,FPurStatus:x,FStdPrice:Q};P.has(N.FinanceCostingId)?P.get(N.FinanceCostingId).Toplantdata.push($):P.set(N.FinanceCostingId,{...S,Material:N==null?void 0:N.Material,Toplantdata:[$]})}),Array.from(P.values())}}};export{dt as a,ct as b,st as u};
