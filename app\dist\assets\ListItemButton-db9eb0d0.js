import{fF as F,fS as G,nB as j,at as u,nC as r,nD as d,r as p,nE as N,as as V,nF as y,fQ as E,fJ as P,aj as I,nG as $,fG as S,nH as w}from"./index-fdfa25a0.js";const D=["alignItems","autoFocus","component","children","dense","disableGutters","divider","focusVisibleClassName","selected","className"],_=(t,e)=>{const{ownerState:s}=t;return[e.root,s.dense&&e.dense,s.alignItems==="flex-start"&&e.alignItemsFlexStart,s.divider&&e.divider,!s.disableGutters&&e.gutters]},M=t=>{const{alignItems:e,classes:s,dense:a,disabled:o,disableGutters:l,divider:c,selected:f}=t,n=S({root:["root",a&&"dense",!l&&"gutters",c&&"divider",o&&"disabled",e==="flex-start"&&"alignItemsFlexStart",f&&"selected"]},w,s);return u({},s,n)},T=F(G,{shouldForwardProp:t=>j(t)||t==="classes",name:"MuiListItemButton",slot:"Root",overridesResolver:_})(({theme:t,ownerState:e})=>u({display:"flex",flexGrow:1,justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minWidth:0,boxSizing:"border-box",textAlign:"left",paddingTop:8,paddingBottom:8,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${r.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:d(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${r.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:d(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${r.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:d(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:d(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${r.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${r.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity}},e.divider&&{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"},e.alignItems==="flex-start"&&{alignItems:"flex-start"},!e.disableGutters&&{paddingLeft:16,paddingRight:16},e.dense&&{paddingTop:4,paddingBottom:4})),U=p.forwardRef(function(e,s){const a=N({props:e,name:"MuiListItemButton"}),{alignItems:o="center",autoFocus:l=!1,component:c="div",children:f,dense:v=!1,disableGutters:n=!1,divider:B=!1,focusVisibleClassName:O,selected:L=!1,className:k}=a,i=V(a,D),x=p.useContext(y),C=p.useMemo(()=>({dense:v||x.dense||!1,alignItems:o,disableGutters:n}),[o,x.dense,v,n]),b=p.useRef(null);E(()=>{l&&b.current&&b.current.focus()},[l]);const m=u({},a,{alignItems:o,dense:C.dense,disableGutters:n,divider:B,selected:L}),g=M(m),R=P(b,s);return I.jsx(y.Provider,{value:C,children:I.jsx(T,u({ref:R,href:i.href||i.to,component:(i.href||i.to)&&c==="div"?"button":c,focusVisibleClassName:$(g.focusVisible,O),ownerState:m,className:$(g.root,k)},i,{classes:g,children:f}))})}),z=U;export{z as L};
