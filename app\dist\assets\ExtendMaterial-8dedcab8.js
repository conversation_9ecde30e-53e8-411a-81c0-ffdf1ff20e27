import{r as g,i as A,l as E,is as M,a,g_ as V,j as h,T as u,hy as b,a3 as D,eM as j,hA as F,F as p,$}from"./index-fdfa25a0.js";function w(n,o){return Array.isArray(o)&&o.find(f=>f.code===n)||""}const P=({label:n,value:o,units:c,onSave:f,isEditMode:z,isExtendMode:k,options:O=[],type:r})=>{var m;const[t,d]=g.useState(o),[R,x]=g.useState(!1),i=A(e=>e.AllDropDown.dropDown),S=E(),v=w(t,i);console.log("dropdownData",t),console.log("value e",o),console.log("label",n),console.log("units",c),console.log("transformedValue",v);const C=A(e=>e.edit.payload);console.log("editField",C),console.log("fieldData",{label:n,value:t,units:c,type:r});let s=n.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");return g.useEffect(()=>{d(o)},[o]),g.useEffect(()=>{console.log("lkey",s),console.log("data",o),S(M({keyname:s.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:o||""}))},[]),console.log("editedValue[key] ",i[s]),console.log("editedValue[key] ",t),a($,{item:!0,children:a(V,{children:k?h(p,{children:[a(u,{variant:"body2",color:"#777",children:n}),r==="Drop Down"?a(b,{options:i[s]??[],value:t&&((m=i[s])==null?void 0:m.filter(e=>e.code===t))||"",onChange:(e,l)=>{console.log("newValue",l),d(l.code),x(!0),console.log("keys",s)},getOptionLabel:e=>{var l,y;return console.log("optionoptionoption",e),e===""?"":`${e&&((l=e[0])==null?void 0:l.code)} - ${e&&((y=e[0])==null?void 0:y.desc)}`},renderOption:(e,l)=>(console.log("option vakue",l),a("li",{...e,children:a(u,{style:{fontSize:12},children:`${l==null?void 0:l.code} - ${l==null?void 0:l.desc}`})})),renderInput:e=>a(D,{...e,variant:"outlined",size:"small",label:null})}):r==="Input"?a(D,{variant:"outlined",size:"small",value:t,onChange:(e,l)=>{d(l)}}):r==="Calendar"?a(j,{size:"small",placeholder:"Select Date Range",format:"dd MMM yyyy",placement:"auto",sx:{height:"2.32rem !important"}}):r==="Radio Button"?a(F,{sx:{borderRadius:"0 !important"},checked:t,onChange:(e,l)=>{d(l)}}):""]}):a(p,{children:h(p,{children:[a(u,{variant:"body2",color:"#777",children:n}),h(u,{variant:"body2",fontWeight:"bold",children:[t," ",c]})]})})})})};export{P as default};
