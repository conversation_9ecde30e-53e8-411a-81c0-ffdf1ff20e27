import{ah as Br,ai as jr,aj as Mr,r as c,l as zr,b as Or,u as qr,i as y,hZ as _r,ig as $r,w as S,hL as z,a as n,j as a,h_ as Q,T as d,B as N,gZ as G,$ as i,hC as un,h$ as gn,i0 as xn,i1 as fn,i2 as Cn,i3 as bn,i4 as yn,i5 as Sn,a0 as de,g_ as D,hN as vn,i6 as Lr,F as I,ht as Wr,J as Vr,i7 as Pn,fV as kn,h1 as Tn,W as he,hI as An,fX as Nn,a3 as In,fH as Fn,a1 as x,gQ as Hr,ho as Ur,h8 as Rr,hp as Yr,hv as En,i8 as Kr,hD as ve,gU as R,ib as me,ic as Jr,id as Xr,ie as Zr,P as Pe,hE as ke,gW as F,ih as wn,ii as Qr,ij as Gr,ix as pe,iy as Dr,hn as eo,hP as Te,ik as Ae}from"./index-fdfa25a0.js";import{E as ee}from"./EditableFieldForProfitCenter-a7957a65.js";import{C as no}from"./CompCodeProfitCenter-a8989840.js";import{T as Bn}from"./Timeline-bb89efb4.js";var Ne={},ro=jr;Object.defineProperty(Ne,"__esModule",{value:!0});var jn=Ne.default=void 0,oo=ro(Br()),so=Mr;jn=Ne.default=(0,oo.default)((0,so.jsx)("path",{d:"M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2zm-2 0-8 5-8-5zm0 12H4V8l8 5 8-5z"}),"MarkunreadOutlined");const uo=()=>{var Xe,Ze,Qe,Ge,De,en,nn,rn,on,sn,tn,ln,an,cn,dn,hn;const[C,Ie]=c.useState(!1);c.useState(0);const[Y,Mn]=c.useState(!0);c.useState({});const[P,zn]=c.useState([]),[On,qn]=c.useState([]),[b,_n]=c.useState();c.useState([]);const[f,ne]=c.useState(0),[Fe,k]=c.useState(""),[$n,T]=c.useState(!1),[to,E]=c.useState(!0),[Ln,w]=c.useState(!1),[lo,B]=c.useState(!1),[Wn,ue]=c.useState(!1),[Vn,j]=c.useState(!1),[Hn,re]=c.useState(!1),[Un,ge]=c.useState(!1),[Rn,v]=c.useState(!1),[Yn,Ee]=c.useState(!1),[Kn,Jn]=c.useState(!1),[Xn,we]=c.useState(!1),[oe,Zn]=c.useState(""),[Qn,Gn]=c.useState(!1),[Dn,er]=c.useState(""),[se,Be]=c.useState(""),[K,nr]=c.useState([]),[W,io]=c.useState([]),[V,ao]=c.useState([]),[je,te]=c.useState(!0),[Me,ze]=c.useState(!0),[rr,or]=c.useState([]),[Oe,sr]=c.useState([]),[tr,qe]=c.useState(!1),M=zr(),_e=Or(),lr=qr();y(r=>r.appSettings);let xe=y(r=>{var l;return(l=r.userManagement.entitiesAndActivities)==null?void 0:l["Profit Center"]}),m=y(r=>r.userManagement.userData),H=y(r=>{var l;return(l=r==null?void 0:r.initialData)==null?void 0:l.IWMMyTask}),s=lr.state;console.log("profitCenterRowData",s),console.log("testrunStatus",Me),y(r=>r.costCenter.singleCCPayload);const fe=y(r=>r.profitCenter.profitCenterViewData),ir=y(r=>r.profitCenter.profitCenterCompCodes),e=y(r=>r.edit.payload);let U=y(r=>r.userManagement.taskData),O=y(r=>r.edit.payload);console.log(O,"singlePCPayloadAfterChange");let $e=y(r=>r.profitCenter.requiredFields);console.log($e,"required_field_for_data");var le={TaskId:U!=null&&U.taskId?U==null?void 0:U.taskId:"",ProfitCenterID:b!=null&&b.profitCenterId?b==null?void 0:b.profitCenterId:"",RequestID:"",Action:"I",TaskStatus:"",ReqCreatedBy:m==null?void 0:m.user_id,ReqCreatedOn:"",RequestStatus:"",CreationId:s!=null&&s.requestId?s==null?void 0:s.requestId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",MassRequestStatus:"",Remarks:se||"",PrctrName:e!=null&&e.Name?e==null?void 0:e.Name:"",LongText:e!=null&&e.LongText?e==null?void 0:e.LongText:"",InChargeUser:e!=null&&e.UserResponsible?e==null?void 0:e.UserResponsible:"",InCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",PrctrHierGrp:e!=null&&e.ProfitCtrGroup?e==null?void 0:e.ProfitCtrGroup:"",Segment:e!=null&&e.Segment?e==null?void 0:e.Segment:"",LockInd:(e==null?void 0:e.Lockindicator)==="true"?"X":"",Template:e!=null&&e.FormPlanningTemp?e==null?void 0:e.FormPlanningTemp:"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",Name2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",Name3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",Name4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",Street:e!=null&&e.Street?e==null?void 0:e.Street:"",City:e!=null&&e.City?e==null?void 0:e.City:"",District:e!=null&&e.District?e==null?void 0:e.District:"",Country:e!=null&&e.CountryReg?e==null?void 0:e.CountryReg:"",Taxjurcode:e!=null&&e.TaxJur?e==null?void 0:e.TaxJur:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",PobxPcd:e!=null&&e.POBoxPCode?e==null?void 0:e.POBoxPCode:"",Region:e!=null&&e.Region?e==null?void 0:e.Region:"",Langu:e!=null&&e.Language?e==null?void 0:e.Language:"EN",Telephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",Telephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",Telebox:e!=null&&e.Telebox?e==null?void 0:e.Telebox:"",Telex:e!=null&&e.Telex?e==null?void 0:e.Telex:"",FaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",Teletex:e!=null&&e.Teletex?e==null?void 0:e.Teletex:"",Printer:e!=null&&e.Printername?e==null?void 0:e.Printername:"",DataLine:e!=null&&e.Dataline?e==null?void 0:e.Dataline:"",ProfitCenter:`P${(Ze=(Xe=s==null?void 0:s.companyCodeCopy)==null?void 0:Xe.newCompanyCodeCopy)==null?void 0:Ze.code}${(Qe=s==null?void 0:s.profitCenterName)==null?void 0:Qe.newProfitCenterName}`?`P${(De=(Ge=s==null?void 0:s.companyCodeCopy)==null?void 0:Ge.newCompanyCodeCopy)==null?void 0:De.code}${(en=s==null?void 0:s.profitCenterName)==null?void 0:en.newProfitCenterName}`:"",ControllingArea:(rn=(nn=s==null?void 0:s.controllingArea)==null?void 0:nn.newControllingArea)!=null&&rn.code?(sn=(on=s==null?void 0:s.controllingArea)==null?void 0:on.newControllingArea)==null?void 0:sn.code:"",ValidfromDate:e!=null&&e.AnalysisPeriodFrom?"/Date("+(e==null?void 0:e.AnalysisPeriodFrom)+")/":"",ValidtoDate:e!=null&&e.AnalysisPeriodTo?"/Date("+(e==null?void 0:e.AnalysisPeriodTo)+")/":"",Testrun:Me,Countryiso:"",LanguIso:"",Logsystem:"",ToCompanycode:K==null?void 0:K.map(r=>({CompCodeID:"",CompanyName:r!=null&&r.companyName?r==null?void 0:r.companyName:"",AssignToPrctr:"X",CompCode:r!=null&&r.companyCodes?r==null?void 0:r.companyCodes:""}))};const[Le,ar]=c.useState(0),cr=(r,l)=>{const o=h=>{M(Te({keyName:r,data:h.body})),ar(p=>p+1)},t=h=>{console.log(h)};S(`/${z}/data/${l}`,"get",o,t)},dr=()=>{var r,l;(l=(r=wn)==null?void 0:r.profitCenter)==null||l.map(o=>{cr(o==null?void 0:o.keyName,o==null?void 0:o.endPoint)})},hr=()=>{var r,l;Le==((l=(r=wn)==null?void 0:r.profitCenter)==null?void 0:l.length)?j(!1):j(!0)};c.useEffect(()=>{hr()},[Le]),c.useEffect(()=>{Zn(_r("PC"))},[]),c.useEffect(()=>{dr(),ur()},[]),c.useEffect(()=>{Cr(),fe.length!==0&&mr()},[fe]);const We=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:r=>a(I,{children:[n(Qr,{index:r.row.id,name:r.row.docName}),n(Gr,{index:r.row.id,name:r.row.docName})]})}],mr=()=>{let r=P[f];console.log("activeTabName",r,P);let l=Object.entries(fe);console.log("viewDataArray",l);const o={};l.map(t=>{console.log("bottle",t[1]);let h=Object.entries(t[1]);return console.log("notebook",h),h.forEach(p=>{p[1].forEach(u=>{o[u.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=u.value})}),t}),console.log("toSetArray",o),M($r(o))},pr=r=>{console.log("compcode",r);const l=t=>{console.log("value",t),M(Te({keyName:"Region",data:t.body}))},o=t=>{console.log(t,"error in dojax")};S(`/${z}/data/getRegionBasedOnCountry?country=${r}`,"get",l,o)},ur=()=>{var t,h,p,u,g,$,J,X;j(!0);var r={id:"",profitCenter:(h=(t=s==null?void 0:s.profitCenter)==null?void 0:t.newProfitCenter)!=null&&h.code?(u=(p=s==null?void 0:s.profitCenter)==null?void 0:p.newProfitCenter)==null?void 0:u.code:"",controllingArea:($=(g=s==null?void 0:s.controllingArea)==null?void 0:g.newControllingArea)!=null&&$.code?(X=(J=s==null?void 0:s.controllingArea)==null?void 0:J.newControllingArea)==null?void 0:X.code:"",reqStatus:"Approved",screenName:"Create"};console.log("displayPayload",r);const l=L=>{var pn;j(!1);const Z=L.body.viewData,Fr=L.body;M(Dr(Z));const mn=Object.keys(Z);zn(mn);const Er=mn.map(A=>({category:A,data:Z[A],setIsEditMode:Ie}));qn(Er),fr(Z),_n(Fr);let Se=(pn=L==null?void 0:L.body)==null?void 0:pn.viewData["Comp Codes"]["Company Code Assignment for Profit Center"];nr(_.zip(Se[0].value,Se[1].value,Se[2].value).map((A,wr)=>({id:wr,companyCodes:A[0].split("$")[0],companyName:A[1],assigned:A[2]}))),pr(Z.Address["Address Data"].find(A=>(A==null?void 0:A.fieldName)==="Country/Reg.").value)},o=L=>{console.log(L)};S(`/${z}/data/displayProfitCenter`,"post",l,o,r)},gr=y(r=>r.profitCenter.requiredFields),xr=()=>{qe(!1)},Ve=()=>eo(O,$e,sr);console.log(gr,"requiredFields"),console.log(rr,"error_field_arr");const fr=r=>{let l=[];for(const o in r){if(r.hasOwnProperty(o)){for(const t in r[o])if(r[o].hasOwnProperty(t)){const h=r[o][t];for(const p of h)if(p.visibility==="0"||p.visibility==="Required"){console.log(p.fieldName,"field.fieldName");let u=p.fieldName.replace(/\s/g,"");l.push(u)}}}or(t=>({...t,error_field_arr:l}))}};console.log("dispcomp",K);const ie=()=>{ue(!0)},Cr=()=>{var o,t,h;const r=p=>{M(Te({keyName:"ProfitCtrGroup",data:p.body}))},l=p=>{console.log(p)};S(`/${z}/data/getProfitCtrGroup?controllingArea=${((o=H==null?void 0:H.body)==null?void 0:o.controllingArea)||((h=(t=s==null?void 0:s.controllingArea)==null?void 0:t.newControllingArea)==null?void 0:h.code)}`,"get",r,l)},ae=()=>{Ie(!0),Mn(!1)},He=()=>{qe(!0)},Ce=()=>{te(!0);const r=Ve();C?r?(ne(l=>l-1),M(pe())):He():(ne(l=>l-1),M(pe()))},be=()=>{const r=Ve();C?r?(ne(l=>l+1),M(pe())):He():(ne(l=>l+1),M(pe()))},q=()=>{ge(!0)},br=()=>{ge(!1)},yr=()=>{T(!1),q(),v("Confirm"),k("Do You Want to Save as Draft ?"),Gn(!0),er("proceed")},Sr=()=>{const r=o=>{if(j(!1),o.statusCode===200){console.log("success"),v("Create"),k(`Profit Center Saved As Draft with ID NPS${o.body} `),T("success"),E(!1),w(!0),ie(),B(!0);const t={artifactId:oe,createdBy:m==null?void 0:m.emailId,artifactType:"ProfitCenter",requestId:`NPS${o==null?void 0:o.body}`},h=u=>{console.log("Second API success",u)},p=u=>{console.error("Second API error",u)};S(`/${Ae}/documentManagement/updateDocRequestId`,"post",h,p,t)}else v("Error"),w(!1),k("Failed Saving Profit Center"),T("danger"),E(!1),B(!0),q();handleClose()},l=o=>{console.log(o)};S(`/${z}/alter/profitCenterAsDraft`,"post",r,l,le)},vr=()=>{const r=o=>{if(j(!1),o.statusCode===200){console.log("success"),v("Create"),k(`Profit Center Submitted for Review with ID CPR${o.body} `),T("success"),E(!1),w(!0),ie(),B(!0);const t={artifactId:oe,createdBy:m==null?void 0:m.emailId,artifactType:"ProfitCenter",requestId:`CPR${o==null?void 0:o.body}`},h=u=>{console.log("Second API success",u)},p=u=>{console.error("Second API error",u)};S(`/${Ae}/documentManagement/updateDocRequestId`,"post",h,p,t)}else ze(!0),te(!0),v("Error"),w(!1),k("Failed Submitting Profit Center"),T("danger"),E(!1),B(!0),q();handleClose()},l=o=>{console.log(o)};S(`/${z}/alter/changeProfitCenterSubmitForReview`,"post",r,l,le)},Ue=()=>{var p,u;re(!0);const r={coArea:b!=null&&b.controllingArea?b==null?void 0:b.controllingArea:(p=H==null?void 0:H.body)==null?void 0:p.controllingArea,name:O!=null&&O.Name?(u=O==null?void 0:O.Name)==null?void 0:u.toUpperCase():""},l=g=>{var $,J,X;g.statusCode===201?(v("Create"),v("Create"),k("All Data has been Validated. Cost Center can be Sent for Review"),T("success"),E(!1),w(!0),ie(),B(!0),Ee(!0),(r.coArea!==""||r.name!=="")&&S(`/${z}/alter/fetchPCDescriptionDupliChk`,"post",o,t,r)):(re(!1),v("Error"),w(!1),k(`${($=g==null?void 0:g.body)!=null&&$.message[0]?(J=g==null?void 0:g.body)==null?void 0:J.message[0]:(X=g==null?void 0:g.body)==null?void 0:X.value}`),T("danger"),E(!1),B(!0),q())},o=g=>{g.body.length===0||!g.body.some($=>$.toUpperCase()===r.name)?(re(!1),te(!1),ze(!1)):(re(!1),v("Duplicate Check"),w(!1),k("There is a direct match for the Profit Center name. Please change the name."),T("danger"),E(!1),B(!0),q(),te(!0))},t=g=>{console.log(g)},h=g=>{console.log(g)};S(`/${z}/alter/validateSingleProfitCenter`,"post",l,h,le)},Pr=()=>{Yn?(ue(!1),Ee(!1)):(ue(!1),_e("/masterDataCockpit/profitCenter"))},Re=()=>{ge(!1)},Ye=()=>{yr()},kr=()=>{j(!0),vr()},Tr=()=>{},Ar=()=>{j(!0);const r=o=>{if(j(!1),o.statusCode===200){v("Create"),k(`Profit Center has been Submitted for Review NPS${o.body}`),T("success"),E(!1),w(!0),ie(),B(!0),console.log("secondapi");const t={artifactId:oe,createdBy:m==null?void 0:m.emailId,artifactType:"ProfitCenter",requestId:`NPS${o==null?void 0:o.body}`},h=u=>{console.log("Second API success",u)},p=u=>{console.error("Second API error",u)};S(`/${Ae}/documentManagement/updateDocRequestId`,"post",h,p,t)}else v("Create"),w(!1),k("Creation Failed"),T("danger"),E(!1),B(!0),q();handleClose()},l=o=>{console.log(o)};S(`/${z}/alter/profitCenterSubmitForReview`,"post",r,l,le)},Nr=()=>{ce(),Ar()},Ir=()=>{we(!0)},ce=()=>{we(!1)},ye=()=>{Jn(!1)},Ke=(r,l)=>{const o=r.target.value;if(o.length>0&&o[0]===" ")Be(o.trimStart());else{let t=o.toUpperCase();Be(t)}};console.log("factorsarray",P);const Je=P.map(r=>{const l=On.filter(o=>{var t;return((t=o.category)==null?void 0:t.split(" ")[0])==(r==null?void 0:r.split(" ")[0])});if(l.length!=0)return{category:r==null?void 0:r.split(" ")[0],data:l[0].data}}).map((r,l)=>{if((r==null?void 0:r.category)=="Basic"&&f==0)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(G,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(t=>(console.log("fieldDatatttt",t),n(ee,{label:t.fieldName,value:t.value,length:t.maxLength,data:e,visibility:t.visibility,onSave:h=>handleFieldSave(t.fieldName,h),isEditMode:C,type:t.fieldType,field:t})))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Indicators"&&f==1)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(G,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(t=>n(ee,{label:t.fieldName,value:t.value,data:e,onSave:h=>handleFieldSave(t.fieldName,h),visibility:t.visibility,isEditMode:C,type:t.fieldType,field:t}))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Comp"&&f==2)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:n(no,{compCodesTabDetails:ir,displayCompCode:K})},r.category)];if((r==null?void 0:r.category)=="Address"&&f==3)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(G,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(t=>n(ee,{label:t.fieldName,value:t.value,data:e,onSave:h=>handleFieldSave(t.fieldName,h),visibility:t.visibility,isEditMode:C,type:t.fieldType,field:t}))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Communication"&&f==4)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(G,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(t=>n(ee,{label:t.fieldName,value:t.value,data:e,onSave:h=>handleFieldSave(t.fieldName,h),visibility:t.visibility,isEditMode:C,type:t.fieldType,field:t}))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="History"&f==5)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(G,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(t=>n(ee,{label:t.fieldName,value:t.value,data:e,onSave:h=>handleFieldSave(t.fieldName,h),visibility:t.visibility,isEditMode:C,type:t.fieldType,field:t}))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Attachments"&&f==6)return[n(I,{children:C?a(I,{children:[n(Lr,{title:"ProfitCenter",useMetaData:!1,artifactId:oe,artifactName:"ProfitCenter"}),a(de,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(i,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(d,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!V.length&&n(un,{width:"100%",rows:V,columns:We,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!V.length&&n(d,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(d,{variant:"h6",children:"Comments"}),!!W.length&&n(Bn,{sx:{[`& .${gn.root}:before`]:{flex:0,padding:0}},children:W.map(o=>a(xn,{children:[a(fn,{children:[n(Cn,{children:n(bn,{sx:{color:"#757575"}})}),n(yn,{})]}),n(Sn,{sx:{py:"12px",px:2},children:n(de,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(N,{sx:{padding:"1rem"},children:a(D,{spacing:1,children:[n(i,{sx:{display:"flex",justifyContent:"space-between"},children:n(d,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:vn(o.createdAt).format("DD MMM YYYY")})}),n(d,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),n(d,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!W.length&&n(d,{variant:"body2",children:"No Comments Found"}),n("br",{})]})]}):a(de,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(i,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(d,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!V.length&&n(un,{width:"100%",rows:V,columns:We,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!V.length&&n(d,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(d,{variant:"h6",children:"Comments"}),!!W.length&&n(Bn,{sx:{[`& .${gn.root}:before`]:{flex:0,padding:0}},children:W.map(o=>a(xn,{children:[a(fn,{children:[n(Cn,{children:n(bn,{sx:{color:"#757575"}})}),n(yn,{})]}),n(Sn,{sx:{py:"12px",px:2},children:n(de,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(N,{sx:{padding:"1rem"},children:a(D,{spacing:1,children:[n(i,{sx:{display:"flex",justifyContent:"space-between"},children:n(d,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:vn(o.createdAt).format("DD MMM YYYY")})}),n(d,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),n(d,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!W.length&&n(d,{variant:"body2",children:"No Comments Found"}),n("br",{})]})})]});return n(I,{children:Vn===!0?n(Wr,{}):a("div",{style:{backgroundColor:"#FAFCFF"},children:[n(Vr,{dialogState:Un,openReusableDialog:q,closeReusableDialog:Re,dialogTitle:Rn,dialogMessage:Fe,handleDialogConfirm:Re,dialogOkText:"OK",dialogSeverity:$n,showCancelButton:!0,handleDialogReject:br,handleExtraText:Dn,showExtraButton:Qn,handleExtraButton:Sr}),Ln&&n(Pn,{openSnackBar:Wn,alertMsg:Fe,handleSnackBarClose:Pr}),Oe.length!=0&&n(Pn,{openSnackBar:tr,alertMsg:"Please fill the following Field: "+Oe.join(", "),handleSnackBarClose:xr}),a(kn,{open:Kn,onClose:ye,sx:{"&::webkit-scrollbar":{width:"1px"}},fullWidth:!0,children:[a(Tn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(d,{variant:"h6",children:"Remarks"}),n(he,{sx:{width:"max-content"},onClick:ye,children:n(An,{})})]}),n(Nn,{sx:{padding:".5rem 1rem"},children:n(In,{autoFocus:!0,margin:"dense",id:"name",label:"Enter Remarks for Correction",type:"text",fullWidth:!0,variant:"standard",value:se,onChange:Ke})}),a(Fn,{sx:{display:"flex",justifyContent:"end"},children:[n(x,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ye,children:"Cancel"}),n(x,{className:"button_primary--normal",type:"save",onClick:Tr,variant:"contained",children:"OK"})]})]}),a(kn,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Xn,onClose:ce,children:[a(Tn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(d,{variant:"h6",children:"Remarks"}),n(he,{sx:{width:"max-content"},onClick:ce,children:n(An,{})})]}),n(Nn,{sx:{padding:".5rem 1rem"},children:n(D,{children:n(N,{sx:{minWidth:400},children:n(Hr,{sx:{height:"auto"},fullWidth:!0,children:n(In,{sx:{backgroundColor:"#F5F5F5"},value:se,onChange:Ke,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),a(Fn,{sx:{display:"flex",justifyContent:"end"},children:[n(x,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ce,children:"Cancel"}),n(x,{className:"button_primary--normal",type:"save",onClick:Nr,variant:"contained",children:"Submit"})]})]}),n(Ur,{sx:{color:"#fff",zIndex:r=>r.zIndex.drawer+1},open:Hn,children:n(Rr,{color:"inherit"})}),a(i,{container:!0,sx:Yr,children:[a(i,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[a(i,{md:12,sx:{display:"flex"},children:[n(i,{children:n(he,{color:"primary","aria-label":"upload picture",component:"label",sx:En,children:n(Kr,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{_e("/masterDataCockpit/profitCenter")}})})}),a(i,{children:[!(s!=null&&s.requestType)&&C?a(i,{item:!0,md:12,children:[n(d,{variant:"h3",children:n("strong",{children:"Create Profit Center "})}),n(d,{variant:"body2",color:"#777",children:"This view creates new Profit Center from existing Profit Center"})]}):"",C&&(s==null?void 0:s.requestType)==="Change"?a(i,{item:!0,md:12,children:[n(d,{variant:"h3",children:n("strong",{children:"Change Profit Center "})}),n(d,{variant:"body2",color:"#777",children:"This view changes the details of the Profit Center"})]}):"",C&&(s==null?void 0:s.requestType)==="Create"?a(i,{item:!0,md:12,children:[n(d,{variant:"h3",children:n("strong",{children:"Create Profit Center "})}),n(d,{variant:"body2",color:"#777",children:"This view creates a new Profit Center"})]}):"",Y?a(i,{item:!0,md:12,children:[n(d,{variant:"h3",children:n("strong",{children:"Display Profit Center "})}),n(d,{variant:"body2",color:"#777",children:"This view displays the details of the Profit Center"})]}):""]})]}),(s==null?void 0:s.reqStatus)==="Correction Pending"?n(i,{children:n(he,{color:"primary","aria-label":"upload picture",component:"label",sx:En,children:n(jn,{sx:{fontSize:"25px",color:"#000000",alignItems:"flex-end"},onClick:()=>{}})})}):"",n(i,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:ve(xe,"Profit Center","ChangePC")&&((m==null?void 0:m.role)==="Super User"&&(s!=null&&s.requestType)&&Y?n(i,{gap:1,sx:{display:"flex"},children:n(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(I,{children:n(i,{item:!0,children:a(x,{variant:"outlined",size:"small",sx:R,onClick:ae,children:["Fill Details",n(me,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(m==null?void 0:m.role)==="Finance"&&(s!=null&&s.requestType)&&Y?n(i,{gap:1,sx:{display:"flex"},children:n(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(I,{children:n(i,{item:!0,children:a(x,{variant:"outlined",size:"small",sx:R,onClick:ae,children:["Fill Details",n(me,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(m==null?void 0:m.role)==="Super User"&&!(s!=null&&s.requestType)&&Y?n(i,{gap:1,sx:{display:"flex"},children:n(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(I,{children:n(i,{item:!0,children:a(x,{variant:"outlined",size:"small",sx:R,onClick:ae,children:["Change",n(me,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(m==null?void 0:m.role)==="Finance"&&!(s!=null&&s.requestType)&&Y?n(i,{gap:1,sx:{display:"flex"},children:n(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(I,{children:n(i,{item:!0,children:a(x,{variant:"outlined",size:"small",sx:R,onClick:ae,children:["Change",n(me,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")})]}),n(i,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:a(N,{width:"70%",sx:{marginLeft:"40px"},children:[n(i,{item:!0,sx:{paddingTop:"2px !important"},children:a(D,{flexDirection:"row",children:[n("div",{style:{width:"10%"},children:n(d,{variant:"body2",color:"#777",children:"Profit Center"})}),a(d,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",`P${(ln=(tn=s==null?void 0:s.companyCodeCopy)==null?void 0:tn.newCompanyCodeCopy)==null?void 0:ln.code}${(an=s==null?void 0:s.profitCenterName)==null?void 0:an.newProfitCenterName}`]})]})}),n(i,{item:!0,sx:{paddingTop:"2px !important"},children:a(D,{flexDirection:"row",children:[n("div",{style:{width:"10%"},children:n(d,{variant:"body2",color:"#777",children:"Controlling Area"})}),a(d,{variant:"body2",fontWeight:"bold",children:[":"," ",(dn=(cn=s==null?void 0:s.controllingArea)==null?void 0:cn.newControllingArea)==null?void 0:dn.code]})]})})]})}),a(i,{container:!0,style:{marginLeft:25},children:[n(Jr,{activeStep:f,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:P.map((r,l)=>n(Xr,{children:n(Zr,{sx:{fontWeight:"700"},children:r})},r))}),Je&&((hn=Je[f])==null?void 0:hn.map((r,l)=>n(N,{sx:{mb:2,width:"100%"},children:n(d,{variant:"body2",children:r})},l)))]})]}),a(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[ve(xe,"Profit Center","ChangePC")&&(!(s!=null&&s.requestType)&&!C?n(Pe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ke,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ce,disabled:f===0,children:"Back"}),n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:be,disabled:f===P.length-1,children:"Next"})]})}):""),ve(xe,"Profit Center","ChangePC")&&((m==null?void 0:m.role)==="Super User"&&!(s!=null&&s.requestType)&&C?n(Pe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ke,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(x,{variant:"contained",size:"small",sx:{button_Outlined:R,mr:1},onClick:Ye,children:"Save As Draft"}),n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ce,disabled:f===0,children:"Back"}),f===P.length-1?a(I,{children:[n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ue,children:"Validate"}),n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:kr,disabled:je,children:"Submit For Review"})]}):n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:be,disabled:f===P.length-1,children:"Next"})]})}):(m==null?void 0:m.role)==="Finance"&&!(s!=null&&s.requestType)&&C?n(Pe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ke,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(x,{variant:"contained",size:"small",sx:{button_Outlined:R,mr:1},onClick:Ye,children:"Save As Draft"}),n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ce,disabled:f===0,children:"Back"}),f===P.length-1?a(I,{children:[n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ue,children:"Validate"}),n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ir,disabled:je,children:"Submit For Review"})]}):n(x,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:be,disabled:f===P.length-1,children:"Next"})]})}):"")]})]})})};export{uo as default};
