import{r as w,a as d,jr as ye,T as O,cK as U,gk as ue,sc as he,l as fe,u as ge,i as L,e as Ne,kA as R,nz as Se,sd as xe,lP as M,g_ as ke,j as b,gK as N,nA as Ae,h as Ie,hy as Ee,lr as q,a3 as Re,F as be,$ as Te,ny as De}from"./index-fdfa25a0.js";import{u as ve}from"./useChangeLogUpdate-3699f77c.js";const Ce=({fallback:e="--",variant:c="text",delay:h=3e3,sx:S={fontSize:"1rem"}})=>{const[A,f]=w.useState(!0);return w.useEffect(()=>{const m=setTimeout(()=>{f(!1)},h);return()=>clearTimeout(m)},[h]),A?d(ye,{variant:c,sx:S}):d(O,{component:"span",sx:S,children:e})},ce=2,ne=U.createContext({}),Le=U.forwardRef((e,c)=>{const h=U.useContext(ne);return d("div",{ref:c,...e,...h})}),we=U.forwardRef(function(c,h){const{children:S,...A}=c,f=[];S.forEach(y=>{f.push(y)});const m=f.length,T=he.HEIGHT,D=()=>m>8?8*T:f.length*T;return d("div",{ref:h,children:d(ne.Provider,{value:A,children:d(ue,{itemData:f,height:D()+2*ce,width:"100%",outerElementType:Le,innerElementType:"ul",itemSize:T,overscanCount:5,itemCount:m,children:({data:y,index:x,style:i})=>{const P=y[x],l={...i,top:i.top+ce};return d("li",{style:{...l,listStyle:"none"},children:P})}})})})}),Me=(e,c,h,S)=>{var D;if(e!=="Region")return!1;const A=(D=h==null?void 0:h.code)==null?void 0:D.toUpperCase(),f=c.some(y=>y.includes("CA-MDG-MRKTNG-US")),m=c.some(y=>y.includes("CA-MDG-MRKTNG-FERT-EUR")||y.includes("CA-MDG-MRKTNG-SALES-EUR"));return f&&m?!1:A==="US"?!f:A==="EUR"?!m:!0};function Pe(e){var $,Y,G,z,W,j,B,F,H,K,Q,J,X,Z,V,s,r,o,p,ee,te,ae,ie;const c=fe(),{updateChangeLog:h}=ve(),S=ge(),f=new URLSearchParams(S.search).get("RequestId"),m=L(a=>a.payload.payloadData),T=S.pathname.includes("DisplayMaterialSAPView");let D=L(a=>a.userManagement.roles);const{t:y}=Ne(),x=L(a=>a.payload||{}),i=L(a=>{var t;return((t=a.AllDropDown)==null?void 0:t.dropDown)||{}}),P=L(a=>{var t;return((t=a.payload)==null?void 0:t.errorFields)||[]}),[l,I]=w.useState(null),n=((z=(G=(Y=($=x==null?void 0:x[e==null?void 0:e.materialID])==null?void 0:$.payloadData)==null?void 0:Y[e==null?void 0:e.viewName])==null?void 0:G[e==null?void 0:e.plantData])==null?void 0:z[e==null?void 0:e.keyName])??((W=x==null?void 0:x.payloadData)==null?void 0:W[e==null?void 0:e.keyName])??(((j=e==null?void 0:e.details)==null?void 0:j.fieldPriority)==="ApplyBus"||e!=null&&e.isRequestHeader?(B=e==null?void 0:e.details)==null?void 0:B.value:null);w.useEffect(()=>{var a,t,g;(((a=e==null?void 0:e.details)==null?void 0:a.visibility)===R.MANDATORY||((t=e==null?void 0:e.details)==null?void 0:t.visibility)==="Required")&&c(Se((e==null?void 0:e.keyName)||"")),((g=e==null?void 0:e.details)==null?void 0:g.visibility)===R.DISPLAY&&c(xe(e==null?void 0:e.keyName))},[c,(F=e==null?void 0:e.details)==null?void 0:F.visibility,e==null?void 0:e.keyName]),w.useEffect(()=>{var a,t,g,k,le,de;if(n!=null&&n!=="")if(n!=null&&n.code)I(n),M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:n,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData});else if(i!=null&&i[e==null?void 0:e.keyName]||(a=i==null?void 0:i[e==null?void 0:e.keyName])!=null&&a[e==null?void 0:e.plantData]){if(!Array.isArray(i==null?void 0:i[e==null?void 0:e.keyName])&&!Array.isArray((t=i==null?void 0:i[e==null?void 0:e.keyName])==null?void 0:t[e==null?void 0:e.plantData])){I(null);return}const u=(g=i[e==null?void 0:e.keyName])!=null&&g.length?(k=i[e==null?void 0:e.keyName])==null?void 0:k.find(E=>{var v,C;return((v=E==null?void 0:E.code)==null?void 0:v.trim())===((C=n==null?void 0:n.toString())==null?void 0:C.trim())}):(de=(le=i[e==null?void 0:e.keyName])==null?void 0:le[e==null?void 0:e.plantData])==null?void 0:de.find(E=>{var v,C;return((v=E==null?void 0:E.code)==null?void 0:v.trim())===((C=n==null?void 0:n.toString())==null?void 0:C.trim())});u?(I({code:u==null?void 0:u.code,desc:u==null?void 0:u.desc}),c(M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:{code:u==null?void 0:u.code,desc:u==null?void 0:u.desc},viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData}))):(I(null),c(M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:null,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData})))}else I(null);else I(null)},[n]);const me=(a,t)=>{var g,k;I(t),c(M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:t??null,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData})),f&&!De.includes(m==null?void 0:m.RequestStatus)&&h({materialID:e==null?void 0:e.selectedMaterialNumber,viewName:e==null?void 0:e.viewName,plantData:e==null?void 0:e.plantData,fieldName:(g=e==null?void 0:e.details)==null?void 0:g.fieldName,jsonName:(k=e==null?void 0:e.details)==null?void 0:k.jsonName,currentValue:`${t==null?void 0:t.code}-${(t==null?void 0:t.desc)??""}`,requestId:m==null?void 0:m.RequestId,childRequestId:f})},_=(H=e==null?void 0:e.details)==null?void 0:H.jsonName;return d(Te,{item:!0,md:2,sx:{marginBottom:"12px !important"},children:((K=e==null?void 0:e.details)==null?void 0:K.visibility)==="Hidden"?null:d(ke,{children:T?b("div",{style:{padding:"16px",backgroundColor:N.primary.white,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",margin:"16px 0",transition:"all 0.3s ease"},children:[b(O,{variant:"body1",style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},title:y((Q=e==null?void 0:e.details)==null?void 0:Q.fieldName),children:[y((J=e==null?void 0:e.details)==null?void 0:J.fieldName)||"Field Name",(((X=e==null?void 0:e.details)==null?void 0:X.visibility)===R.REQUIRED||((Z=e==null?void 0:e.details)==null?void 0:Z.visibility)===Ae.MANDATORY)&&d("span",{style:{color:N.error.darkRed,marginLeft:"5px",fontSize:"1.1rem"},children:"*"})]}),d("div",{style:{fontSize:"0.8rem",color:N.black.dark,marginTop:"4px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",cursor:"pointer"},children:l!=null&&l.code||l!=null&&l.desc?d(Ie,{title:l!=null&&l.code?`${l==null?void 0:l.code} - ${(l==null?void 0:l.desc)||""}`:"--",arrow:!0,children:b("span",{children:[d("strong",{style:{fontWeight:600,color:N.secondary.grey,marginRight:"6px",letterSpacing:"0.5px",wordSpacing:"1px"},children:l==null?void 0:l.code}),(l==null?void 0:l.desc)&&b("span",{style:{fontWeight:500,color:N.secondary.grey,letterSpacing:"0.5px",wordSpacing:"1px"},children:["- ",l==null?void 0:l.desc]})]})}):d(Ce,{fallback:"--"})})]}):b(be,{children:[b(O,{variant:"body2",color:N.secondary.grey,sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:y((V=e==null?void 0:e.details)==null?void 0:V.fieldName),children:[y((s=e==null?void 0:e.details)==null?void 0:s.fieldName)||"Field Name",(((r=e==null?void 0:e.details)==null?void 0:r.visibility)==="Required"||((o=e==null?void 0:e.details)==null?void 0:o.visibility)===R.MANDATORY)&&d("span",{style:{color:N.error.dark},children:"*"})]}),d(Ee,{sx:{height:"31px","& .MuiAutocomplete-listbox":{padding:0,"& .MuiAutocomplete-option":{paddingLeft:"16px",paddingTop:"4px",paddingBottom:"4px",justifyContent:"flex-start"}},"& .MuiAutocomplete-option":{display:"flex",alignItems:"center",minHeight:"36px"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:N.black.dark,color:N.black.dark},backgroundColor:N.hover.light}},fullWidth:!0,disabled:(e==null?void 0:e.disabled)||((p=e.details)==null?void 0:p.visibility)===R.DISPLAY,size:"small",value:l,onChange:me,options:(ee=i==null?void 0:i[e==null?void 0:e.keyName])!=null&&ee.length?i==null?void 0:i[e==null?void 0:e.keyName]:((te=i==null?void 0:i[e==null?void 0:e.keyName])==null?void 0:te[e==null?void 0:e.plantData])||[],required:((ae=e==null?void 0:e.details)==null?void 0:ae.visibility)===R.MANDATORY||((ie=e==null?void 0:e.details)==null?void 0:ie.visibility)==="Required",ListboxComponent:we,getOptionLabel:a=>a!=null&&a.desc?`${(a==null?void 0:a.code)||""} - ${(a==null?void 0:a.desc)||""}`:`${(a==null?void 0:a.code)||""}`,getOptionDisabled:a=>Me(e==null?void 0:e.keyName,D,a),renderOption:(a,t)=>d(O,{...a,component:"li",style:{fontSize:12,padding:"8px 16px",width:"100%",cursor:"pointer",display:"flex",alignItems:"start"},title:`${_===q.REQUEST_TYPE?"":t==null?void 0:t.code}${t!=null&&t.desc&&_!==q.REQUEST_TYPE?` - ${t==null?void 0:t.desc}`:`${t==null?void 0:t.desc}`}`,children:b("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[d("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc&&_!==q.REQUEST_TYPE?` - ${t==null?void 0:t.desc}`:""]})}),renderInput:a=>{var t,g,k;return d(Re,{...a,variant:"outlined",placeholder:e!=null&&e.disabled||((t=e.details)==null?void 0:t.visibility)===R.DISPLAY?"":y(`SELECT ${((k=(g=e==null?void 0:e.details)==null?void 0:g.fieldName)==null?void 0:k.toUpperCase())||""}`),error:P.includes((e==null?void 0:e.keyName)||""),InputProps:{...a.InputProps}})}})]})})})}export{Pe as A,Ce as S};
