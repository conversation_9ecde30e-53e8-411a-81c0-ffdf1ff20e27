import{i as o,l as S,b as E,r,a as l,F as k,v as D,ae as v,af as F,w as m,ag as p}from"./index-fdfa25a0.js";import{W as f,c as b,u as Q}from"./propData-e8199bd0.js";import{r as y,a as J}from"./userData-010d5077.js";import"./Workflow-0d087b9d.js";import"./index-3f2e0745.js";import"./DialogContentText-8ac052ae.js";import"./ListItemButton-db9eb0d0.js";import"./StepButton-fdbf0590.js";import"./ToggleButtonGroup-c02e6027.js";import"./makeStyles-1dfd4db4.js";import"./asyncToGenerator-88583e02.js";import"./dayjs.min-774e293a.js";import"./isBetween-fe8614a5.js";function P(){let a=o(i=>{var e;return(e=i.userManagement)==null?void 0:e.userData});o(i=>{var e;return(e=i.userManagement)==null?void 0:e.groups});const n=o(i=>i.applicationConfig);let N=S();const I=E(),[d,V]=r.useState(null),[U,L]=r.useState(null),R={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://ca-gbd-ca-caf-cw-caf-iwm.cfapps.us10-001.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},h=i=>{N(v({module:"RequestHistory",filterData:{reqId:i==null?void 0:i.ATTRIBUTE_1}})),I(F.REQUEST_HISTORY)},u=()=>{console.log("fetchFilterView")},w=()=>{console.log("clearFilterView")},T=()=>{m(`/${p}/api/v1/usersMDG/getUsersMDG`,"get",i=>{var e=i.data,s=e==null?void 0:e.map(t=>({...t,userId:t==null?void 0:t.emailId})),c={...i,data:s};V(c)})},W=()=>{m(`/${p}/api/v1/groupsMDG/getAllGroupsMDG`,"get",i=>{var e=i.data,s=e==null?void 0:e.map(t=>({...t,groupName:t==null?void 0:t.name})),c={...i,data:s};L(c)})},M=(i,e)=>{console.log("Success flag.",i),console.log("Task Payload.",e)};return r.useEffect(()=>{T(),W()},[]),l("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:l(k,{children:l(f,{token:"eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vY2EtZ2JkLmF1dGhlbnRpY2F0aW9uLnVzMTAuaGFuYS5vbmRlbWFuZC5jb20vdG9rZW5fa2V5cyIsImtpZCI6ImRlZmF1bHQtand0LWtleS1kZjk5ODA5MzZhIiwidHlwIjoiSldUIiwiamlkIjogIlNMVzNNUjQ5UDZTMGJwWVJNOWJJZVQ3Z0Q1aDh2SkVpM1ZrOFVEeUVhY3c9In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UiGLR24hLDpu661TnyEfCOSMqY5SAjZTwuJbPANHuTXVwZEZwU0y6G3kMkRFn-9NgvxNEkTwIY2vjipbsFvahSnQHeze4doZlQDTU4lP2m_pGCBsXNF3R8kgpdTt_zFPbAwpK0xNz7dA79vVL-J0cPrFhqEWxf6wLQf4L--o150tb69-dFCsOfPP_O6Kuaw5DMvtfHm9Toe4RhBSVJj1zKrhGn-M-7rNl1wUDuc0WTvJeB7yMHz3Q7MtigXj8bdTtjuH_dyLqLVUdnNPnz8HY9EjShPCqAuBtfiqs_Tb167DZyeiaqJCDg5ZmLKtrKQZUdBGuAvMq2ZgkFktjMh72g",configData:b,destinationData:R,userData:{...a,user_id:a==null?void 0:a.emailId},userPermissions:Q,userList:y(d),groupList:J(U),useWorkAccess:n.environment==="localhost",useConfigServerDestination:n.environment==="localhost",inboxTypeKey:"MY_COMPLETED_TASKS",workspaceLabel:"Completed Tasks",subInboxTypeKey:null,onTaskClick:h,onActionComplete:M,workspaceFiltersByAPIDriven:!1,isFilterView:!1,savedFilterViewData:[],selectedFilterView:null,fetchFilterViewList:u,clearFilterView:w,cachingBaseUrl:D,selectedTabId:null,externalSystems:[]})})})}export{P as default};
