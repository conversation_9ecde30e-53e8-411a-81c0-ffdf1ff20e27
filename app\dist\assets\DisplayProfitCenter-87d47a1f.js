import{r as g,l as hr,i as W,a as e,hC as Je,F as H,hA as Ss,b as Ps,u as Ns,hZ as As,ig as Ts,j as d,h_ as ae,T as C,B as X,gZ as de,$ as u,h$ as Zn,i0 as Qn,i1 as Gn,i2 as wn,i3 as Rn,i4 as Dn,i5 as er,a0 as Be,g_ as he,hN as Ye,i6 as Is,ht as Es,J as Fs,i7 as nr,fV as rr,h1 as sr,W as Ke,hI as lr,fX as or,gQ as ir,a3 as tr,fH as cr,a1 as a,ho as qs,h8 as zs,hp as Bs,hv as $s,i8 as js,gU as A,i9 as Ms,ia as Os,hD as $e,ib as je,ic as Ls,id as ks,ie as _s,P as k,hE as U,gW as x,ii as Us,ij as Vs,w as z,hL as j,ih as ar,ix as Me,ik as fe,iy as Ws,hn as Hs,hP as Xe}from"./index-fdfa25a0.js";import{E as Ce}from"./EditableFieldForProfitCenter-a7957a65.js";import{T as dr}from"./Timeline-bb89efb4.js";const Ys=({displayCompCode:f=[]})=>{const[be,ne]=g.useState([]);hr();const Oe=W(ee=>ee.AllDropDown.dropDown.CompCodeBasedOnControllingArea??[]);return console.log(be,f,Oe,"props1234"),e(H,{children:e(Je,{width:"100%",rows:f??[],columns:[{field:"id",headerName:"ID",type:"text",hide:"true",editable:"false"},{field:"companyCodes",headerName:"Company Codes",width:350,editable:"false"},{field:"companyName",headerName:"Company Name",type:"text",editable:"false",width:350},{field:"assigned",headerName:"Assigned",width:350,editable:"true",renderCell:ee=>(console.log("params",ee),e(Ss,{sx:{padding:0},checked:ee.row.assigned===!0||ee.row.assigned==="X",onChange:Le=>{console.log("dddd",f,Le.target.checked);let ye=0,B=f==null?void 0:f.map(le=>le.id===ee.row.id?(ye++,{...le,assigned:le.assigned==!1?"X":""}):le);ne(B),console.log("finalcount",ye),console.log("companycoderows",B)}}))}],getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1})})},Rs=()=>{var fn,Cn,bn,yn,vn,Sn,Pn,Nn,An,Tn,In,En,Fn,qn,zn,Bn,$n,jn,Mn,On,Ln,kn,_n,Un;const[f,be]=g.useState(!1);g.useState(0);const[ne,Oe]=g.useState(!0);g.useState({});const[T,ee]=g.useState([]),[Le,ye]=g.useState([]),[B,le]=g.useState();g.useState([]);const[h,ve]=g.useState(0),[Ze,v]=g.useState(""),[mr,S]=g.useState(!1),[Ks,P]=g.useState(!0),[ur,N]=g.useState(!1),[Xs,I]=g.useState(!1),[gr,ke]=g.useState(!1),[pr,F]=g.useState(!0),[xr,R]=g.useState(!1),[fr,Se]=g.useState(!1),[Cr,b]=g.useState(!1),[br,_e]=g.useState(!1),[yr,Qe]=g.useState(!1),[Pe,Ne]=g.useState(""),vr=W(s=>s.appSettings),[me,Sr]=g.useState([]),[oe,Pr]=g.useState([]),[ie,Nr]=g.useState([]),[Ar,Ge]=g.useState(!1),[Q,ue]=g.useState(!0),[ge,Tr]=g.useState(""),[Ir,q]=g.useState(!1),[Er,we]=g.useState(""),[Fr,Re]=g.useState(!1),[De,Ue]=g.useState(""),[qr,G]=g.useState(!0),[zr,Br]=g.useState([]),[en,$r]=g.useState([]),[Js,jr]=g.useState(!1),[Mr,nn]=g.useState(!1),[Ae,Or]=g.useState([]),[Lr,kr]=g.useState(""),D=hr(),rn=Ps(),_r=Ns(),Ve=W(s=>s.profitCenter.profitCenterViewData);W(s=>{var c;return(c=s.userManagement.entitiesAndActivities)==null?void 0:c.DisplayProfitCenter});let Te=W(s=>{var c;return(c=s.userManagement.entitiesAndActivities)==null?void 0:c["Profit Center"]});W(s=>{var c;return(c=s.userManagement.entitiesAndActivities)==null?void 0:c.ChangeProfitCenter});let i=W(s=>s.userManagement.userData),m=W(s=>{var c;return(c=s==null?void 0:s.initialData)==null?void 0:c.IWMMyTask}),n=_r.state;console.log("profitCenterRowData",n),W(s=>s.costCenter.singleCCPayload),W(s=>s.profitCenter.profitCenterCompCodes);let sn=W(s=>s.edit.payload);console.log(sn,"singlePCPayloadAfterChange");const r=W(s=>s.edit.payload);let l=W(s=>s.userManagement.taskData);console.log(l==null?void 0:l.subject,"taskRowDetails?.subject"),console.log(m,"taskData==================");let ln=W(s=>s.profitCenter.requiredFields);console.log(ln,"required_field_for_data");var Y={TaskId:l!=null&&l.taskId?l==null?void 0:l.taskId:"",ProfitCenterID:B!=null&&B.profitCenterId?B==null?void 0:B.profitCenterId:"",RequestID:"",Action:(n==null?void 0:n.requestType)==="Create"?"I":(n==null?void 0:n.requestType)==="Change"?"U":(l==null?void 0:l.processDesc)==="Create"?"I":((l==null?void 0:l.processDesc)==="Change","U"),TaskStatus:"",ReqCreatedBy:i==null?void 0:i.user_id,ReqCreatedOn:l!=null&&l.createdOn?"/Date("+(l==null?void 0:l.createdOn)+")/":n!=null&&n.createdOn?"/Date("+Date.parse(n==null?void 0:n.createdOn)+")/":"",RequestStatus:"",CreationId:(l==null?void 0:l.processDesc)==="Create"?l==null?void 0:l.subject.slice(3):(n==null?void 0:n.requestType)==="Create"?n==null?void 0:n.requestId.slice(3):"",EditId:(l==null?void 0:l.processDesc)==="Change"?l==null?void 0:l.subject.slice(3):(n==null?void 0:n.requestType)==="Change"?n==null?void 0:n.requestId.slice(3):"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"",MassRequestStatus:"",Remarks:Pe||"",PrctrName:r!=null&&r.Name?r==null?void 0:r.Name:"",LongText:r!=null&&r.LongText?r==null?void 0:r.LongText:"",InChargeUser:r!=null&&r.UserResponsible?r==null?void 0:r.UserResponsible:"",InCharge:r!=null&&r.PersonResponsible?r==null?void 0:r.PersonResponsible:"",Department:r!=null&&r.Department?r==null?void 0:r.Department:"",PrctrHierGrp:r!=null&&r.ProfitCtrGroup?r==null?void 0:r.ProfitCtrGroup:"",Segment:r!=null&&r.Segment?r==null?void 0:r.Segment:"",LockInd:(r==null?void 0:r.Lockindicator)===!0?"X":"",Template:r!=null&&r.FormPlanningTemp?r==null?void 0:r.FormPlanningTemp:"",Title:r!=null&&r.Title?r==null?void 0:r.Title:"",Name1:r!=null&&r.Name1?r==null?void 0:r.Name1:"",Name2:r!=null&&r.Name2?r==null?void 0:r.Name2:"",Name3:r!=null&&r.Name3?r==null?void 0:r.Name3:"",Name4:r!=null&&r.Name4?r==null?void 0:r.Name4:"",Street:r!=null&&r.Street?r==null?void 0:r.Street:"",City:r!=null&&r.City?r==null?void 0:r.City:"",District:r!=null&&r.District?r==null?void 0:r.District:"",Country:r!=null&&r.CountryReg?r==null?void 0:r.CountryReg:"",Taxjurcode:r!=null&&r.TaxJur?r==null?void 0:r.TaxJur:"",PoBox:r!=null&&r.POBox?r==null?void 0:r.POBox:"",PostlCode:r!=null&&r.PostalCode?r==null?void 0:r.PostalCode:"",PobxPcd:r!=null&&r.POBoxPCode?r==null?void 0:r.POBoxPCode:"",Region:r!=null&&r.Region?r==null?void 0:r.Region:"",Langu:r!=null&&r.Language?r==null?void 0:r.Language:"EN",Telephone:r!=null&&r.Telephone1?r==null?void 0:r.Telephone1:"",Telephone2:r!=null&&r.Telephone2?r==null?void 0:r.Telephone2:"",Telebox:r!=null&&r.Telebox?r==null?void 0:r.Telebox:"",Telex:r!=null&&r.Telex?r==null?void 0:r.Telex:"",FaxNumber:r!=null&&r.FaxNumber?r==null?void 0:r.FaxNumber:"",Teletex:r!=null&&r.Teletex?r==null?void 0:r.Teletex:"",Printer:r!=null&&r.Printername?r==null?void 0:r.Printername:"",DataLine:r!=null&&r.Dataline?r==null?void 0:r.Dataline:"",ProfitCenter:n!=null&&n.profitCenter?n==null?void 0:n.profitCenter:(fn=m==null?void 0:m.body)==null?void 0:fn.profitCenter,ControllingArea:B!=null&&B.controllingArea?B==null?void 0:B.controllingArea:(Cn=m==null?void 0:m.body)==null?void 0:Cn.controllingArea,ValidfromDate:r!=null&&r.AnalysisPeriodFrom?"/Date("+(r==null?void 0:r.AnalysisPeriodFrom)+")/":"",ValidtoDate:r!=null&&r.AnalysisPeriodTo?"/Date("+(r==null?void 0:r.AnalysisPeriodTo)+")/":"",Testrun:qr,Countryiso:"",LanguIso:"",Logsystem:"",ToCompanycode:me==null?void 0:me.map(s=>({CompCodeID:s!=null&&s.ccId?s==null?void 0:s.ccId:"",CompanyName:s!=null&&s.companyName?s==null?void 0:s.companyName:"",AssignToPrctr:(s==null?void 0:s.assigned)===!0?"X":"",CompCode:s!=null&&s.companyCodes?s==null?void 0:s.companyCodes:""}))};const on=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:s=>d(H,{children:[e(Us,{index:s.row.id,name:s.row.docName}),e(Vs,{index:s.row.id,name:s.row.docName})]})}],Ur=()=>{var t,p,y,E,$,se,pe;F(!0);var s=(t=m==null?void 0:m.body)!=null&&t.id?{id:(p=m==null?void 0:m.body)!=null&&p.id?(y=m==null?void 0:m.body)==null?void 0:y.id:"",profitCenter:(E=m==null?void 0:m.body)!=null&&E.profitCenter?($=m==null?void 0:m.body)==null?void 0:$.profitCenter:"",controllingArea:(se=m==null?void 0:m.body)==null?void 0:se.controllingArea,reqStatus:(pe=m==null?void 0:m.body)==null?void 0:pe.reqStatus,screenName:(l==null?void 0:l.processDesc)==="Create"?"Create":"Change"}:{id:n!=null&&n.reqStatus?n==null?void 0:n.id:"",profitCenter:n!=null&&n.profitCenter||n!=null&&n.profitCenter?n==null?void 0:n.profitCenter:"",controllingArea:n!=null&&n.controllingArea?n==null?void 0:n.controllingArea:"",reqStatus:n!=null&&n.reqStatus?n==null?void 0:n.reqStatus:"Approved",screenName:"Change"};const c=Z=>{var xe,Vn;if(Z.statusCode===200){F(!1);const ce=Z.body.viewData,bs=Z.body;D(Ws(ce)),Or(fs((xe=ce["Basic Data"])==null?void 0:xe["General Data"],"Name"));const Wn=Object.keys(ce);ee(Wn);const ys=Wn.map(V=>({category:V,data:ce[V],setIsEditMode:be}));ye(ys),Kr(ce),le(bs);let ze=(Vn=Z==null?void 0:Z.body)==null?void 0:Vn.viewData["Comp Codes"]["Company Code Assignment for Profit Center"];console.log("compcodedata",ze),Sr(_.zip(ze[0].value,ze[1].value,ze[2].value).map((V,vs)=>{var Hn,Yn,Kn,Xn,Jn;return console.log("ccitem",V),{id:vs,companyCodes:(Hn=V[0])!=null&&Hn.split("$$$")[0]?(Yn=V[0])==null?void 0:Yn.split("$$$")[0]:"",companyName:(Kn=V[1])!=null&&Kn.split("$$$")[0]?(Xn=V[1])==null?void 0:Xn.split("$$$")[0]:"",assigned:V[2]?V[2]:"",ccId:((Jn=V[0])==null?void 0:Jn.split("$$$")[1])??""}})),Hr(ce.Address["Address Data"].find(V=>(V==null?void 0:V.fieldName)==="Country/Reg.").value)}else jr(!0),N(!1),b("Error"),v("Unable to fetch data of Profit Center"),Ue("danger"),S("danger"),P(!0),Se(!0)},o=Z=>{console.log(Z)};z(`/${j}/data/displayProfitCenter`,"post",c,o,s),kr(s.screenName),console.log(s.screenName,"displayPayload.sceenname")};console.log("dispcomp",me);const K=()=>{ke(!0)},Vr=W(s=>s.profitCenter.requiredFields),Wr=()=>{nn(!1)},tn=()=>Hs(sn,ln,$r);console.log(Vr,"requiredFields"),console.log(Ae,"profitCenterNameinRow");const Hr=s=>{console.log("compcode",s);const c=t=>{console.log("value",t),D(Xe({keyName:"Region",data:t.body}))},o=t=>{console.log(t,"error in dojax")};z(`/${j}/data/getRegionBasedOnCountry?country=${s}`,"get",c,o)},Yr=()=>{var o;const s=t=>{D(Xe({keyName:"ProfitCtrGroup",data:t.body}))},c=t=>{console.log(t)};z(`/${j}/data/getProfitCtrGroup?controllingArea=${((o=m==null?void 0:m.body)==null?void 0:o.controllingArea)||(n==null?void 0:n.controllingArea)}`,"get",s,c)};console.log(zr,"error_field_arr");const Kr=s=>{let c=[];for(const o in s){if(s.hasOwnProperty(o)){for(const t in s[o])if(s[o].hasOwnProperty(t)){const p=s[o][t];for(const y of p)if(y.visibility==="0"||y.visibility==="Required"){console.log(y.fieldName,"field.fieldName");let E=y.fieldName.replace(/\s/g,"");c.push(E)}}}Br(t=>({...t,error_field_arr:c}))}},[cn,Xr]=g.useState(0),Jr=(s,c)=>{const o=p=>{D(Xe({keyName:s,data:p.body})),Xr(y=>y+1)},t=p=>{console.log(p)};z(`/${j}/data/${c}`,"get",o,t)},Zr=()=>{var s,c;(c=(s=ar)==null?void 0:s.profitCenter)==null||c.map(o=>{Jr(o==null?void 0:o.keyName,o==null?void 0:o.endPoint)})},Qr=()=>{var s,c;cn==((c=(s=ar)==null?void 0:s.profitCenter)==null?void 0:c.length)?F(!1):F(!0)};g.useEffect(()=>{Qr()},[cn]),g.useEffect(()=>{Zr(),Tr(As("PC"))},[]),g.useEffect(()=>{Ur(),Yr()},[]),g.useEffect(()=>{Ve.length!==0&&xs()},[Ve]);const Ie=()=>{be(!0),Oe(!1)},an=()=>{nn(!0)},O=()=>{ue(!0);const s=tn();f?s?(ve(c=>c-1),D(Me())):an():(ve(c=>c-1),D(Me()))},L=()=>{const s=tn();f?s?(ve(c=>c+1),D(Me())):an():(ve(c=>c+1),D(Me()))},M=()=>{Se(!0)},Gr=()=>{const s=o=>{F(!1),o.statusCode===200?(console.log("success"),b("Create"),v(`Profit Center Submitted for Approval with ID NPS${o.body} `),S("success"),P(!1),N(!0),K(),I(!0)):(b("Error"),N(!1),v("Failed Submitting Profit Center"),S("danger"),P(!1),I(!0),M(),G(!0)),handleClose()},c=o=>{console.log(o)};z(`/${j}/alter/profitCenterApprovalSubmit`,"post",s,c,Y)},wr=()=>{const s=o=>{F(!1),o.statusCode===201?(console.log("success"),b("Create"),v(`${o.message} `),S("success"),P(!1),N(!0),K(),I(!0)):(b("Error"),N(!1),v("Failed Approving Profit Center"),S("danger"),P(!1),I(!0),M(),G(!0)),handleClose()},c=o=>{console.log(o)};z(`/${j}/alter/createProfitCenterApproved`,"post",s,c,Y)},Rr=()=>{const s=o=>{if(F(!1),o.statusCode===200){console.log("success"),b("Create"),v(`Profit Center Submitted for Review with ID NPS${o.body} `),S("success"),P(!1),N(!0),K(),I(!0);const t={artifactId:ge,createdBy:i==null?void 0:i.emailId,artifactType:"ProfitCenter",requestId:`NPS${o==null?void 0:o.body}`},p=E=>{console.log("Second API success",E)},y=E=>{console.error("Second API error",E)};z(`/${fe}/documentManagement/updateDocRequestId`,"post",p,y,t)}else b("Error"),N(!1),v("Failed Submitting Profit Center"),S("danger"),P(!1),I(!0),M(),G(!0);handleClose()},c=o=>{console.log(o)};z(`/${j}/alter/profitCenterSubmitForReview`,"post",s,c,Y)},Dr=()=>{S(!1),M(),b("Confirm"),v("Do You Want to Save as Draft ?"),q(!0),we("proceed"),Ue("Change")},es=()=>{S(!1),M(),b("Confirm"),v("Do You Want to Save as Draft ?"),q(!0),we("proceed"),Ue("Create")},ns=()=>{if(We(),F(!0),console.log(De,"dialogType"),De==="Change"){const s=o=>{if(F(!1),o.statusCode===200){console.log("success"),b("Create"),v(`Profit Center Saved As Draft with ID CPS${o.body} `),q(!1),S("success"),P(!1),N(!0),K(),I(!0);const t={artifactId:ge,createdBy:i==null?void 0:i.emailId,artifactType:"ProfitCenter",requestId:`CPS${o==null?void 0:o.body}`},p=E=>{console.log("Second API success",E)},y=E=>{console.error("Second API error",E)};z(`/${fe}/documentManagement/updateDocRequestId`,"post",p,y,t)}else b("Error"),N(!1),v("Failed Saving Profit Center"),q(!1),S("danger"),P(!1),I(!0),M();handleClose()},c=o=>{console.log(o)};z(`/${j}/alter/changeProfitCenterAsDraft`,"post",s,c,Y)}else{const s=o=>{if(F(!1),o.statusCode===200){console.log("success"),b("Create"),v(`Profit Center Saved As Draft with ID NPS${o.body} `),q(!1),S("success"),P(!1),N(!0),K(),I(!0);const t={artifactId:ge,createdBy:i==null?void 0:i.emailId,artifactType:"ProfitCenter",requestId:`NPS${o==null?void 0:o.body}`},p=E=>{console.log("Second API success",E)},y=E=>{console.error("Second API error",E)};z(`/${fe}/documentManagement/updateDocRequestId`,"post",p,y,t)}else b("Error"),N(!1),v("Failed Saving Profit Center"),q(!1),S("danger"),P(!1),I(!0),M();handleClose()},c=o=>{console.log(o)};z(`/${j}/alter/profitCenterAsDraft`,"post",s,c,Y)}},rs=()=>{const s=o=>{if(F(!1),o.statusCode===200){console.log("success"),b("Create"),v(`Profit Center Submitted for Review with ID CPS${o.body} `),q(!1),S("success"),P(!1),N(!0),K(),I(!0);const t={artifactId:ge,createdBy:i==null?void 0:i.emailId,artifactType:"ProfitCenter",requestId:`CPS${o==null?void 0:o.body}`},p=E=>{console.log("Second API success",E)},y=E=>{console.error("Second API error",E)};z(`/${fe}/documentManagement/updateDocRequestId`,"post",p,y,t)}else b("Error"),N(!1),v("Failed Submitting Profit Center"),q(!1),S("danger"),P(!1),I(!0),M(),G(!0);handleClose()},c=o=>{console.log(o)};z(`/${j}/alter/changeProfitCenterSubmitForReview`,"post",s,c,Y)},ss=()=>{const s=o=>{F(!1),o.statusCode===201?(console.log("success"),b("Create"),v(`${o.message} `),q(!1),S("success"),P(!1),N(!0),K(),I(!0)):(b("Error"),N(!1),v("Failed Saving Profit Center"),q(!1),S("danger"),P(!1),I(!0),M(),G(!0)),handleClose()},c=o=>{console.log(o)};z(`/${j}/alter/changeProfitCenterApproved`,"post",s,c,Y)},ls=()=>{const s=o=>{F(!1),o.statusCode===200?(console.log("success"),b("Create"),v(`Profit Center Submitted for Approval with ID CPS${o.body} `),q(!1),S("success"),P(!1),N(!0),K(),I(!0)):(b("Error"),N(!1),v("Failed Submitting Profit Center"),q(!1),S("danger"),P(!1),I(!0),M(),G(!0)),handleClose()},c=o=>{console.log(o)};z(`/${j}/alter/changeProfitCenterApprovalSubmit`,"post",s,c,Y)},Ee=()=>{R(!0);const s=o=>{var t,p,y;o.statusCode===201?(R(!1),b("Create"),b("Create"),v("All Data has been Validated. Profit Center can be Sent for Review"),q(!1),S("success"),P(!1),N(!0),K(),I(!0),_e(!0),ue(!1)):(R(!1),b("Error"),N(!1),v(`${(t=o==null?void 0:o.body)!=null&&t.message[0]?(p=o==null?void 0:o.body)==null?void 0:p.message[0]:(y=o==null?void 0:o.body)==null?void 0:y.value}`),q(!1),S("danger"),P(!1),I(!0),M())},c=o=>{console.log(o)};z(`/${j}/alter/validateSingleProfitCenter`,"post",s,c,Y)},te=()=>{var y,E;R(!0),console.log((n==null||n.requestType,l==null||l.processDesc,"requesttype4")),console.log(Lr,"screenName56"),console.log(B,"action4");const s={coArea:B!=null&&B.controllingArea?B==null?void 0:B.controllingArea:(y=m==null?void 0:m.body)==null?void 0:y.controllingArea,name:r!=null&&r.Name?(E=r==null?void 0:r.Name)==null?void 0:E.toUpperCase():""};console.log(s,"duplicateCheckPayload");const c=$=>{var se,pe,Z,xe;$.statusCode===201?(b("Create"),b("Create"),v("All Data has been Validated. Profit Center can be Sent for Review"),q(!1),S("success"),P(!1),N(!0),K(),I(!0),_e(!0),(s.coArea!==""||s.name!=="")&&(ue(!1),((se=s==null?void 0:s.name)==null?void 0:se.toUpperCase())===(Ae==null?void 0:Ae.toUpperCase())?R(!1):(R(!1),z(`/${j}/alter/fetchPCDescriptionDupliChk`,"post",o,t,s)))):(R(!1),b("Error"),N(!1),v(`${(pe=$==null?void 0:$.body)!=null&&pe.message[0]?(Z=$==null?void 0:$.body)==null?void 0:Z.message[0]:(xe=$==null?void 0:$.body)==null?void 0:xe.value}`),q(!1),S("danger"),P(!1),I(!0),M())},o=$=>{$.body.length===0||!$.body.some(se=>se.toUpperCase()===s.name)?(R(!1),ue(!1)):(R(!1),b("Duplicate Check"),N(!1),v("There is a direct match for the Profit Center name. Please change the name."),q(!1),S("danger"),P(!1),I(!0),M(),ue(!0))},t=$=>{console.log($)},p=$=>{console.log($)};z(`/${j}/alter/validateSingleProfitCenter`,"post",c,p,Y)},os=()=>{br?(ke(!1),_e(!1)):(ke(!1),rn("/masterDataCockpit/profitCenter"))},We=()=>{Se(!1)},dn=()=>{F(!0),wr()},is=()=>{F(!0),Gr()},hn=()=>{F(!0),Rr()},Fe=()=>{Dr()},mn=()=>{es()},un=()=>{F(!0),ss()},He=()=>{F(!0),rs()},gn=()=>{F(!0),ls()},ts=()=>{(i==null?void 0:i.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Create"||(l==null?void 0:l.processDesc)==="Create")?(F(!0),cs()):(i==null?void 0:i.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Change"||(l==null?void 0:l.processDesc)==="Change")?(F(!0),as()):(i==null?void 0:i.role)==="Approver"&&((n==null?void 0:n.requestType)==="Create"||(l==null?void 0:l.processDesc)==="Create")?(F(!0),ds()):(i==null?void 0:i.role)==="Approver"&&((n==null?void 0:n.requestType)==="Change"||(l==null?void 0:l.processDesc)==="Change")&&(F(!0),hs())},cs=()=>{const s=o=>{F(!1),o.statusCode===200?(console.log("success"),b("Create"),v(`Profit Center Submitted for Correction with ID NPS${o.body}`),q(!1),S("success"),P(!1),N(!0),K(),I(!0)):(b("Error"),N(!1),v("Failed Submitting Profit Center for Correction"),q(!1),S("danger"),P(!1),I(!0),M()),re()},c=o=>{console.log(o)};z(`/${j}/alter/profitCenterSendForCorrection`,"post",s,c,Y)},as=()=>{const s=o=>{F(!1),o.statusCode===200?(console.log("success"),b("Create"),v(`Profit Center Submitted for Correction with ID CPS${o.body}`),q(!1),S("success"),P(!1),N(!0),K(),I(!0)):(b("Error"),N(!1),v("Failed Submitting Profit Center for Correction"),q(!1),S("danger"),P(!1),I(!0),M()),re()},c=o=>{console.log(o)};z(`/${j}/alter/changeProfitCenterSendForCorrection`,"post",s,c,Y)},ds=()=>{const s=o=>{F(!1),o.statusCode===200?(console.log("success"),b("Create"),v(`Profit Center Submitted for Correction with ID NPS${o.body}`),q(!1),S("success"),P(!1),N(!0),K(),I(!0)):(b("Error"),N(!1),v("Failed Submitting Profit Center for Correction"),q(!1),S("danger"),P(!1),I(!0),M()),re()},c=o=>{console.log(o)};z(`/${j}/alter/profitCenterSendForReview`,"post",s,c,Y)},hs=()=>{const s=o=>{F(!1),o.statusCode===200?(console.log("success"),b("Create"),v(`Profit Center Submitted for Correction with ID CPS${o.body}`),q(!1),S("success"),P(!1),N(!0),K(),I(!0)):(b("Error"),N(!1),v("Failed Submitting Profit Center for Correction"),q(!1),S("danger"),P(!1),I(!0),M()),re()},c=o=>{console.log(o)};z(`/${j}/alter/changeProfitCenterSendForReview`,"post",s,c,Y)},qe=()=>{G(!1),Qe(!0)},re=()=>{Ne(""),G(!0),Qe(!1)},pn=(s,c)=>{const o=s.target.value;if(o.length>0&&o[0]===" ")Ne(o.trimStart());else{let t=o.toUpperCase();Ne(t)}},w=()=>{Ne(""),G(!0),Ge(!1)},J=()=>{G(!1),Ge(!0)},ms=()=>{(i==null?void 0:i.role)==="Finance"&&((n==null?void 0:n.requestType)==="Create"||(l==null?void 0:l.processDesc)==="Create")&&f?(w(),hn()):(i==null?void 0:i.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Create"||(l==null?void 0:l.processDesc)==="Create")&&!f?(w(),is()):(i==null?void 0:i.role)==="Approver"&&((n==null?void 0:n.requestType)==="Create"||(l==null?void 0:l.processDesc)==="Create")&&!f?(w(),dn()):(i==null?void 0:i.role)==="Finance"&&!(n!=null&&n.requestType)&&f||(i==null?void 0:i.role)==="Finance"&&((n==null?void 0:n.requestType)==="Change"||(l==null?void 0:l.processDesc)==="Change")&&f?(w(),He()):(i==null?void 0:i.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Change"||(l==null?void 0:l.processDesc)==="Change")&&!f?(w(),gn()):(i==null?void 0:i.role)==="Approver"&&((n==null?void 0:n.requestType)==="Change"||(l==null?void 0:l.processDesc)==="Change")&&!f&&(w(),un())},us=()=>{let s=l!=null&&l.subject?l==null?void 0:l.subject:n==null?void 0:n.requestId,c=o=>{console.log(o.documentDetailDtoList,"data.documentDetailDtoList");var t=[];o.documentDetailDtoList.forEach(p=>{console.log(o.documentDetailDtoList,"data.");var y={id:p.documentId,docType:p.fileType,docName:p.fileName,uploadedOn:Ye(p.docCreationDate).format(vr.date),uploadedBy:p.createdBy};console.log(y,"tempRow"),t.push(y)}),Nr(t)};z(`/${fe}/documentManagement/getDocByRequestId/${s}`,"get",c)},gs=s=>{Re(s)},ps=()=>{Re(!0)},xs=()=>{let s=T[h];console.log("activeTabName",s,T);let c=Object.entries(Ve);console.log("viewDataArray",c);const o={};c.map(t=>{console.log("bottle",t[1]);let p=Object.entries(t[1]);return console.log("notebook",p),p.forEach(y=>{y[1].forEach(E=>{o[E.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=E.value})}),t}),console.log("toSetArray",o),D(Ts(o))},fs=(s,c)=>{console.log("getvalueforfieldname",s,c);const o=s==null?void 0:s.find(t=>(t==null?void 0:t.fieldName)===c);return o?o.value:""},Cs=()=>{let s=l!=null&&l.subject?l==null?void 0:l.subject:n==null?void 0:n.requestId,c=t=>{console.log("commentsdata",t);var p=[];t.body.forEach(y=>{var E={id:y.requestId,comment:y.comment,user:y.createdByUser,createdAt:y.updatedAt};p.push(E)}),Pr(p),console.log("commentrows",p.length)},o=t=>{console.log(t)};z(`/${j}/activitylog/fetchTaskDetailsForRequestId?requestId=${s}`,"get",c,o)};g.useEffect(()=>{us(),Cs()},[]),console.log("factorsarray",T);const xn=T.map(s=>{const c=Le.filter(o=>{var t;return((t=o.category)==null?void 0:t.split(" ")[0])==(s==null?void 0:s.split(" ")[0])});if(c.length!=0)return{category:s==null?void 0:s.split(" ")[0],data:c[0].data}}).map((s,c)=>{if((s==null?void 0:s.category)=="Basic"&&h==0)return[e(u,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ae},children:[e(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),e(X,{sx:{width:"100%"},children:e(de,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(u,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(t=>(console.log("fieldDatatttt",t),e(Ce,{label:t.fieldName,value:t.value,length:t.maxLength,data:r,visibility:t.visibility,onSave:p=>handleFieldSave(t.fieldName,p),isEditMode:f,type:t.fieldType,field:t,taskRequestId:n==null?void 0:n.requestId})))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Indicators"&&h==1)return[e(u,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ae},children:[e(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),e(X,{sx:{width:"100%"},children:e(de,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(u,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(t=>e(Ce,{label:t.fieldName,value:t.value,length:t.maxLength,data:r,visibility:t.visibility,onSave:p=>handleFieldSave(t.fieldName,p),isEditMode:f,type:t.fieldType,field:t,taskRequestId:n==null?void 0:n.requestId}))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Comp"&&h==2)return[e(u,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ae},children:[e(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),e(X,{sx:{width:"100%"},children:e(de,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(Ys,{displayCompCode:me})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Address"&&h==3)return[e(u,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ae},children:[e(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),e(X,{sx:{width:"100%"},children:e(de,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(u,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(t=>e(Ce,{label:t.fieldName,value:t.value,length:t.maxLength,data:r,visibility:t.visibility,onSave:p=>handleFieldSave(t.fieldName,p),isEditMode:f,type:t.fieldType,field:t,taskRequestId:n==null?void 0:n.requestId}))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Communication"&&h==4)return[e(u,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ae},children:[e(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),e(X,{sx:{width:"100%"},children:e(de,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(u,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(t=>e(Ce,{label:t.fieldName,value:t.value,length:t.maxLength,data:r,visibility:t.visibility,onSave:p=>handleFieldSave(t.fieldName,p),isEditMode:f,type:t.fieldType,field:t,taskRequestId:n==null?void 0:n.requestId}))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="History"&h==5)return[e(u,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ae},children:[e(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),e(X,{sx:{width:"100%"},children:e(de,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(u,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(t=>e(Ce,{label:t.fieldName,value:t.value,length:t.maxLength,data:r,visibility:t.visibility,onSave:p=>handleFieldSave(t.fieldName,p),isEditMode:f,type:t.fieldType,field:t,taskRequestId:n==null?void 0:n.requestId}))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Attachments"&&h==6)return[e(H,{children:f?d(H,{children:[e(Is,{title:"ProfitCenter",useMetaData:!1,artifactId:ge,artifactName:"ProfitCenter"}),d(Be,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[e(u,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:e(C,{variant:"h6",children:e("strong",{children:"Attachments"})})}),!!ie.length&&e(Je,{width:"100%",rows:ie,columns:on,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!ie.length&&e(C,{variant:"body2",children:"No Attachments Found"}),e("br",{}),e(C,{variant:"h6",children:"Comments"}),!!oe.length&&e(dr,{sx:{[`& .${Zn.root}:before`]:{flex:0,padding:0}},children:oe.map(o=>d(Qn,{children:[d(Gn,{children:[e(wn,{children:e(Rn,{sx:{color:"#757575"}})}),e(Dn,{})]}),e(er,{sx:{py:"12px",px:2},children:e(Be,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:e(X,{sx:{padding:"1rem"},children:d(he,{spacing:1,children:[e(u,{sx:{display:"flex",justifyContent:"space-between"},children:e(C,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ye(o.createdAt).format("DD MMM YYYY")})}),e(C,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),e(C,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!oe.length&&e(C,{variant:"body2",children:"No Comments Found"}),e("br",{})]})]}):d(Be,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[e(u,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:e(C,{variant:"h6",children:e("strong",{children:"Attachments"})})}),!!ie.length&&e(Je,{width:"100%",rows:ie,columns:on,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!ie.length&&e(C,{variant:"body2",children:"No Attachments Found"}),e("br",{}),e(C,{variant:"h6",children:"Comments"}),!!oe.length&&e(dr,{sx:{[`& .${Zn.root}:before`]:{flex:0,padding:0}},children:oe.map(o=>d(Qn,{children:[d(Gn,{children:[e(wn,{children:e(Rn,{sx:{color:"#757575"}})}),e(Dn,{})]}),e(er,{sx:{py:"12px",px:2},children:e(Be,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:e(X,{sx:{padding:"1rem"},children:d(he,{spacing:1,children:[e(u,{sx:{display:"flex",justifyContent:"space-between"},children:e(C,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ye(o.createdAt).format("DD MMM YYYY")})}),e(C,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),e(C,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!oe.length&&e(C,{variant:"body2",children:"No Comments Found"}),e("br",{})]})})]});return e(H,{children:pr===!0?e(Es,{}):d("div",{style:{backgroundColor:"#FAFCFF"},children:[e(Fs,{dialogState:fr,openReusableDialog:M,closeReusableDialog:We,dialogTitle:Cr,dialogMessage:Ze,handleDialogConfirm:We,dialogOkText:"OK",showCancelButton:!0,showExtraButton:Ir,dialogSeverity:mr,handleDialogReject:()=>{Se(!1)},handleExtraText:Er,handleExtraButton:ns}),ur&&e(nr,{openSnackBar:gr,alertMsg:Ze,handleSnackBarClose:os}),en.length!=0&&e(nr,{openSnackBar:Mr,alertMsg:"Please fill the following Field: "+en.join(", "),handleSnackBarClose:Wr}),d(rr,{open:yr,onClose:re,hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},children:[d(sr,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[e(C,{variant:"h6",children:"Remarks"}),e(Ke,{sx:{width:"max-content"},onClick:re,children:e(lr,{})})]}),e(or,{sx:{padding:".5rem 1rem"},children:e(he,{children:e(X,{sx:{minWidth:400},children:e(ir,{sx:{height:"auto"},fullWidth:!0,children:e(tr,{sx:{backgroundColor:"#F5F5F5"},value:Pe,onChange:pn,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),d(cr,{sx:{display:"flex",justifyContent:"end"},children:[e(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:re,children:"Cancel"}),e(a,{className:"button_primary--normal",type:"save",onClick:ts,variant:"contained",children:"OK"})]})]}),d(rr,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Ar,onClose:w,children:[d(sr,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[e(C,{variant:"h6",children:"Remarks"}),e(Ke,{sx:{width:"max-content"},onClick:w,children:e(lr,{})})]}),e(or,{sx:{padding:".5rem 1rem"},children:e(he,{children:e(X,{sx:{minWidth:400},children:e(ir,{sx:{height:"auto"},fullWidth:!0,children:e(tr,{sx:{backgroundColor:"#F5F5F5"},value:Pe,onChange:pn,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),d(cr,{sx:{display:"flex",justifyContent:"end"},children:[e(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:w,children:"Cancel"}),e(a,{className:"button_primary--normal",type:"save",onClick:ms,variant:"contained",children:"Submit"})]})]}),e(qs,{sx:{color:"#fff",zIndex:s=>s.zIndex.drawer+1},open:xr,children:e(zs,{color:"inherit"})}),d(u,{container:!0,sx:Bs,children:[d(u,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[d(u,{md:12,sx:{display:"flex"},children:[e(u,{children:e(Ke,{color:"primary","aria-label":"upload picture",component:"label",sx:$s,children:e(js,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{rn(-1)}})})}),d(u,{children:[!(n!=null&&n.requestType)&&f?d(u,{item:!0,md:12,children:[e(C,{variant:"h3",children:e("strong",{children:"Change Profit Center "})}),e(C,{variant:"body2",color:"#777",children:"This view changes the details of the Profit Center"})]}):"",f&&(n==null?void 0:n.requestType)==="Change"?d(u,{item:!0,md:12,children:[e(C,{variant:"h3",children:e("strong",{children:"Change Profit Center "})}),e(C,{variant:"body2",color:"#777",children:"This view changes the details of the Profit Center"})]}):"",f&&(n==null?void 0:n.requestType)==="Create"?d(u,{item:!0,md:12,children:[e(C,{variant:"h3",children:e("strong",{children:"Create Profit Center "})}),e(C,{variant:"body2",color:"#777",children:"This view creates a new Profit Center"})]}):"",ne?d(u,{item:!0,md:12,children:[e(C,{variant:"h3",children:e("strong",{children:"Display Profit Center "})}),e(C,{variant:"body2",color:"#777",children:"This view displays the details of the Profit Center"})]}):""]})]}),d(u,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:[n!=null&&n.requestId||l!=null&&l.processDesc?e(u,{children:e(a,{variant:"outlined",size:"small",sx:A,onClick:ps,title:"Change Log",children:e(Ms,{sx:{padding:"2px"},fontSize:"small"})})}):"",Fr&&e(Os,{open:!0,closeModal:gs,requestId:n!=null&&n.requestId?n==null?void 0:n.requestId:l==null?void 0:l.subject,requestType:n!=null&&n.requestType?n==null?void 0:n.requestType:(bn=m==null?void 0:m.body)==null?void 0:bn.processDesc,pageName:"profitCenter",controllingArea:n!=null&&n.controllingArea?n==null?void 0:n.controllingArea:(yn=m==null?void 0:m.body)==null?void 0:yn.controllingArea,centerName:n!=null&&n.profitCenter?n==null?void 0:n.profitCenter:(vn=m==null?void 0:m.body)==null?void 0:vn.profitCenter}),$e(Te,"Profit Center","ChangePC")&&((i==null?void 0:i.role)==="Super User"&&(n!=null&&n.requestType)&&((Sn=l==null?void 0:l.itmStatus)==null?void 0:Sn.toUpperCase())!=="OPEN"&&ne?e(u,{gap:1,sx:{display:"flex"},children:e(u,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:e(H,{children:e(u,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:A,onClick:Ie,children:["Fill Details",e(je,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(i==null?void 0:i.role)==="Finance"&&(n!=null&&n.requestType||l!=null&&l.processDesc)&&((Pn=l==null?void 0:l.itmStatus)==null?void 0:Pn.toUpperCase())!=="OPEN"&&ne?e(u,{gap:1,sx:{display:"flex"},children:e(u,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:e(H,{children:e(u,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:A,onClick:Ie,children:["Fill Details",e(je,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(i==null?void 0:i.role)==="Super User"&&!(n!=null&&n.requestType)&&((Nn=l==null?void 0:l.itmStatus)==null?void 0:Nn.toUpperCase())!=="OPEN"&&ne?e(u,{gap:1,sx:{display:"flex"},children:e(u,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:e(H,{children:e(u,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:A,onClick:Ie,children:["Change",e(je,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(i==null?void 0:i.role)==="Finance"&&!(n!=null&&n.requestType)&&((An=l==null?void 0:l.itmStatus)==null?void 0:An.toUpperCase())!=="OPEN"&&ne?e(u,{gap:1,sx:{display:"flex"},children:e(u,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:e(H,{children:e(u,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:A,onClick:Ie,children:["Change",e(je,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")]})]}),e(u,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:d(X,{width:"70%",sx:{marginLeft:"40px"},children:[e(u,{item:!0,sx:{paddingTop:"2px !important"},children:d(he,{flexDirection:"row",children:[e("div",{style:{width:"10%"},children:e(C,{variant:"body2",color:"#777",children:"Profit Center"})}),d(C,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",n!=null&&n.profitCenter?n==null?void 0:n.profitCenter:(Tn=m==null?void 0:m.body)==null?void 0:Tn.profitCenter]})]})}),e(u,{item:!0,sx:{paddingTop:"2px !important"},children:d(he,{flexDirection:"row",children:[e("div",{style:{width:"10%"},children:e(C,{variant:"body2",color:"#777",children:"Controlling Area"})}),d(C,{variant:"body2",fontWeight:"bold",children:[":"," ",n!=null&&n.controllingArea?n==null?void 0:n.controllingArea:(In=m==null?void 0:m.body)==null?void 0:In.controllingArea]})]})})]})}),d(u,{container:!0,style:{marginLeft:25},children:[e(Ls,{activeStep:h,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:T.map((s,c)=>e(ks,{children:e(_s,{sx:{fontWeight:"700"},children:s})},s))}),xn&&((En=xn[h])==null?void 0:En.map((s,c)=>e(X,{sx:{mb:2,width:"100%"},children:e(C,{variant:"body2",children:s})},c)))]})]}),d(u,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[$e(Te,"Profit Center","ChangePC")&&(!(n!=null&&n.requestType)&&!f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})})),$e(Te,"Profit Center","ChangePC")&&((i==null?void 0:i.role)==="Super User"&&!(n!=null&&n.requestType)&&((Fn=l==null?void 0:l.itmStatus)==null?void 0:Fn.toUpperCase())!=="OPEN"&&f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[l!=null&&l.taskId?"":e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:Fe,children:"Save As Draft"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),h===T.length-1?d(H,{children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:te,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:He,disabled:Q,children:"Submit For Review"})]}):e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Finance"&&!(n!=null&&n.requestType)&&((qn=l==null?void 0:l.itmStatus)==null?void 0:qn.toUpperCase())!=="OPEN"&&f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[l!=null&&l.taskId?"":e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:Fe,children:"Save As Draft"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),h===T.length-1?d(H,{children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:te,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,disabled:Q,children:"Submit For Review"})]}):e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):""),$e(Te,"Profit Center","ChangePC")&&((i==null?void 0:i.role)==="Super User"&&((zn=l==null?void 0:l.itmStatus)==null?void 0:zn.toUpperCase())!=="OPEN"&&(n==null?void 0:n.requestType)==="Create"&&!f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ee,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:dn,disabled:Q,children:"Approve"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,children:"Submit For Approval"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Super User"&&(n==null?void 0:n.requestType)==="Change"&&((Bn=l==null?void 0:l.itmStatus)==null?void 0:Bn.toUpperCase())!=="OPEN"&&!f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ee,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:un,disabled:Q,children:"Approve"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:gn,children:"Submit For Approval"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Create"||(l==null?void 0:l.processDesc)==="Create")&&(($n=l==null?void 0:l.itmStatus)==null?void 0:$n.toUpperCase())!=="OPEN"&&!f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"outlined",size:"small",sx:{button_Outlined:A,mr:1},onClick:qe,children:"Correction"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,children:"Submit For Approval"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Change"||(l==null?void 0:l.processDesc)==="Change")&&((jn=l==null?void 0:l.itmStatus)==null?void 0:jn.toUpperCase())!=="OPEN"&&!f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"outlined",size:"small",sx:{button_Outlined:A,mr:1},onClick:qe,children:"Correction"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,children:"Submit For Approval"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Approver"&&((n==null?void 0:n.requestType)==="Create"||(l==null?void 0:l.processDesc)==="Create")&&((Mn=l==null?void 0:l.itmStatus)==null?void 0:Mn.toUpperCase())!=="OPEN"&&!f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"outlined",size:"small",sx:{button_Outlined:A,mr:1},onClick:qe,children:"Correction"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ee,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,disabled:Q,children:"Approve"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Approver"&&((n==null?void 0:n.requestType)==="Change"||(l==null?void 0:l.processDesc)==="Change")&&((On=l==null?void 0:l.itmStatus)==null?void 0:On.toUpperCase())!=="OPEN"&&!f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"outlined",size:"small",sx:{button_Outlined:A,mr:1},onClick:qe,children:"Correction"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ee,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,disabled:Q,children:"Approve"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Super User"&&(n==null?void 0:n.requestType)==="Create"&&((Ln=l==null?void 0:l.itmStatus)==null?void 0:Ln.toUpperCase())!=="OPEN"&&f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),h===T.length-1?e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:hn,children:"Submit For Review"}):e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Super User"&&(n==null?void 0:n.requestType)==="Change"&&((kn=l==null?void 0:l.itmStatus)==null?void 0:kn.toUpperCase())!=="OPEN"&&f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,children:"Submit For Review"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Finance"&&((n==null?void 0:n.requestType)==="Create"||(l==null?void 0:l.processDesc)==="Create")&&((_n=l==null?void 0:l.itmStatus)==null?void 0:_n.toUpperCase())!=="OPEN"&&f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[l!=null&&l.taskId?"":e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:mn,children:"Save As Draft"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),h===T.length-1?d(H,{children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:te,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,disabled:Q,children:"Submit For Review"})]}):e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Finance"&&((n==null?void 0:n.requestType)==="Change"||(l==null?void 0:l.processDesc)==="Change")&&((Un=l==null?void 0:l.itmStatus)==null?void 0:Un.toUpperCase())!=="OPEN"&&f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[l!=null&&l.taskId?"":e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:Fe,children:"Save As Draft"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),h===T.length-1?d(H,{children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:te,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,disabled:Q,children:"Submit For Review"})]}):e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Finance"&&(n==null?void 0:n.requestType)==="Create"&&(n==null?void 0:n.reqStatus)==="Draft"&&f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[l!=null&&l.taskId?"":e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:mn,children:"Save As Draft"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),h===T.length-1?d(H,{children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:te,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,disabled:Q,children:"Submit For Review"})]}):e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):(i==null?void 0:i.role)==="Finance"&&(n==null?void 0:n.requestType)==="Change"&&(n==null?void 0:n.reqStatus)==="Draft"&&f?e(k,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[l!=null&&l.taskId?"":e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:Fe,children:"Save As Draft"}),e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:O,disabled:h===0,children:"Back"}),h===T.length-1?d(H,{children:[e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:te,children:"Validate"}),e(a,{variant:"contained",size:"small",sx:{button_Outlined:A,mr:1},onClick:J,disabled:Q,children:"Submit For Review"})]}):e(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:h===T.length-1,children:"Next"})]})}):"")]})]})})};export{Rs as default};
