import{r as l,b as Gt,u as Ot,l as Vt,i as z,hZ as $t,hN as q,j as f,a as t,fV as ee,h1 as te,fH as ae,hl as zt,g_ as oe,hC as xe,$ as w,F as qt,ih as et,T as g,ht as Jt,hI as tt,W as J,B as at,gQ as jt,a3 as Ut,fX as se,a1 as E,J as Wt,i7 as Ht,hp as Xt,hv as Kt,i8 as Qt,h as Zt,kf as Yt,i6 as xt,hE as ea,gW as ne,P as ta,h8 as aa,ho as oa,w as A,hL as O,hP as sa,ik as ot}from"./index-fdfa25a0.js";const ca=()=>{var be,ye,Se,Ae;const[j,re]=l.useState(!0),[st,D]=l.useState(!1),[nt,na]=l.useState("1"),[U,rt]=l.useState([]),[it,y]=l.useState(!1),[lt,ie]=l.useState(!1),[ct,k]=l.useState(!1),[ra,R]=l.useState(!1),[dt,W]=l.useState(!1),[ut,le]=l.useState(!1),[ce,N]=l.useState(""),[mt,T]=l.useState(!1),[ia,M]=l.useState(!0),[Ct,de]=l.useState(!1),[B,ft]=l.useState([]),[ue,me]=l.useState(!0),[pt,Ce]=l.useState(!1),[ht,fe]=l.useState(!1),[H,gt]=l.useState(""),[X,pe]=l.useState(""),[he,L]=l.useState(!1),[Dt,vt]=l.useState([]),K=Gt();Ot();const Pt=Vt(),b=z(e=>e.profitCenter.MultipleProfitCenterData),h=z(e=>e.appSettings);let S=z(e=>e.profitCenter.handleMassMode);console.log("massHandleType",S);let m=z(e=>e.userManagement.userData);const[ge,wt]=l.useState(0),bt=(e,o)=>{const a=d=>{Pt(sa({keyName:e,data:d.body})),wt(s=>s+1)},i=d=>{console.log(d)};A(`/${O}/data/${o}`,"get",a,i)},yt=()=>{var e,o;(o=(e=et)==null?void 0:e.profitCenter)==null||o.map(a=>{bt(a==null?void 0:a.keyName,a==null?void 0:a.endPoint)})},St=()=>{var e,o;ge==((o=(e=et)==null?void 0:e.profitCenter)==null?void 0:o.length)?re(!1):re(!0)};l.useEffect(()=>{St()},[ge]),l.useEffect(()=>{gt($t("PC"))},[]),l.useEffect(()=>{yt()},[]);const At=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1,renderCell:e=>{const o=B.find(a=>a.profitCenter===e.value);return console.log(o,"isDirectMatch"),console.log(e,"params"),o&&o.code===400?t(g,{sx:{fontSize:"12px",color:"red"},children:e.value}):t(g,{sx:{fontSize:"12px"},children:e.value})}},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"profitCenterName",headerName:"Profit Center Name",editable:!1,flex:1,renderCell:e=>{const o=Dt.includes(e.row.profitCenterName);return t(g,{sx:{fontSize:"12px",color:o?"red":"inherit"},children:e.value})}},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"profitCenterGroup",headerName:"Profit Center Group",editable:!1,flex:1},{field:"analysisPeriodFrom",headerName:"Ananlysis Period From",editable:!1,flex:1,renderCell:e=>t(g,{sx:{fontSize:"12px"},children:q(e.row.analysisPeriodFrom).format(h==null?void 0:h.dateFormat)})},{field:"analysisPeriodTo",headerName:"Analysis Period To",editable:!1,flex:1,renderCell:e=>t(g,{sx:{fontSize:"12px"},children:q(e.row.analysisPeriodTo).format(h==null?void 0:h.dateFormat)})}];console.log("multipleProfitData",b);const Q=()=>{W(!0)},kt=()=>{fe(!0)},De=()=>{fe(!1)},V=()=>{le(!0)},ve=()=>{le(!1)},Rt=()=>{},Nt=()=>{lt?(W(!1),ie(!1)):(W(!1),K("/masterDataCockpit/profitCenter"))},n=(e,o)=>{console.log("getvalueforfieldname",e,o);const a=e==null?void 0:e.find(i=>(i==null?void 0:i.fieldName)===o);return a?a.value:""},Tt=e=>{e.length>0?(L(!0),console.log("selectedIds1",e)):L(!1),console.log("selectedIds",e),rt(e)};let Z=(Se=(ye=(be=b[0])==null?void 0:be.viewData)==null?void 0:ye["Comp Codes"])==null?void 0:Se["Company Code Assignment for Profit Center"];const G=b==null?void 0:b.map((e,o)=>{var d,s,u,v,P,C;const a=e,i=((d=e==null?void 0:e.viewData)==null?void 0:d["Basic Data"])||{};return{id:o,profitCenter:a==null?void 0:a.profitCenter,controllingArea:a==null?void 0:a.controllingArea,profitCenterName:((s=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Name"))==null?void 0:s.value)||"",personResponsible:((u=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Person Responsible"))==null?void 0:u.value)||"",profitCenterGroup:((v=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Profit Ctr Group"))==null?void 0:v.value)||"",analysisPeriodFrom:q((P=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Analysis Period From"))==null?void 0:P.value).format(h==null?void 0:h.dateFormat)||"",analysisPeriodTo:q((C=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Analysis Period To"))==null?void 0:C.value).format(h==null?void 0:h.dateFormat)||""}});_.zip(Z[0].value,Z[1].value,Z[2].value).map(e=>({CompCodeID:0,CompCode:e[0],CompanyName:e[1],AssignToPrctr:e[2]==!0?"X":"",Venture:"",RecInd:"",EquityTyp:"",JvOtype:"",JvJibcl:"",JvJibsa:""}));var I=b.map(e=>{var o,a,i,d,s,u,v,P,C,c,r,p,F,ke,Re,Ne,Te,Me,Ie,Fe,Ee,Be,_e,Le,Ge,Oe,Ve,$e,ze,qe,Je,je,Ue,We,He,Xe,Ke,Qe,Ze,Ye;return console.log("samsung",e),{ProfitCenterID:"",Action:S==="Create"?"I":"U",RequestID:"",TaskStatus:"",TaskId:"",remarks:X||"",ReqCreatedBy:m==null?void 0:m.user_id,ReqCreatedOn:m!=null&&m.createdOn?"/Date("+(m==null?void 0:m.createdOn)+")/":"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:S==="Create"?"Mass Create":"Mass Change",MassRequestStatus:"",PrctrName:n((o=e==null?void 0:e.viewData["Basic Data"])==null?void 0:o["General Data"],"Name"),LongText:n((a=e==null?void 0:e.viewData["Basic Data"])==null?void 0:a["General Data"],"Long Text"),InChargeUser:n((i=e==null?void 0:e.viewData["Basic Data"])==null?void 0:i["General Data"],"User Responsible"),InCharge:n((d=e==null?void 0:e.viewData["Basic Data"])==null?void 0:d["General Data"],"Person Responsible"),Department:n((s=e==null?void 0:e.viewData["Basic Data"])==null?void 0:s["General Data"],"Department"),PrctrHierGrp:n((u=e==null?void 0:e.viewData["Basic Data"])==null?void 0:u["General Data"],"Profit Ctr Group"),Segment:n((v=e==null?void 0:e.viewData["Basic Data"])==null?void 0:v["General Data"],"Segment"),LockInd:n((P=e==null?void 0:e.viewData.Indicators)==null?void 0:P.Indicator,"Lock indicator")===!0?"X":"",Template:n((C=e==null?void 0:e.viewData.Indicators)==null?void 0:C["Formula Planning"],"Form. Planning Temp"),Title:n((c=e==null?void 0:e.viewData.Address)==null?void 0:c["Address Data"],"Title"),Name1:n((r=e==null?void 0:e.viewData.Address)==null?void 0:r["Address Data"],"Name 1"),Name2:n((p=e==null?void 0:e.viewData.Address)==null?void 0:p["Address Data"],"Name 2"),Name3:n((F=e==null?void 0:e.viewData.Address)==null?void 0:F["Address Data"],"Name 3"),Name4:n((ke=e==null?void 0:e.viewData.Address)==null?void 0:ke["Address Data"],"Name 4"),Street:n((Re=e==null?void 0:e.viewData.Address)==null?void 0:Re["Address Data"],"Street"),City:n((Ne=e==null?void 0:e.viewData.Address)==null?void 0:Ne["Address Data"],"City"),District:n((Te=e==null?void 0:e.viewData.Address)==null?void 0:Te["Address Data"],"District"),Country:n((Me=e==null?void 0:e.viewData.Address)==null?void 0:Me["Address Data"],"Country/Reg."),Taxjurcode:n((Ie=e==null?void 0:e.viewData.Address)==null?void 0:Ie["Address Data"],"Tax Jur."),PoBox:n((Fe=e==null?void 0:e.viewData.Address)==null?void 0:Fe["Address Data"],"P.O.Box"),PostlCode:n((Ee=e==null?void 0:e.viewData.Address)==null?void 0:Ee["Address Data"],"Postal Code"),PobxPcd:n((Be=e==null?void 0:e.viewData.Address)==null?void 0:Be["Address Data"],"PO Box PCode"),Region:n((_e=e==null?void 0:e.viewData.Address)==null?void 0:_e["Address Data"],"Region"),Langu:n((Le=e==null?void 0:e.viewData.Communication)==null?void 0:Le["Communication Data"],"Language"),Telephone:n((Ge=e==null?void 0:e.viewData.Communication)==null?void 0:Ge["Communication Data"],"Telephone 1"),Telephone2:n((Oe=e==null?void 0:e.viewData.Communication)==null?void 0:Oe["Communication Data"],"Telephone 2"),Telebox:n((Ve=e==null?void 0:e.viewData.Communication)==null?void 0:Ve["Communication Data"],"Telebox"),Telex:n(($e=e==null?void 0:e.viewData.Communication)==null?void 0:$e["Communication Data"],"Telex"),FaxNumber:n((ze=e==null?void 0:e.viewData.Communication)==null?void 0:ze["Communication Data"],"Fax Number"),Teletex:n((qe=e==null?void 0:e.viewData.Communication)==null?void 0:qe["Communication Data"],"Teletex"),Printer:n((Je=e==null?void 0:e.viewData.Communication)==null?void 0:Je["Communication Data"],"Printer name"),DataLine:n((je=e==null?void 0:e.viewData.Communication)==null?void 0:je["Communication Data"],"Data line"),ProfitCenter:e==null?void 0:e.profitCenter,ControllingArea:e==null?void 0:e.controllingArea,ValidfromDate:n((Ue=e==null?void 0:e.viewData["Basic Data"])==null?void 0:Ue["General Data"],"Analysis Period From"),ValidtoDate:n((We=e==null?void 0:e.viewData["Basic Data"])==null?void 0:We["General Data"],"Analysis Period To"),Testrun:he,Countryiso:"",LanguIso:"",Logsystem:"",ToCompanycode:_.zip((Xe=(He=e==null?void 0:e.viewData)==null?void 0:He["Comp Codes"])==null?void 0:Xe["Company Code Assignment for Profit Center"][0].value,(Qe=(Ke=e==null?void 0:e.viewData)==null?void 0:Ke["Comp Codes"])==null?void 0:Qe["Company Code Assignment for Profit Center"][1].value,(Ye=(Ze=e==null?void 0:e.viewData)==null?void 0:Ze["Comp Codes"])==null?void 0:Ye["Company Code Assignment for Profit Center"][2].value).map(x=>({CompCodeID:"",CompCode:x[0],CompanyName:x[1],AssignToPrctr:(x[2]===!0,"X"),Venture:"",RecInd:"",EquityTyp:"",JvOtype:"",JvJibcl:"",JvJibsa:""}))}});const Mt=(e,o)=>e.every(a=>!o.includes(a)),It=()=>{const e=G.filter((s,u)=>U.includes(u));console.log("selectedData",e);const o=e.map(s=>({...I[s==null?void 0:s.id]}));let a=I;a=o,console.log("selectedProfitCenterRows",o);const i=s=>{if(s.statusCode===200){console.log("success"),y("Create"),N(`Mass Profit Center Sent for Review with ID NPM${s.body}`),T("success"),M(!1),k(!0),Q(),R(!0),D(!1);const u={artifactId:H,createdBy:m==null?void 0:m.emailId,artifactType:"ProfitCenter",requestId:`NPM${s==null?void 0:s.body}`},v=C=>{console.log("Second API success",C)},P=C=>{console.error("Second API error",C)};A(`/${ot}/documentManagement/updateDocRequestId`,"post",v,P,u)}else y("Error"),k(!1),N("Failed Submitting the Profit Center for Review "),T("danger"),M(!1),R(!0),V(),D(!1);handleClose(),D(!1)},d=s=>{console.log(s)};A(`/${O}/massAction/profitCentersSubmitForReview`,"post",i,d,a)},Ft=()=>{D(!0);const e=G.filter((r,p)=>U.includes(p));console.log("selectedData",e);const o=e.map(r=>({...I[r==null?void 0:r.id]}));console.log("selectedProfitCenterRows",o);const a=[],i=[];o.map(r=>{var p={coArea:r==null?void 0:r.ControllingArea,name:r==null?void 0:r.PrctrName.toUpperCase()};a.push(p),i.push(r==null?void 0:r.PrctrName.toUpperCase())});const d=[];b.map(r=>{var p;d.push((p=r.profitCenterName)==null?void 0:p.toUpperCase())});let s=Mt(d,i);console.log(s,"isuserEditPCName"),console.log("duplicateCheckPayload",a.coArea),console.log(i,"duplicatePCName");let u=I;u=o;const v=r=>{r.statusCode===400?(ft(r.body),de(!0),D(!1)):(y("Create"),console.log("success"),y("Create"),N("All Data has been Validated. Profit Center can be Send for Review"),T("success"),M(!1),k(!0),Q(),R(!0),ie(!0),(a.coArea!==""||a.name!=="")&&A(`/${O}/alter/fetchPCDescriptionsDupliChk`,"post",P,C,a))},P=r=>{if(console.log("dataaaa",r),r.body.length===0||!r.body.some(p=>a.some(F=>p.matches.includes(F.name.toUpperCase()))))alert(1),D(!1),me(!1),L(!0);else{alert(2);const p=r.body.map(F=>F.matches[0]);D(!1),y("Duplicate Check"),k(!1),N("There is a direct match for the Profit Center name."),T("danger"),M(!1),R(!0),V(),me(!0),vt(p)}},C=r=>{console.log(r)},c=r=>{console.log(r)};A(`/${O}/massAction/validateMassProfitCenter`,"post",v,c,u)},Et=()=>{const e=G.filter((s,u)=>U.includes(u));console.log("selectedData",e);const o=e.map(s=>({...I[s==null?void 0:s.id]}));let a=I;a=o,console.log("selectedProfitCenterRows",o);const i=s=>{if(s.statusCode===200){console.log("success"),y("Create"),N(`Mass Profit Center Sent for Review with ID CPM${s.body}`),T("success"),M(!1),k(!0),Q(),R(!0),D(!1);const u={artifactId:H,createdBy:m==null?void 0:m.emailId,artifactType:"ProfitCenter",requestId:`CPM${s==null?void 0:s.body}`},v=C=>{console.log("Second API success",C)},P=C=>{console.error("Second API error",C)};A(`/${ot}/documentManagement/updateDocRequestId`,"post",v,P,u)}else y("Error"),k(!1),N("Failed Submitting the Profit Center for Review "),T("danger"),M(!1),R(!0),V(),D(!1);handleClose(),D(!1)},d=s=>{console.log(s)};A(`/${O}/massAction/changeProfitCentersSubmitForReview`,"post",i,d,a)},Pe=()=>{de(!1)},Bt=()=>{D(!0),$(),S==="Create"?It():Et()},_t=(e,o)=>{const a=e.target.value;if(a.length>0&&a[0]===" ")pe(a.trimStart());else{let i=a.toUpperCase();pe(i)}},Lt=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}];console.log("profitValidationError",B);const Y=(Ae=B==null?void 0:B.filter(e=>(e==null?void 0:e.code)===400))==null?void 0:Ae.map((e,o)=>{var a;if(e.code===400)return{id:o,profitCenter:e==null?void 0:e.profitCenter,error:(a=e==null?void 0:e.status)==null?void 0:a.message}});console.log("validationRows",Y);const $=()=>{L(!0),Ce(!1)},we=()=>{L(!1),Ce(!0)};return f(qt,{children:[j===!0?t(Jt,{}):f("div",{children:[f(ee,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:pt,onClose:$,children:[f(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(g,{variant:"h6",children:"Remarks"}),t(J,{sx:{width:"max-content"},onClick:$,children:t(tt,{})})]}),t(se,{sx:{padding:".5rem 1rem"},children:t(oe,{children:t(at,{sx:{minWidth:400},children:t(jt,{sx:{height:"auto"},fullWidth:!0,children:t(Ut,{sx:{backgroundColor:"#F5F5F5"},value:X,onChange:_t,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),f(ae,{sx:{display:"flex",justifyContent:"end"},children:[t(E,{sx:{width:"max-content",textTransform:"capitalize"},onClick:$,children:"Cancel"}),t(E,{className:"button_primary--normal",type:"save",onClick:Bt,variant:"contained",children:"Submit"})]})]}),t(Wt,{dialogState:ut,openReusableDialog:V,closeReusableDialog:ve,dialogTitle:it,dialogMessage:ce,handleDialogConfirm:ve,dialogOkText:"OK",handleExtraButton:Rt,dialogSeverity:mt}),ct&&t(Ht,{openSnackBar:dt,alertMsg:ce,handleSnackBarClose:Nt}),f("div",{style:{...zt,backgroundColor:"#FAFCFF"},children:[t(w,{container:!0,sx:Xt,children:f(w,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[f(w,{item:!0,md:11,sx:{display:"flex"},children:[t(w,{children:t(J,{color:"primary","aria-label":"upload picture",component:"label",sx:Kt,children:t(Qt,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{K("/masterDataCockpit/profitCenter")}})})}),t(w,{children:S==="Create"?f(w,{item:!0,md:12,children:[t(g,{variant:"h3",children:t("strong",{children:"Create Multiple Profit Centers"})}),t(g,{variant:"body2",color:"#777",children:"This view creates multiple Profit Centers"})]}):f(w,{item:!0,md:12,children:[t(g,{variant:"h3",children:t("strong",{children:"Change Multiple Profit Centers"})}),t(g,{variant:"body2",color:"#777",children:"This view changes multiple Profit Centers"})]})})]}),t(w,{item:!0,md:1,sx:{display:"flex"},children:t(Zt,{title:"Upload documents if any",arrow:!0,children:t(J,{onClick:kt,children:t(Yt,{})})})}),f(ee,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:ht,onClose:De,children:[t(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:t(g,{variant:"h6",children:"Add Attachment"})}),t(se,{sx:{padding:".5rem 1rem"},children:t(oe,{children:t(at,{sx:{minWidth:400},children:t(xt,{title:"ProfitCenter",useMetaData:!1,artifactId:H,artifactName:"ProfitCenter"})})})}),t(ae,{children:t(E,{onClick:De,children:"Close"})})]})]})}),t(w,{item:!0,sx:{position:"relative"},children:t(oe,{children:t(xe,{isLoading:j,width:"100%",title:"Profit Center Master List ("+G.length+")",rows:G,columns:At,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Tt,callback_onRowSingleClick:e=>{console.log("paramss",e);const o=e.row.profitCenter,a=b.find(i=>i.profitCenter===o);K(`/masterDataCockpit/profitCenter/createMultipleProfitCenter/editMultipleProfitCenter/${o}`,{state:{tabsData:a,rowData:e.row}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})})]}),t(ta,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:f(ea,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:nt,children:[t(E,{variant:"contained",size:"small",sx:{...ne,mr:1},onClick:Ft,disabled:!he,children:"Validate"}),S==="Create"?t(E,{variant:"contained",size:"small",sx:{...ne},onClick:we,disabled:ue,children:"Submit for Review"}):S==="Change"?t(E,{variant:"contained",size:"small",sx:{...ne},onClick:we,disabled:ue,children:"Submit for Review"}):""]})}),f(ee,{open:Ct,fullWidth:!0,onClose:Pe,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[f(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(g,{variant:"h6",color:"red",children:"Errors"}),t(J,{sx:{width:"max-content"},onClick:Pe,children:t(tt,{})})]}),t(se,{sx:{padding:".5rem 1rem"},children:Y&&t(xe,{isLoading:j,width:"100%",rows:Y,columns:Lt,pageSize:10,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),t(ae,{sx:{display:"flex",justifyContent:"end"}})]})]}),t(oa,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+1},open:st,children:t(aa,{color:"inherit"})})]})};export{ca as default};
