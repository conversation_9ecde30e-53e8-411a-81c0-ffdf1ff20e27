import{mj as T,mk as f,fF as d,ml as c,r,fB as C,as as R,at as a,aj as l,mm as g,mn as h,mo as j}from"./index-fdfa25a0.js";function v(s){return T("MuiTimeline",s)}f("MuiTimeline",["root","positionLeft","positionRight","positionAlternate","positionAlternateReverse"]);const w=["position","className"],y=s=>{const{position:e,classes:o}=s,t={root:["root",e&&c(e)]};return j(t,v,o)},M=d("ul",{name:"MuiTimeline",slot:"Root",overridesResolver:(s,e)=>{const{ownerState:o}=s;return[e.root,o.position&&e[c(o.position)]]}})({display:"flex",flexDirection:"column",padding:"6px 16px",flexGrow:1}),P=r.forwardRef(function(e,o){const t=C({props:e,name:"MuiTimeline"}),{position:i="right",className:m}=t,p=R(t,w),n=a({},t,{position:i}),u=y(n),x=r.useMemo(()=>({position:i}),[i]);return l.jsx(g.Provider,{value:x,children:l.jsx(M,a({className:h(u.root,m),ownerState:n,ref:o},p))})}),N=P;export{N as T};
