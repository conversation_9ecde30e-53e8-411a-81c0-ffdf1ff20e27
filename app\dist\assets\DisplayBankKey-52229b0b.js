import{r as C,i as J,l as qt,it as Ln,a as r,g_ as ee,j as i,T as g,hy as Un,a3 as pe,gR as rr,gS as tr,hJ as nr,$ as x,hA as mt,F as V,is as yn,w as k,iu as $,hP as Se,b as _n,u as Wn,hZ as Vn,h_ as ft,B as D,gZ as Ct,hC as xt,h$ as St,i0 as gt,i1 as vt,i2 as bt,i3 as At,i4 as Nt,i5 as Tt,a0 as ze,hN as Ke,i6 as Zn,ht as Hn,J as Yn,i7 as Et,fV as qe,h1 as Me,W as $e,hI as er,fX as je,gQ as he,fH as Oe,a1 as l,ho as Jn,h8 as Gn,hp as Xn,hv as Qn,i8 as Bn,gU as j,i9 as wn,ia as Rn,hD as Le,ib as Ue,ic as Dn,id as Pn,ie as Kn,P as _,hE as W,gW as f,ii as es,ij as rs,ik as ue,ig as ts,ih as Ft,iv as It,iw as ns,hn as ss}from"./index-fdfa25a0.js";import{T as kt}from"./Timeline-bb89efb4.js";function os(v,X){return Array.isArray(X)&&X.find(G=>G.code===v)||""}const zt=({label:v,value:X,units:B,data:G,onSave:I,isEditMode:me,visibility:u,length:ge,isExtendMode:sr,options:or=[],type:m})=>{var T,We;const[P,oe]=C.useState(X),[Mt,ye]=C.useState(!1),[N,ve]=C.useState(!1),A=J(a=>a.AllDropDown.dropDown),fe=qt(),_e=os(P,A);J(a=>a.bankKey.requiredFields),console.log("editedValue",v,P),console.log("dropdownData",A),console.log("value e",X),console.log("label",v),console.log("units",B),console.log("transformedValue",_e),J(a=>a.edit.payload),console.log("editField",P);const ce={label:v,value:P,units:B,type:m};console.log("fieldData",ce),C.useEffect(()=>{me&&(u==="0"||u==="Required")&&fe(Ln(S))},[]);let S=v.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");C.useEffect(()=>{oe(X)},[X]);const re=a=>{fe(yn({keyname:S.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:a}))},F=a=>{const b=w=>{console.log("value",a),fe(Se({keyName:"Region1",data:w.body}))},p=w=>{console.log(w,"error in dojax")};k(`/${$}/data/getRegionBasedOnCountry?country=${a}`,"get",b,p)},cr=a=>{const b=w=>{fe(Se({keyName:"Region2",data:w.body}))},p=w=>{console.log(w,"error in dojax")};k(`/${$}/data/getRegionBasedOnCountry?country=${a}`,"get",b,p)};return r(x,{item:!0,children:r(ee,{children:me?i(V,{children:[i(g,{variant:"body2",color:"#777",children:[v,u==="Required"||u==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),m==="Drop Down"?r(Un,{options:A[S]??[],value:G[S]&&((T=A[S])==null?void 0:T.filter(a=>a.code===G[S]))&&G[S]&&((We=A[S])==null?void 0:We.filter(a=>a.code===G[S])[0])||"",onChange:(a,b,p)=>{(u==="Required"||u==="0")&&b===null&&ve(!0),v==="Country 1"&&F(b==null?void 0:b.code),v==="Country 2"&&cr(b==null?void 0:b.code),re(p==="clear"?"":b==null?void 0:b.code),oe(b==null?void 0:b.code),ye(!0)},getOptionLabel:a=>a===""||(a==null?void 0:a.code)===""?"":`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`??"",renderOption:(a,b)=>(console.log("option vakue",b),r("li",{...a,children:r(g,{style:{fontSize:12},children:`${b==null?void 0:b.code} - ${b==null?void 0:b.desc}`})})),renderInput:a=>r(pe,{...a,variant:"outlined",placeholder:`Select ${ce.label}`,size:"small",label:null,error:N})}):m==="Input"?r(pe,{variant:"outlined",size:"small",value:G[S].toUpperCase(),placeholder:`Enter ${ce.label}`,inputProps:{maxLength:ge},onChange:a=>{const b=a.target.value;if(b.length>0&&b[0]===" ")re(b.trimStart());else{let p=b.toUpperCase();re(p)}(u==="Required"||u==="0")&&b.length<=0&&ve(!0),oe(b.toUpperCase())},error:N}):m==="Calendar"?r(rr,{dateAdapter:tr,children:r(nr,{slotProps:{textField:{size:"small"}},placeholder:"Select Date Range"})}):m==="Radio Button"?r(x,{item:!0,md:2,children:r(mt,{sx:{padding:0},checked:G[S],onChange:(a,b)=>{re(b),oe(b)}})}):""]}):r(V,{children:i(V,{children:[i(g,{variant:"body2",color:"#777",children:[v,u==="Required"||u==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),r(g,{variant:"body2",fontWeight:"bold",children:m==="Radio Button"?r(mt,{sx:{padding:0},checked:P,disabled:!0}):P})]})})})})},ls=()=>{var vr,br,Ar,Nr,Tr,Er,Fr,Ir,kr,zr,qr,Mr,$r,jr,Or,Lr,Ur,yr,_r,Wr,Vr,Zr,Hr,Yr,Jr,Gr,Xr,Qr,Br,wr,Rr,Dr,Pr,Kr,et,rt,tt,nt,st,ot;const[v,X]=C.useState(!1);C.useState(0);const[B,G]=C.useState(!0);C.useState({});const[I,me]=C.useState([]),[u,ge]=C.useState(0),[sr,or]=C.useState([]),[m,P]=C.useState(),[oe,Mt]=C.useState(!1);C.useState([]);const[ye,N]=C.useState(!1),[ve,A]=C.useState(!1),[fe,_e]=C.useState(""),[ce,S]=C.useState(""),[re,F]=C.useState(!1),[cr,T]=C.useState(!0),[We,a]=C.useState(!1),[b,p]=C.useState(!1),[w,be]=C.useState(!1),[$t,Ae]=C.useState(!1),[jt,Ve]=C.useState(!1),[Ot,ir]=C.useState(!1),[R,Ne]=C.useState(""),[Te,Lt]=C.useState(!0),[ie,Ut]=C.useState(""),[le,yt]=C.useState([]),[de,_t]=C.useState([]),[Wt,lr]=C.useState(!1),[Ce,Q]=C.useState(!0),[Vt,Zt]=C.useState(!1),[Ze,Ht]=C.useState([]),[Yt,dr]=C.useState(!1),[Jt,ar]=C.useState(!1),[Gt,He]=C.useState(!1),[Xt,hr]=C.useState(!1),[Ye,xe]=C.useState(!1),te=qt(),H=_n(),Qt=Wn(),Bt=J(o=>o.appSettings);let Ee=J(o=>{var d;return(d=o.userManagement.entitiesAndActivities)==null?void 0:d["Bank Key"]}),z=J(o=>{var d;return(d=o==null?void 0:o.initialData)==null?void 0:d.IWMMyTask}),c=J(o=>o.userManagement.userData),t=Qt.state,n=J(o=>o.userManagement.taskData);const e=J(o=>o.edit.payload);let wt=J(o=>o.edit.payload),Rt=J(o=>o.bankKey.requiredFields);const Je=J(o=>o.bankKey.bankKeyViewData);console.log("ccroewdata",e),console.log("bankKeyRowData",t),console.log("Remarks",m);const ur=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:o=>i(V,{children:[r(es,{index:o.row.id,name:o.row.docName}),r(rs,{index:o.row.id,name:o.row.docName})]})}],Dt=()=>{let o=n!=null&&n.subject?n==null?void 0:n.subject:t==null?void 0:t.requestId,d=s=>{console.log(s.documentDetailDtoList,"data.documentDetailDtoList");var h=[];s.documentDetailDtoList.forEach(E=>{console.log(s.documentDetailDtoList,"data.");var q={id:E.documentId,docType:E.fileType,docName:E.fileName,uploadedOn:Ke(E.docCreationDate).format(Bt.date),uploadedBy:E.createdBy};console.log(q,"tempRow"),h.push(q)}),yt(h)};k(`/${ue}/documentManagement/getDocByRequestId/${o}`,"get",d)},Pt=()=>{let o=n!=null&&n.subject?n==null?void 0:n.subject:t==null?void 0:t.requestId,d=h=>{console.log("commentsdata",h);var E=[];h.body.forEach(q=>{var M={id:q.requestId,comment:q.comment,user:q.createdByUser,createdAt:q.updatedAt};E.push(M)}),_t(E),console.log("commentrows",E.length)},s=h=>{console.log(h)};k(`/${$}/activitylog/fetchTaskDetailsForRequestId?requestId=${o}`,"get",d,s)};console.log("activeTabName",I);const Kt=()=>{I[u];let o=Object.entries(Je);console.log("viewDataArray",o);const d={};o.map(s=>{console.log("bottle",s[1]);let h=Object.entries(s[1]);return console.log("notebook",h),h.forEach(E=>{E[1].forEach(q=>{d[q.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=q.value})}),s}),console.log("toSetArray",d),te(ts(d))};C.useEffect(()=>{Je.length!==0&&Kt()},[Je]);var O={AddressDto:{AddressID:m!=null&&m.AddressId?m==null?void 0:m.AddressId:"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Name2:e!=null&&e.Name1?e==null?void 0:e.Name1:"",Name3:e!=null&&e.Name2?e==null?void 0:e.Name2:"",Name4:e!=null&&e.Name3?e==null?void 0:e.Name3:"",Sort1:e!=null&&e.SearchTerm1?e==null?void 0:e.SearchTerm1:"",Sort2:e!=null&&e.SearchTerm2?e==null?void 0:e.SearchTerm2:"",BuildLong:e!=null&&e.BuildingCode?e==null?void 0:e.BuildingCode:"",RoomNo:e!=null&&e.RoomNumber?e==null?void 0:e.RoomNumber:"",Floor:e!=null&&e.Floor?e==null?void 0:e.Floor:"",COName:e!=null&&e.co?e==null?void 0:e.co:"",StrSuppl1:e!=null&&e.Street1?e==null?void 0:e.Street1:"",StrSuppl2:e!=null&&e.Street2?e==null?void 0:e.Street2:"",Street:e!=null&&e.Street3?e==null?void 0:e.Street3:"",HouseNo:e!=null&&e.HouseNumber?e==null?void 0:e.HouseNumber:"",HouseNo2:e!=null&&e.Supplement?e==null?void 0:e.Supplement:"",StrSuppl3:e!=null&&e.Street4?e==null?void 0:e.Street4:"",Location:e!=null&&e.Street5?e==null?void 0:e.Street5:"",District:e!=null&&e.District?e==null?void 0:e.District:"",HomeCity:e!=null&&e.OtherCity?e==null?void 0:e.OtherCity:"",PostlCod1:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",PostlCod2:e!=null&&e.PostalCode1?e==null?void 0:e.PostalCode1:"",PostlCod3:e!=null&&e.CompanyPostCd?e==null?void 0:e.CompanyPostCd:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PoBoxCit:e!=null&&e.POBoxCity?e==null?void 0:e.POBoxCity:"",PoBoxReg:(vr=e==null?void 0:e.Region2)!=null&&vr.code?(br=e==null?void 0:e.Region2)==null?void 0:br.code:e!=null&&e.Region2?e==null?void 0:e.Region2:"",PoboxCtry:(Ar=e==null?void 0:e.Country2)!=null&&Ar.code?(Nr=e==null?void 0:e.Country2)==null?void 0:Nr.code:e!=null&&e.Country2?e==null?void 0:e.Country2:"",Country:(Tr=e==null?void 0:e.Country1)!=null&&Tr.code?(Er=e==null?void 0:e.Country1)==null?void 0:Er.code:e!=null&&e.Country1?e==null?void 0:e.Country1:"",TimeZone:(Fr=e==null?void 0:e.TimeZone)!=null&&Fr.code?(Ir=e==null?void 0:e.TimeZone)==null?void 0:Ir.code:e!=null&&e.TimeZone?e==null?void 0:e.TimeZone:"",Taxjurcode:(kr=e==null?void 0:e.TaxJurisdiction)!=null&&kr.code?(zr=e==null?void 0:e.TaxJurisdiction)==null?void 0:zr.code:e!=null&&e.TaxJurisdiction?e==null?void 0:e.TaxJurisdiction:"",Transpzone:(qr=e==null?void 0:e.TransportZone)!=null&&qr.code?(Mr=e==null?void 0:e.TransportZone)==null?void 0:Mr.code:e!=null&&e.TransportZone?e==null?void 0:e.TransportZone:"",Regiogroup:($r=e==null?void 0:e.StructureGroup)!=null&&$r.code?(jr=e==null?void 0:e.StructureGroup)==null?void 0:jr.code:e!=null&&e.StructureGroup?e==null?void 0:e.StructureGroup:"",DontUseS:(Or=e==null?void 0:e.Undeliverable)!=null&&Or.code?(Lr=e==null?void 0:e.Undeliverable)==null?void 0:Lr.code:e!=null&&e.Undeliverable?e==null?void 0:e.Undeliverable:"",DontUseP:(Ur=e==null?void 0:e.Undeliverable1)!=null&&Ur.code?(yr=e==null?void 0:e.Undeliverable1)==null?void 0:yr.code:e!=null&&e.Undeliverable1?e==null?void 0:e.Undeliverable1:"",PoWONo:(e==null?void 0:e.POBoxwoNo)===!0?"X":"",DeliServType:e!=null&&e.DelvryServType?e==null?void 0:e.DelvryServType:"",DeliServNumber:e!=null&&e.DeliveryServiceNo?e==null?void 0:e.DeliveryServiceNo:"",Township:e!=null&&e.Township?e==null?void 0:e.Township:"",Langu:(_r=e==null?void 0:e.Language)!=null&&_r.code?(Wr=e==null?void 0:e.Language)==null?void 0:Wr.code:e!=null&&e.Language?e==null?void 0:e.Language:"",Tel1Numbr:e!=null&&e.Telephone?e==null?void 0:e.Telephone:"",Tel1Ext:e!=null&&e.Extension?e==null?void 0:e.Extension:"",FaxNumber:e!=null&&e.Fax?e==null?void 0:e.Fax:"",MobilePhone:e!=null&&e.MobilePhone?e==null?void 0:e.MobilePhone:"",FaxExtens:e!=null&&e.Extension1?e==null?void 0:e.Extension1:"",EMail:e!=null&&e.EMailAddress?e==null?void 0:e.EMailAddress:"",AdrNotes:e!=null&&e.Notes?e==null?void 0:e.Notes:"",Region:(Vr=e==null?void 0:e.Region1)!=null&&Vr.code?(Zr=e==null?void 0:e.Region1)==null?void 0:Zr.code:e!=null&&e.Region1?e==null?void 0:e.Region1:"",PoBoxLobby:e!=null&&e.PoBoxLobby?e==null?void 0:e.PoBoxLobby:""},BankKeyID:m!=null&&m.BankKeyId?m==null?void 0:m.BankKeyId:"",ReqCreatedBy:c==null?void 0:c.user_id,ReqCreatedOn:n!=null&&n.createdOn?"/Date("+(n==null?void 0:n.createdOn)+")/":t!=null&&t.createdOn?"/Date("+Date.parse(t==null?void 0:t.createdOn)+")/":"",RequestStatus:t!=null&&t.reqStatus?t==null?void 0:t.reqStatus:"",CreationId:(n==null?void 0:n.processDesc)==="Create"?n==null?void 0:n.subject.slice(3):(t==null?void 0:t.requestType)==="Create"?(Hr=t==null?void 0:t.requestId)==null?void 0:Hr.slice(3):"",EditId:(n==null?void 0:n.processDesc)==="Change"?n==null?void 0:n.subject.slice(3):(t==null?void 0:t.requestType)==="Change"?(Yr=t==null?void 0:t.requestId)==null?void 0:Yr.slice(3):"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:(t==null?void 0:t.requestType)==="Create"?"Create":(t==null?void 0:t.requestType)==="Change"?"Change":(n==null?void 0:n.processDesc)==="Create"?"Create":((n==null?void 0:n.processDesc)==="Change","Change"),TaskId:n!=null&&n.taskId?n==null?void 0:n.taskId:"",Remarks:R||"",Action:(t==null?void 0:t.requestType)==="Create"?"I":(t==null?void 0:t.requestType)==="Change"?"U":(n==null?void 0:n.processDesc)==="Create"?"I":((n==null?void 0:n.processDesc)==="Change","U"),Validation:Ce===!0?"X":"",BankCtry:t!=null&&t.bankCtryReg?t==null?void 0:t.bankCtryReg:m!=null&&m.BankCtry?m==null?void 0:m.BankCtry:"",BankKey:t!=null&&t.bankKey?t==null?void 0:t.bankKey:m!=null&&m.BankKey?m==null?void 0:m.BankKey:"",BankName:e!=null&&e.BankName?e==null?void 0:e.BankName:"",BankRegion:(Jr=e==null?void 0:e.Region)!=null&&Jr.code?(Gr=e==null?void 0:e.Region)==null?void 0:Gr.code:e!=null&&e.Region?e==null?void 0:e.Region:"",BankStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",City:e!=null&&e.City?e==null?void 0:e.City:"",BankBranch:e!=null&&e.BankBranch?e==null?void 0:e.BankBranch:"",SwiftCode:e!=null&&e.SWIFTBIC?e==null?void 0:e.SWIFTBIC:"",BankGroup:e!=null&&e.BankGroup?e==null?void 0:e.BankGroup:"",PobkCurac:(e==null?void 0:e.PostbankAcct)===!0?"X":"",BankNo:e!=null&&e.BankNumber?e==null?void 0:e.BankNumber:""};const en=o=>{console.log("valueeeeeeeeeeee",o);const d=h=>{console.log("121212",h),te(Se({keyName:"Region",data:h.body}))},s=h=>{console.log(h,"error in dojax")};k(`/${$}/data/getRegionBasedOnCountry?country=${o}`,"get",d,s)},rn=o=>{const d=h=>{console.log("value",o),te(Se({keyName:"Region1",data:h.body}))},s=h=>{console.log(h,"error in dojax")};k(`/${$}/data/getRegionBasedOnCountry?country=${o}`,"get",d,s)},[pr,tn]=C.useState(0),nn=(o,d)=>{const s=E=>{te(Se({keyName:o,data:E.body})),tn(q=>q+1)},h=E=>{console.log(E)};k(`/${$}/data/${d}`,"get",s,h)},sn=()=>{var o,d;(d=(o=Ft)==null?void 0:o.bankKey)==null||d.map(s=>{nn(s==null?void 0:s.keyName,s==null?void 0:s.endPoint)})},on=()=>{var o,d;pr==((d=(o=Ft)==null?void 0:o.bankKey)==null?void 0:d.length)?be(!1):be(!0)};C.useEffect(()=>{on()},[pr]),C.useEffect(()=>{sn(),Dt(),Pt()},[]),C.useEffect(()=>{Ut(Vn("BK"))},[]),console.log("taskData",z),console.log("taskRowDetails",n);const cn=()=>{var h,E,q,M,ct,it,lt;p(!0);var o=(h=z==null?void 0:z.body)!=null&&h.id?{id:(E=z==null?void 0:z.body)!=null&&E.id?(q=z==null?void 0:z.body)==null?void 0:q.id:"",bankKey:(M=z==null?void 0:z.body)!=null&&M.bankKey?(ct=z==null?void 0:z.body)==null?void 0:ct.bankKey:"",bankCtry:(it=z==null?void 0:z.body)==null?void 0:it.bankCtry,reqStatus:(lt=z==null?void 0:z.body)==null?void 0:lt.reqStatus,screenName:(n==null?void 0:n.processDesc)==="Create"?"Create":"Change"}:{id:t!=null&&t.reqStatus?t==null?void 0:t.id:"",bankKey:t!=null&&t.bankKey?t==null?void 0:t.bankKey:"",bankCtry:t!=null&&t.bankCtryReg?t==null?void 0:t.bankCtryReg:"",reqStatus:t!=null&&t.reqStatus?t==null?void 0:t.reqStatus:"Approved",screenName:t!=null&&t.requestType?t==null?void 0:t.requestType:"Change"};console.log("payload",o);const d=Y=>{var dt,at,ht,ut,pt;if(Y.statusCode===200){p(!1),en((dt=Y==null?void 0:Y.body)==null?void 0:dt.BankCtry),rn((pt=(ut=(ht=(at=Y==null?void 0:Y.body)==null?void 0:at.viewData)==null?void 0:ht["Address Details"])==null?void 0:ut["Street Address"])==null?void 0:pt.find(ae=>(ae==null?void 0:ae.fieldName)==="Country 1").value),console.log("dataaaaaaa",Y.body);const Pe=Y.body.viewData,$n=Y.body;te(ns(Pe));const ke=Object.keys(Pe);console.log("categorykeys",ke),!(t!=null&&t.requestType)&&!(n!=null&&n.processDesc)&&!v?me(ke.slice(0,-1)):me(ke),console.log("factorsarrayyyyy",I);const jn=["Attachment & Documents"];I.concat(jn);const On=ke.map(ae=>({category:ae,data:Pe[ae]}));or(On),P($n)}else p(!1),Zt(!0),A(!1),N("Error"),S("Unable to fetch data of Bank Key"),_e("danger"),F("danger"),T(!0),Ae(!0)},s=Y=>{console.log(Y)};k(`/${$}/data/displayBankKey`,"post",d,s,o)};C.useEffect(()=>{cn()},[]);const mr=()=>{Ae(!1)},ln=()=>{H("/masterDataCockpit/bankKey")},dn=()=>{Ot?(Ve(!1),ir(!1)):(Ve(!1),H("/masterDataCockpit/bankKey"))},fr=()=>ss(wt,Rt,Ht);console.log("formvalidation",Ze);const L=()=>{fr()?(ge(d=>d-1),te(It())):gr()},U=()=>{fr()?(ge(d=>d+1),te(It())):gr()},Fe=()=>{X(!0),I.push("Attachments & Comments"),G(!1)},an=()=>{gn()},hn=()=>{vn()},Ge=()=>{p(!0),bn(),Q(!1)},Cr=()=>{An(),Q(!1)},Xe=()=>{p(!0),Nn()},un=()=>{p(!0),Tn()},xr=()=>{En()},Sr=()=>{Fn()},y=()=>{Ae(!0)},Z=()=>{Ve(!0)},K=()=>{be(!0);const o=s=>{be(!1),s.statusCode===201?(Lt(!1),N("Create"),console.log("success"),N("Create"),S("All Data has been Validated. Bank Key can be Send for Review"),F("success"),T(!1),A(!0),Z(),a(!0),ir(!0),Q(!1)):(N("Error"),A(!1),s.body.message.length>0?S(s.body.message[0]):S(s.body.value),F("danger"),T(!1),a(!0),y()),handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/validateSingleBankKey`,"post",o,d,O)},pn=()=>{(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")?mn():(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")?fn():(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")?Cn():(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&xn()},mn=()=>{p(!0);const o=s=>{s.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Correction with ID NBS${s.body}`),F("success"),T(!1),A(!0),Z(),a(!0),p(!1)):(N("Error"),A(!1),S("Failed Submitting Bank Key for Correction"),F("danger"),T(!1),a(!0),y(),p(!1)),ne()},d=s=>{console.log(s)};console.log("remarkssssssssss",R),k(`/${$}/alter/bankKeySendForCorrection`,"post",o,d,O)},fn=()=>{p(!0);const o=s=>{s.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Correction with ID CBS${s.body}`),F("success"),T(!1),A(!0),Z(),a(!0),p(!1)):(N("Error"),A(!1),S("Failed Submitting Bank Key for Correction"),F("danger"),T(!1),a(!0),y(),p(!1)),ne()},d=s=>{console.log(s)};console.log("hsdfjgdh",O),k(`/${$}/alter/changeBankKeySendForReview`,"post",o,d,O)},Cn=()=>{p(!0);const o=s=>{s.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Correction with ID NBS${s.body}`),F("success"),T(!1),A(!0),Z(),a(!0),p(!1)):(N("Error"),A(!1),S("Failed Submitting Bank Key for Correction"),F("danger"),T(!1),a(!0),y(),p(!1)),ne()},d=s=>{console.log(s)};k(`/${$}/alter/bankKeySendForReview`,"post",o,d,O)},xn=()=>{p(!0);const o=s=>{s.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Correction with ID CBS${s.body}`),F("success"),T(!1),A(!0),Z(),a(!0),p(!1)):(N("Error"),A(!1),S("Failed Submitting Bank Key for Correction"),F("danger"),T(!1),a(!0),y(),p(!1)),ne()},d=s=>{console.log(s)};console.log("remarksssaaaa",R),k(`/${$}/alter/changeBankKeySendForCorrection`,"post",o,d,O)},Sn=()=>{p(!0);const o=s=>{if(p(!1),s.statusCode===200){console.log("success"),N("Change"),S(`Bank Key has been submitted for approval CBS${s.body}`),F("success"),T(!1),A(!0),Z(),a(!0),p(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`CBS${s==null?void 0:s.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${ue}/documentManagement/updateDocRequestId`,"post",E,q,h),H("/masterDataCockpit/bankKey")}else N("Change"),A(!1),S("Change Failed"),F("danger"),T(!1),a(!0),y(),p(!1);handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/changeBankKeyApprovalSubmit`,"post",o,d,O)},Qe=()=>{Q(!1),ar(!0)},Be=()=>{Ne(""),Q(!0),ar(!1)},we=I.map(o=>{const d=sr.filter(s=>{var h;return((h=s.category)==null?void 0:h.split(" ")[0])==(o==null?void 0:o.split(" ")[0])});if(d.length!=0)return{category:o==null?void 0:o.split(" ")[0],data:d[0].data}}).map((o,d)=>{if(console.log("categoryData",o==null?void 0:o.category),(o==null?void 0:o.category)=="Bank"&&u==0)return[r(x,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(o.data).map(s=>i(x,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ft},children:[r(g,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:s}),r(D,{sx:{width:"100%"},children:r(Ct,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:r(x,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:o.data[s].map(h=>(console.log("fieldDatatttt",h),r(zt,{data:e,length:h.maxLength,label:h.fieldName,value:h.value,visibility:h.visibility,onSave:E=>handleFieldSave(h.fieldName,E),isEditMode:v,type:h.fieldType,field:h})))})})})]},s))},o.category)];if((o==null?void 0:o.category)=="Address"&&u==1)return[r(x,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(o.data).map(s=>i(x,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ft},children:[r(g,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:s}),r(D,{sx:{width:"100%"},children:r(Ct,{children:r(x,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:o.data[s].map(h=>r(zt,{data:e,label:h.fieldName,value:h==null?void 0:h.value,onSave:E=>handleFieldSave(h.fieldName,E),isEditMode:v,type:h.fieldType},h.fieldName))})})})]},s))},o.category)];if((o==null?void 0:o.category)=="Attachments"&&u==2)return[r(V,{children:v?i(V,{children:[r(Zn,{title:"BankKey",useMetaData:!1,artifactId:ie,artifactName:"BankKey"}),i(ze,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[r(x,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:r(g,{variant:"h6",children:r("strong",{children:"Attachments"})})}),!!le.length&&r(xt,{width:"100%",rows:le,columns:ur,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!le.length&&r(g,{variant:"body2",children:"No Attachments Found"}),r("br",{}),r(g,{variant:"h6",children:"Comments"}),!!de.length&&r(kt,{sx:{[`& .${St.root}:before`]:{flex:0,padding:0}},children:de.map(s=>i(gt,{children:[i(vt,{children:[r(bt,{children:r(At,{sx:{color:"#757575"}})}),r(Nt,{})]}),r(Tt,{sx:{py:"12px",px:2},children:r(ze,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:r(D,{sx:{padding:"1rem"},children:i(ee,{spacing:1,children:[r(x,{sx:{display:"flex",justifyContent:"space-between"},children:r(g,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ke(s.createdAt).format("DD MMM YYYY")})}),r(g,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:s.user}),r(g,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:s.comment})]})})})})]}))}),!de.length&&r(g,{variant:"body2",children:"No Comments Found"}),r("br",{})]})]}):i(ze,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[r(x,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:r(g,{variant:"h6",children:r("strong",{children:"Attachments"})})}),!!le.length&&r(xt,{width:"100%",rows:le,columns:ur,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!le.length&&r(g,{variant:"body2",children:"No Attachments Found"}),r("br",{}),r(g,{variant:"h6",children:"Comments"}),!!de.length&&r(kt,{sx:{[`& .${St.root}:before`]:{flex:0,padding:0}},children:de.map(s=>i(gt,{children:[i(vt,{children:[r(bt,{children:r(At,{sx:{color:"#757575"}})}),r(Nt,{})]}),r(Tt,{sx:{py:"12px",px:2},children:r(ze,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:r(D,{sx:{padding:"1rem"},children:i(ee,{spacing:1,children:[r(x,{sx:{display:"flex",justifyContent:"space-between"},children:r(g,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ke(s.createdAt).format("DD MMM YYYY")})}),r(g,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:s.user}),r(g,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:s.comment})]})})})})]}))}),!de.length&&r(g,{variant:"body2",children:"No Comments Found"}),r("br",{})]})})]});console.log("tabcontents",we);const gn=()=>{p(!0);const o=s=>{s.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Approval with ID CBS${s.body}`),F("success"),T(!1),A(!0),Z(),a(!0),p(!1),H("/masterDataCockpit/bankKey")):(N("Approve"),A(!1),S("Failed Submitting Bank Key"),F("danger"),T(!1),a(!0),y(),p(!1)),handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/changeBankKeyApprovalSubmit`,"post",o,d,O)},vn=()=>{p(!0);const o=s=>{s.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Approval with ID NBS${s.body}`),F("success"),T(!1),A(!0),Z(),a(!0),p(!1),H("/masterDataCockpit/bankKey")):(N("Error"),A(!1),S("Failed Submitting Bank Key"),F("danger"),T(!1),a(!0),y(),p(!1)),handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/bankKeyApprovalSubmit`,"post",o,d,O)},bn=()=>{const o=s=>{if(s.statusCode===200){console.log("success"),N("Create"),S(`Bank Key Submitted For Review with ID CBS${s.body} `),F("success"),T(!1),A(!0),Z(),a(!0),X(!1),G(!0),p(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`CBS${s==null?void 0:s.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${ue}/documentManagement/updateDocRequestId`,"post",E,q,h),H("/masterDataCockpit/bankKey")}else N("Error"),A(!1),S("Failed Submitting Bank Key"),F("danger"),T(!1),a(!0),y(),p(!1),He(!1);handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/changeBankKeySubmitForReview`,"post",o,d,O)},An=()=>{p(!0);const o=s=>{if(s.statusCode===200){console.log("success"),N("Create"),S(`Bank Key Submitted for Review with ID NBS${s.body} `),F("success"),T(!1),A(!0),Z(),a(!0),p(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`NBS${s==null?void 0:s.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${ue}/documentManagement/updateDocRequestId`,"post",E,q,h),H("/masterDataCockpit/bankKey")}else N("Error"),A(!1),S("Failed Saving the Data"),F("danger"),T(!1),a(!0),y(),p(!1),Q(!0);handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/bankKeySubmitForReview`,"post",o,d,O)},Nn=()=>{const o=s=>{if(p(!1),s.statusCode===200){console.log("success"),N("Create"),S(`Bank Key Saved As Draft with ID CBS${s.body} `),F("success"),T(!1),A(!0),Z(),a(!0),p(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`CBS${s==null?void 0:s.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${ue}/documentManagement/updateDocRequestId`,"post",E,q,h),H("/masterDataCockpit/bankKey")}else N("Error"),A(!1),S("Failed Saving Bank Key"),F("danger"),T(!1),a(!0),y(),p(!1);handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/changeBankKeyAsDraft`,"post",o,d,O)},Tn=()=>{const o=s=>{if(p(!1),s.statusCode===200){console.log("success"),N("Create"),S(`Bank Key Saved As Draft with ID NBS${s.body} `),F("success"),T(!1),A(!0),Z(),a(!0),p(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`NBS${s==null?void 0:s.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${ue}/documentManagement/updateDocRequestId`,"post",E,q,h),H("/masterDataCockpit/bankKey")}else N("Error"),A(!1),S("Failed Saving Bank Key"),F("danger"),T(!1),a(!0),y(),p(!1);handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/bankKeyAsDraft`,"post",o,d,O)},En=()=>{p(!0);const o=s=>{s.statusCode===201?(console.log("success"),N("Create"),S(`${s.message}`),F("success"),T(!1),A(!0),Z(),a(!0),p(!1),H("/masterDataCockpit/bankKey")):(N("Error"),A(!1),S("Failed Approving Bank Key"),F("danger"),T(!1),a(!0),y(),p(!1)),handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/changeBankKeyApproved`,"post",o,d,O)},Fn=()=>{p(!0);const o=s=>{s.statusCode===201?(console.log("success"),N("Create"),S(`${s.message}`),F("success"),T(!1),A(!0),Z(),a(!0),p(!1),H("/masterDataCockpit/bankKey")):(N("Error"),A(!1),S("Failed Approving the Bank Key"),F("danger"),T(!1),a(!0),y(),p(!1)),handleClose()},d=s=>{console.log(s)};k(`/${$}/alter/createBankKeyApproved`,"post",o,d,O)},Ie=()=>{Q(!1),lr(!0)},ne=()=>{Ne(""),Q(!0),lr(!1)},se=()=>{Q(!1),He(!0)},Re=()=>{xe(!1),Q(!0),He(!1)},In=()=>{Ae(!1)},kn=()=>{(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&v?R.length<=0?xe(!0):(xe(!1),Cr()):(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&!v?hn():(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&!v?Sr():(c==null?void 0:c.role)==="Finance"&&!(t!=null&&t.requestType)&&v?R.length<=0?xe(!0):(xe(!1),Ge()):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&v?Ge():(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&!v?an():(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&!v&&xr()},De=(o,d)=>{const s=o.target.value;if(s.length>0&&s[0]===" ")Ne(s.trimStart());else{let h=s.toUpperCase();Ne(h)}},gr=()=>{dr(!0)},zn=()=>{dr(!1)},qn=o=>{hr(o)},Mn=()=>{hr(!0)};return console.log(t==null?void 0:t.bankKey,"bankKeyRowData?.bankKey"),r(V,{children:b===!0?r(Hn,{}):i("div",{style:{backgroundColor:"#FAFCFF"},children:[r(Yn,{apiError:Vt,dialogState:$t,openReusableDialog:y,closeReusableDialog:mr,dialogTitle:ye,dialogMessage:ce,handleDialogConfirm:mr,dialogOkText:"OK",handleExtraButton:ln,dialogSeverity:re,showCancelButton:!0,handleDialogReject:In}),ve&&r(Et,{openSnackBar:jt,alertMsg:ce,handleSnackBarClose:dn}),Ze.length!=0&&r(Et,{openSnackBar:Yt,alertMsg:"Please fill the following Field: "+Ze.join(", "),handleSnackBarClose:zn}),i(qe,{open:Wt,onClose:ne,hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},children:[i(Me,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(g,{variant:"h6",children:"REMARKS"}),r($e,{sx:{width:"max-content"},onClick:ne,children:r(er,{})})]}),r(je,{sx:{padding:".5rem 1rem"},children:r(ee,{children:i(D,{sx:{minWidth:400},children:[r(he,{sx:{height:"auto"},fullWidth:!0,children:r(pe,{sx:{backgroundColor:"#F5F5F5"},onChange:De,multiline:!0,value:R,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})}),Ye&&r(x,{children:r(g,{style:{color:"red"},children:"Please Enter Remarks"})})]})})}),i(Oe,{sx:{display:"flex",justifyContent:"end"},children:[r(l,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ne,children:"Cancel"}),r(l,{className:"button_primary--normal",type:"save",onClick:pn,variant:"contained",children:"Submit"})]})]}),r(Jn,{sx:{color:"#fff",zIndex:o=>o.zIndex.drawer+1},open:w,children:r(Gn,{color:"inherit"})}),i(x,{container:!0,sx:Xn,children:[i(x,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[i(x,{md:9,sx:{display:"flex"},children:[r(x,{children:r($e,{color:"primary","aria-label":"upload picture",component:"label",sx:Qn,children:r(Bn,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{H("/masterDataCockpit/bankKey")}})})}),i(x,{children:[v?i(x,{item:!0,md:12,children:[r(g,{variant:"h3",children:r("strong",{children:"Change Bank Key: "})}),r(g,{variant:"body2",color:"#777",children:"This view edits the details of the Bank Key"})]}):"",B?i(x,{item:!0,md:12,children:[r(g,{variant:"h3",children:r("strong",{children:"Display Bank Key "})}),r(g,{variant:"body2",color:"#777",children:"This view displays the details of the Bank Key"})]}):""]})]}),i(x,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:[t!=null&&t.requestId||n!=null&&n.processDesc?r(x,{children:r(l,{variant:"outlined",size:"small",sx:j,onClick:Mn,title:"Change Log",children:r(wn,{sx:{padding:"2px"},fontSize:"small"})})}):"",Xt&&r(Rn,{open:!0,closeModal:qn,requestId:t!=null&&t.requestId?t==null?void 0:t.requestId:n==null?void 0:n.subject,requestType:t!=null&&t.requestType?t==null?void 0:t.requestType:(Xr=z==null?void 0:z.body)==null?void 0:Xr.processDesc,pageName:"bankKey",controllingArea:t!=null&&t.bankCtryReg?t==null?void 0:t.bankCtryReg:m==null?void 0:m.BankCtry,centerName:t!=null&&t.bankKey?t==null?void 0:t.bankKey:m==null?void 0:m.BankKey}),Le(Ee,"Bank Key","ChangeBK")&&((c==null?void 0:c.role)==="Super User"&&(t!=null&&t.requestType)&&((Qr=n==null?void 0:n.itmStatus)==null?void 0:Qr.toUpperCase())!=="OPEN"&&B?r(x,{gap:1,sx:{display:"flex"},children:r(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:r(V,{children:r(x,{item:!0,children:i(l,{variant:"outlined",size:"small",sx:j,onClick:Fe,children:["Fill Details",r(Ue,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&(t!=null&&t.requestType)&&B?r(x,{gap:1,sx:{display:"flex"},children:r(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:r(V,{children:r(x,{item:!0,children:i(l,{variant:"outlined",size:"small",sx:j,onClick:Fe,children:["Fill Details",r(Ue,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Super User"&&!(t!=null&&t.requestType)&&B?r(x,{gap:1,sx:{display:"flex"},children:r(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:r(V,{children:r(x,{item:!0,children:i(l,{variant:"outlined",size:"small",sx:j,onClick:Fe,children:["Change",r(Ue,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&!(t!=null&&t.requestType)&&B?r(x,{gap:1,sx:{display:"flex"},children:r(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:r(V,{children:r(x,{item:!0,children:i(l,{variant:"outlined",size:"small",sx:j,onClick:Fe,children:["Change",r(Ue,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")]})]}),r(x,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:i(D,{width:"70%",sx:{marginLeft:"40px"},children:[r(x,{item:!0,sx:{paddingTop:"2px !important"},children:i(ee,{flexDirection:"row",children:[r("div",{style:{width:"20%"},children:r(g,{variant:"body2",color:"#777",children:"Bank Country"})}),i(g,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",t!=null&&t.BankCtry?t==null?void 0:t.BankCtry:m!=null&&m.BankCtry?m==null?void 0:m.BankCtry:""]})]})}),r(x,{item:!0,sx:{paddingTop:"2px !important"},children:i(ee,{flexDirection:"row",children:[r("div",{style:{width:"20%"},children:r(g,{variant:"body2",color:"#777",children:"Bank Key"})}),i(g,{variant:"body2",fontWeight:"bold",children:[":"," ",t!=null&&t.bankKey?t==null?void 0:t.bankKey:m!=null&&m.BankKey?m==null?void 0:m.BankKey:""]})]})})]})}),i(x,{container:!0,style:{marginLeft:25},children:[r(Dn,{activeStep:u,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:I.map((o,d)=>r(Pn,{children:r(Kn,{sx:{fontWeight:"700"},children:o})},o))}),we&&((Br=we[u])==null?void 0:Br.map((o,d)=>r(D,{sx:{mb:2,width:"100%"},children:r(g,{variant:"body2",children:o})},d)))]})]}),i(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[Le(Ee,"Bank Key","ChangeBK")&&(!(t!=null&&t.requestType)&&((wr=n==null?void 0:n.itmStatus)==null?void 0:wr.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):""),Le(Ee,"Bank Key","ChangeBK")&&((c==null?void 0:c.role)==="Super User"&&!(t!=null&&t.requestType)&&((Rr=n==null?void 0:n.itmStatus)==null?void 0:Rr.toUpperCase())!=="OPEN"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{button_Outlined:j,mr:1},onClick:Xe,children:"Save As Draft"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),u===I.length-1?i(V,{children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,disabled:u===0,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Qe,disabled:Te,children:"Submit For Review"})]}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&!(t!=null&&t.requestType)&&((Dr=n==null?void 0:n.itmStatus)==null?void 0:Dr.toUpperCase())!=="OPEN"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{button_Outlined:j,mr:1},onClick:Xe,children:"Save As Draft"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),u===I.length-1?i(V,{children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:se,disabled:Te,children:"Submit For Review"})]}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):""),Le(Ee,"Bank Key","ChangeBK")&&((c==null?void 0:c.role)==="Super User"&&(t==null?void 0:t.requestType)==="Create"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{button_Outlined:j,mr:1},onClick:Sr,disabled:Ce,children:"Approve"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Qe,children:"Submit For Approval"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(t==null?void 0:t.requestType)==="Change"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{button_Outlined:j,mr:1},onClick:xr,disabled:Ce,children:"Approve"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Qe,children:"Submit For Approval"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&((Pr=n==null?void 0:n.itmStatus)==null?void 0:Pr.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:j,mr:1},onClick:Ie,children:"Correction"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:se,children:"Submit For Approval"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&((Kr=n==null?void 0:n.itmStatus)==null?void 0:Kr.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&((et=n==null?void 0:n.itmStatus)==null?void 0:et.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&((rt=n==null?void 0:n.itmStatus)==null?void 0:rt.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:j,mr:1},onClick:Ie,children:"Correction"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:se,children:"Submit For Approval"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&((tt=n==null?void 0:n.itmStatus)==null?void 0:tt.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:j,mr:1},onClick:Ie,children:"Correction"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{button_Outlined:j,mr:1},onClick:se,disabled:Ce,children:"Approve"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&((nt=n==null?void 0:n.itmStatus)==null?void 0:nt.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:j,mr:1},onClick:Ie,children:"Correction"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{button_Outlined:j,mr:1},onClick:se,disabled:Ce,children:"Approve"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(t==null?void 0:t.requestType)==="Create"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),u===I.length-1?r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Cr,children:"Submit For Review"}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(t==null?void 0:t.requestType)==="Change"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),u===I.length-1?r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Ge,children:"Submit For Review"}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&((st=n==null?void 0:n.itmStatus)==null?void 0:st.toUpperCase())!=="OPEN"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:j,mr:1},onClick:un,children:"Save As Draft"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),u===I.length-1?i(V,{children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:se,disabled:Te,children:"Submit For Review"})]}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&((ot=n==null?void 0:n.itmStatus)==null?void 0:ot.toUpperCase())!=="OPEN"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:j,mr:1},onClick:Xe,children:"Save As Draft"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:u===0,children:"Back"}),u===I.length-1?i(V,{children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:se,disabled:Te,children:"Submit For Review"})]}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:u===I.length-1,children:"Next"})]})}):"")]}),i(qe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Jt,onClose:Be,children:[i(Me,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(g,{variant:"h6",children:"REMARKS"}),r($e,{sx:{width:"max-content"},onClick:Be,children:r(er,{})})]}),r(je,{sx:{padding:".5rem 1rem"},children:r(ee,{children:i(D,{sx:{minWidth:400},children:[r(he,{sx:{height:"auto"},fullWidth:!0,children:r(pe,{sx:{backgroundColor:"#F5F5F5"},value:R,onChange:De,multiline:!0,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})}),Ye&&r(x,{children:r(g,{style:{color:"red"},children:"Please Enter Remarks"})})]})})}),i(Oe,{sx:{display:"flex",justifyContent:"end"},children:[r(l,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Be,children:"Cancel"}),r(l,{className:"button_primary--normal",type:"save",onClick:Sn,variant:"contained",children:"Submit"})]})]}),i(qe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Gt,onClose:Re,children:[i(Me,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(g,{variant:"h6",children:"REMARKS"}),r($e,{sx:{width:"max-content"},onClick:Re,children:r(er,{})})]}),r(je,{sx:{padding:".5rem 1rem"},children:r(ee,{children:i(D,{sx:{minWidth:400},children:[r(he,{sx:{height:"auto"},fullWidth:!0,children:r(pe,{sx:{backgroundColor:"#F5F5F5"},value:R,onChange:De,multiline:!0,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})}),Ye&&r(x,{children:r(g,{style:{color:"red"},children:"Please Enter Remarks"})})]})})}),i(Oe,{sx:{display:"flex",justifyContent:"end"},children:[r(l,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Re,children:"Cancel"}),r(l,{className:"button_primary--normal",type:"save",onClick:kn,variant:"contained",children:"Submit"})]})]}),i(qe,{open:oe,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[r(Me,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:r(g,{variant:"h6",children:"New Bank Key"})}),r(je,{sx:{padding:".5rem 1rem"},children:i(x,{container:!0,spacing:1,children:[i(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(g,{children:["Bank Key",r("span",{style:{color:"red"},children:"*"})]}),r(he,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:r(pe,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",placeholder:"Enter Bank Key Name",required:!0})})]}),i(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(g,{children:["Valid From",r("span",{style:{color:"red"},children:"*"})]}),r(he,{fullWidth:!0,sx:{margin:".5em 0px"},children:r(rr,{dateAdapter:tr,children:r(nr,{slotProps:{textField:{size:"small"}}})})})]}),i(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(g,{children:["Valid To",r("span",{style:{color:"red"},children:"*"})]}),r(he,{fullWidth:!0,sx:{margin:".5em 0px"},children:r(rr,{dateAdapter:tr,children:r(nr,{slotProps:{textField:{size:"small"}}})})})]})]})}),i(Oe,{sx:{display:"flex",justifyContent:"end"},children:[r(l,{sx:{width:"max-content",textTransform:"capitalize"},children:"Cancel"}),r(l,{className:"button_primary--normal",type:"save",variant:"contained",children:"Proceed"})]})]})]})})};export{ls as default};
