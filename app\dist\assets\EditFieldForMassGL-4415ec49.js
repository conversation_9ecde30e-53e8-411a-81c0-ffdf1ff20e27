import{r as f,i as $,l as V,sw as ee,a as r,g_ as te,j as A,T as R,hy as ae,a3 as B,gR as oe,gS as re,hJ as se,$ as K,hA as _,F as N,hN as ce,is as j,sx as ne,w as J,hK as W,hP as G}from"./index-fdfa25a0.js";function le(a,n){return Array.isArray(n)&&n.find(S=>S.code===a)||""}const me=({label:a,value:n,length:P,units:S,onSave:de,fieldGroup:l,isEditMode:H,activeTabIndex:M,visibility:d,isExtendMode:ge,pcTabs:O,selectedRowData:z,options:ie=[],type:m})=>{var q,I,U;const[g,C]=f.useState(n),[he,Q]=f.useState(!1),p=$(e=>e.AllDropDown.dropDown),c=$(e=>e.generalLedger.MultipleGLData),i=V();le(g,p),$(e=>e.edit.payload);let T={},k=-1;for(let e=0;e<(c==null?void 0:c.length);e++)if(((q=c[e])==null?void 0:q.GLAccount)===z){T=c[e],k=e;break}console.log("selectedrowdata",z,c);let h=O[M];console.log("activerow",k,T,h);const D=(e,t)=>{const s=e==null?void 0:e.find(o=>(o==null?void 0:o.fieldName)===t);return s?s.value:""},y=c[k];let u=a.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");f.useEffect(()=>{C(n)},[n]),f.useEffect(()=>{(d==="0"||d==="Required")&&(console.log(d,"visibility"),i(ee(u)))},[h]);const E={label:a,value:g,units:S,type:m};console.log("fieldData",E,y);const v=(e,t)=>{console.log("fieldGroup",l,e,t),i(j({keyname:u.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:t}));let s=c==null?void 0:c.map((o,w)=>{let F=O[M];if(w===k){let b=o.viewData,Y=o.viewData[F];console.log("temp",Y);let x=o.viewData[F][l];return console.log("temp2",x),{...o,viewData:{...b,[F]:{...Y,[l]:x==null?void 0:x.map(L=>L.fieldName===e?{...L,value:t}:L)}}}}else return o});console.log("changedData",s),i(ne(s))},X=e=>{console.log("compcode",e);const t=o=>{console.log("value",o),i(j({keyname:"Currency",data:""})),i(G({keyName:"Currency",data:o.body}))},s=o=>{console.log(o,"error in dojax")};J(`/${W}/data/getCurrency?companyCode=${e==null?void 0:e.code}`,"get",t,s)},Z=e=>{console.log("countryyyyy",e);const t=o=>{console.log("value",o),i(j({keyname:"Region",data:""})),i(G({keyName:"Region",data:o.body}))},s=o=>{console.log(o,"error in dojax")};J(`/${W}/data/getRegionBasedOnCountry?country=${e==null?void 0:e.code}`,"get",t,s)};return f.useEffect(()=>{(a==="Key Of Last Interest Calc"||a==="Date Of Last Interest Run")&&C(parseInt(n.replace("/Date(","").replace(")/","")))},[n]),console.log("editedValue[key] ",p[u]),console.log("editedValue[key] ",g),r(K,{item:!0,children:r(te,{children:H?A(N,{children:[A(R,{variant:"body2",color:"#777",children:[a," ",d==="Required"||d==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),m==="Drop Down"?r(ae,{options:p[u]??[],value:D(y.viewData[h][l],a)&&((I=p[u])==null?void 0:I.filter(e=>e.code===D(y.viewData[h][l],a)))&&((U=p[u])==null?void 0:U.filter(e=>e.code===D(y.viewData[h][l],a))[0])||"",onChange:(e,t)=>{a==="Comp Code"&&X(t),a==="Country/Reg"&&Z(t),v(a,t==null?void 0:t.code),console.log("newValue",t),C(t.code),Q(!0),console.log("keys",u)},getOptionLabel:e=>(console.log("optionn",e),e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??""),renderOption:(e,t)=>(console.log("option vakue",t),r("li",{...e,children:r(R,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})})),renderInput:e=>r(B,{...e,variant:"outlined",placeholder:`Select ${E.label}`,size:"small",label:null})}):m==="Input"?r(B,{variant:"outlined",size:"small",value:D(y.viewData[h][l],a).toUpperCase(),placeholder:`ENTER ${E.label.toUpperCase()}`,inputProps:{maxLength:P},onChange:e=>{console.log("event",e.target.value);const t=e.target.value;if(t.length>0&&t[0]===" ")v(a,t.trimStart());else{let s=t.toUpperCase();v(a,s)}}}):m==="Calendar"?r(oe,{dateAdapter:re,children:r(se,{slotProps:{textField:{size:"small"}},value:g,placeholder:"Select Date Range",onChange:e=>{v(e),C(e)}})}):m==="Radio Button"?r(K,{item:!0,md:2,children:r(_,{sx:{padding:0},checked:D(y.viewData[h][l],a)==!0,onChange:e=>{console.log("oncheckbox",a,e.target.checked),v(a,e.target.checked)}})}):""]}):r(N,{children:A(N,{children:[A(R,{variant:"body2",color:"#777",children:[a," ",d==="Required"||d==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),A(R,{variant:"body2",fontWeight:"bold",children:[a==="Analysis Period From"||a==="Analysis Period To"?ce(g).format("DD MMM YYYY"):g,m==="Radio Button"?r(_,{sx:{padding:0},checked:g,disabled:!0}):""]})]})})})})};export{me as E};
