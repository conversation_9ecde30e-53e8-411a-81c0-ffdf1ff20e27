import{ah as X,ai as Y,aj as w,cK as S,a as t,j as f,hA as J,V as M,kG as Q,gK as j,B as U,F as O,a3 as Z,h8 as V,hy as N,gk as p}from"./index-fdfa25a0.js";var P={},D=Y;Object.defineProperty(P,"__esModule",{value:!0});var ee=P.default=void 0,re=D(X()),le=w;ee=P.default=(0,re.default)((0,le.jsx)("path",{d:"M16 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V8zm3 16H5V5h10v4h4zM7 17h10v-2H7zm5-10H7v2h5zm-5 6h10v-2H7z"}),"FeedOutlined");const _=8,F=S.createContext({}),te=S.forwardRef((r,g)=>{const s=S.useContext(F);return t("div",{ref:g,...r,...s})});function se(r){const{children:g,...s}=r,h=[];g.forEach(x=>{h.push(x)});const i=h.length,c=48,z=()=>i>8?8*c:h.length*c;return t("div",{ref:r.ref,children:t(F.Provider,{value:s,children:t(p,{itemData:h,height:z()+2*_,width:"100%",outerElementType:te,innerElementType:"ul",itemSize:c,overscanCount:5,itemCount:i,children:({data:x,index:v,style:y})=>{const A=x[v],o={...y,top:y.top+_};return S.cloneElement(A,{style:o})}})})})}const de=({param:r,mandatory:g=!1,dropDownData:s,allDropDownData:h,selectedValues:i,inputState:c,handleSelectAll:z,handleSelectionChange:x,handleMatInputChange:v,handleScroll:y,dropdownRef:A,errors:o,formatOptionLabel:b,handlePopoverOpen:H,handlePopoverClose:R,handleMouseEnterPopover:T,handleMouseLeavePopover:$,isPopoverVisible:L,popoverId:m,popoverAnchorEl:q,popoverRef:B,popoverContent:G,isMaterialNum:k=!1,isLoading:K=!1,isSelectAll:W=!1,singleSelect:n=!1})=>{const E=()=>{const l=k?(s==null?void 0:s[r==null?void 0:r.key])||[]:(s==null?void 0:s[r==null?void 0:r.key])||(h==null?void 0:h[r==null?void 0:r.key])||[];return W&&l.length>0&&!n?["Select All",...l]:l},a=()=>{if(!n)return i[r.key]||[];const l=i[r.key];return Array.isArray(l)&&l.length>0?l[0]:null};return t(N,{multiple:!n,disableListWrap:!0,ListboxComponent:se,options:E(),getOptionLabel:l=>typeof l=="string"?l:l==="Select All"?"Select All":b(l),value:n?a():i[r.key]||[],inputValue:k&&!n?c==null?void 0:c.code:void 0,onChange:(l,e)=>{!n&&e.includes("Select All")?z(r.key,E().filter(u=>u!=="Select All")):n?x(r.key,e?[e]:[]):x(r.key,e)},disableCloseOnSelect:!n,renderOption:(l,e,{selected:u})=>{var d,C;return f("li",{...l,style:{display:"flex",alignItems:"center",width:"100%",cursor:"pointer"},children:[!n&&t(J,{checked:e==="Select All"?((d=i[r.key])==null?void 0:d.length)===E().length-1:(C=i[r.key])==null?void 0:C.some(I=>(I==null?void 0:I.code)===(e==null?void 0:e.code)),sx:{marginRight:1}}),typeof e=="string"||e==="Select All"?t("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:e,children:e}):f("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:`${e==null?void 0:e.code}${e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""}`,children:[t("strong",{children:e==null?void 0:e.code}),e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""]})]})},renderTags:(l,e)=>{if(n)return null;const u=l.map(d=>typeof d=="string"?d:b(d)).join("<br />");return l.length>1?f(O,{children:[t(M,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${b(l[0])}`,...e({index:0})}),t(M,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`+${l.length-1}`,onMouseEnter:d=>H(d,u),onMouseLeave:R}),t(Q,{id:m,open:L,anchorEl:q,onClose:R,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},onMouseEnter:T,onMouseLeave:$,ref:B,sx:{"& .MuiPopover-paper":{backgroundColor:j.primary.whiteSmoke,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"10px",fontSize:"0.875rem",color:j.blue.main,border:"1px solid #ddd"}},children:t(U,{sx:{maxHeight:"270px",overflowY:"auto",padding:"5px"},dangerouslySetInnerHTML:{__html:G}})})]}):l.map((d,C)=>t(M,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${b(d)}`,...e({index:C})}))},renderInput:l=>{var e,u;return t(Z,{...l,label:g?f(O,{children:[f("strong",{children:["Select ",r.key]})," ",t("span",{style:{color:(u=(e=j)==null?void 0:e.error)==null?void 0:u.dark},children:"*"})]}):`Select ${r.key}`,variant:"outlined",error:!!o[r.key],helperText:o[r.key],onChange:v||void 0,ListboxProps:{onScroll:k?y:void 0,ref:k?A:void 0},InputProps:{...l.InputProps,endAdornment:f(O,{children:[K?t(V,{size:20,sx:{mr:1}}):null,l.InputProps.endAdornment]})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"8px",height:50,boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},"& .MuiInputLabel-root":{fontWeight:500}}})}},r.key)};export{de as F,ee as d};
