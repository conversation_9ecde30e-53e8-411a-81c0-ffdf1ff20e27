import{r as y,l as fr,b as br,u as vr,i as R,hZ as Sr,a as n,j as h,h_ as xe,T as A,B as Y,gZ as fe,$ as C,im as be,hC as Mn,h$ as jn,i0 as Un,i1 as Vn,i2 as Wn,i3 as Gn,i4 as _n,i5 as kn,a0 as Ne,g_ as D,hN as We,i6 as yr,F as U,ht as Ar,J as Er,i7 as Hn,fV as Kn,h1 as Xn,W as Ge,hI as Yn,fX as Zn,gQ as Jn,a3 as Qn,fH as Pn,a1 as d,ho as gr,h8 as Tr,hp as Nr,hv as Ir,i8 as qr,gU as $,i9 as Fr,ia as zr,hD as Ie,ib as qe,ic as Br,id as $r,ie as Or,P as _,hE as k,gW as x,ih as Ln,w as N,io as z,ig as Mr,ip as <PERSON>,ii as jr,ij as Ur,ik as he,hP as ee,iq as Vr,hn as Wr}from"./index-fdfa25a0.js";import{T as wn}from"./Timeline-bb89efb4.js";const Kr=()=>{var hn,an,pn,un,mn,Cn,xn,fn,bn,vn,Sn,yn,An,En,gn,Tn,Nn,In,qn,Fn,zn,Bn,$n;const[I,_e]=y.useState(!1);y.useState(0);const[ae,Rn]=y.useState(!0);y.useState({});const[F,Dn]=y.useState([]),[f,ve]=y.useState(0),[ke,et]=y.useState([]),[u,nt]=y.useState();y.useState(!1),y.useState([]);const[tt,b]=y.useState(!1),[rt,E]=y.useState(!1),[He,v]=y.useState(""),[ot,S]=y.useState(!1),[Gr,g]=y.useState(!0),[_r,T]=y.useState(!1),[st,m]=y.useState(!1),[ct,ze]=y.useState(!1),[it,Be]=y.useState(!1),[lt,$e]=y.useState(!1),[ne,Se]=y.useState(""),[se,dt]=y.useState([]),[ht,Ke]=y.useState(!1),[ce,at]=y.useState([]),[pt,Xe]=y.useState(!1),[ut,Oe]=y.useState(!0),[mt,Q]=y.useState(!1),[Z,P]=y.useState(!0),[Ct,K]=y.useState(!1),[xt,Ye]=y.useState(!1),[ft,Me]=y.useState(""),[Ze,bt]=y.useState(0),[Je,je]=y.useState(""),[vt,St]=y.useState(""),[Qe,yt]=y.useState([]),[ie,At]=y.useState(""),[Et,Pe]=y.useState(!1),H=fr(),Le=br(),gt=vr(),we=R(s=>s.appSettings);let ye=R(s=>{var i;return(i=s.userManagement.entitiesAndActivities)==null?void 0:i["General Ledger"]}),a=R(s=>{var i;return(i=s==null?void 0:s.initialData)==null?void 0:i.IWMMyTask}),c=R(s=>s.userManagement.userData),e=gt.state,Tt=R(s=>s.generalLedger.requiredFields),Ae=R(s=>s.edit.selectedCheckBox);console.log("extended",Ae);let t=R(s=>s.userManagement.taskData);const o=R(s=>s.edit.payload);console.log("ccroewdata",o);const te=R(s=>s.generalLedger.generalLedgerViewData);console.log(te,"generalLedgerViewData"),console.log("generalLedgerRowData",e),console.log("costCenterDetails",ke),console.log(ne,"Remarks");const Nt=()=>{Pe(!1)};console.log("taskData",a),console.log("taskRowDetails",t),console.log("glids",u);const X=()=>{Se(""),Oe(!0),Xe(!1)},It=(s,i)=>{const r=p=>{H(ee({keyName:s,data:p.body})),bt(B=>B+1)},l=p=>{console.log(p)};N(`/${z}/data/${i}`,"get",r,l)},qt=()=>{var s,i;(i=(s=Ln)==null?void 0:s.generalLedger)==null||i.map(r=>{It(r==null?void 0:r.keyName,r==null?void 0:r.endPoint)})},Ft=()=>{var r,l;const s=p=>{H(ee({keyName:"TaxCategory",data:p.body}))},i=p=>{console.log(p)};N(`/${z}/data/getTaxCategory?companyCode=${(r=a==null?void 0:a.body)!=null&&r.compCode?(l=a==null?void 0:a.body)==null?void 0:l.compCode:e==null?void 0:e.compCode}`,"get",s,i)},zt=()=>{var r,l;const s=p=>{H(ee({keyName:"HouseBank",data:p.body}))},i=p=>{console.log(p)};N(`/${z}/data/getHouseBank?companyCode=${(r=a==null?void 0:a.body)!=null&&r.compCode?(l=a==null?void 0:a.body)==null?void 0:l.compCode:e==null?void 0:e.compCode}`,"get",s,i)},Bt=()=>{var r,l;const s=p=>{H(ee({keyName:"FieldStatusGroup",data:p.body}))},i=p=>{console.log(p)};N(`/${z}/data/getFieldStatusGroup?companyCode=${(r=a==null?void 0:a.body)!=null&&r.compCode?(l=a==null?void 0:a.body)==null?void 0:l.compCode:e==null?void 0:e.compCode}`,"get",s,i)},$t=()=>{var r,l;const s=p=>{H(ee({keyName:"GroupAccountNumber",data:p.body}))},i=p=>{console.log(p)};N(`/${z}/data/getGroupAccountNumber?chartAccount=${(r=a==null?void 0:a.body)!=null&&r.coa?(l=a==null?void 0:a.body)==null?void 0:l.coa:e==null?void 0:e.chartOfAccount}`,"get",s,i)},Ot=()=>{var r,l;const s=p=>{H(ee({keyName:"AlternativeAccountNumber",data:p.body}))},i=p=>{console.log(p)};N(`/${z}/data/getAlternativeAccountNumber?chartAccount=${(r=a==null?void 0:a.body)!=null&&r.coa?(l=a==null?void 0:a.body)==null?void 0:l.coa:e==null?void 0:e.chartOfAccount}`,"get",s,i)},Mt=()=>{var r,l;const s=p=>{H(ee({keyName:"AccountGroup",data:p.body}))},i=p=>{console.log(p)};N(`/${z}/data/getAccountGroupCodeDesc?chartAccount=${(r=a==null?void 0:a.body)!=null&&r.coa?(l=a==null?void 0:a.body)==null?void 0:l.coa:e==null?void 0:e.chartOfAccount}`,"get",s,i)};y.useEffect(()=>{At(Sr("GL"))},[]);var j={GeneralLedgerID:u!=null&&u.GeneralLedgeId?u==null?void 0:u.GeneralLedgeId:"",Action:(e==null?void 0:e.requestType)==="Create"?"I":(e==null?void 0:e.requestType)==="Change"?"U":(t==null?void 0:t.processDesc)==="Create"?"I":(t==null?void 0:t.processDesc)==="Change"?"U":(e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend"||(e==null?void 0:e.requestType)==="Extend"?"I":"U",RequestID:"",TaskStatus:"",TaskId:t!=null&&t.taskId?t==null?void 0:t.taskId:"",Remarks:ne||"",Info:"",CreationId:(t==null?void 0:t.processDesc)==="Create"?t==null?void 0:t.subject.slice(3):(e==null?void 0:e.requestType)==="Create"?e==null?void 0:e.requestId.slice(3):"",EditId:(t==null?void 0:t.processDesc)==="Change"?t==null?void 0:t.subject.slice(3):(e==null?void 0:e.requestType)==="Change"?e==null?void 0:e.requestId.slice(3):"",ExtendId:(t==null?void 0:t.processDesc)==="Extend"?t==null?void 0:t.subject.slice(3):(e==null?void 0:e.requestType)==="Extend"&&e.requestId?e==null?void 0:e.requestId.slice(3):"",MassExtendId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:(e==null?void 0:e.requestType)==="Create"?"Create":(e==null?void 0:e.requestType)==="Change"?"Change":(e==null?void 0:e.requestType)==="Extend"?"Extend":(t==null?void 0:t.processDesc)==="Create"?"Create":(t==null?void 0:t.processDesc)==="Change"?"Change":((t==null?void 0:t.processDesc)==="Extend","Extend"),ReqCreatedBy:c==null?void 0:c.user_id,ReqCreatedOn:t!=null&&t.createdOn?"/Date("+(t==null?void 0:t.createdOn)+")/":e!=null&&e.createdOn?"/Date("+Date.parse(e==null?void 0:e.createdOn)+")/":"",ReqUpdatedOn:"",RequestStatus:"",Testrun:ut===!0,COA:e!=null&&e.chartOfAccount?e==null?void 0:e.chartOfAccount:u!=null&&u.ChartOfAccount?u==null?void 0:u.ChartOfAccount:"",CompanyCode:e!=null&&e.compCode?e==null?void 0:e.compCode:u!=null&&u.CompCode?u==null?void 0:u.CompCode:"",CoCodeToExtend:Ae[0]?Ae.join(","):u!=null&&u.CompanyCodesExtendedTo?u==null?void 0:u.CompanyCodesExtendedTo:"",GLAccount:e!=null&&e.glAccount?e==null?void 0:e.glAccount:u!=null&&u.GLAccount?u==null?void 0:u.GLAccount:"",Accounttype:o!=null&&o.AccountType?o==null?void 0:o.AccountType:"",AccountGroup:o!=null&&o.AccountGroup?o==null?void 0:o.AccountGroup:"",GLname:o!=null&&o.ShortText?o==null?void 0:o.ShortText:"",Description:o!=null&&o.LongText?o==null?void 0:o.LongText:"",TradingPartner:o!=null&&o.TradingPartner?o==null?void 0:o.TradingPartner:"",GroupAccNo:o!=null&&o.GroupAccountNumber?o==null?void 0:o.GroupAccountNumber:"121100",AccountCurrency:o!=null&&o.AccountCurrency?o==null?void 0:o.AccountCurrency:"",Exchangerate:o!=null&&o.ExchangeRateDifferenceKey?o==null?void 0:o.ExchangeRateDifferenceKey:"",Balanceinlocrcy:(o==null?void 0:o.OnlyBalanceInLocalCurrency)===!0?"X":"",Taxcategory:o!=null&&o.TaxCategory?o==null?void 0:o.TaxCategory:"",Pstnwotax:(o==null?void 0:o.PostingWithoutTaxAllowed)===!0?"X":"",ReconAcc:o!=null&&o.ReconAccountForAccountType?o==null?void 0:o.ReconAccountForAccountType:"",Valuationgrp:o!=null&&o.ValuationGroup?o==null?void 0:o.ValuationGroup:"",AlterAccno:o!=null&&o.AlternativeAccountNumber?o==null?void 0:o.AlternativeAccountNumber:"",Openitmmanage:(o==null?void 0:o.OpenItemManagement)===!0?"X":"",Sortkey:o!=null&&o.SortKey?o==null?void 0:o.SortKey:"",CostEleCategory:o!=null&&o.CostElementCategory?o==null?void 0:o.CostElementCategory:"",FieldStsGrp:o!=null&&o.FieldStatusGroup?o==null?void 0:o.FieldStatusGroup:"",PostAuto:(o==null?void 0:o.PostAutomaticallyOnly)===!0?"X":"",Supplementautopost:(o==null?void 0:o.SupplementAutoPostings)===!0?"X":"",Planninglevel:o!=null&&o.PlanningLevel?o==null?void 0:o.PlanningLevel:"",Relvnttocashflow:(o==null?void 0:o.RelevantToCashFlows)===!0?"X":"",HouseBank:o!=null&&o.HouseBank?o==null?void 0:o.HouseBank:"",AccountId:o!=null&&o.AccountID?o==null?void 0:o.AccountID:"",Interestindicator:o!=null&&o.InterestIndicator?o==null?void 0:o.InterestIndicator:"",ICfrequency:o!=null&&o.InterestCalculationFrequency?o==null?void 0:o.InterestCalculationFrequency:"",KeydateofLIC:o!=null&&o.KeyDateOfLastIntCalc?o==null?void 0:o.KeyDateOfLastIntCalc:"",LastIntrstundate:o!=null&&o.DateOfLastInterestRun?o==null?void 0:o.DateOfLastInterestRun:"",AccmngExistsys:"",Infationkey:"",Tolerancegrp:"",AuthGroup:"",AccountClerk:"",ReconAccReady:"",PostingBlocked:"",PlanningBlocked:""};const jt=()=>{let s=F[f];console.log("activeTabName",s,F);let i=Object.entries(te);console.log("viewDataArray",i);const r={};i.map(l=>{console.log("bottle",l[1]);let p=Object.entries(l[1]);return console.log("notebook",p),p.forEach(B=>{B[1].forEach(q=>{(q==null?void 0:q.fieldType)==="Calendar"?r[q.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=parseInt(q.value.replace("/Date(","").replace(")/","")):r[q.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=q.value})}),l}),console.log("toSetArray",r),H(Mr(r))},Ut=s=>{console.log("compcode",s);const i=l=>{console.log("value",l),H(ee({keyName:"CostElementCategory",data:l.body}))},r=l=>{console.log(l,"error in dojax")};N(`/${z}/data/getCostElementCategory?accountType=${s}`,"get",i,r)},Vt=()=>{var l,p,B,q,ue,me,O;var s=(l=a==null?void 0:a.body)!=null&&l.id?{id:(p=a==null?void 0:a.body)!=null&&p.id?(B=a==null?void 0:a.body)==null?void 0:B.id:"",glAccount:(q=a==null?void 0:a.body)!=null&&q.glAccount?(ue=a==null?void 0:a.body)==null?void 0:ue.glAccount:"",compCode:(me=a==null?void 0:a.body)==null?void 0:me.compCode,reqStatus:(O=a==null?void 0:a.body)==null?void 0:O.reqStatus,screenName:(t==null?void 0:t.processDesc)==="Create"?"Create":"Change"}:{id:e!=null&&e.reqStatus?e==null?void 0:e.id:"",glAccount:e!=null&&e.glAccount?e==null?void 0:e.glAccount:"",compCode:e!=null&&e.compCode?e==null?void 0:e.compCode:"",reqStatus:e!=null&&e.reqStatus?e==null?void 0:e.reqStatus:"Approved",screenName:(e==null?void 0:e.requestType)==="Extend"||(e==null?void 0:e.requestType)==="Create"?"Create":((e==null?void 0:e.requestType)==="Change","Change")};const i=w=>{const oe=w.body.viewData,Te=w.body;H(Vr(oe));const Ce=Object.keys(oe);Dn(Ce);const xr=["Attachment & Documents"];F.concat(xr);const On=Ce.map(de=>({category:de,data:oe[de],setIsEditMode:_e}));Ut(oe["Type/Description"]["Control in COA"].find(de=>(de==null?void 0:de.fieldName)==="Account Type").value),et(On),nt(Te),console.log("mappedData",On,u)},r=w=>{console.log(w)};N(`/${z}/data/displayGeneralLedger`,"post",i,r,s),St(s.screenName)};console.log("ID",u),y.useEffect(()=>{Vt(),Ft(),Bt(),zt(),Mt(),Ot(),$t(),qt()},[]),y.useEffect(()=>{te.length!==0&&jt()},[te]);const Re=()=>{ze(!1)},Wt=()=>{lt?(Be(!1),$e(!1)):(Be(!1),Le("/masterDataCockpit/generalLedger"))},De=()=>{Pe(!0)},en=()=>Wr(o,Tt,yt),W=()=>{P(!0);const s=en();I?s?(ve(i=>i-1),H(Fe())):De():(ve(i=>i-1),H(Fe()))},G=()=>{const s=en();I?s?(ve(i=>i+1),H(Fe())):De():(ve(i=>i+1),H(Fe()))},Ee=()=>{_e(!0),Rn(!1)},Gt=s=>{Ye(s)},nn=()=>{m(!0),X(),ir()},_t=()=>{m(!0),X(),lr()},tn=()=>{m(!0),X(),dr()},ge=()=>{m(!0),X(),hr()},kt=()=>{m(!0),X(),ar()},rn=()=>{m(!0),X(),pr()},on=()=>{m(!0),X(),ur()},Ht=()=>{m(!0),X(),mr()},sn=()=>{m(!0),X(),Cr()},M=()=>{ze(!0)},V=()=>{Be(!0)},Kt=()=>{Ye(!0)},pe=()=>{Q(!0);const s=r=>{var l,p,B;r.statusCode===201?(b("Create"),b("Create"),v("All Data has been Validated. General Ledger can be Send for Review"),P(!1),K(!1),S("success"),g(!1),E(!0),V(),T(!0),$e(!0),P(!1),Q(!1)):(Q(!1),b("Error"),E(!1),v(`${(l=r==null?void 0:r.body)!=null&&l.message[0]?(p=r==null?void 0:r.body)==null?void 0:p.message[0]:(B=r==null?void 0:r.body)==null?void 0:B.value}`),K(!1),S("danger"),g(!1),T(!0),M(),P(!0))},i=r=>{console.log(r)};N(`/${z}/alter/validateSingleGeneralLedger`,"post",s,i,j)},re=()=>{var q,ue,me;Q(!0);var s={glName:o!=null&&o.ShortText?(q=o==null?void 0:o.ShortText)==null?void 0:q.toUpperCase():"",compCode:e!=null&&e.compCode?e==null?void 0:e.compCode:u!=null&&u.CompCode?u==null?void 0:u.CompCode:""};let i="";(ue=te==null?void 0:te["Type/Description"])==null||ue.Description.map(O=>{(O==null?void 0:O.fieldName)==="Short Text"&&(i=O==null?void 0:O.value)}),console.log((me=s==null?void 0:s.glName)==null?void 0:me.toUpperCase(),i==null?void 0:i.toUpperCase(),"checkingElement");const r=O=>{var w,oe,Te,Ce;O.statusCode===201?(b("Create"),b("Create"),v("All Data has been Validated. General Ledger can be Send for Review"),P(!1),K(!1),S("success"),g(!1),E(!0),V(),T(!0),$e(!0),(s.compCode!==""||s.glName!=="")&&(P(!1),((w=s==null?void 0:s.glName)==null?void 0:w.toUpperCase())===(i==null?void 0:i.toUpperCase())&&vt==="Change"?Q(!1):N(`/${z}/alter/fetchGlNameNCompCodeDupliChk`,"post",p,B,s)),Q(!1)):(Q(!1),b("Error"),E(!1),v(`${(oe=O==null?void 0:O.body)!=null&&oe.message[0]?(Te=O==null?void 0:O.body)==null?void 0:Te.message[0]:(Ce=O==null?void 0:O.body)==null?void 0:Ce.value}`),K(!1),S("danger"),g(!1),T(!0),M(),P(!0)),handleClose()},l=O=>{console.log(O)},p=O=>{O.body.length===0||!O.body.some(w=>w.toUpperCase()===s.glName)?(Q(!1),P(!1),Oe(!1)):(Q(!1),b("Duplicate Check"),E(!1),v("There is a direct match for the Short Text. Please change the Short Text."),K(!1),S("danger"),g(!1),T(!0),M(),P(!0))},B=O=>{console.log(O)};N(`/${z}/alter/validateSingleGeneralLedger`,"post",r,l,j)},Xt=()=>{(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")?(m(!0),Zt()):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")?(m(!0),Jt()):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")?(m(!0),Qt()):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")?(m(!0),Pt()):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")?(m(!0),Lt()):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&(m(!0),wt())},Yt=()=>{(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&I?rn():(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&!I?tn():(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&!I?sn():(c==null?void 0:c.role)==="Finance"&&!(e!=null&&e.requestType)&&I||(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&I?ge():(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&!I?nn():(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&!I?on():(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&I?kt():(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&!I?_t():(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&!I&&Ht()},Zt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID NLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};console.log("remarkssssssssss",ne),N(`/${z}/alter/generalLedgerSendForCorrection`,"post",s,i,j)},Jt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID CLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};console.log("hsdfjgdh",j),N(`/${z}/alter/changeGeneralLedgerSendForCorrection`,"post",s,i,j)},Qt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID ELS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};console.log("hsdfjgdh",j),N(`/${z}/alter/extendGeneralLedgerSendForCorrection`,"post",s,i,j)},Pt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID NLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};N(`/${z}/alter/generalLedgerSendForReview`,"post",s,i,j)},Lt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID CLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};console.log("remarksssaaaa",ne),N(`/${z}/alter/changeGeneralLedgerSendForReview`,"post",s,i,j)},wt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID ELS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};N(`/${z}/alter/extendGeneralLedgerSendForReview`,"post",s,i,j)},cn=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:s=>h(U,{children:[n(jr,{index:s.row.id,name:s.row.docName}),n(Ur,{index:s.row.id,name:s.row.docName})]})}],Rt=()=>{let s=t!=null&&t.subject?t==null?void 0:t.subject:e==null?void 0:e.requestId,i=r=>{var l=[];r.documentDetailDtoList.forEach(p=>{var B={id:p.documentId,docType:p.fileType,docName:p.fileName,uploadedOn:We(p.docCreationDate).format(we.date),uploadedBy:p.createdBy};l.push(B)}),dt(l)};N(`/${he}/documentManagement/getDocByRequestId/${s}`,"get",i)},Dt=()=>{let s=t!=null&&t.subject?t==null?void 0:t.subject:e==null?void 0:e.requestId,i=l=>{console.log("commentsdata",l);var p=[];l.body.forEach(B=>{var q={id:B.requestId,comment:B.comment,user:B.createdByUser,createdAt:B.updatedAt};p.push(q)}),at(p)},r=l=>{console.log(l)};N(`/${z}/activitylog/fetchTaskDetailsForRequestId?requestId=${s}`,"get",i,r)},er=()=>{nr()},nr=()=>{S(!1),M(),b("Confirm"),v("Do You Want to Save as Draft ?"),K(!0),Me("proceed"),je("Create")},Ue=()=>{tr()},tr=()=>{S(!1),M(),b("Confirm"),v("Do You Want to Save as Draft?"),K(!0),Me("proceed"),je("Change")},rr=()=>{or()},or=()=>{S(!1),M(),b("Confirm"),v("Do You Want to Save as Draft?"),K(!0),Me("proceed"),je("Extend")},sr=()=>{if(console.log(Je,"dialogType"),Ve(),m(!0),Je==="Change"){const s=r=>{if(m(!1),r.statusCode===200){console.log("success"),b("Create"),v(`General Ledger Saved As Draft with ID CLS${r.body} `),K(!1),S("success"),g(!1),E(!0),V(),T(!0);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`CLS${r==null?void 0:r.body}`},p=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${he}/documentManagement/updateDocRequestId`,"post",p,B,l)}else b("Error"),E(!1),v("Failed Saving General Ledger"),K(!1),S("danger"),g(!1),T(!0),M();handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/changeGeneralLedgerAsDraft`,"post",s,i,j)}else{const s=r=>{if(Ve(),m(!1),r.statusCode===200){console.log("success"),b("Create"),v(`Cost Center Saved As Draft with ID NLS${r.body} `),K(!1),S("success"),g(!1),E(!0),V(),T(!0);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`NCS${r==null?void 0:r.body}`},p=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${he}/documentManagement/updateDocRequestId`,"post",p,B,l)}else b("Error"),E(!1),v("Failed Saving General Ledger"),K(!1),S("danger"),g(!1),T(!0),M();handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/generalLedgerAsDraft`,"post",s,i,j)}},J=()=>{Oe(!1),Xe(!0)},cr=()=>{var s,i;Ze==((i=(s=Ln)==null?void 0:s.generalLedger)==null?void 0:i.length)?m(!1):m(!0)};y.useEffect(()=>{Rt(),Dt()},[]),y.useEffect(()=>{cr()},[Ze]);const ln=F.map(s=>{const i=ke.filter(r=>{var l;return((l=r.category)==null?void 0:l.split(" ")[0])==(s==null?void 0:s.split(" ")[0])});if(i.length!=0)return{category:s==null?void 0:s.split(" ")[0],data:i[0].data}}).map((s,i)=>{if(console.log("categoryy",s.category),(s==null?void 0:s.category)=="Type/Description"&&f==0)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>h(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>(console.log("fieldDatatttt",l),n(be,{length:l.maxLength,label:l.fieldName,value:l.value,visibility:l.visibility,onSave:p=>handleFieldSave(l.fieldName,p),data:o,isEditMode:I,type:l.fieldType,field:l,taskRequestId:e==null?void 0:e.requestId,generalLedgerRowData:e})))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Control"&&f==1)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>h(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>n(be,{label:l.fieldName,value:(l==null?void 0:l.value)==="X",onSave:p=>handleFieldSave(l.fieldName,p),isEditMode:I,type:l.fieldType,taskRequestId:e==null?void 0:e.requestId,visibility:l.visibility,data:o,generalLedgerRowData:e},l.fieldName))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Create/Bank/Interest"&&f==2)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>h(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>n(be,{label:l.fieldName,value:l.value,onSave:p=>handleFieldSave(l.fieldName,p),isEditMode:I,type:l.fieldType,taskRequestId:e==null?void 0:e.requestId,data:o,generalLedgerRowData:e},l.fieldName))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Keyword/Translation"&&f==3)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>h(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>n(be,{label:l.fieldName,value:l.value,onSave:p=>handleFieldSave(l.fieldName,p),isEditMode:I,type:l.fieldType,taskRequestId:e==null?void 0:e.requestId,visibility:l.visibility,data:o,generalLedgerRowData:e},l.fieldName))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Information"&&f==4)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>h(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>n(be,{label:l.fieldName,value:l.value,onSave:p=>handleFieldSave(l.fieldName,p),isEditMode:I,type:l.fieldType,taskRequestId:e==null?void 0:e.requestId,visibility:l.visibility,data:o,generalLedgerRowData:e},l.fieldName))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Attachments")return[n(U,{children:I?h(U,{children:[n(yr,{title:"GeneralLedger",useMetaData:!1,artifactId:ie,artifactName:"GeneralLedger"}),h(Ne,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(C,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(A,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!se.length&&n(Mn,{width:"100%",rows:se,columns:cn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!se.length&&n(A,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(A,{variant:"h6",children:"Comments"}),!!ce.length&&n(wn,{sx:{[`& .${jn.root}:before`]:{flex:0,padding:0}},children:ce.map(r=>h(Un,{children:[h(Vn,{children:[n(Wn,{children:n(Gn,{sx:{color:"#757575"}})}),n(_n,{})]}),n(kn,{sx:{py:"12px",px:2},children:n(Ne,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(Y,{sx:{padding:"1rem"},children:h(D,{spacing:1,children:[n(C,{sx:{display:"flex",justifyContent:"space-between"},children:n(A,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:We(r.createdAt).format("DD MMM YYYY")})}),n(A,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:r.user}),n(A,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:r.comment})]})})})})]}))}),!ce.length&&n(A,{variant:"body2",children:"No Comments Found"}),n("br",{})]})]}):h(Ne,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(C,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(A,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!se.length&&n(Mn,{width:"100%",rows:se,columns:cn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!se.length&&n(A,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(A,{variant:"h6",children:"Comments"}),!!ce.length&&n(wn,{sx:{[`& .${jn.root}:before`]:{flex:0,padding:0}},children:ce.map(r=>h(Un,{children:[h(Vn,{children:[n(Wn,{children:n(Gn,{sx:{color:"#757575"}})}),n(_n,{})]}),n(kn,{sx:{py:"12px",px:2},children:n(Ne,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(Y,{sx:{padding:"1rem"},children:h(D,{spacing:1,children:[n(C,{sx:{display:"flex",justifyContent:"space-between"},children:n(A,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"14px"},children:We(r.createdAt).format(we.date)})}),n(A,{sx:{fontSize:"14px",color:" #757575",fontWeight:"500"},children:r.user}),n(A,{sx:{fontSize:"14px",color:"#1D1D1D",fontWeight:"600"},children:r.comment})]})})})})]}))}),!ce.length&&n(A,{variant:"body2",children:"No Comments Found"}),n("br",{})]})})]}),ir=()=>{const s=r=>{m(),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Approval with ID CLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Approve"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/changeGeneralLedgerApprovalSubmit`,"post",s,i,j)},lr=()=>{const s=r=>{m(),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Approval with ID ELS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Approve"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/extendGeneralLedgerApprovalSubmit`,"post",s,i,j)},dr=()=>{const s=r=>{m(),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Approval with ID NLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Error"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/generalLedgerApprovalSubmit`,"post",s,i,j)},hr=()=>{const s=r=>{if(m(),r.statusCode===200){console.log("success"),b("Create"),v(`General Ledger Submitted For Review with ID CLS${r.body} `),S("success"),g(!1),E(!0),V(),T(!0),m(!1);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`CLS${r==null?void 0:r.body}`},p=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${he}/documentManagement/updateDocRequestId`,"post",p,B,l)}else b("Error"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1);handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/changeGeneralLedgerSubmitForReview`,"post",s,i,j)},ar=()=>{const s=r=>{if(m(),r.statusCode===200){console.log("success"),b("Create"),v(`General Ledger Submitted For Review with ID ELS${r.body} `),S("success"),g(!1),E(!0),V(),T(!0),m(!1);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`ELS${r==null?void 0:r.body}`},p=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${he}/documentManagement/updateDocRequestId`,"post",p,B,l)}else b("Error"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1);handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/extendGeneralLedgerSubmitForReview`,"post",s,i,j)},pr=()=>{const s=r=>{if(m(!1),r.statusCode===200){console.log("success"),b("Create"),v(`General Ledger Submitted for Review with ID NLS${r.body} `),S("success"),g(!1),E(!0),V(),T(!0),m(!1);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`NLS${r==null?void 0:r.body}`},p=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${he}/documentManagement/updateDocRequestId`,"post",p,B,l)}else b("Error"),E(!1),v("Failed Saving the Data"),S("danger"),g(!1),T(!0),M(),m(!1);handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/generalLedgerSubmitForReview`,"post",s,i,j)},ur=()=>{const s=r=>{m(),r.statusCode===201?(console.log("success"),b("Create"),v(`${r.message}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Error"),E(!1),v("Failed Approving General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/changeGeneralLedgerApproved`,"post",s,i,j)},mr=()=>{const s=r=>{m(),r.statusCode===201?(console.log("success"),b("Create"),v(`${r.message}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Error"),E(!1),v("Failed Approving General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/extendGeneralLedgerApproved`,"post",s,i,j)},Cr=()=>{const s=r=>{m(),r.statusCode===201?(console.log("success"),b("Create"),v(`${r.message}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Error"),E(!1),v("Failed Approving the General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/createGeneralLedgerApproved`,"post",s,i,j)},le=()=>{Ke(!0)},L=()=>{Se(""),Ke(!1)},dn=s=>{const i=s.target.value;if(i.length>0&&i[0]===" ")Se(i.trimStart());else{let r=i.toUpperCase();Se(r)}},Ve=()=>{ze(!1)};return console.log("factorsarray",F),n(U,{children:st===!0?n(Ar,{}):h("div",{style:{backgroundColor:"#FAFCFF"},children:[n(Er,{dialogState:ct,openReusableDialog:M,closeReusableDialog:Re,dialogTitle:tt,dialogMessage:He,handleDialogConfirm:Re,dialogOkText:"OK",showExtraButton:Ct,showCancelButton:!0,dialogSeverity:ot,handleDialogReject:Ve,handleExtraText:ft,handleExtraButton:sr}),rt&&n(Hn,{openSnackBar:it,alertMsg:He,handleSnackBarClose:Wt}),Qe.length!=0&&n(Hn,{openSnackBar:Et,alertMsg:"Please fill the following Field: "+Qe.join(", "),handleSnackBarClose:Nt}),h(Kn,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:ht,onClose:L,children:[h(Xn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(A,{variant:"h6",children:"Remarks"}),n(Ge,{sx:{width:"max-content"},onClick:L,children:n(Yn,{})})]}),n(Zn,{sx:{padding:".5rem 1rem"},children:n(D,{children:n(Y,{sx:{minWidth:400},children:n(Jn,{sx:{height:"auto"},fullWidth:!0,children:n(Qn,{sx:{backgroundColor:"#F5F5F5"},onChange:dn,value:ne,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),h(Pn,{sx:{display:"flex",justifyContent:"end"},children:[n(d,{sx:{width:"max-content",textTransform:"capitalize"},onClick:L,children:"Cancel"}),n(d,{className:"button_primary--normal",type:"save",onClick:Xt,variant:"contained",children:"Submit"})]})]}),h(Kn,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:pt,onClose:X,children:[h(Xn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(A,{variant:"h6",children:"Remarks"}),n(Ge,{sx:{width:"max-content"},onClick:X,children:n(Yn,{})})]}),n(Zn,{sx:{padding:".5rem 1rem"},children:n(D,{children:n(Y,{sx:{minWidth:400},children:n(Jn,{sx:{height:"auto"},fullWidth:!0,children:n(Qn,{sx:{backgroundColor:"#F5F5F5"},value:ne.toUpperCase(),onChange:dn,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),h(Pn,{sx:{display:"flex",justifyContent:"end"},children:[n(d,{sx:{width:"max-content",textTransform:"capitalize"},onClick:X,children:"Cancel"}),n(d,{className:"button_primary--normal",type:"save",onClick:Yt,variant:"contained",children:"Submit"})]})]}),n(gr,{sx:{color:"#fff",zIndex:s=>s.zIndex.drawer+1},open:mt,children:n(Tr,{color:"inherit"})}),h(C,{container:!0,sx:Nr,children:[h(C,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[h(C,{md:9,sx:{display:"flex"},children:[n(C,{children:n(Ge,{color:"primary","aria-label":"upload picture",component:"label",sx:Ir,children:n(qr,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{Le("/masterDataCockpit/generalLedger")}})})}),h(C,{children:[I?(t==null?void 0:t.processDesc)==="Create"||(e==null?void 0:e.requestType)==="Create"?n(U,{children:h(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Create General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):(t==null?void 0:t.processDesc)==="Change"||(e==null?void 0:e.requestType)==="Change"?n(U,{children:h(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Change General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):(t==null?void 0:t.processDesc)==="Extend"||(e==null?void 0:e.requestType)==="Extend"?n(U,{children:h(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Extend General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):n(U,{children:h(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Change General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):"",ae?h(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Display General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view displays the details of the General Ledger"})]}):""]})]}),h(C,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:[e!=null&&e.requestId||t!=null&&t.processDesc?n(C,{children:n(d,{variant:"outlined",size:"small",sx:$,onClick:Kt,title:"Change Log",children:n(Fr,{sx:{padding:"2px"},fontSize:"small"})})}):"",xt&&n(zr,{open:!0,closeModal:Gt,requestId:e!=null&&e.requestId?e==null?void 0:e.requestId:t==null?void 0:t.subject,requestType:e!=null&&e.requestType?e==null?void 0:e.requestType:(hn=a==null?void 0:a.body)==null?void 0:hn.processDesc,pageName:"generalLedger",controllingArea:e!=null&&e.controllingArea?e==null?void 0:e.controllingArea:(an=a==null?void 0:a.body)==null?void 0:an.compCode,centerName:e!=null&&e.costCenter?e==null?void 0:e.costCenter:(pn=a==null?void 0:a.body)==null?void 0:pn.glAccount}),Ie(ye,"General Ledger","ChangeGL")&&((c==null?void 0:c.role)==="Super User"&&(e!=null&&e.requestType)&&((un=t==null?void 0:t.itmStatus)==null?void 0:un.toUpperCase())!=="OPEN"&&ae?n(C,{gap:1,sx:{display:"flex"},children:n(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(U,{children:n(C,{item:!0,children:h(d,{variant:"outlined",size:"small",sx:$,onClick:Ee,children:["Fill Details",n(qe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&(e!=null&&e.requestType||t!=null&&t.processDesc)&&((mn=t==null?void 0:t.itmStatus)==null?void 0:mn.toUpperCase())!=="OPEN"&&ae?n(C,{gap:1,sx:{display:"flex"},children:n(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(U,{children:n(C,{item:!0,children:h(d,{variant:"outlined",size:"small",sx:$,onClick:Ee,children:["Fill Details",n(qe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Super User"&&!(e!=null&&e.requestType)&&((Cn=t==null?void 0:t.itmStatus)==null?void 0:Cn.toUpperCase())!=="OPEN"&&ae?n(C,{gap:1,sx:{display:"flex"},children:n(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(U,{children:n(C,{item:!0,children:h(d,{variant:"outlined",size:"small",sx:$,onClick:Ee,children:["Change",n(qe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&!(e!=null&&e.requestType)&&((xn=t==null?void 0:t.itmStatus)==null?void 0:xn.toUpperCase())!=="OPEN"&&ae?n(C,{gap:1,sx:{display:"flex"},children:n(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(U,{children:n(C,{item:!0,children:h(d,{variant:"outlined",size:"small",sx:$,onClick:Ee,children:["Change",n(qe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")]})]}),n(C,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:h(Y,{width:"100%",sx:{marginLeft:"40px"},children:[n(C,{item:!0,sx:{paddingTop:"2px !important"},children:h(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"Chart of Accounts"})}),h(A,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",e!=null&&e.chartOfAccount?e==null?void 0:e.chartOfAccount:u!=null&&u.ChartOfAccount?u==null?void 0:u.ChartOfAccount:""]})]})}),(e==null?void 0:e.requestType)!="Extend"?n(C,{item:!0,sx:{paddingTop:"2px !important"},children:h(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"Company Code"})}),h(A,{variant:"body2",fontWeight:"bold",children:[":"," ",e!=null&&e.compCode?e==null?void 0:e.compCode:u!=null&&u.CompCode?u==null?void 0:u.CompCode:""]})]})}):n(U,{children:n(C,{item:!0,sx:{paddingTop:"2px !important"},children:h(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"Reference Company Code"})}),h(A,{variant:"body2",fontWeight:"bold",children:[":"," ",e!=null&&e.compCode?e==null?void 0:e.compCode:u!=null&&u.CompCode?u==null?void 0:u.CompCode:""]})]})})}),n(C,{item:!0,sx:{paddingTop:"2px !important"},children:h(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"G/L Account"})}),h(A,{variant:"body2",fontWeight:"bold",children:[":"," ",e!=null&&e.glAccount?e==null?void 0:e.glAccount:u!=null&&u.GLAccount?u==null?void 0:u.GLAccount:""]})]})}),n(C,{item:!0,sx:{paddingTop:"2px !important"},children:h(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"Extended Company Code"})}),h(A,{variant:"body2",fontWeight:"bold",children:[": ",Ae.join(", ")]})]})})]})}),h(C,{container:!0,style:{marginLeft:25},children:[n(Br,{activeStep:f,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:F.map((s,i)=>n($r,{children:n(Or,{sx:{fontWeight:"700"},children:s})},s))}),ln&&((fn=ln[f])==null?void 0:fn.map((s,i)=>n(Y,{sx:{mb:2,width:"100%"},children:n(A,{variant:"body2",children:s})},i)))]})]}),h(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[Ie(ye,"General Ledger","ChangeGL")&&(!(e!=null&&e.requestType)&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})})),Ie(ye,"General Ledger","ChangeGL")&&((c==null?void 0:c.role)==="Super User"&&!(e!=null&&e.requestType)&&((bn=t==null?void 0:t.itmStatus)==null?void 0:bn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:Ue,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?h(U,{children:[n(d,{variant:"contained",size:"small",sx:{...$,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:ge,disabled:Z,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&!(e!=null&&e.requestType)&&((vn=t==null?void 0:t.itmStatus)==null?void 0:vn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:Ue,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?h(U,{children:[n(d,{variant:"contained",size:"small",sx:{...$,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:J,disabled:Z,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):""),Ie(ye,"General Ledger","ChangeGL")&&((c==null?void 0:c.role)==="Super User"&&(e==null?void 0:e.requestType)==="Create"&&((Sn=t==null?void 0:t.itmStatus)==null?void 0:Sn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:pe,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:sn,disabled:Z,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:tn,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(e==null?void 0:e.requestType)==="Change"&&((yn=t==null?void 0:t.itmStatus)==null?void 0:yn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:pe,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:on,disabled:Z,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:nn,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((An=t==null?void 0:t.itmStatus)==null?void 0:An.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:J,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((En=t==null?void 0:t.itmStatus)==null?void 0:En.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:J,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((gn=t==null?void 0:t.itmStatus)==null?void 0:gn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:pe,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:J,disabled:Z,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((Tn=t==null?void 0:t.itmStatus)==null?void 0:Tn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:pe,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:J,disabled:Z,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&((Nn=t==null?void 0:t.itmStatus)==null?void 0:Nn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:pe,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:J,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(e==null?void 0:e.requestType)==="Create"&&((In=t==null?void 0:t.itmStatus)==null?void 0:In.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?h(U,{children:[n(d,{variant:"contained",size:"small",sx:{...$,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:rn,disabled:Z,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(e==null?void 0:e.requestType)==="Change"&&((qn=t==null?void 0:t.itmStatus)==null?void 0:qn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?h(U,{children:[n(d,{variant:"contained",size:"small",sx:{...$,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:ge,disabled:Z,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((Fn=t==null?void 0:t.itmStatus)==null?void 0:Fn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:er,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?h(U,{children:[n(d,{variant:"contained",size:"small",sx:{...$,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:J,disabled:Z,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((zn=t==null?void 0:t.itmStatus)==null?void 0:zn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:Ue,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?h(U,{children:[n(d,{variant:"contained",size:"small",sx:{...$,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:J,disabled:Z,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&((Bn=t==null?void 0:t.itmStatus)==null?void 0:Bn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:J,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&(($n=t==null?void 0:t.itmStatus)==null?void 0:$n.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:h(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:rr,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?h(U,{children:[n(d,{variant:"contained",size:"small",sx:{...$,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:J,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):"")]})]})})};export{Kr as default};
