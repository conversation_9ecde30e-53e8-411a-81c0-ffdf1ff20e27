import{a as e,gQ as y,j as h,$ as c,hA as R,lV as E,gO as H,iR as S,a8 as u,iS as m,r as C,T as P,F as I}from"./index-fdfa25a0.js";const L=({field:s,checked:i,radioValue:d,onCheckboxChange:p,onRadioChange:x,isDisabled:l})=>e(c,{container:!0,md:6,alignItems:"center",children:e(y,{fullWidth:!0,children:h(c,{container:!0,alignItems:"center",children:[e(c,{item:!0,md:1,children:e(R,{checked:i,onChange:p,disabled:l})}),e(c,{item:!0,md:4,children:h(E,{sx:H,children:[s,":"]})}),e(c,{item:!0,md:7,children:h(S,{sx:{display:"flex",justifyContent:"center"},row:!0,value:d==="0"?"Mandatory":d,onChange:x,children:[e(u,{value:"Mandatory",control:e(m,{}),label:"Mandatory",disabled:l?!0:!i}),e(u,{value:"Hide",control:e(m,{}),label:"Hide",disabled:l?!0:!i}),e(u,{value:"Optional",control:e(m,{}),label:"Optional",disabled:l?!0:!i})]})})]})})}),T=({fields:s,heading:i,childCheckedStates:d,setChildCheckedStates:p,childRadioValues:x,setChildRadioValues:l,onSubmitButtonClick:W,mandatoryFields:f,DisabledChildCheck:g,fieldVisibility:F})=>{C.useState(null);const[b,k]=C.useState(!1),[v,w]=C.useState("");C.useState(s.reduce((n,a)=>(n[a]={mandatory:f.includes(a),hide:!1,optional:!1},n),{}));const M=n=>{const a=n.target.checked;k(a);const o={...d};s.forEach(t=>{g[t]||(a&&!o[t]?o[t]=!0:!a&&o[t]&&(o[t]=!1))}),p(o);const r={...x};s.forEach(t=>{g[t]||(a?r[t]=v:r[t]="")}),l(r)},O=n=>{const a=n.target.value;w(a),l(o=>{const r={...o};return s.forEach(t=>{g[t]||d[t]&&(r[t]=a)}),r})},j=n=>a=>{const o=a.target.checked;p(r=>({...r,[n]:o})),!o&&F[n]!=="0"&&(console.log(F[n],"hemlo"),l(r=>({...r,[n]:F[n]})))},z=n=>a=>{const o=a.target.value;l(r=>({...r,[n]:o}))};return h(I,{children:[h(c,{container:!0,sx:{backgroundColor:"#F1F0FF",border:"1px",borderRadius:"10px"},children:[e(c,{item:!0,md:8,children:e(u,{style:{marginLeft:"1.5px"},control:e(R,{checked:b,onChange:M}),label:e(P,{sx:{fontWeight:"700",margin:"0px !important"},children:i})})}),e(c,{item:!0,md:4,children:e(y,{sx:{display:"flex",alignItems:"center",width:"100%",fontWeight:"bold",padding:"2px 0","&:hover":{backgroundColor:"#F1F0FF"}},component:"fieldset",children:h(S,{row:!0,value:v,onChange:O,sx:{fontSize:"10px !important"},children:[e(u,{sx:{fontSize:"8px !important"},value:"Mandatory",control:e(m,{}),label:"Mandatory",disabled:!b}),e(u,{sx:{fontSize:"12px !important"},value:"Hide",control:e(m,{}),label:"Hide",disabled:!b}),e(u,{sx:{fontSize:"8px !important"},value:"Optional",control:e(m,{}),label:"Optional",disabled:!b})]})})})]}),e(c,{container:!0,md:12,sx:{display:"flex"},children:s.map(n=>e(L,{field:n,checked:d[n]||!1,isDisabled:g[n]||!1,radioValue:x[n]||"",onCheckboxChange:j(n),onRadioChange:z(n),setChildCheckedStates:p,setChildRadioValues:l},n))})]})};export{T as R};
