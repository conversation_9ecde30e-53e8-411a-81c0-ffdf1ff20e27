import{r as g,a as s,kD as F,a8 as O,hA as I,T as b,B as f,j as C,W as T,hz as K,h8 as Q,kE as X,kF as Y,a3 as Z,gk as u,P as p,kG as M,V as L,F as G}from"./index-fdfa25a0.js";const on=({matGroup:i,selectedMaterialGroup:n,setSelectedMaterialGroup:c,isDropDownLoading:V,placeholder:d="Select Option",onInputChange:S,minCharacters:r=0})=>{const[m,k]=g.useState(!1),[$,a]=g.useState(null),[R,_]=g.useState(""),[j,v]=g.useState(!1),[l,w]=g.useState(""),B=g.useRef(null),z=g.useRef(null),t=g.useMemo(()=>{const o=Array.isArray(i)?i.map(h=>({value:h.code,label:h.desc||h.code})):[];return l?o.filter(h=>h.value.toLowerCase().includes(l.toLowerCase())||h.label.toLowerCase().includes(l.toLowerCase())):o},[i,l]),W=o=>{const h=o.target.value;w(h),S&&S(o)},y=g.useCallback(o=>{const h=n.some(x=>x.code===o.value);c(h?n.filter(x=>x.code!==o.value):[...n,{code:o.value,desc:o.label}])},[n,c]),A=g.useCallback(()=>{(n==null?void 0:n.length)===i.length?c([]):c(i.map(o=>({code:o.code,desc:o.desc})))},[i,n==null?void 0:n.length,c]),D=g.useCallback(()=>{c([])},[c]),H=(o,h)=>{a(o.currentTarget),_(h),v(!0)},E=()=>{v(!1)},N=()=>{v(!0)},U=()=>{v(!1)},q=g.useCallback(({index:o,style:h})=>{if(o===0)return s(f,{component:"div",sx:{...h,padding:"4px 8px",cursor:"pointer","&:hover":{backgroundColor:"action.hover"}},onClick:A,children:s(F,{sx:{width:"100%",py:.5},children:s(O,{sx:{margin:0,"& .MuiFormControlLabel-label":{flex:1}},control:s(I,{size:"small",checked:(n==null?void 0:n.length)===i.length,indeterminate:(n==null?void 0:n.length)>0&&(n==null?void 0:n.length)<(i==null?void 0:i.length),sx:{py:.5}}),label:s(b,{sx:{fontSize:13},children:"Select All"})})})});const x=t[o-1],P=n.some(e=>e.code===x.value);return s(f,{component:"div",sx:{...h,padding:"4px 8px",cursor:"pointer","&:hover":{backgroundColor:"action.hover"}},onClick:()=>y(x),children:s(F,{sx:{width:"100%",py:.5},children:s(O,{sx:{margin:0,"& .MuiFormControlLabel-label":{flex:1}},control:s(I,{size:"small",checked:P,sx:{py:.5}}),label:C(b,{sx:{fontSize:13},children:[s("strong",{children:x.value})," - ",x.label]})})})})},[t,n,y,A,i==null?void 0:i.length]);g.useEffect(()=>{const o=h=>{z.current&&!z.current.contains(h.target)&&k(!1)};return document.addEventListener("mousedown",o),()=>{document.removeEventListener("mousedown",o)}},[]);const J=()=>{var o,h;return(n==null?void 0:n.length)===0?null:(n==null?void 0:n.length)>1?C(G,{children:[s(L,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:(o=n==null?void 0:n[0])==null?void 0:o.code}),s(L,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"},ml:.5},label:`+${(n==null?void 0:n.length)-1}`,onMouseEnter:x=>{const P=n.slice(1).map(e=>`<strong>${e.code}</strong> - ${e.desc}`).join("<br />");H(x,P)},onMouseLeave:E})]}):s(L,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:(h=n==null?void 0:n[0])==null?void 0:h.code})};return C(f,{ref:z,sx:{position:"relative",width:"100%"},children:[s(Z,{fullWidth:!0,size:"small",placeholder:d==null?void 0:d.toUpperCase(),value:l,onChange:W,onFocus:()=>k(!0),InputProps:{startAdornment:J(),endAdornment:C(f,{sx:{display:"flex",alignItems:"center"},children:[(n==null?void 0:n.length)>0&&s(T,{size:"small",onClick:o=>{o.stopPropagation(),D(),w("")},sx:{padding:"4px",mr:.5,"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:s(K,{sx:{fontSize:"16px"}})}),V?s(Q,{size:20,sx:{mr:1}}):s(T,{size:"small",onClick:o=>{o.stopPropagation(),k(!m)},sx:{padding:"4px"},children:m?s(X,{sx:{fontSize:"20px"}}):s(Y,{sx:{fontSize:"20px"}})})]})}}),m&&s(p,{sx:{position:"absolute",top:"100%",left:0,right:0,mt:1,maxHeight:300,zIndex:1e3},children:t.length===0?s(f,{sx:{p:2,textAlign:"center"},children:s(b,{variant:"body2",color:"text.secondary",children:l.length<r?`Please enter at least ${r} characters`:"No options found"})}):s(u,{height:Math.min(t.length*45+45,300),itemCount:t.length+1,itemSize:45,width:"100%",ref:B,children:q})}),s(M,{open:j,anchorEl:$,onClose:E,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"},onMouseEnter:N,onMouseLeave:U,PaperProps:{sx:{p:1,maxWidth:300}},children:s(b,{variant:"body2",dangerouslySetInnerHTML:{__html:R}})})]})};export{on as L};
