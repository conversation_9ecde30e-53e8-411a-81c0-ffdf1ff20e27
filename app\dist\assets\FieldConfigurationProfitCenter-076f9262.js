import{b as Se,r as s,j as y,a as e,kI as ye,i7 as Ce,hx as xe,$ as E,g as G,A as Y,T as O,f as J,gM as ke,hE as Fe,a1 as De,P as ue,w as Q,hL as X,sz as Ne,l as Te,F as fe,hl as Ae,aB as je,hp as qe,hq as We,hu as Ie,h as me,W as pe,hv as be,hw as we,sA as He,aa as Le,ab as Ve,B as Pe,sB as _e}from"./index-fdfa25a0.js";import{R as ve}from"./ReusableFieldCatalog-d0853fd5.js";const ze=({})=>{const u=Se();s.useState("");const[C,d]=s.useState(null),[F,D]=s.useState([]),[f,B]=s.useState({}),[Me,Z]=s.useState(null),[N,T]=s.useState({}),[ee,se]=s.useState({}),[x,A]=s.useState({}),[te,j]=s.useState(!1),[ae,ie]=s.useState(!1),[oe,q]=s.useState(!1),[W,I]=s.useState(""),[le,w]=s.useState(!1),[Ee,H]=s.useState(!1),[Be,L]=s.useState(!0),[ne,V]=s.useState(!1),[Re,R]=s.useState(!1),P=()=>{u("/masterDataCockpit/profitCenter")},ce=()=>{u("/masterDataCockpit/profitCenter")},de=()=>{setOpen(!1)},$=()=>{j(!1),u("/masterDataCockpit/profitCenter")},re=()=>{j(!0)},K=()=>{ie(!0)},ge=()=>{const r=p=>{const n=[],v=[];Object.keys(p.body).map(o=>{const t=p.body[o];Object.keys(t).map(b=>{const c=p.body[o][b];if(Array.isArray(c)){let M={heading:b,fields:c.map(l=>l.fieldName),viewName:o,fieldVisibility:c.map(l=>({fieldName:l.fieldName,visibility:l.visibility}))};n.push(M),console.log(n,"hello"),c.forEach(l=>{console.log("Field Name:",l.fieldName),console.log("Is Required:",l.Required),l.Required==="true"&&v.push(l.fieldName)})}})}),D(n),console.log("Required Fields:",v);const k={},a={},i={};n.forEach(o=>{const{heading:t,fields:b,viewName:c,fieldVisibility:M}=o;k[c]||(k[c]={heading:c,subheadings:[]}),k[c].subheadings.push({heading:t,fields:b}),M.forEach(l=>{let h=l.visibility==="Required"?"Mandatory":l.visibility==="Hidden"?"Hide":l.visibility==="0"?"0":"Optional";a[l.fieldName]=h,l.visibility==="0"&&(i[l.fieldName]=!0)})}),B(k),A(a),se(i),T(i),console.log(k,"Fieldset")},m=p=>{console.log(p)};Q(`/${X}/data/getFieldCatalogueDetails?screenName=Change`,"get",r,m)};s.useEffect(()=>{ge()},[]);const U=()=>{console.log("helloooo");let r={};Object.keys(f).forEach(n=>{f[n].subheadings.forEach(k=>{const{heading:a,fields:i}=k;i.forEach(o=>{if(x[o]!=="0"&&N[o]){const t=x[o]==="Mandatory"?"Required":x[o]==="Hide"?"Hidden":"Optional";r[n]||(r[n]=[]),r[n].some(c=>c.fieldName===o)||r[n].push({fieldName:o,cardName:a,viewName:n,visibility:t,screenName:"Change"})}})})});const m=n=>{console.log(n,"example"),R(),n.statusCode===200?(console.log("success"),q("Submit"),I("Field Catalog has been submitted successfully"),w("success"),L(!1),V(!0),re(),H(!0),R(!1)):(q("Submit"),V(!1),I("Submission Failed"),w("danger"),L(!1),H(!0),K(),R(!1)),de()},p=n=>{console.log(n)};Object.keys(r).forEach(n=>{const v=r[n];v.length>0?Q(`/${X}/alter/changeVisibility`,"post",m,p,v):console.log(`No payload data to send for viewName: ${n}`)}),dispatch(Ne())};return y("div",{children:[e(ye,{dialogState:ae,openReusableDialog:K,closeReusableDialog:P,dialogTitle:oe,dialogMessage:W,handleDialogConfirm:P,dialogOkText:"OK",handleExtraButton:ce,dialogSeverity:le}),ne&&e(Ce,{openSnackBar:te,alertMsg:W,handleSnackBarClose:$}),e(E,{container:!0,sx:xe,children:e(E,{item:!0,md:12,children:Object.keys(f).map(r=>y(G,{sx:{mb:2},className:"filter-accordion",children:[e(Y,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:r})}),e(J,{children:f[r].subheadings.map((m,p)=>y(G,{sx:{mb:2},children:[e(Y,{expandIcon:e(ke,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:m.heading})}),e(J,{children:e("div",{sx:{fontSize:"25px"},children:e(ve,{fields:m.fields,heading:m.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:A,onSubmitButtonClick:()=>U(),mandatoryFields:F,DisabledChildCheck:ee})})})]},p))})]},r))})}),e(ue,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(Fe,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(r,m)=>{Z(F[m]),d(m)},children:e(De,{size:"small",variant:"contained",onClick:U,children:"Submit"})})})]})},$e=()=>{const u=document.getElementsByTagName("HTML")[0],C=document.getElementsByTagName("BODY")[0];let d=u.clientWidth,F=C.clientWidth;const D=document.getElementById("e-invoice-export"),f=D.scrollWidth-D.clientWidth;f>D.clientWidth&&(d+=f,F+=f),u.style.width=d+"px",C.style.width=F+"px",_e(D).then(B=>B.toDataURL("image/png",1)).then(B=>{Ke(B,"FieldCatalog.png"),u.style.width=null,C.style.width=null})},Ke=(u,C)=>{const d=window.document.createElement("a");d.href=u,d.download=C,(document.body||document.documentElement).appendChild(d),typeof d.click=="function"?d.click():(d.target="_blank",d.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),URL.revokeObjectURL(d.href),d.remove()},Ye=({})=>{const u=Se();s.useState("");const[C,d]=s.useState(null),[F,D]=s.useState([]),[f,B]=s.useState({}),[Me,Z]=s.useState(null),[N,T]=s.useState({}),[ee,se]=s.useState({}),[x,A]=s.useState({}),[te,j]=s.useState(!1),[ae,ie]=s.useState(!1),[oe,q]=s.useState(!1),[W,I]=s.useState(""),[le,w]=s.useState(!1),[Ee,H]=s.useState(!1),[Be,L]=s.useState(!0),[ne,V]=s.useState(!1),[Re,R]=s.useState(!1),[P,ce]=s.useState(0),de=["For Create","For Change"];Te();const $=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},re=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},K=()=>{setOpen(!1)},ge=()=>{j(!1),u("/masterDataCockpit/profitCenter")},U=()=>{j(!0)},r=()=>{ie(!0)},m=()=>{const a=o=>{const t=[],b=[];Object.keys(o.body).map(h=>{const _=o.body[h];Object.keys(_).map(z=>{const S=o.body[h][z];if(Array.isArray(S)){let he={heading:z,fields:S.map(g=>g.fieldName),viewName:h,fieldVisibility:S.map(g=>({fieldName:g.fieldName,visibility:g.visibility}))};t.push(he),console.log(t,"hello"),S.forEach(g=>{console.log("Field Name:",g.fieldName),console.log("Is Required:",g.Required),g.Required==="true"&&b.push(g.fieldName)})}})}),D(t),console.log("Required Fields:",b);const c={},M={},l={};t.forEach(h=>{const{heading:_,fields:z,viewName:S,fieldVisibility:he}=h;c[S]||(c[S]={heading:S,subheadings:[]}),c[S].subheadings.push({heading:_,fields:z}),he.forEach(g=>{let Oe=g.visibility==="Required"?"Mandatory":g.visibility==="Hidden"?"Hide":g.visibility==="0"?"0":"Optional";M[g.fieldName]=Oe,g.visibility==="0"&&(l[g.fieldName]=!0)})}),B(c),A(M),se(l),T(l),console.log(c,"Fieldset")},i=o=>{console.log(o)};Q(`/${X}/data/getFieldCatalogueDetails?screenName=Create`,"get",a,i)};s.useEffect(()=>{m()},[]);const p=()=>{let a={};Object.keys(f).forEach(t=>{f[t].subheadings.forEach(c=>{const{heading:M,fields:l}=c;l.forEach(h=>{if(x[h]!=="0"&&N[h]){const _=x[h]==="Mandatory"?"Required":x[h]==="Hide"?"Hidden":"Optional";a[t]||(a[t]=[]),a[t].some(S=>S.fieldName===h)||a[t].push({fieldName:h,cardName:M,viewName:t,visibility:_,screenName:"Create"})}})})});const i=t=>{console.log(t,"example"),R(),t.statusCode===200?(console.log("success"),q("Submit"),I("Field Catalog has been submitted successfully"),w("success"),L(!1),V(!0),U(),H(!0),R(!1)):(q("Submit"),V(!1),I("Submission Failed"),w("danger"),L(!1),H(!0),r(),R(!1)),K()},o=t=>{console.log(t)};Object.keys(a).forEach(t=>{const b=a[t];b.length>0?Q(`/${X}/alter/changeVisibility`,"post",i,o,b):console.log(`No payload data to send for viewName: ${t}`)})},n=[[e(fe,{children:y(E,{container:!0,sx:xe,children:[e(E,{item:!0,md:12,children:Object.keys(f).map(a=>y(G,{sx:{mb:2},className:"filter-accordion",children:[e(Y,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:a})}),e(J,{children:f[a].subheadings.map((i,o)=>y(G,{sx:{mb:2},children:[e(Y,{expandIcon:e(ke,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:i.heading})}),e(J,{children:e("div",{sx:{fontSize:"25px"},children:e(ve,{fields:i.fields,heading:i.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:A,onSubmitButtonClick:()=>p(),mandatoryFields:F,DisabledChildCheck:ee})})})]},o))})]},a))}),e(ue,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(Fe,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(a,i)=>{Z(F[i]),d(i)},children:e(De,{size:"small",variant:"contained",onClick:p,children:"Submit"})})})]})})],[e(fe,{children:e(ze,{})})]],v=(a,i)=>{ce(i)};s.useState(""),s.useState([]);function k(){}return y("div",{children:[e(ye,{dialogState:ae,openReusableDialog:r,closeReusableDialog:$,dialogTitle:oe,dialogMessage:W,handleDialogConfirm:$,dialogOkText:"OK",handleExtraButton:re,dialogSeverity:le}),ne&&e(Ce,{openSnackBar:te,alertMsg:W,handleSnackBarClose:ge}),e("div",{style:{...Ae,backgroundColor:"#FAFCFF"},children:y(je,{spacing:1,children:[y(E,{container:!0,sx:qe,children:[y(E,{item:!0,md:5,sx:We,children:[e(O,{variant:"h3",children:e("strong",{children:"Field Configurations"})}),e(O,{variant:"body2",color:"#777",children:"This view displays the setiings for configuring the Fields"})]}),e(E,{item:!0,md:7,sx:{display:"flex"},children:y(E,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[e(Ie,{title:"Search for fields in different views",module:"FieldSelection",keyName:"string",message:"Search for fields in different views"}),e(me,{title:"Reload",children:e(pe,{sx:be,children:e(we,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:k})})}),e(me,{title:"Export",children:e(pe,{sx:be,children:e(He,{onClick:$e})})})]})})]}),e(ue,{children:e(Le,{value:P,onChange:v,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:de.map((a,i)=>e(Ve,{sx:{fontSize:"12px",fontWeight:"700"},label:a},i))})}),n[P].map((a,i)=>e(Pe,{children:a},i))]})})]})};export{Ye as default};
