import{r as f,l as It,b as Ft,u as Et,i as J,hZ as qt,w as q,hK as j,a as n,j as d,h_ as ge,T as v,B as X,gZ as be,$ as p,hC as Gr,h$ as Pr,i0 as wr,i1 as Rr,i2 as Dr,i3 as en,i4 as rn,i5 as nn,a0 as qe,g_ as G,hN as ve,i6 as zt,F as H,ht as Bt,J as jt,i7 as tn,fV as Ve,h1 as We,W as He,hI as on,fX as Ke,gQ as le,a3 as ze,fH as Je,a1 as a,ho as $t,h8 as Mt,hp as Ot,hv as Lt,i8 as Ut,gU as M,i9 as kt,ia as _t,hD as Be,ib as je,ic as Vt,id as Wt,ie as Ht,P as _,hE as V,gW as x,hy as Kt,gR as sn,gS as ln,hJ as cn,ig as Jt,ih as dn,hs as $e,ii as Xt,ij as Yt,ik as ce,hn as Qt,il as Zt,hP as de}from"./index-fdfa25a0.js";import{E as Se}from"./EditableFieldForCostCenter-7fa6d38f.js";import{T as an}from"./Timeline-bb89efb4.js";const oo=()=>{var xr,gr,br,vr,Sr,Ar,Cr,yr,Tr,Nr,Ir,Fr,Er,qr,zr,Br,jr,$r,Mr,Or,Lr,Ur,kr,_r,Vr,Wr;const[F,Xe]=f.useState(!1);f.useState(0);const[ae,hn]=f.useState(!0);f.useState({});const[z,mn]=f.useState([]),[b,Ae]=f.useState(0),[Ye,pn]=f.useState([]),[u,un]=f.useState(),[fn,Gt]=f.useState(!1);f.useState([]);const[xn,A]=f.useState(!1),[gn,N]=f.useState(!1),[Pt,bn]=f.useState(!1),[Qe,C]=f.useState(""),[vn,y]=f.useState(!1),[wt,I]=f.useState(!0),[Rt,E]=f.useState(!1),[Sn,B]=f.useState(!0),[An,Ce]=f.useState(!1),[Cn,Me]=f.useState(!1),[yn,Oe]=f.useState(!1),[P,ye]=f.useState(""),[te,Tn]=f.useState([]),[Nn,Ze]=f.useState(!1),[In,Ge]=f.useState(!1),[Fn,En]=f.useState([]);f.useState({});const[w,qn]=f.useState([]),[Z,he]=f.useState(!0);f.useState(!0);const[oe,zn]=f.useState(""),[Bn,T]=f.useState(!1),[jn,Pe]=f.useState(""),[we,Le]=f.useState(""),[Re,$n]=f.useState([]),[Mn,De]=f.useState(!1),[On,me]=f.useState(!0),[Dt,Ln]=f.useState(""),[se,Un]=f.useState([]),[kn,R]=f.useState(!1),W=It(),er=Ft(),_n=Et(),D=J(s=>s.appSettings);let pe=J(s=>{var c;return(c=s.userManagement.entitiesAndActivities)==null?void 0:c["Cost Center"]});console.log("iwaAccessData",pe);let m=J(s=>{var c;return(c=s==null?void 0:s.initialData)==null?void 0:c.IWMMyTask}),l=J(s=>s.userManagement.userData),r=_n.state,t=J(s=>s.userManagement.taskData);console.log(t,"taskRowDetailscost");const e=J(s=>s.edit.payload);let rr=J(s=>s.edit.payload),nr=J(s=>s.costCenter.requiredFields);console.log(nr,"required_field_for_data"),t!=null&&t.subject?t==null||t.subject:r==null||r.requestId,console.log("ccroewdata",new Date(`${u==null?void 0:u.ValidFrom} GMT-0000`).toUTCString()),console.log(m,"taskData in costCenter"),console.log("costCenterRowData",r),console.log("costCenterDetails",Ye),console.log(P,"Remarks");const Ue=J(s=>s.costCenter.costCenterViewData),Vn=J(s=>s.costCenter.requiredFields);console.log(Vn,"requiredFields");const Wn=()=>{De(!1)};let Hn="/Date("+1705506186e3+")/";console.log("createdOn",Hn);const[Kn,tr]=f.useState(!1);var O={TaskId:t!=null&&t.taskId?t==null?void 0:t.taskId:"",CostCenterHeaderID:u!=null&&u.costCenterHeaderId?u==null?void 0:u.costCenterHeaderId:"",ControllingArea:(gr=(xr=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:xr.newControllingAreaCopyFrom)!=null&&gr.code?(vr=(br=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:br.newControllingAreaCopyFrom)==null?void 0:vr.code:r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:(Sr=m==null?void 0:m.body)==null?void 0:Sr.controllingArea,Testrun:On,Action:(r==null?void 0:r.requestType)==="Create"?"I":(r==null?void 0:r.requestType)==="Change"?"U":(t==null?void 0:t.processDesc)==="Create"?"I":((t==null?void 0:t.processDesc)==="Change","U"),ReqCreatedBy:l==null?void 0:l.user_id,ReqCreatedOn:t!=null&&t.createdOn?"/Date("+(t==null?void 0:t.createdOn)+")/":r!=null&&r.createdOn?"/Date("+Date.parse(r==null?void 0:r.createdOn)+")/":"",RequestStatus:"",CreationId:(t==null?void 0:t.processDesc)==="Create"?t==null?void 0:t.subject.slice(3):(r==null?void 0:r.requestType)==="Create"?r==null?void 0:r.requestId.slice(3):"",EditId:(t==null?void 0:t.processDesc)==="Change"?t==null?void 0:t.subject.slice(3):(r==null?void 0:r.requestType)==="Change"?r==null?void 0:r.requestId.slice(3):"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:(r==null?void 0:r.requestType)==="Create"?"Create":(r==null?void 0:r.requestType)==="Change"?"Change":(t==null?void 0:t.processDesc)==="Create"?"Create":((t==null?void 0:t.processDesc)==="Change","Change"),MassRequestStatus:"",Remarks:P||"",Toitem:[{CostCenterID:u!=null&&u.costCenterId?u==null?void 0:u.costCenterId:"",ValidFrom:u!=null&&u.validFrom?u==null?void 0:u.validFrom:"",ValidTo:u!=null&&u.validTo?u==null?void 0:u.validTo:"",Costcenter:r!=null&&r.costCenter?r==null?void 0:r.costCenter:(Ar=m==null?void 0:m.body)==null?void 0:Ar.costCenter,PersonInCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",CostcenterType:e!=null&&e.CostCenterCategory?e==null?void 0:e.CostCenterCategory:"",CostctrHierGrp:"TUK1-PRODU",BusArea:e!=null&&e.BusinessArea?e==null?void 0:e.BusinessArea:"",CompCode:e!=null&&e.CompanyCode?e==null?void 0:e.CompanyCode:"TUK1",Currency:e!=null&&e.Currency?e==null?void 0:e.Currency:"",ProfitCtr:e!=null&&e.ProfitCenter?e==null?void 0:e.ProfitCenter:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Descript:e!=null&&e.Description?e==null?void 0:e.Description:"",PersonInChargeUser:e!=null&&e.UserResponsible?e==null?void 0:e.UserResponsible:"",RecordQuantity:(e==null?void 0:e.RecordQuantity)===!0?"X":"",LockIndActualPrimaryCosts:(e==null?void 0:e.ActualPrimaryCosts)===!0?"X":"",LockIndPlanPrimaryCosts:(e==null?void 0:e.PlanPrimaryCosts)===!0?"X":"",LockIndActSecondaryCosts:(e==null?void 0:e.ActsecondaryCosts)===!0?"X":"",LockIndPlanSecondaryCosts:(e==null?void 0:e.PlanSecondaryCosts)===!0?"X":"",LockIndActualRevenues:(e==null?void 0:e.ActualRevenue)===!0?"X":"",LockIndPlanRevenues:(e==null?void 0:e.PlanRevenue)===!0?"X":"",LockIndCommitmentUpdate:(e==null?void 0:e.CommitmentUpdate)===!0?"X":"",ConditionTableUsage:"",Application:"",CstgSheet:e!=null&&e.CostingSheet?e==null?void 0:e.CostingSheet:"",ActyIndepTemplate:e!=null&&e.ActyIndepFromPlngTemp?e==null?void 0:e.ActyIndepFromPlngTemp:"",ActyDepTemplate:e!=null&&e.ActyDepFromPlngTemp?e==null?void 0:e.ActyDepFromPlngTemp:"",AddrTitle:e!=null&&e.Title?e==null?void 0:e.Title:"",AddrName1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",AddrName2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",AddrName3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",AddrName4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",AddrStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",AddrCity:e!=null&&e.Location?e==null?void 0:e.Location:"",AddrDistrict:e!=null&&e.District?e==null?void 0:e.District:"",AddrCountry:e!=null&&e.CountryReg?e==null?void 0:e.CountryReg:"",AddrCountryIso:"",AddrTaxjurcode:e!=null&&e.Jurisdiction?e==null?void 0:e.Jurisdiction:"",AddrPoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",AddrPostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",AddrPobxPcd:e!=null&&e.POBoxPostCod?e==null?void 0:e.POBoxPostCod:"",AddrRegion:e!=null&&e.Region?e==null?void 0:e.Region:"",TelcoLangu:"",TelcoLanguIso:e!=null&&e.LanguageKey?e==null?void 0:e.LanguageKey:"",TelcoTelephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",TelcoTelephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",TelcoTelebox:e!=null&&e.TeleboxNumber?e==null?void 0:e.TeleboxNumber:"",TelcoTelex:e!=null&&e.TelexNumber?e==null?void 0:e.TelexNumber:"",TelcoFaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",TelcoTeletex:e!=null&&e.TeletexNumber?e==null?void 0:e.TeletexNumber:"",TelcoPrinter:e!=null&&e.PrinterDestination?e==null?void 0:e.PrinterDestination:"",TelcoDataLine:e!=null&&e.DataLine?e==null?void 0:e.DataLine:"",ActyDepTemplateAllocCc:e!=null&&e.ActyDepAllocTemp?e==null?void 0:e.ActyDepAllocTemp:"",ActyDepTemplateSk:e!=null&&e.TempActStatKeyFigure?e==null?void 0:e.TempActStatKeyFigure:"",ActyIndepTemplateAllocCc:e!=null&&e.ActyIndepAllocTemp?e==null?void 0:e.ActyIndepAllocTemp:"",ActyIndepTemplateSk:e!=null&&e.TempActStatKeyFigure?e==null?void 0:e.TempActStatKeyFigure:"",AvcActive:!1,AvcProfile:"",BudgetCarryingCostCtr:"",CurrencyIso:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",FuncArea:e!=null&&e.FunctionalArea?e==null?void 0:e.FunctionalArea:"",FuncAreaFixAssigned:"",FuncAreaLong:"",Fund:"",FundFixAssigned:"",GrantFixAssigned:"",GrantId:"",JvEquityTyp:"",JvJibcl:"",JvJibsa:"",JvOtype:"",JvRecInd:"",JvVenture:"",Logsystem:""}]};console.log("errorsFields",rr,Fn.error_field_arr);const or=()=>Qt(rr,nr,$n),Jn=s=>{let c=[];for(const o in s){if(s.hasOwnProperty(o)){for(const i in s[o])if(s[o].hasOwnProperty(i)){console.log(s[o][i],"viewData[section][fieldGroup]");const h=s[o][i];for(const S of h)if(S.visibility==="0"||S.visibility==="Required"){console.log(S.fieldName,"field.fieldName");let g=S.fieldName.replace(/\s/g,"");c.push(g)}}}En(i=>({...i,error_field_arr:c}))}};console.log("taskData",m),console.log("taskRowDetails",t);const Xn=()=>{z[b];let s=Object.entries(Ue);console.log("viewDataArray",s);const c={};s.map(o=>{console.log("bottle",o[1]);let i=Object.entries(o[1]);return console.log("notebook",i),i.forEach(h=>{h[1].forEach(S=>{c[S.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=S.value})}),o}),console.log("toSetArray",c),W(Jt(c))},Yn=()=>{var i,h,S,g,ne,ue,fe,xe,Hr,Kr,Jr,Xr;var s=(i=m==null?void 0:m.body)!=null&&i.id?{id:(h=m==null?void 0:m.body)!=null&&h.id?(S=m==null?void 0:m.body)==null?void 0:S.id:"",costCenter:(g=m==null?void 0:m.body)!=null&&g.costCenter?(ne=m==null?void 0:m.body)==null?void 0:ne.costCenter:"",controllingArea:(ue=m==null?void 0:m.body)==null?void 0:ue.controllingArea,reqStatus:(fe=m==null?void 0:m.body)==null?void 0:fe.reqStatus,screenName:(t==null?void 0:t.processDesc)==="Create"?"Create":"Change"}:{id:r!=null&&r.reqStatus?r==null?void 0:r.id:"",costCenter:r!=null&&r.costCenter?(xe=r==null?void 0:r.costCenter)==null?void 0:xe.split(" ")[0]:"",controllingArea:(Kr=(Hr=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:Hr.newControllingAreaCopyFrom)!=null&&Kr.code?(Xr=(Jr=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:Jr.newControllingAreaCopyFrom)==null?void 0:Xr.code:r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:"",reqStatus:r!=null&&r.reqStatus?r==null?void 0:r.reqStatus:"Approved",screenName:r!=null&&r.requestType?r==null?void 0:r.requestType:"Change"};const c=ie=>{var Yr;if(ie.statusCode===200){const Q=ie.body.viewData;W(Zt(Q));const Tt=ie.body;console.log("ccdata",ie.body),Rn(Q["Basic Data"]["Basic Data"].find(K=>(K==null?void 0:K.fieldName)==="Company Code").value),Un(st((Yr=Q==null?void 0:Q["Basic Data"])==null?void 0:Yr.Names,"Name")),Dn(Q.Address["Address Data"].find(K=>(K==null?void 0:K.fieldName)==="Country/Reg").value);const Qr=Object.keys(Q);mn(Qr);const Nt=["Attachment & Documents"];z.concat(Nt);const Zr=Qr.map(K=>({category:K,data:Q[K],setIsEditMode:Xe}));pn(Zr),un(Tt),console.log("mappedData",Zr,u),Jn(Q)}else bn(!0),N(!1),A("Error"),C("Unable to fetch data of Cost Center"),Le("danger"),y("danger"),I(!0),Ce(!0)},o=ie=>{console.log(ie)};q(`/${j}/data/displayCostCenter`,"post",c,o,s),Ln(s.screenName)},[sr,Qn]=f.useState(0),Zn=(s,c)=>{const o=h=>{W(de({keyName:s,data:h.body})),Qn(S=>S+1)},i=h=>{console.log(h)};q(`/${j}/data/${c}`,"get",o,i)},Gn=()=>{var s,c;(c=(s=dn)==null?void 0:s.costCenter)==null||c.map(o=>{Zn(o==null?void 0:o.keyName,o==null?void 0:o.endPoint)})},Pn=()=>{var s,c;sr==((c=(s=dn)==null?void 0:s.costCenter)==null?void 0:c.length)?B(!1):B(!0)};f.useEffect(()=>{Pn()},[sr]),f.useEffect(()=>{zn(qt("CC"))},[]),f.useEffect(()=>{Gn(),Yn(),et(),nt(),tt()},[]),f.useEffect(()=>{Ue.length!==0&&Xn()},[Ue]);const ir=()=>{Ce(!1)},wn=()=>{yn?(Me(!1),Oe(!1)):(Me(!1),er("/masterDataCockpit/costCenter"))},L=()=>{he(!0);const s=or();F?s?(Ae(c=>c-1),W($e())):mr():(Ae(c=>c-1),W($e()))},U=()=>{const s=or();F?s?(Ae(c=>c+1),W($e())):mr():(Ae(c=>c+1),W($e()))},Rn=s=>{console.log("compcode",s);const c=i=>{console.log("value",i),W(de({keyName:"Currency",data:i.body}))},o=i=>{console.log(i,"error in dojax")};q(`/${j}/data/getCurrency?companyCode=${s}`,"get",c,o)},Dn=s=>{console.log("compcode",s);const c=i=>{console.log("value",i),W(de({keyName:"Region",data:i.body}))},o=i=>{console.log(i,"error in dojax")};q(`/${j}/data/getRegionBasedOnCountry?country=${s}`,"get",c,o)},et=()=>{var o,i;const s=h=>{W(de({keyName:"HierarchyArea",data:h.body}))},c=h=>{console.log(h)};q(`/${j}/data/getHierarchyArea?controllingArea=${(o=m==null?void 0:m.body)!=null&&o.controllingArea?(i=m==null?void 0:m.body)==null?void 0:i.controllingArea:r==null?void 0:r.controllingArea}`,"get",s,c)},rt=s=>{tr(s)},nt=()=>{var o,i;const s=h=>{W(de({keyName:"CompanyCode",data:h.body}))},c=h=>{console.log(h)};q(`/${j}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${(o=m==null?void 0:m.body)!=null&&o.controllingArea?(i=m==null?void 0:m.body)==null?void 0:i.controllingArea:r==null?void 0:r.controllingArea}`,"get",s,c)},tt=()=>{var o,i;const s=h=>{W(de({keyName:"ProfitCenter",data:h.body}))},c=h=>{console.log(h)};q(`/${j}/data/getProfitCenterAsPerControllingArea?controllingArea=${(o=m==null?void 0:m.body)!=null&&o.controllingArea?(i=m==null?void 0:m.body)==null?void 0:i.controllingArea:r==null?void 0:r.controllingArea}`,"get",s,c)},Te=()=>{Xe(!0),hn(!1)},lr=()=>{B(!0),Y(),pt()},cr=()=>{B(!0),Y(),ut()},Ne=()=>{B(!0),me(!1),Y(),ft()},dr=()=>{B(!0),me(!1),Y(),xt()},ke=()=>{bt()},ot=()=>{gt()},ar=()=>{B(!0),Y(),St()},hr=()=>{B(!0),Y(),At()},$=()=>{Ce(!0)},k=()=>{Me(!0)},Ie=()=>{R(!0);const s=o=>{var i,h,S;R(!1),o.statusCode===201?(R(!1),A("Create"),A("Create"),C("All Data has been Validated. Cost Center can be Sent for Review"),T(!1),y("success"),I(!1),N(!0),k(),E(!0),Oe(!0),he(!1)):(R(!1),A("Error"),N(!1),C(`${(i=o==null?void 0:o.body)!=null&&i.message[0]?(h=o==null?void 0:o.body)==null?void 0:h.message[0]:(S=o==null?void 0:o.body)==null?void 0:S.value}`),T(!1),y("danger"),I(!1),E(!0),$())},c=o=>{console.log(o)};q(`/${j}/alter/validateCostCenter`,"post",s,c,O)},st=(s,c)=>{console.log("getvalueforfieldname",s,c);const o=s==null?void 0:s.find(i=>(i==null?void 0:i.fieldName)===c);return o?o.value:""},Fe=()=>{var S;R(!0);const s={coArea:r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:u!=null&&u.controllingArea?u==null?void 0:u.controllingArea:"",name:e!=null&&e.Name?(S=e==null?void 0:e.Name)==null?void 0:S.toUpperCase():""},c=g=>{var ne,ue,fe,xe;R(!1),g.statusCode===201?(A("Create"),A("Create"),C("All Data has been Validated. Cost Center can be Sent for Review"),T(!1),y("success"),I(!1),N(!0),k(),E(!0),Oe(!0),console.log(se==null?void 0:se.toUpperCase(),"costcenterNameinRow"),(s.coArea!==""||s.name!=="")&&(he(!1),((ne=s==null?void 0:s.name)==null?void 0:ne.toUpperCase())===(se==null?void 0:se.toUpperCase())?R(!1):q(`/${j}/alter/fetchCCDescriptionDupliChk`,"post",o,i,s))):(A("Error"),N(!1),C(`${(ue=g==null?void 0:g.body)!=null&&ue.message[0]?(fe=g==null?void 0:g.body)==null?void 0:fe.message[0]:(xe=g==null?void 0:g.body)==null?void 0:xe.value}`),T(!1),y("danger"),I(!1),E(!0),$())},o=g=>{g.body.length===0||!g.body.some(ne=>ne.toUpperCase()===s.name)||(A("Duplicate Check"),N(!1),C("There is a direct match for the Cost Center name. Please change the name."),T(!1),y("danger"),I(!1),E(!0),$()),he(!1)},i=g=>{console.log(g)},h=g=>{console.log(g)};q(`/${j}/alter/validateCostCenter`,"post",c,h,O)},mr=()=>{De(!0)},it=()=>{(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")?(B(!0),lt()):(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")?(B(!0),ct()):(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")?(B(!0),dt()):(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&(B(!0),at())},lt=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Correction with ID NCS${o.body}`),T(!1),y("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center for Correction"),T(!1),y("danger"),I(!1),E(!0),$()),ee()},c=o=>{console.log(o)};console.log("remarkssssssssss",P),q(`/${j}/alter/costCenterSendForCorrection`,"post",s,c,O)},ct=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Correction with ID CCS${o.body}`),T(!1),y("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center for Correction"),T(!1),y("danger"),I(!1),E(!0),$()),ee()},c=o=>{console.log(o)};console.log("hsdfjgdh",O),q(`/${j}/alter/changeCostCenterSendForCorrection`,"post",s,c,O)},dt=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Correction with ID NCS${o.body}`),T(!1),y("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center for Correction"),T(!1),y("danger"),I(!1),E(!0),$()),ee()},c=o=>{console.log(o)};q(`/${j}/alter/costCenterSendForReview`,"post",s,c,O)},at=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Correction with ID CCS${o.body}`),T(!1),y("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center for Correction"),T(!1),y("danger"),I(!1),E(!0),$()),ee()},c=o=>{console.log(o)};console.log("remarksssaaaa",P),q(`/${j}/alter/changeCostCenterSendForReview`,"post",s,c,O)},pr=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:s=>d(H,{children:[n(Xt,{index:s.row.id,name:s.row.docName}),n(Yt,{index:s.row.id,name:s.row.docName})]})}];console.log("statecomment",w);const ht=()=>{let s=t!=null&&t.subject?t==null?void 0:t.subject:r==null?void 0:r.requestId,c=o=>{var i=[];o.documentDetailDtoList.forEach(h=>{var S={id:h.documentId,docType:h.fileType,docName:h.fileName,uploadedOn:ve(h.docCreationDate).format(D.date),uploadedBy:h.createdBy};i.push(S)}),Tn(i)};q(`/${ce}/documentManagement/getDocByRequestId/${s}`,"get",c)},mt=()=>{let s=t!=null&&t.subject?t==null?void 0:t.subject:r==null?void 0:r.requestId,c=i=>{console.log("commentsdata",i);var h=[];i.body.forEach(S=>{var g={id:S.requestId,comment:S.comment,user:S.createdByUser,createdAt:S.updatedAt};h.push(g)}),qn(h),console.log("commentrows",h)},o=i=>{console.log(i)};q(`/${j}/activitylog/fetchTaskDetailsForRequestId?requestId=${s}`,"get",c,o)};f.useEffect(()=>{ht(),mt()},[]);const ur=z.map(s=>{const c=Ye.filter(o=>{var i;return((i=o.category)==null?void 0:i.split(" ")[0])==(s==null?void 0:s.split(" ")[0])});if(c.length!=0)return{category:s==null?void 0:s.split(" ")[0],data:c[0].data}}).map((s,c)=>{if((s==null?void 0:s.category)=="Basic"&&b==0)return[n(p,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(p,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(X,{sx:{width:"100%"},children:n(be,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(p,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>(console.log("fieldDatatttt",i),n(Se,{length:i.maxLength,label:i.fieldName,data:e,value:i.value,visibility:i.visibility,onSave:h=>handleFieldSave(i.fieldName,h),isEditMode:F,type:i.fieldType,field:i,taskRequestId:r==null?void 0:r.requestId})))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Control"&&b==1)return[n(p,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(p,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(X,{sx:{width:"100%"},children:n(be,{children:n(p,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>n(Se,{data:e,label:i.fieldName,value:(i==null?void 0:i.value)==="X",onSave:h=>handleFieldSave(i.fieldName,h),visibility:i.visibility,isEditMode:F,type:i.fieldType,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Templates"&&b==2)return[n(p,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(p,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(X,{sx:{width:"100%"},children:n(be,{children:n(p,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>n(Se,{label:i.fieldName,data:e,value:i.value,onSave:h=>handleFieldSave(i.fieldName,h),isEditMode:F,visibility:i.visibility,type:i.fieldType,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Address"&&b==3)return[n(p,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(p,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(X,{sx:{width:"100%"},children:n(be,{children:n(p,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>n(Se,{label:i.fieldName,data:e,value:i.value,onSave:h=>handleFieldSave(i.fieldName,h),isEditMode:F,type:i.fieldType,visibility:i.visibility,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Communication"&&b==4)return[n(p,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(p,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(X,{sx:{width:"100%"},children:n(be,{children:n(p,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>n(Se,{label:i.fieldName,data:e,value:i.value,onSave:h=>handleFieldSave(i.fieldName,h),isEditMode:F,visibility:i.visibility,type:i.fieldType,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Attachments"&&b==5)return[n(H,{children:F?d(H,{children:[n(zt,{title:"CostCenter",useMetaData:!1,artifactId:oe,artifactName:"CostCenter"}),d(qe,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(p,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(v,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!te.length&&n(Gr,{width:"100%",rows:te,columns:pr,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!te.length&&n(v,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(v,{variant:"h6",children:"Comments"}),!!w.length&&n(an,{sx:{[`& .${Pr.root}:before`]:{flex:0,padding:0}},children:w.map(o=>d(wr,{children:[d(Rr,{children:[n(Dr,{children:n(en,{sx:{color:"#757575"}})}),n(rn,{})]}),n(nn,{sx:{py:"12px",px:2},children:n(qe,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(X,{sx:{padding:"1rem"},children:d(G,{spacing:1,children:[n(p,{sx:{display:"flex",justifyContent:"space-between"},children:n(v,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:ve(o.createdAt).format("DD MMM YYYY")})}),n(v,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),n(v,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!w.length&&n(v,{variant:"body2",children:"No Comments Found"}),n("br",{})]})]}):d(qe,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(p,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(v,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!te.length&&n(Gr,{width:"100%",rows:te,columns:pr,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!te.length&&n(v,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(v,{variant:"h6",children:"Comments"}),!!w.length&&n(an,{sx:{[`& .${Pr.root}:before`]:{flex:0,padding:0}},children:w.map(o=>d(wr,{children:[d(Rr,{children:[n(Dr,{children:n(en,{sx:{color:"#757575"}})}),n(rn,{})]}),n(nn,{sx:{py:"12px",px:2},children:n(qe,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(X,{sx:{padding:"1rem"},children:d(G,{spacing:1,children:[n(p,{sx:{display:"flex",justifyContent:"space-between"},children:n(v,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:ve(o.createdAt).format("DD MMM YYYY")})}),n(v,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),n(v,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!w.length&&n(v,{variant:"body2",children:"No Comments Found"}),n("br",{})]})})]}),pt=()=>{const s=o=>{if(B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Submitted for Review with ID CCS${o.body} `),T(!1),y("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`CCS${o==null?void 0:o.body}`},h=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",h,S,i)}else A("Error"),N(!1),C("Failed Submitting Profit Center"),T(!1),y("danger"),I(!1),E(!0),$();handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/changeCostCenterApprovalSubmit`,"post",s,c,O)},ut=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Approval with ID NCS${o.body}`),T(!1),y("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center"),T(!1),y("danger"),I(!1),E(!0),$()),handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/costCenterApprovalSubmit`,"post",s,c,O)},ft=()=>{const s=o=>{if(B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Submitted For Review with ID CCS${o.body} `),T(!1),y("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`CCS${o==null?void 0:o.body}`},h=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",h,S,i)}else A("Error"),N(!1),C("Failed Submitting Cost Center"),T(!1),y("danger"),I(!1),E(!0),$();handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/changeCostCenterSubmitForReview`,"post",s,c,O)},xt=()=>{const s=o=>{if(B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Submitted for Review with ID NCS${o.body} `),T(!1),y("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},h=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",h,S,i)}else A("Error"),N(!1),C("Failed Saving the Data"),T(!1),y("danger"),I(!1),E(!0),$(),me(!0);handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/costCenterSubmitForReview`,"post",s,c,O)},gt=()=>{y(!1),$(),A("Confirm"),C("Do You Want to Save as Draft ?"),T(!0),Pe("proceed"),Le("Create")},bt=()=>{y(!1),$(),A("Confirm"),C("Do You Want to Save as Draft?"),T(!0),Pe("proceed"),Le("Change")},vt=()=>{if(console.log(we,"dialogType"),_e(),B(!0),we==="Change"){const s=o=>{if(B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Saved As Draft with ID CCS${o.body} `),T(!1),y("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`CCS${o==null?void 0:o.body}`},h=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",h,S,i)}else A("Error"),N(!1),C("Failed Saving Cost Center"),T(!1),y("danger"),I(!1),E(!0),$();handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/changeCostCenterAsDraft`,"post",s,c,O)}else{const s=o=>{if(_e(),B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Saved As Draft with ID NCS${o.body} `),T(!1),y("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},h=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",h,S,i)}else A("Error"),N(!1),C("Failed Saving Cost Center"),T(!1),y("danger"),I(!1),E(!0),$();handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/costCenterAsDraft`,"post",s,c,O)}},St=()=>{const s=o=>{B(!1),o.statusCode===201?(console.log("success"),A("Create"),C(`${o.message}`),T(!1),y("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Approving Cost Center"),T(!1),y("danger"),I(!1),E(!0),$()),handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/changeCostCenterApproved`,"post",s,c,O)},At=()=>{const s=o=>{B(!1),o.statusCode===201?(console.log("success"),A("Create"),C(`${o.message}`),T(!1),y("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Approving the Cost Center"),T(!1),y("danger"),I(!1),E(!0),$()),handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/createCostCenterApproved`,"post",s,c,O)},Ee=()=>{Ze(!0)},ee=()=>{ye(""),Ze(!1)},fr=(s,c)=>{const o=s.target.value;if(o.length>0&&o[0]===" ")ye(o.trimStart());else{let i=o.toUpperCase();ye(i)}},re=()=>{me(!1),Ge(!0)},Y=()=>{ye(""),me(!0),Ge(!1)},Ct=()=>{(l==null?void 0:l.role)==="Finance"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&F?dr():(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&!F?cr():(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&!F?hr():(l==null?void 0:l.role)==="Finance"&&!(r!=null&&r.requestType)&&F||(l==null?void 0:l.role)==="Finance"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&F?Ne():(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&!F?lr():(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&!F&&ar()},yt=()=>{tr(!0)},_e=()=>{Ce(!1)};return console.log("factorsarray",z),n(H,{children:Sn===!0?n(Bt,{}):d("div",{style:{backgroundColor:"#FAFCFF"},children:[n(jt,{dialogState:An,openReusableDialog:$,closeReusableDialog:ir,dialogTitle:xn,dialogMessage:Qe,handleDialogConfirm:ir,dialogOkText:"OK",showExtraButton:Bn,showCancelButton:!0,dialogSeverity:vn,handleDialogReject:_e,handleExtraText:jn,handleExtraButton:vt}),gn&&n(tn,{openSnackBar:Cn,alertMsg:Qe,handleSnackBarClose:wn}),Re.length!=0&&n(tn,{openSnackBar:Mn,alertMsg:"Please fill the following Field: "+Re.join(", "),handleSnackBarClose:Wn}),d(Ve,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Nn,onClose:ee,children:[d(We,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(v,{variant:"h6",children:"Remarks"}),n(He,{sx:{width:"max-content"},onClick:ee,children:n(on,{})})]}),n(Ke,{sx:{padding:".5rem 1rem"},children:n(G,{children:n(X,{sx:{minWidth:400},children:n(le,{sx:{height:"auto"},fullWidth:!0,children:n(ze,{sx:{backgroundColor:"#F5F5F5"},onChange:fr,value:P,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),d(Je,{sx:{display:"flex",justifyContent:"end"},children:[n(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ee,children:"Cancel"}),n(a,{className:"button_primary--normal",type:"save",onClick:it,variant:"contained",children:"Submit"})]})]}),n($t,{sx:{color:"#fff",zIndex:s=>s.zIndex.drawer+1},open:kn,children:n(Mt,{color:"inherit"})}),d(Ve,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:In,onClose:Y,children:[d(We,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(v,{variant:"h6",children:"Remarks"}),n(He,{sx:{width:"max-content"},onClick:Y,children:n(on,{})})]}),n(Ke,{sx:{padding:".5rem 1rem"},children:n(G,{children:n(X,{sx:{minWidth:400},children:n(le,{sx:{height:"auto"},fullWidth:!0,children:n(ze,{sx:{backgroundColor:"#F5F5F5"},value:P,onChange:fr,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),d(Je,{sx:{display:"flex",justifyContent:"end"},children:[n(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Y,children:"Cancel"}),n(a,{className:"button_primary--normal",type:"save",onClick:Ct,variant:"contained",children:"Submit"})]})]}),d(p,{container:!0,sx:Ot,children:[d(p,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[d(p,{md:9,sx:{display:"flex"},children:[n(p,{children:n(He,{color:"primary","aria-label":"upload picture",component:"label",sx:Lt,children:n(Ut,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{er(-1)}})})}),d(p,{children:[F?d(p,{item:!0,md:12,children:[n(v,{variant:"h3",children:n("strong",{children:"Change Cost Center: "})}),n(v,{variant:"body2",color:"#777",children:"This view edits the details of the Cost Center"})]}):"",ae?d(p,{item:!0,md:12,children:[n(v,{variant:"h3",children:n("strong",{children:"Display Cost Center "})}),n(v,{variant:"body2",color:"#777",children:"This view displays the details of the Cost Center"})]}):""]})]}),d(p,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:[r!=null&&r.requestId||t!=null&&t.processDesc?n(p,{children:n(a,{variant:"outlined",size:"small",sx:M,onClick:yt,title:"Change Log",children:n(kt,{sx:{padding:"2px"},fontSize:"small"})})}):"",Kn&&n(_t,{open:!0,closeModal:rt,requestId:r!=null&&r.requestId?r==null?void 0:r.requestId:t==null?void 0:t.subject,requestType:r!=null&&r.requestType?r==null?void 0:r.requestType:(Cr=m==null?void 0:m.body)==null?void 0:Cr.processDesc,pageName:"costCenter",controllingArea:r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:(yr=m==null?void 0:m.body)==null?void 0:yr.controllingArea,centerName:r!=null&&r.costCenter?r==null?void 0:r.costCenter:(Tr=m==null?void 0:m.body)==null?void 0:Tr.costCenter}),Be(pe,"Cost Center","ChangeCC")&&((l==null?void 0:l.role)==="Super User"&&(r!=null&&r.requestType)&&((Nr=t==null?void 0:t.itmStatus)==null?void 0:Nr.toUpperCase())!=="OPEN"&&ae?n(p,{gap:1,sx:{display:"flex"},children:n(p,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(H,{children:n(p,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:M,onClick:Te,children:["Fill Details",n(je,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(l==null?void 0:l.role)==="Finance"&&(r!=null&&r.requestType||t!=null&&t.processDesc)&&((Ir=t==null?void 0:t.itmStatus)==null?void 0:Ir.toUpperCase())!=="OPEN"&&ae?n(p,{gap:1,sx:{display:"flex"},children:n(p,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(H,{children:n(p,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:M,onClick:Te,children:["Fill Details",n(je,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(l==null?void 0:l.role)==="Super User"&&!(r!=null&&r.requestType)&&((Fr=t==null?void 0:t.itmStatus)==null?void 0:Fr.toUpperCase())!=="OPEN"&&ae?n(p,{gap:1,sx:{display:"flex"},children:n(p,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(H,{children:n(p,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:M,onClick:Te,children:["Change",n(je,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(l==null?void 0:l.role)==="Finance"&&!(r!=null&&r.requestType)&&((Er=t==null?void 0:t.itmStatus)==null?void 0:Er.toUpperCase())!=="OPEN"&&ae?n(p,{gap:1,sx:{display:"flex"},children:n(p,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(H,{children:n(p,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:M,onClick:Te,children:["Change",n(je,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")]})]}),d(p,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[d(p,{item:!0,md:10,sx:{marginLeft:"40px"},children:[n(p,{item:!0,sx:{paddingTop:"2px !important"},children:d(G,{flexDirection:"row",children:[n("div",{style:{width:"12%"},children:n(v,{variant:"body2",color:"#777",children:"Cost Center"})}),d(v,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",r!=null&&r.costCenter?r==null?void 0:r.costCenter:u!=null&&u.costCenter?u==null?void 0:u.costCenter:""]})]})}),n(p,{item:!0,sx:{paddingTop:"2px !important"},children:d(G,{flexDirection:"row",children:[n("div",{style:{width:"12%"},children:n(v,{variant:"body2",color:"#777",children:"Controlling Area"})}),d(v,{variant:"body2",fontWeight:"bold",children:[":"," ",r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:u!=null&&u.controllingArea?u==null?void 0:u.controllingArea:""]})]})}),n(p,{item:!0,sx:{paddingTop:"2px !important"},children:d(G,{flexDirection:"row",children:[n("div",{style:{width:"12%"},children:n(v,{variant:"body2",color:"#777",children:"Valid From"})}),d(v,{variant:"body2",fontWeight:"bold",children:[": ",ve(u==null?void 0:u.validFrom).format(D==null?void 0:D.dateFormat)]})]})}),n(p,{item:!0,sx:{paddingTop:"2px !important"},children:d(G,{flexDirection:"row",children:[n("div",{style:{width:"12%"},children:n(v,{variant:"body2",color:"#777",children:"Valid To"})}),d(v,{variant:"body2",fontWeight:"bold",children:[": ",ve(u==null?void 0:u.validTo).format(D==null?void 0:D.dateFormat)]}),n(v,{variant:"body2",fontWeight:"bold"})]})})]}),n(p,{item:!0,md:2,sx:{marginLeft:"40px"}})]}),d(p,{container:!0,style:{marginLeft:25},children:[n(Vt,{activeStep:b,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:z.map((s,c)=>n(Wt,{children:n(Ht,{sx:{fontWeight:"700"},children:s})},s))}),ur&&((qr=ur[b])==null?void 0:qr.map((s,c)=>n(X,{sx:{mb:2,width:"100%"},children:n(v,{variant:"body2",children:s})},c)))]})]}),d(p,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[Be(pe,"Cost Center","ChangeCC")&&(!(r!=null&&r.requestType)&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})})),Be(pe,"Cost Center","ChangeCC")&&((l==null?void 0:l.role)==="Super User"&&!(r!=null&&r.requestType)&&((zr=t==null?void 0:t.itmStatus)==null?void 0:zr.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(a,{variant:"contained",size:"small",sx:{button_Outlined:M,mr:1},onClick:ke,children:"Save As Draft"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?d(H,{children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Fe,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ne,disabled:Z,children:"Submit For Review"})]}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Finance"&&!(r!=null&&r.requestType)&&((Br=t==null?void 0:t.itmStatus)==null?void 0:Br.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(a,{variant:"contained",size:"small",sx:{button_Outlined:M,mr:1},onClick:ke,children:"Save As Draft"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?d(H,{children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Fe,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,disabled:Z,children:"Submit For Review"})]}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):""),Be(pe,"Cost Center","ChangeCC")&&((l==null?void 0:l.role)==="Super User"&&(r==null?void 0:r.requestType)==="Create"&&((jr=t==null?void 0:t.itmStatus)==null?void 0:jr.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ie,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{button_Outlined:M,mr:1},onClick:hr,disabled:Z,children:"Approve"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:cr,children:"Submit For Approval"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Super User"&&(r==null?void 0:r.requestType)==="Change"&&(($r=t==null?void 0:t.itmStatus)==null?void 0:$r.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ie,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{button_Outlined:M,mr:1},onClick:ar,disabled:Z,children:"Approve"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:lr,children:"Submit For Approval"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((Mr=t==null?void 0:t.itmStatus)==null?void 0:Mr.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"outlined",size:"small",sx:{button_Outlined:M,mr:1},onClick:Ee,children:"Correction"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,children:"Submit For Approval"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((Or=t==null?void 0:t.itmStatus)==null?void 0:Or.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"outlined",size:"small",sx:{button_Outlined:M,mr:1},onClick:Ee,children:"Correction"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,children:"Submit For Approval"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((Lr=t==null?void 0:t.itmStatus)==null?void 0:Lr.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"outlined",size:"small",sx:{button_Outlined:M,mr:1},onClick:Ee,children:"Correction"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ie,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{button_Outlined:M,mr:1},onClick:re,disabled:Z,children:"Approve"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((Ur=t==null?void 0:t.itmStatus)==null?void 0:Ur.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"outlined",size:"small",sx:{button_Outlined:M,mr:1},onClick:Ee,children:"Correction"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ie,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{button_Outlined:M,mr:1},onClick:re,disabled:Z,children:"Approve"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Super User"&&(r==null?void 0:r.requestType)==="Create"&&((kr=t==null?void 0:t.itmStatus)==null?void 0:kr.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:dr,children:"Submit For Review"}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Super User"&&(r==null?void 0:r.requestType)==="Change"&&((_r=t==null?void 0:t.itmStatus)==null?void 0:_r.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ne,children:"Submit For Review"}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Finance"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((Vr=t==null?void 0:t.itmStatus)==null?void 0:Vr.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t.taskId?"":n(a,{variant:"contained",size:"small",sx:{button_Outlined:M,mr:1},onClick:ot,children:"Save As Draft"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?d(H,{children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Fe,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,disabled:Z,children:"Submit For Review"})]}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Finance"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((Wr=t==null?void 0:t.itmStatus)==null?void 0:Wr.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t.taskId?"":n(a,{variant:"contained",size:"small",sx:{button_Outlined:M,mr:1},onClick:ke,children:"Save As Draft"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?d(H,{children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Fe,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,disabled:Z,children:"Submit For Review"})]}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Finance"&&"")]}),d(Ve,{open:fn,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(We,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:n(v,{variant:"h6",children:"New Cost Center"})}),n(Ke,{sx:{padding:".5rem 1rem"},children:d(p,{container:!0,spacing:1,children:[d(p,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[d(v,{children:["Cost Center",n("span",{style:{color:"red"},children:"*"})]}),n(le,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:n(ze,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",placeholder:"Enter Cost Center Name",required:!0})})]}),d(p,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[d(v,{children:["Controlling Area",n("span",{style:{color:"red"},children:"*"})]}),n(le,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:n(Kt,{sx:{height:"42px"},required:"true",size:"small",renderInput:s=>n(ze,{sx:{fontSize:"12px !important"},...s,variant:"outlined",placeholder:"Select Cost Center"})})})]}),d(p,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[d(v,{children:["Valid From",n("span",{style:{color:"red"},children:"*"})]}),n(le,{fullWidth:!0,sx:{margin:".5em 0px"},children:n(sn,{dateAdapter:ln,children:n(cn,{slotProps:{textField:{size:"small"}}})})})]}),d(p,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[d(v,{children:["Valid To",n("span",{style:{color:"red"},children:"*"})]}),n(le,{fullWidth:!0,sx:{margin:".5em 0px"},children:n(sn,{dateAdapter:ln,children:n(cn,{slotProps:{textField:{size:"small"}}})})})]})]})}),d(Je,{sx:{display:"flex",justifyContent:"end"},children:[n(a,{sx:{width:"max-content",textTransform:"capitalize"},children:"Cancel"}),n(a,{className:"button_primary--normal",type:"save",variant:"contained",children:"Proceed"})]})]})]})})};export{oo as default};
