let container_filter = {
    margin: "0px !important",
    marginTop: "0px",
    marginBottom: "1.5rem !important",
  };
  
  let outermostContainer = {
    padding: "1.5rem 1rem 0px 1.5rem",
    // backgroundColor: (theme)=> theme.palette.success.main,
    // paddingBottom: "65px",
    boxSizing: "border-box",
  };
  
  let outerContainer_Information = {
    paddingTop: "0px",
  };
  let outermostContainer_Information = {
    marginBottom: "0.25rem !important",
    justifyContent: "space-between"
  };
  let information_Header = {
    marginBottom: ".5rem",
  };
  
  let container_table = {
    margin: "0px !important",
  };
  let container_tableHeader = {
    marginBottom: "12px",
  };
  let button_Primary = {
    backgroundColor: (theme) => theme.palette.primary.main,
    color: "#ffffff",
    minWidth: "max-content",
    padding: "6px 12px",
    textTransform: "capitalize",
    height: "2rem",
    marginLeft: "0px 1rem",
    "&:hover": {
      backgroundColor: (theme) => theme.palette.primary.main,
    },
  };
  
  let button_Outlined = {
    minWidth: "max-content",
    padding: "6px 12px",
    textTransform: "capitalize",
    height: "2rem",
  };
  let button_Marginleft = {
    marginLeft: "0.5rem",
  };
  let button_MarginRight = {
    marginRight: "0.5rem",
  };
  let iconButton_SpacingSmall = {
    padding: " 0.25rem",
    height: "max-content",
  };
  let primary_Color = {
    color: (theme) => theme.palette.primary.main,
  };
  let container_Padding = {
    padding: "1rem 1.5rem",
  };
  let container_columnGap = {
    marginBottom: "1rem ",
  };
  let icon_MarginLeft = {
    marginLeft: "0.5rem",
  };
  let icon_MarginRight = {
    marginRight: "0.5rem",
  };
  let search_SizeNormal = {
    height: "2rem",
  };
  let font_Small = {
    fontSize: "12px",
  };
  let menu_Height = {
    height: "6rem",
  };
  let item_RowGap = {
    marginLeft: ".5rem",
  };
  let customscrollbar = {
    "& ::-webkit-scrollbar-track": {
      boxShadow: "inset 0px 0px 2px grey",
    },
    "& ::-webkit-scrollbar": {
      width: "5px",
    },
    "& ::-webkit-scrollbar-thumb": {
      backgroundColor: " rgb(107, 100, 100)",
  
      borderRadius: "100px",
    },
    "& ::-webkit-scrollbar-thumb:hover": {
      backgroundColor: "rgb(146, 145, 145)",
  
      borderRadius: "100px",
    },
  };
  let bottom_Navigation = {
    display: "flex",
    justifyContent: "flex-end",
    paddingTop: "0.7rem",
    paddingBottom: "0.7rem",
  };
  export {
    customscrollbar,
    item_RowGap,
    container_columnGap,
    container_Padding,
    primary_Color,
    menu_Height,
    container_filter,
    outermostContainer,
    outerContainer_Information,
    outermostContainer_Information,
    information_Header,
    button_Primary,
    button_Outlined,
    button_Marginleft,
    button_MarginRight,
    iconButton_SpacingSmall,
    icon_MarginLeft,
    icon_MarginRight,
    search_SizeNormal,
    font_Small,
    container_table,
    container_tableHeader,
    bottom_Navigation,
  };
  