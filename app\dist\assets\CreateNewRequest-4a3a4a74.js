import{ah as St,ai as xt,aj as Et,r as l,a as e,iL as jl,i as Y,iM as N,b as Xs,l as Fn,k as On,iN as Bl,h as zt,iO as Vl,iP as Ys,w as Te,G as Ce,hg as at,j as d,fV as qn,B as I,T as Me,aa as as,ab as bn,W as Lt,R as Js,fX as An,F as Je,iQ as Kn,gK as <PERSON>,he as gn,fH as Nn,E as Qn,a1 as qe,h1 as Zn,gQ as Gl,iR as Wl,a8 as es,iS as Ps,i7 as Un,hM as zl,y as Xe,hP as fn,hf as ds,iT as Xl,iU as Ks,iV as Qs,u as mn,iW as Yl,iX as cn,iY as kn,iZ as Ve,i_ as Jl,i$ as Kl,j0 as Ql,j1 as qs,j2 as pn,j3 as ts,j4 as L,j5 as Ye,J as Zs,hy as Zl,hA as ns,V as eo,a3 as Wn,hI as cs,Z as to,P as ss,M as no,N as so,O as Hs,Q as Ge,S as lo,j6 as Pt,j7 as Tn,j8 as oo,j9 as ro,hj as vn,ja as io,jb as ao,jc as Mn,jd as co,g_ as _n,je as uo,jf as Mt,jg as ho,jh as el,ji as ls,jj as Ln,jk as fo,jl as go,jm as po,jn as mo,e as Xt,jo as So,jp as xo,cY as In,$ as it,jq as zn,jr as ie,g as Eo,A as Co,f as To,a0 as un,gZ as hn,m as bo,h_ as Ao,js as No,jt as vo,ju as _o,hW as Oo,jv as Xn,jw as Dn,jx as Ro,hv as yo,jy as Mo,hC as tl,jz as Lo,hd as os,jA as Io,jB as Do,hi as wo,iC as ko,jC as Pn,jD as Fs,jE as Po,jF as qo,jG as Ho,jH as Fo,jI as Uo,jJ as $o,jK as jo,jL as Bo,jM as Vo,jN as Go,jO as Wo,o as zo,p as Xo,jP as Us,jQ as Yo,jR as Jo,jS as Ko,jT as Qo,hZ as Zo,jU as $s,jV as er,i9 as js,ia as tr,af as Cn,i8 as nr,id as sr,ic as lr,jW as or,jX as rr,gU as ir,gW as ar,jY as wn,jZ as dr,H as Yn,j_ as cr,j$ as ur}from"./index-fdfa25a0.js";import{H as hr,u as nl,a as fr,b as gr,c as pr,d as sl,B as us,e as Bs,O as mr,f as qt,g as Sr,h as ll,i as ol,j as xr,T as Er,k as Cr,l as Tr,R as br,m as Ar,A as Nr,n as vr,o as _r}from"./PermIdentityOutlined-42e26a93.js";import{d as Vs}from"./DeleteOutlineOutlined-9e9a8646.js";import{d as rs}from"./Description-5b38f787.js";import{G as Or}from"./GenericTabs-8e261948.js";import Rr from"./AdditionalData-a38f586a.js";import{u as yr}from"./useMaterialFieldConfig-6dda1d2a.js";import{d as rl,F as Gs}from"./FilterChangeDropdown-22e6e937.js";import{m as Mr}from"./makeStyles-1dfd4db4.js";import{d as Lr,a as Ir,b as Dr}from"./FileUploadOutlined-bf833d2e.js";import{A as wr}from"./AttachmentUploadDialog-cc0b6643.js";import{u as kr}from"./useDisplayDataDto-69e1aee1.js";import{S as Pr}from"./StepButton-fdbf0590.js";import"./FilterField-6f6e20f9.js";import"./useChangeLogUpdate-3699f77c.js";import"./AutoCompleteType-3a9c9c9d.js";import"./dayjs.min-774e293a.js";import"./AdapterDayjs-cd3745c6.js";import"./isBetween-fe8614a5.js";import"./useFinanceCostingRows-2aab0ea4.js";import"./AttachFile-1c547195.js";import"./Timeline-bb89efb4.js";import"./useCustomDtCall-04c3c72a.js";import"./GenericViewGeneral-4b82ad14.js";import"./DeleteOutline-1f72afa8.js";var hs={},qr=xt;Object.defineProperty(hs,"__esModule",{value:!0});var il=hs.default=void 0,Hr=qr(St()),Fr=Et;il=hs.default=(0,Hr.default)((0,Fr.jsx)("path",{d:"M5 20h14v-2H5zM19 9h-4V3H9v6H5l7 7z"}),"Download");const Ur=l.forwardRef(function(g,A){return e(jl,{direction:"down",ref:A,...g})}),$r=({open:f,onClose:g,parameters:A,templateName:_,allDropDownData:y,name:Z,onSearch:E})=>{var $,C,me,Oe,tt,Se;const[i,p]=l.useState({}),[F,w]=l.useState({}),x=Y(a=>a.request.salesOrgDTData),[h,J]=l.useState({}),[te,X]=l.useState(""),[he,be]=l.useState([]),[xe,M]=l.useState(!1),[Ae,se]=l.useState(!1),[u,P]=l.useState("success"),[ne,K]=l.useState(!1),[T,le]=l.useState(""),[Q,ce]=l.useState(""),[de,B]=l.useState(!1),[D,V]=l.useState("systemGenerated"),[ge,He]=l.useState([]),[Le,Ht]=l.useState(""),[Ct,It]=l.useState(!1),Ie=Y(a=>a.payload.payloadData),dt=Y(a=>a.request.requestHeader.requestId),z=Y(a=>a.payload.dataLoading),[pe,r]=l.useState({}),[Ue,Dt]=l.useState(0),[De]=l.useState(200),[Tt,bt]=l.useState(0),[$e,Yt]=l.useState({code:"",desc:""}),[Jt,ct]=l.useState(null),ve=l.useRef(null),[ut,je]=l.useState({[N.MATERIAL_NUM]:!1,[N.PLANT]:!1,[N.SALES_ORG]:!1,[N.DIVISION]:!1,[N.DIST_CHNL]:!1,[N.WAREHOUSE]:!1,[N.STORAGE_LOC]:!1,[N.MRP_CTRLER]:!1}),[At,Nt]=l.useState(null),[Ft,Ze]=l.useState(""),[O,et]=l.useState(!1),Kt=l.useRef(null),we=Xs(),ht=Fn(),[oe,_e]=l.useState(0),[wt,Ut]=l.useState(null),{customError:nt}=On(),[Ke,ft]=l.useState([]),vt=A,_t=vt==null?void 0:vt.map(a=>({field:a.key,headerName:a.key,editable:!0,flex:2})),gt=l.useCallback(a=>{a.preventDefault();const q=(a.clipboardData||window.clipboardData).getData("Text").trim().split(`
`).map((k,H)=>{const re=k.split("	"),fe={id:H+1};return _t.forEach((yt,dn)=>{fe[yt.field]=re[dn]||""}),fe});ft(q)},[]);l.useEffect(()=>{f||(p({}),r({}))},[f]),l.useEffect(()=>{if(oe===1)return document.addEventListener("paste",gt),()=>{document.removeEventListener("paste",gt)}},[oe,gt]);const ye=(a,S)=>{_e(S),oe===1&&Ut("handlePasteMaterialData")},$t=Bl(({className:a,...S})=>e(zt,{...S,classes:{popper:a}}),{target:"e1qkid610"})({[`& .${Vl.tooltip}`]:{maxWidth:"none"}},""),jt={convertJsonToExcel:()=>{let a=[];_t==null||_t.forEach(S=>{S.headerName.toLowerCase()!=="action"&&!S.hide&&a.push({header:S.headerName,key:S.field})}),zl({fileName:"Material Data",columns:a,rows:Ke})}},st=(a,S)=>{Nt(a.currentTarget),Ze(S),et(!0)},lt=()=>{et(!1)},ue=()=>{et(!0)},Be=()=>{et(!1)},kt=!!At?"custom-popover":void 0,U=(a,S)=>{var b;p(q=>({...q,[a]:S})),a===N.MATERIAL_TYPE&&(He([]),Yt({code:"",desc:""}),Dt(0),(b=S==null?void 0:S[0])!=null&&b.code&&Rt("",!0,S)),S.length>0&&J(q=>({...q,[a]:""}))};l.useEffect(()=>{w(Qt(i)),ht(Ys(Qt(i)))},[i]),l.useEffect(()=>{if(Ke){let a=Zt(Ke);p(a)}},[Ke]);const Qe=(a,S)=>{var q;const b=((q=i[a])==null?void 0:q.length)===S.length;p(k=>({...k,[a]:b?[]:S})),b||J(k=>({...k,[a]:""}))},Qt=a=>{const S={};for(const b in a)a.hasOwnProperty(b)&&(S[b]=a[b].map(q=>q.code).join(","));return S},Zt=a=>{const S={};return a.forEach(b=>{Object.keys(b).forEach(q=>{q!=="id"&&b[q].trim()!==""&&(S[q]||(S[q]=[]),S[q].push({code:b[q].trim()}))})}),S},We=()=>{var k;ce(ds.REPORT_LOADING),X(!0),g();let a=((k=Xl[Ie==null?void 0:Ie.TemplateName])==null?void 0:k.map(H=>H.key))||[],S={};oe===0?S={materialDetails:[a.reduce((H,re)=>(H[re]=F!=null&&F[re]?F==null?void 0:F[re]:"",H),{})],templateHeaders:"",requestId:dt,templateName:Ie!=null&&Ie.TemplateName?Ie.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""}:S={materialDetails:[a.reduce((H,re)=>(H[re]=Ke.map(fe=>{var yt;return(yt=fe[re])==null?void 0:yt.trim()}).filter(fe=>fe!=="").join(",")||"",H),{})],templateHeaders:"",requestId:dt,templateName:Ie!=null&&Ie.TemplateName?Ie.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""};const b=H=>{const re=URL.createObjectURL(H),fe=document.createElement("a");fe.href=re,fe.setAttribute("download",`${Ie.TemplateName}_Mass Change.xlsx`),document.body.appendChild(fe),fe.click(),document.body.removeChild(fe),URL.revokeObjectURL(re),X(!1),ce(""),se(!0),le(`${Ie.TemplateName}_Mass Change.xlsx has been downloaded successfully.`),P("success"),Ot(),setTimeout(()=>{we("/requestBench")},2400)},q=()=>{X(!1)};Te(`/${Ce}${at.EXCEL.DOWNLOAD_EXCEL_WITH_DATA}`,"postandgetblob",b,q,S)},Ot=()=>{K(!0)},en=()=>{K(!1)},Bt=()=>{B(!1),V("systemGenerated")},Vt=a=>{var S;V((S=a==null?void 0:a.target)==null?void 0:S.value)},tn=()=>{D==="systemGenerated"&&(We(),Bt()),D==="mailGenerated"&&Bt()};l.useEffect(()=>{var a;(a=i==null?void 0:i[N.MATERIAL_TYPE])!=null&&a.code&&Rt("",!0)},[]);const nn=a=>{var q;const S=(q=a.target.value)==null?void 0:q.toUpperCase();Yt({code:S,desc:""}),Dt(0),Jt&&clearTimeout(Jt);const b=setTimeout(()=>{var k,H,re;(H=(k=i==null?void 0:i[N.MATERIAL_TYPE])==null?void 0:k[0])!=null&&H.code&&Rt(S,!0,(re=i==null?void 0:i[N.MATERIAL_TYPE])==null?void 0:re.code)},500);ct(b)},Rt=(a="",S=!1,b)=>{var re,fe,yt,dn,Rn,yn,En;je(Ee=>({...Ee,[N.MATERIAL_NUM]:!0}));const q={matlType:(((re=b==null?void 0:b[0])==null?void 0:re.code)||((yt=(fe=i==null?void 0:i[N.MATERIAL_TYPE])==null?void 0:fe[0])==null?void 0:yt.code))??"",materialNo:a??"",top:De,skip:S?0:Ue,salesOrg:((Rn=(dn=x==null?void 0:x.uniqueSalesOrgList)==null?void 0:dn.map(Ee=>Ee.code))==null?void 0:Rn.join("$^$"))||""},k=Ee=>{(Ee==null?void 0:Ee.statusCode)===Xe.STATUS_200&&(bt((Ee==null?void 0:Ee.count)||0),S?(He((Ee==null?void 0:Ee.body)||[]),r(ot=>({...ot,[N.MATERIAL_NUM]:Ee.body||[]}))):(He(ot=>[...ot,...(Ee==null?void 0:Ee.body)||[]]),r(ot=>({...ot,[N.MATERIAL_NUM]:[...ot[N.MATERIAL_NUM]||[],...Ee.body||[]]}))),je(ot=>({...ot,[N.MATERIAL_NUM]:!1})))},H=Ee=>{nt(Ee),je(ot=>({...ot,[N.MATERIAL_NUM]:!1}))};Te(`/${Ce}${(En=(yn=at)==null?void 0:yn.DATA)==null?void 0:En.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",k,H,q)};l.useEffect(()=>{Ue>0&&Rt($e==null?void 0:$e.code,!1)},[Ue]);const xn=a=>{var k;const{scrollTop:S,scrollHeight:b,clientHeight:q}=a.target;S+q>=b-10&&!ut[N.MATERIAL_NUM]&&((k=pe==null?void 0:pe[N.MATERIAL_NUM])==null?void 0:k.length)<Tt&&Dt(H=>H+De)};l.useEffect(()=>{A==null||A.forEach(a=>{var S,b,q,k;a.key===((S=N)==null?void 0:S.MRP_CTRLER)&&(y!=null&&y.MrpCtrler)?r(H=>{var re;return{...H,[(re=N)==null?void 0:re.MRP_CTRLER]:y.MrpCtrler}}):[(b=N)==null?void 0:b.PLANT,(q=N)==null?void 0:q.SALES_ORG,(k=N)==null?void 0:k.WAREHOUSE].includes(a.key)&&on(a.key)})},[]),l.useEffect(()=>{sn()},[]),l.useEffect(()=>{var a;i[(a=N)==null?void 0:a.SALES_ORG]&&sn()},[i[($=N)==null?void 0:$.SALES_ORG]]),l.useEffect(()=>{var a;i[(a=N)==null?void 0:a.PLANT]&&ln()},[i[(C=N)==null?void 0:C.PLANT]]);const Gt=async()=>{E(i,"0",a=>{be(a),M(!0),a&&a.length>0&&g()})},sn=()=>{var q;je(k=>({...k,[N.DIST_CHNL]:!0}));let a={salesOrg:i[N.SALES_ORG]?(q=i[N.SALES_ORG])==null?void 0:q.map(k=>k==null?void 0:k.code).join("$^$"):""};const S=k=>{r(H=>({...H,[N.DIST_CHNL]:k.body})),ht(fn({keyName:"StoreLoc",data:pe==null?void 0:pe[N.DIST_CHNL]})),je(H=>({...H,[N.DIST_CHNL]:!1}))},b=k=>{console.error(k),je(H=>({...H,[N.DIST_CHNL]:!1}))};Te(`/${Ce}/data/getDistrChan`,"post",S,b,a)},ln=()=>{var q,k;je(H=>({...H,[N.STORAGE_LOC]:!0})),i[N.SALES_ORG]&&((q=i[N.SALES_ORG])==null||q.map(H=>H==null?void 0:H.code).join("$^$"));const a=H=>{r(re=>({...re,[N.STORAGE_LOC]:H.body})),ht(fn({keyName:"DistrChan",data:pe==null?void 0:pe[N.STORAGE_LOC]})),je(re=>({...re,[N.STORAGE_LOC]:!1}))},S=H=>{console.error(H),je(re=>({...re,[N.STORAGE_LOC]:!1}))},b=(k=i[N.PLANT])==null?void 0:k.map(H=>H.code).join(",");Te(`/${Ce}${at.DATA.GET_STORAGE_LOCATION_SET_BASED_ON_PLANT}`,"post",a,S,{plant:b})},on=a=>{je(k=>({...k,[a]:!0}));const S={[N.PLANT]:"/getPlant",[N.SALES_ORG]:"/getSalesOrg",[N.WAREHOUSE]:"/getWareHouseNo"},b=k=>{r(H=>({...H,[a]:k.body})),ht(fn({keyName:a,data:k==null?void 0:k.body})),je(H=>({...H,[a]:!1}))},q=k=>{console.log(k),je(H=>({...H,[a]:!1}))};Te(`/${Ce}/data${S[a]}`,"get",b,q)},rn=a=>{var b,q;const S=k=>k.code&&k.desc?`${k.code} - ${k.desc}`:k.code||"";if(a.key===N.MATERIAL_TYPE)return e(Gs,{param:a,dropDownData:{[N.MATERIAL_TYPE]:a.options},allDropDownData:y,selectedValues:i,inputState:$e,handleSelectAll:Qe,handleSelectionChange:U,dropdownRef:ve,errors:h,formatOptionLabel:S,handlePopoverOpen:st,handlePopoverClose:lt,handleMouseEnterPopover:ue,handleMouseLeavePopover:Be,isPopoverVisible:O,popoverId:kt,popoverAnchorEl:At,popoverRef:Kt,popoverContent:Ft,isLoading:ut[a.key],singleSelect:!0});if(a.key===N.MATERIAL_NUM)return e(hr,{param:a,dropDownData:pe,allDropDownData:y,selectedValues:i,inputState:$e,handleSelectAll:Qe,handleSelectionChange:U,handleMatInputChange:nn,handleScroll:xn,dropdownRef:ve,errors:h,formatOptionLabel:S,handlePopoverOpen:st,handlePopoverClose:lt,handleMouseEnterPopover:ue,handleMouseLeavePopover:Be,isPopoverVisible:O,popoverId:kt,popoverAnchorEl:At,popoverRef:Kt,popoverContent:Ft,isMaterialNum:!0,isLoading:ut[N.MATERIAL_NUM],hasMoreItems:Tt>(((b=pe==null?void 0:pe[N.MATERIAL_NUM])==null?void 0:b.length)||0),totalCount:Tt,loadedCount:((q=pe==null?void 0:pe[N.MATERIAL_NUM])==null?void 0:q.length)||0});if(a.key===N.PLANT||a.key===N.SALES_ORG||a.key===N.MRP_CTRLER||a.key===N.DIVISION||a.key===N.WAREHOUSE||a.key===N.DIST_CHNL||a.key===N.STORAGE_LOC)return e(Gs,{param:a,dropDownData:pe,allDropDownData:y,selectedValues:i,inputState:$e,handleSelectAll:Qe,handleSelectionChange:U,dropdownRef:ve,errors:h,formatOptionLabel:S,handlePopoverOpen:st,handlePopoverClose:lt,handleMouseEnterPopover:ue,handleMouseLeavePopover:Be,isPopoverVisible:O,popoverId:kt,popoverAnchorEl:At,popoverRef:Kt,popoverContent:Ft,isLoading:ut[a.key]})},an=()=>Object.values(i).some(a=>Array.isArray(a)&&a.length>0);return d(Je,{children:[d(qn,{open:f,TransitionComponent:Ur,keepMounted:!0,onClose:()=>{},maxWidth:Z==="Extend"?"lg":"xs",fullWidth:!0,children:[d(I,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[e(rl,{color:"primary",sx:{marginRight:"0.5rem"}}),d(Me,{variant:"h6",component:"div",color:"primary",children:[_," Search Filter(s)"]})]}),d(as,{value:oe,onChange:ye,sx:{borderBottom:1,borderColor:"divider"},children:[e(bn,{label:"Search Filter"}),Z!=="Extend"&&e(bn,{label:d(I,{display:"flex",alignItems:"center",children:[e("span",{children:"Copy Material"}),oe===1&&e(zt,{title:"Export Table",children:e(Lt,{sx:{padding:"4px",width:"28px",height:"28px"},onClick:jt.convertJsonToExcel,children:e(Js,{iconName:"IosShare"})})})]})})]}),d(An,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[oe===0&&e(Je,{children:e(I,{sx:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:2},children:A==null?void 0:A.map(a=>e(I,{sx:{marginBottom:"1rem"},children:rn(a)},a.key))})}),oe===1&&e(I,{children:e(Kn,{style:{height:400,width:"100%"},rows:Ke,columns:_t})}),Ct&&d(Me,{variant:"h6",color:(Oe=(me=Re)==null?void 0:me.error)==null?void 0:Oe.dark,children:["* ",Le]}),e(gn,{blurLoading:z})]}),d(Nn,{sx:{padding:"0.5rem 1.5rem",display:"flex",justifyContent:"space-between"},children:[e("div",{children:e(Me,{variant:"h6",color:(Se=(tt=Re)==null?void 0:tt.error)==null?void 0:Se.dark,children:xe&&(he==null?void 0:he.length)===0?Qn.DATA_NOT_FOUND_FOR_SEARCH:""})}),d("div",{style:{display:"flex",gap:"8px"},children:[e(qe,{onClick:g,color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),e(qe,{onClick:Gt,variant:"contained",disabled:!an(),sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"})]})]})]}),d(qn,{open:de,onClose:Bt,children:[e(Zn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:e(Me,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:"Select Download Option"})}),e(An,{children:e(Gl,{children:d(Wl,{row:!0,"aria-labelledby":"demo-row-radio-buttons-group-label",name:"row-radio-buttons-group",value:D,onChange:Vt,children:[e($t,{arrow:!0,placement:"bottom",title:e("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be downloaded"}),children:e(es,{value:"systemGenerated",control:e(Ps,{}),label:"System-Generated"})}),e($t,{arrow:!0,placement:"bottom",title:e("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be sent to your email"}),children:e(es,{value:"mailGenerated",control:e(Ps,{}),label:"Mail-Generated"})})]})})}),e(Nn,{children:e(qe,{variant:"contained",onClick:tn,children:"OK"})})]}),e(gn,{blurLoading:te,loaderMessage:Q}),Ae&&e(Un,{openSnackBar:ne,alertMsg:T,alertType:u,handleSnackBarClose:en})]})},jr=({openSearchMat:f,setOpenSearchMat:g,AddCopiedMaterial:A})=>{const[_,y]=l.useState(!1),Z=Y(p=>p.AllDropDown.dropDown),E={Extend:[{key:"Material Type",options:Ks},{key:"Material Number",options:[]},{key:"Plant",options:[]},{key:"Sales Org",options:[]},{key:"Distribution Channel",options:[]},{key:"Storage Location",options:[]},{key:"Division",options:[]}]},i=(p,F="0",w)=>{var te,X,he,be,xe,M,Ae,se,u,P,ne,K;const x={materialNo:((X=(te=p==null?void 0:p["Material Number"])==null?void 0:te.map(T=>T.code))==null?void 0:X.join(","))??"",division:((be=(he=p==null?void 0:p.Division)==null?void 0:he.map(T=>T.code))==null?void 0:be.join(","))??"",plant:((M=(xe=p==null?void 0:p.Plant)==null?void 0:xe.map(T=>T.code))==null?void 0:M.join(","))??"",salesOrg:((se=(Ae=p==null?void 0:p["Sales Org"])==null?void 0:Ae.map(T=>T.code))==null?void 0:se.join(","))??"",distrChan:((P=(u=p==null?void 0:p["Distribution Channel"])==null?void 0:u.map(T=>T.code))==null?void 0:P.join(","))??"",storageLocation:((K=(ne=p==null?void 0:p["Storage Location"])==null?void 0:ne.map(T=>T.code))==null?void 0:K.join(","))??"",top:200,skip:F},h=T=>{var le;if((T==null?void 0:T.statusCode)===Xe.STATUS_200){const Q=(le=T==null?void 0:T.body)==null?void 0:le.map(ce=>{if(ce.Views){const de=ce.Views.split(",").map(B=>B.trim()).filter(B=>!Qs.includes(B)).join(",");return{...ce,Views:de}}return ce});A(Q||[]),w==null||w(Q||[]),y(!1)}},J=()=>{y(!1),w==null||w([])};y(!0),Te(`/${Ce}${at.DATA.GET_EXTEND_SEARCH_SET}`,"post",h,J,x)};return d(Je,{children:[e($r,{open:f,onClose:()=>g(!1),parameters:E.Extend,onSearch:(p,F,w)=>i(p,F,w),templateName:"Extend",name:"Extend",allDropDownData:Z,buttonName:"Search"}),e(gn,{blurLoading:_})]})};var fs={},Br=xt;Object.defineProperty(fs,"__esModule",{value:!0});var is=fs.default=void 0,Vr=Br(St()),Ws=Et;is=fs.default=(0,Vr.default)([(0,Ws.jsx)("path",{d:"M15.5 5H11l5 7-5 7h4.5l5-7z"},"0"),(0,Ws.jsx)("path",{d:"M8.5 5H4l5 7-5 7h4.5l5-7z"},"1")],"DoubleArrow");const Gr=Mr(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),rt={NOT_EXTENDED:"notExtended",EXTENDED:"Extended"},Wr=f=>{var ys,Ms,Ls,Is,Ds,ws;const g=Gr(),{customError:A}=On(),_=Fn(),{fetchMaterialFieldConfig:y}=yr(),{getNextDisplayDataForCreate:Z}=nl(),{fetchValuationClassData:E}=fr(),i=Y(t=>t.payload.payloadData),p=i==null?void 0:i.RequestType,F=Y(t=>t.applicationConfig),w=Y(t=>t.paginationData),x=Y(t=>t.payload),h=Y(t=>t.request.materialRows),J=Y(t=>{var n;return((n=t.AllDropDown)==null?void 0:n.dropDown)||{}}),te=Y(t=>t.tabsData.allTabsData);let X=Y(t=>t.userManagement.taskData);const he=Y(t=>t.tabsData.allMaterialFieldConfigDT),be=mn(),xe=new URLSearchParams(be.search),M=xe.get("RequestId"),Ae=xe.get("RequestType"),[se,u]=l.useState(0),[P,ne]=l.useState(null),[K,T]=l.useState(null),le="Basic Data",[Q,ce]=l.useState([le]),[de,B]=l.useState([]),[D,V]=l.useState(h||[]),ge=Y(t=>t.selectedSections.selectedSections),[He,Le]=l.useState(!1),[Ht,Ct]=l.useState(!1),[It,Ie]=l.useState(""),[dt,z]=l.useState([]),[pe,r]=l.useState(0),[Ue,Dt]=l.useState({code:"",desc:""}),[De,Tt]=l.useState(!1),{fetchDataAndDispatch:bt}=Yl(),[$e,Yt]=l.useState(!0),[Jt,ct]=l.useState(D.length+1),[ve,ut]=l.useState(0),[je,At]=l.useState(h.length>0),[Nt,Ft]=l.useState({}),[Ze,O]=l.useState({}),[et,Kt]=l.useState([]),[we,ht]=l.useState({}),[oe,_e]=l.useState([]),[wt,Ut]=l.useState(!1),[nt,Ke]=l.useState(""),[ft,vt]=l.useState("Basic Data"),[_t,gt]=l.useState(!1),[ye,$t]=l.useState(null),jt=Y(t=>t.request.salesOrgDTData),st=(ys=x==null?void 0:x[ye])==null?void 0:ys.headerData,lt=(i==null?void 0:i.Region)===cn.EUR?{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null}:{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null},[ue,Be]=l.useState([lt]),[Sn,kt]=l.useState([]),[U,Qe]=l.useState({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:{value:null,options:[]}}),[Qt,Zt]=l.useState(!1),[We,Ot]=l.useState({}),[en,Bt]=l.useState("success"),Vt=(Ms=x==null?void 0:x[ye])==null?void 0:Ms.payloadData,[tn,nn]=l.useState(!1),[Rt,xn]=l.useState([]),[Gt,sn]=l.useState(""),[ln,on]=l.useState([]),{getDynamicWorkflowDT:rn}=gr(),[an,$]=l.useState(!1),[C,me]=l.useState(!1),[Oe,tt]=l.useState(!1),[Se,a]=l.useState(""),[S,b]=l.useState({"Sales Organization":!1,"Distribution Channel":{},Plant:{},"Storage Location":{},warehouse:{},"Mrp Profile":!1}),[q,k]=l.useState(!1),[H,re]=l.useState(0),{fetchTabSpecificData:fe}=pr(),{getContryBasedOnPlant:yt}=Sr({doAjax:Te,customError:A,fetchDataAndDispatch:bt,destination_MaterialMgmt:Ce}),{extendFilteredButtons:dn,showWfLevels:Rn}=sl(X,F,vn,ls),yn=kn(dn,[qt.HANDLE_SUBMIT_FOR_APPROVAL,qt.HANDLE_SAP_SYNDICATION,qt.HANDLE_SUBMIT_FOR_REVIEW,qt.HANDLE_SUBMIT]),En=t=>{!t||!Array.isArray(t)||t.forEach(n=>{var s,o,c,m,R,v,j,G,ae,W,Ne,ke,ee,Fe,Pe,mt;if((o=(s=n.plant)==null?void 0:s.value)!=null&&o.code){if(fe((m=(c=n.plant)==null?void 0:c.value)==null?void 0:m.code,Ve.PLANT),(R=n.salesOrg)!=null&&R.code||(j=(v=n.dc)==null?void 0:v.value)!=null&&j.code){const Wt=`${((G=n.salesOrg)==null?void 0:G.code)||""}-${((W=(ae=n.dc)==null?void 0:ae.value)==null?void 0:W.code)||""}`;fe(Wt,Ve.SALES)}(ke=(Ne=n.warehouse)==null?void 0:Ne.value)!=null&&ke.code&&fe((Fe=(ee=n.warehouse)==null?void 0:ee.value)==null?void 0:Fe.code,Ve.WAREHOUSE),yt((mt=(Pe=n.plant)==null?void 0:Pe.value)==null?void 0:mt.code)}})},Ee=t=>{if(!t||!Array.isArray(t))return[];let n=(i==null?void 0:i.Region)===cn.EUR?t==null?void 0:t.filter(s=>s!==Ve.WAREHOUSE&&s!==Ve.WORKSCHEDULING&&s!==Ve.WORK_SCHEDULING):[...t];return n.sort((s,o)=>s===Ve.BASIC_DATA?-1:o===Ve.BASIC_DATA?1:0),n},ot=async()=>{var t,n;try{const s=await rn(p,i==null?void 0:i.Region,"",(n=(t=x[ye])==null?void 0:t.Tochildrequestheaderdata)==null?void 0:n.MaterialGroupType,X==null?void 0:X.ATTRIBUTE_3);on(s)}catch(s){A(s)}};l.useEffect(()=>{p&&(i!=null&&i.Region)&&ye&&(X!=null&&X.ATTRIBUTE_3)&&ot()},[p,i==null?void 0:i.Region,ye,X==null?void 0:X.ATTRIBUTE_3]),l.useEffect(()=>{var t,n,s,o,c,m,R,v,j,G,ae,W,Ne,ke,ee;if(V(h),At((h==null?void 0:h.length)>0),(h==null?void 0:h.length)>0){$t((t=h==null?void 0:h[0])==null?void 0:t.id),jn(((s=(n=h==null?void 0:h[0])==null?void 0:n.materialType)==null?void 0:s.code)||((o=h==null?void 0:h[0])==null?void 0:o.materialType)),ut(0),a((c=h==null?void 0:h[0])==null?void 0:c.materialNumber),vt(Ve.BASIC_DATA),ce((R=(m=h==null?void 0:h[0])==null?void 0:m.views)!=null&&R.length?Ee((v=h==null?void 0:h[0])==null?void 0:v.views):Ee([le]));const Fe=Jl(x),Pe=Kl(Fe);let mt=JSON.parse(JSON.stringify(Pe));_(Ql(mt)),_(qs({keyName:"selectedMaterialID",data:(j=h==null?void 0:h[0])==null?void 0:j.id})),(W=(ae=x==null?void 0:x[(G=h==null?void 0:h[0])==null?void 0:G.id])==null?void 0:ae.Tochildrequestheaderdata)!=null&&W.ChildRequestId&&_(qs({keyName:"childRequestId",data:(ee=(ke=x==null?void 0:x[(Ne=h==null?void 0:h[0])==null?void 0:Ne.id])==null?void 0:ke.Tochildrequestheaderdata)==null?void 0:ee.ChildRequestId}))}},[h]),l.useEffect(()=>{(D==null?void 0:D.length)===0&&Le(!1)},[D]),l.useEffect(()=>{["Sales Organization","Mrp Profile"].forEach(Nl)},[]),l.useEffect(()=>{if(M){const t=st==null?void 0:st.orgData;(t==null?void 0:t.length)>0&&t.some(n=>{var s,o,c,m,R;return((o=(s=n.plant)==null?void 0:s.value)==null?void 0:o.code)&&(((c=n.salesOrg)==null?void 0:c.code)||((R=(m=n.dc)==null?void 0:m.value)==null?void 0:R.code))})&&En(t)}},[st==null?void 0:st.orgData]),l.useEffect(()=>{var t,n;Q.includes((t=Ve)==null?void 0:t.SALES)&&!Q.includes((n=Ve)==null?void 0:n.SALES_PLANT)&&ce(s=>Ee([...s,"Sales-Plant"]))},[Q]);const ml=()=>{Bn()},$n=(t="",n=!1)=>{var m,R,v,j;const s={materialNo:t??"",top:500,skip:n?0:pe,salesOrg:((R=(m=jt==null?void 0:jt.uniqueSalesOrgList)==null?void 0:m.map(G=>G.code))==null?void 0:R.join("$^$"))||""},o=G=>{(G==null?void 0:G.statusCode)===Xe.STATUS_200&&(z(n?G==null?void 0:G.body:ae=>[...ae,...G==null?void 0:G.body]),Tt(!1))},c=()=>{Tt(!1)};Tt(!0),Te(`/${Ce}${(j=(v=at)==null?void 0:v.DATA)==null?void 0:j.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",o,c,s)},ze=!pn.includes(f==null?void 0:f.requestStatus),Sl=t=>{const n=o=>{(o==null?void 0:o.statusCode)===Xe.STATUS_200&&Kt(o==null?void 0:o.body)},s=o=>{console.error(o,"while fetching the validation data of material number")};Te(`/${Ce}/data/getNumberRangeForMaterialType?materialType=${t==null?void 0:t.code}`,"get",n,s)};function bs(t){const n=o=>{var c;if((o==null?void 0:o.statusCode)===Xe.STATUS_200){let m=(c=o==null?void 0:o.body)==null?void 0:c.filter(R=>!Qs.includes(R));(i==null?void 0:i.Region)===cn.EUR&&(m=m==null?void 0:m.filter(R=>R!==Ve.WAREHOUSE&&R!==Ve.WORK_SCHEDULING&&R!==Ve.WORKSCHEDULING)),_e(m)}},s=o=>{A(o)};Te(`/${Ce}/data/getViewForMaterialType?materialType=${t}`,"get",n,s)}l.useEffect(()=>{$n()},[]);const As=((Ls=et==null?void 0:et[1])==null?void 0:Ls.External)==="X",Ns=et==null?void 0:et.some(t=>t.ExtNAwock==="X");function jn(t){var o;const n=(i==null?void 0:i.Region)||cn.US;if(!he.some(c=>c[n]&&c[n][t])&&t)y(t,n);else if(!t)_(ts({}));else{const c=he==null?void 0:he.find(m=>(m==null?void 0:m[n])&&(m==null?void 0:m[n][t]));c&&_(ts((o=c[n][t])==null?void 0:o.allfields))}t&&E(t)}const pt=t=>{const{id:n,field:s,value:o}=t,c=D.map(m=>m.id===n?{...m,[s]:o}:m);ht({...we,[s]:o}),s===io.MATERIALTYPE&&(Sl(o),bs(o),ce([le]),ao([lt]),_(Mn({materialID:n,keyName:"views",data:[le]})),_(Mn({materialID:n,keyName:"orgData",data:""})),jn(o==null?void 0:o.code)),V(c),_(Mn({materialID:n,keyName:s,data:o}))},vs=t=>{var n,s,o,c,m,R,v,j,G;$t(t.row.id),Ot(t.row),a(t.row.materialNumber),_e((n=t==null?void 0:t.row)==null?void 0:n.views),jn(((o=(s=t==null?void 0:t.row)==null?void 0:s.materialType)==null?void 0:o.code)||((c=t.row)==null?void 0:c.materialType)),ce((m=t==null?void 0:t.row)!=null&&m.views?(R=t.row)==null?void 0:R.views:[le]),Be((j=(v=t==null?void 0:t.row)==null?void 0:v.orgData)!=null&&j.length?(G=t.row)==null?void 0:G.orgData:[lt]),ut(0),vt("Basic Data")},xl=()=>{Ct(!0)},Bn=()=>{Ct(!1)},Vn=(t,n)=>{n==="backdropClick"||n==="escapeKeyDown"||gt(!1)},El=()=>ce(Ee(oe)),Cl=t=>{if(Zt(!1),t!=null&&t.length){let n=[...D];t==null||t.forEach(s=>{var R,v;const o=s==null?void 0:s.Material;let c={...s},m=(R=x==null?void 0:x[s.id])!=null&&R.payloadData?JSON.parse(JSON.stringify((v=x==null?void 0:x[s.id])==null?void 0:v.payloadData)):"";c.id=o,c.globalMaterialDescription="",c.materialNumber="",c.included=!0,c.industrySector=s==null?void 0:s.IndSector,c.materialType=s==null?void 0:s.MatlType,c.materialNumber=s==null?void 0:s.Material,c.globalMaterialDescription=s==null?void 0:s.MaterialDescrption,c.views=s!=null&&s.Views?s==null?void 0:s.Views.split(",").map(j=>j.trim()):[le],c.validated=!1,n.push(c),_(co({materialID:o,data:c,payloadData:m}))}),B(s=>[...s,...n.map(o=>({material:o==null?void 0:o.Material,views:o==null?void 0:o.views}))]),V(n),_(Tn(n)),ct(Jt+1),At(!0),Yt(!0)}},_s=[{field:"included",headerName:"Included",flex:.5,align:"center",headerAlign:"center",renderCell:t=>{var n;return t!=null&&t.row?e(ns,{checked:(n=t==null?void 0:t.row)==null?void 0:n.included,disabled:ze,onChange:s=>{var o;(o=t==null?void 0:t.row)!=null&&o.id&&pt({id:t.row.id,field:"included",value:s.target.checked})}}):null}},{field:"lineNumber",headerName:"Line Number",flex:.6,editable:p==="Create",align:"center",headerAlign:"center",renderCell:t=>{const s=((h==null?void 0:h.findIndex(o=>{var c;return(o==null?void 0:o.id)===((c=t==null?void 0:t.row)==null?void 0:c.id)}))+1)*10;return e("div",{children:s})}},{field:"industrySector",headerName:"Industry Sector",flex:1,align:"center",headerAlign:"center",renderCell:t=>{var n,s,o,c,m;return e(Pt,{options:(J==null?void 0:J.IndSector)||[],value:(n=t==null?void 0:t.row)==null?void 0:n.industrySector,onChange:R=>pt({id:t.row.id,field:"industrySector",value:R}),placeholder:"Select Industry Sector",minWidth:"90%",disabled:!0,listWidth:232,title:`${((o=(s=t.row)==null?void 0:s.industrySector)==null?void 0:o.code)||""} - ${((m=(c=t.row)==null?void 0:c.industrySector)==null?void 0:m.desc)||""}`})}},{field:"materialType",headerName:"Material Type",flex:1,align:"center",headerAlign:"center",renderCell:t=>{var n,s,o,c,m;return e(Pt,{options:Ks||[],value:(n=t==null?void 0:t.row)==null?void 0:n.materialType,onChange:R=>pt({id:t.row.id,field:"materialType",value:R}),placeholder:"Select Material Type",disabled:!0,minWidth:"90%",listWidth:232,title:`${((o=(s=t.row)==null?void 0:s.materialType)==null?void 0:o.code)||""} - ${((m=(c=t.row)==null?void 0:c.materialType)==null?void 0:m.desc)||""}`})}},{field:"materialNumber",headerName:"Material Number",flex:p==="Extend"?.8:1,editable:!(!As&&!Ns),align:"center",headerAlign:"center",renderHeader:()=>d("span",{children:["Material Number",e("span",{style:{color:"red"},children:"*"})]}),renderCell:t=>{var n,s;return e(Je,{children:(i==null?void 0:i.RequestType)===L.EXTEND?e(Wn,{fullWidth:!0,placeholder:"Enter Material Number",variant:"outlined",size:"small",name:"material number",value:(n=t==null?void 0:t.row)==null?void 0:n.materialNumber,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:Re.black.dark,color:Re.black.dark}}},onChange:(o,c)=>pt({id:t.row.id,field:"materialNumber",value:c}),disabled:!As&&!Ns}):(s=t==null?void 0:t.row)==null?void 0:s.materialNumber})}},{field:"globalMaterialDescription",flex:p==="Extend"?.8:1,headerName:"Material Description",renderHeader:()=>d("span",{children:["Material Description",e("span",{style:{color:"red"},children:"*"})]}),renderCell:t=>{var n,s;return e(Je,{children:(i==null?void 0:i.RequestType)===L.EXTEND?e(Wn,{fullWidth:!0,placeholder:"Enter Material Description",variant:"outlined",disabled:!0,size:"small",name:"material description",sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:Re.black.dark,color:Re.black.dark}}},onChange:(o,c)=>pt({id:t.row.id,field:"globalMaterialDescription",value:c}),value:(n=t==null?void 0:t.row)==null?void 0:n.globalMaterialDescription}):(s=t==null?void 0:t.row)==null?void 0:s.globalMaterialDescription})},align:"center",headerAlign:"center",editable:!0},{field:"views",headerName:"",flex:p==="Extend"?1.5:1,align:"center",headerAlign:"center",renderCell:t=>d(_n,{direction:"row",spacing:0,alignItems:"center",children:[e(qe,{variant:"contained",size:"small",sx:{minWidth:80},onClick:()=>{var n,s;bs(t.row.materialType),Ut(!0),Ke(t.row.id),Ot(t.row),ce((n=t==null?void 0:t.row)!=null&&n.Views?(s=t==null?void 0:t.row)==null?void 0:s.Views:[le])},children:"Views"}),e(is,{color:"disabled",fontSize:"small",sx:{mx:.5}}),e(qe,{variant:"contained",size:"small",sx:{minWidth:100},onClick:()=>{var n,s,o,c;gt(!0),Ke(t.row.id),Be((s=(n=t==null?void 0:t.row)==null?void 0:n.orgData)!=null&&s.length?(o=t.row)==null?void 0:o.orgData:[lt]),Ot(t.row),Os((c=t==null?void 0:t.row)==null?void 0:c.materialNumber,rt.NOT_EXTENDED),me(!1)},children:"ORG Data"}),e(is,{color:"disabled",fontSize:"small",sx:{mx:.5}}),e(zt,{title:"Click after changing Views or ORG Data",children:e(Lt,{onClick:()=>{var n,s;gt(!0),Ke(t.row.id),me(!0),Ot(t.row),Os((n=t==null?void 0:t.row)==null?void 0:n.materialNumber,rt.EXTENDED),Qe(Sn.find(o=>{var c;return o.id===((c=t.row)==null?void 0:c.id)})||{id:(s=t.row)==null?void 0:s.id,plant:null,salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:null})},disabled:ze,color:"primary",size:"small",children:e(Bs,{})})})]})},{field:"action",headerName:"Action",flex:.9,align:"center",headerAlign:"center",renderCell:t=>e(_n,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:!M&&e(zt,{title:"Delete Row",children:e(Lt,{onClick:()=>{V(D.filter(n=>n.id!==t.row.id)),_(uo(t.row.id)),_(Tn(D.filter(n=>n.id!==t.row.id))),D!=null&&D.length||Le(!1)},color:"error",children:e(Vs,{})})})})}],Os=(t,n)=>{b(c=>({...c,"Sales Organization":!0}));const s=c=>{if((c==null?void 0:c.statusCode)===Xe.STATUS_200){let m;n===rt.NOT_EXTENDED?m=Mt(c.body):m=c.body.length>0?Mt(c.body):[],O(R=>({...R,"Sales Organization":m}))}b(m=>({...m,"Sales Organization":!1}))},o=()=>{b(c=>({...c,"Sales Organization":!1}))};Te(`/${Ce}/data/${n===rt.NOT_EXTENDED?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${t}&region=${i==null?void 0:i.Region}`,"get",s,o)},Tl=(t,n,s,o)=>{b(v=>({...v,Plant:{...v.Plant,[o]:!0}}));const c=v=>{if((v==null?void 0:v.statusCode)===Xe.STATUS_200){let j;n===rt.NOT_EXTENDED?j=Mt(v.body):j=v.body.length>0?Mt(v.body||[]):[],O(G=>({...G,Plant:j}))}b(j=>({...j,Plant:{...j.Plant,[o]:!1}}))},m=()=>{b(v=>({...v,Plant:{...v.Plant,[o]:!1}}))},R=s?`&salesOrg=${s.code}`:"";Te(`/${Ce}/data/${n===rt.NOT_EXTENDED?"getPlantNotExtended":"getPlantExtended"}?materialNo=${t}&region=${i==null?void 0:i.Region}${R}`,"get",c,m)},bl=(t,n,s,o)=>{b(v=>({...v,warehouse:{...v.warehouse,[o]:!0}}));const c=v=>{if((v==null?void 0:v.statusCode)===Xe.STATUS_200){let j;n===rt.NOT_EXTENDED?j=Mt(v.body):j=v.body.length>0?Mt(v.body||[]):[],O(G=>({...G,warehouse:j}))}b(j=>({...j,warehouse:{...j.warehouse,[o]:!1}}))},m=()=>{b(v=>({...v,warehouse:{...v.warehouse,[o]:!1}}))},R=s?`&plant=${s.code}`:"";Te(`/${Ce}/data/${n===rt.NOT_EXTENDED?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${t}&region=${i==null?void 0:i.Region}${R}`,"get",c,m)},Al=(t,n)=>{var s,o;ut(n),vt(((s=t==null?void 0:t.target)==null?void 0:s.id)==="AdditionalKey"?"Additional Data":(o=Q==null?void 0:Q.filter(c=>c!==Ve.STORAGE))==null?void 0:o[n])},Nl=t=>{b(c=>({...c,[t]:!0}));const n={"Sales Organization":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},s=c=>{if((c==null?void 0:c.statusCode)===Xe.STATUS_200){const m=Mt(c.body);O(R=>({...R,[t]:m}))}b(m=>({...m,[t]:!1}))},o=c=>{console.error(c),b(m=>({...m,[t]:!1}))};Te(`/${Ce}/data${n[t]}`,"get",s,o)},vl=t=>{ho(t,Q,Vt,ye,ue,_,Ln)},_l=(t,n,s)=>(o,c)=>{var W,Ne,ke;let m={},R="",v="";s==="Purchasing"||s==="Costing"?(m={materialNo:n==null?void 0:n.Material,plant:n==null?void 0:n.Plant},v=n==null?void 0:n.Plant,R=`/${Ce}/data/displayLimitedPlantData`):s==="Accounting"?(m={materialNo:n==null?void 0:n.Material,valArea:n==null?void 0:n.ValArea},v=n==null?void 0:n.ValArea,R=`/${Ce}/data/displayLimitedAccountingData`):s==="Sales"&&(m={materialNo:n==null?void 0:n.Material,salesOrg:n==null?void 0:n.SalesOrg,distChnl:n==null?void 0:n.DistrChan},v=`${n==null?void 0:n.SalesOrg}-${n==null?void 0:n.DistrChan}`,R=`/${Ce}/data/displayLimitedSalesData`);const j=ee=>{var Fe,Pe,mt;(ee==null?void 0:ee.statusCode)===Xe.STATUS_200&&(s==="Purchasing"||s==="Costing"?_(Ln({materialID:ye,viewID:s,itemID:n==null?void 0:n.Plant,data:(Fe=ee==null?void 0:ee.body)==null?void 0:Fe.SpecificPlantDataViewDto[0]})):s==="Accounting"?_(Ln({materialID:ye,viewID:s,itemID:n==null?void 0:n.ValArea,data:(Pe=ee==null?void 0:ee.body)==null?void 0:Pe.SpecificAccountingDataViewDto[0]})):s==="Sales"&&_(Ln({materialID:ye,viewID:s,itemID:`${n==null?void 0:n.SalesOrg}-${n==null?void 0:n.DistrChan}`,data:(mt=ee==null?void 0:ee.body)==null?void 0:mt.SpecificSalesDataViewDto[0]})))},G=()=>{};!((ke=(Ne=(W=x==null?void 0:x[ye])==null?void 0:W.payloadData)==null?void 0:Ne[s])!=null&&ke[v])&&Te(R,"post",j,G,m),T(c?t:null)},Ol=()=>te&&ft&&(te[ft]||ft==="Additional Data")?ft==="Additional Data"?[e(Rr,{disabled:ze,materialID:ye,selectedMaterialNumber:Se})]:[e(Or,{disabled:ze,materialID:ye,basicData:Nt,setBasicData:Ft,dropDownData:Ze,allTabsData:te,basicDataTabDetails:te[ft],activeViewTab:ft,selectedViews:Q,handleAccordionClick:_l,missingValidationPlant:Rt,selectedMaterialNumber:Se,callGetCountryBasedonSalesOrg:Oe})]:e(Je,{}),Rl=t=>{const n=t.target.value;Dt({code:n,desc:""}),r(0),P&&clearTimeout(P);const s=setTimeout(()=>{$n(n,!0)},500);ne(s)};l.useEffect(()=>{pe>0&&$n(Ue==null?void 0:Ue.code)},[pe]);const yl=(t,n,s)=>{var o;if(t==="Sales Organization"){Ml(n,s);const c=(o=D==null?void 0:D.find(m=>m.id===nt))==null?void 0:o.materialNumber;Tl(c,C?rt.EXTENDED:rt.NOT_EXTENDED,n,s)}},Ml=(t,n,s="",o="")=>(b(c=>({...c,"Distribution Channel":{...c["Distribution Channel"],[n]:!0}})),new Promise((c,m)=>{var G;const R=ae=>{if(ae.statusCode===Xe.STATUS_200){const W=Mt(ae.body);let Ne=JSON.parse(JSON.stringify(s||ue));C?Qe(ke=>({...ke,salesOrg:t,dc:{value:null,options:(W==null?void 0:W.length)>0?W:[]}})):(Ne[n].salesOrg=t,Ne[n].dc.options=W,Be(Ne))}b(W=>({...W,"Distribution Channel":{...W["Distribution Channel"],[n]:!1}})),c(ae)},v=ae=>{b(W=>({...W,"Distribution Channel":{...W["Distribution Channel"],[n]:!1}})),m(ae)};let j=(G=D==null?void 0:D.find(ae=>ae.id===nt))==null?void 0:G.materialNumber;j&&Te(`/${Ce}/data/${C?"getDistributionChannelExtended":"getDistributionChannelNotExtended"}?materialNo=${j}&salesOrg=${t==null?void 0:t.code}`,"get",R,v)})),Ll=(t,n)=>{var o;Il(t,n);const s=(o=D==null?void 0:D.find(c=>c.id===nt))==null?void 0:o.materialNumber;bl(s,C?rt.EXTENDED:rt.NOT_EXTENDED,t,n)},Il=(t,n,s="",o)=>{var G;b(ae=>({...ae,"Storage Location":{...ae["Storage Location"],[n]:!0}}));const c=ae=>{if(b(W=>({...W,"Storage Location":{...W["Storage Location"],[n]:!1}})),ae.statusCode===Xe.STATUS_200){const W=Mt(ae.body);let Ne=JSON.parse(JSON.stringify(s||ue));C?Qe(ke=>({...ke,plant:{value:t,options:[]},sloc:{value:null,options:(W==null?void 0:W.length)>0?W:[]}})):(Ne[n].plant.value=t,Ne[n].sloc.options=W,Be(Ne))}if(o){_(Mn({materialID:o==null?void 0:o.id,keyName:"orgData",data:rowOption}));let W=(D==null?void 0:D.length)||[JSON.parse(JSON.stringify(o))],Ne=W.findIndex(ke=>ke.id===(o==null?void 0:o.id));W[Ne].orgData=rowOption,_(Tn(W))}},m=ae=>{console.error(ae),b(W=>({...W,"Storage Location":{...W["Storage Location"],[n]:!1}}))};let R=(G=D.find(ae=>ae.id===nt))==null?void 0:G.materialNumber;const v=ue[n],j=v!=null&&v.salesOrg?`&salesOrg=${v.salesOrg.code}`:"";R&&Te(`/${Ce}/data/${C?"getStorageLocationExtended":"getStorageLocationNotExtended"}?materialNo=${R}&region=${i==null?void 0:i.Region}&plant=${t==null?void 0:t.code}${j}`,"get",c,m)},Dl=(t,n)=>{let s=JSON.parse(JSON.stringify(ue));s[n].dc.value=t,Be(s)},wl=t=>{let n=JSON.parse(JSON.stringify(ue));n.splice(t,1),Be(n)},kl=(t,n)=>{let s=JSON.parse(JSON.stringify(ue));s[n].sloc.value=t,Be(s)},Pl=(t,n)=>{let s=JSON.parse(JSON.stringify(ue));s[n].warehouse.value=t,Be(s)},ql=(t,n)=>{let s=JSON.parse(JSON.stringify(ue));s[n].mrpProfile=t,Be(s)},Hl=()=>{let t=JSON.parse(JSON.stringify(ue));t.push({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}),Be(t)},Fl=(t,n,s,o)=>{var v,j,G,ae,W,Ne,ke,ee,Fe,Pe,mt;const c={material:We==null?void 0:We.materialNumber,wareHouseNumber:((j=(v=t==null?void 0:t.warehouse)==null?void 0:v.value)==null?void 0:j.code)??"",plant:((ae=(G=t==null?void 0:t.plant)==null?void 0:G.value)==null?void 0:ae.code)??"",salesOrg:((W=t==null?void 0:t.salesOrg)==null?void 0:W.code)??"",storageLocation:((ke=(Ne=t==null?void 0:t.sloc)==null?void 0:Ne.value)==null?void 0:ke.code)??"",distributionChannel:((Fe=(ee=t==null?void 0:t.dc)==null?void 0:ee.value)==null?void 0:Fe.code)??"",valArea:((mt=(Pe=t==null?void 0:t.plant)==null?void 0:Pe.value)==null?void 0:mt.code)??""},m=Wt=>{const ks=fo(Wt==null?void 0:Wt.body,n,s,o,We),$l=p===L.EXTEND_WITH_UPLOAD||Ae===L.EXTEND_WITH_UPLOAD?go(x,ks):po(x,ks);_(mo({data:$l})),tt(!Oe)},R=Wt=>{A(Wt)};Te(`/${Ce}${at.DATA.COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND}`,"post",m,R,c)},Ul=[{id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}],Rs=t=>{_(el(t)),u(t)};l.useEffect(()=>{var t,n;(w==null?void 0:w.page)!==0&&(p===((t=L)==null?void 0:t.EXTEND_WITH_UPLOAD)||p===((n=L)==null?void 0:n.EXTEND))&&Z(),u((w==null?void 0:w.page)||0)},[w==null?void 0:w.page]);const Gn=C?Ul:ue;return d("div",{children:[e("div",{style:{padding:"0",width:"100%",margin:"0"},children:d("div",{style:{height:300,width:"100%"},children:[e(I,{sx:{display:"flex",justifyContent:"flex-end",marginBottom:"10px"},children:e(qe,{variant:"contained",color:"primary",onClick:()=>{$(!0)},disabled:He||ze||M&&(f==null?void 0:f.requestStatus)!==Ye.DRAFT,children:"+ Add"})}),D&&(D==null?void 0:D.length)>0?e(Je,{children:e(Kn,{rows:D,columns:_s,pageSize:50,page:se,rowsPerPageOptions:[50],rowCount:(w==null?void 0:w.totalElements)||0,onRowClick:vs,onCellEditCommit:pt,onPageChange:t=>Rs(t),disableSelectionOnClick:!0,getRowClassName:t=>t.id===ye?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})}):e(Je,{children:e(Kn,{rows:D,columns:_s,pageSize:5,rowsPerPageOptions:[5],page:se,onRowClick:vs,onCellEditCommit:pt,onPageChange:t=>Rs(t),disableSelectionOnClick:!0,getRowClassName:t=>t.id===ye?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})]})}),ye&&je&&(D==null?void 0:D.length)>0&&ge.length>0&&te&&((Is=Object.getOwnPropertyNames(te))==null?void 0:Is.length)>0&&d(I,{sx:{marginTop:"45px"},children:[d(as,{sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:Re.background.container,borderBottom:`1px solid ${Re.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:Re.black.graphite,"&.Mui-selected":{color:Re.primary.main,fontWeight:700},"&:hover":{color:Re.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:Re.primary.main,height:"3px"}},value:ve,onChange:Al,className:g.customTabs,"aria-label":"material tabs",children:[Q&&ue.length>0&&(Q==null?void 0:Q.length)>0?(Ds=Q==null?void 0:Q.filter(t=>t!==Ve.STORAGE))==null?void 0:Ds.map((t,n)=>e(bn,{label:t},n)):e(Je,{}),e(bn,{label:"Additional Data",id:"AdditionalKey"},"Additional data")]}),(D==null?void 0:D.length)>0&&e(I,{sx:{padding:2,marginTop:2},children:Ol()}),e(us,{activeTab:ve,submitForApprovalDisabled:$e,filteredButtons:yn,workFlowLevels:ln,showWfLevels:Rn,childRequestHeaderData:(ws=x==null?void 0:x[ye])==null?void 0:ws.Tochildrequestheaderdata})]}),e("div",{}),e(Zs,{dialogState:Ht,openReusableDialog:xl,closeReusableDialog:Bn,dialogTitle:"Warning",dialogMessage:It,showCancelButton:!1,handleOk:ml,handleDialogConfirm:Bn,dialogOkText:"OK",dialogSeverity:"danger"}),wt&&e(qn,{fullWidth:!0,maxWidth:!1,open:!0,onClose:Vn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:d(I,{sx:{width:"600px !important"},children:[d(Zn,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[e(rs,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),e("span",{children:"Select Views"})]}),e(An,{sx:{paddingBottom:".5rem"},children:d(I,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[e(Zl,{size:"small",multiple:!0,fullWidth:!0,options:oe,disabled:ze,disableCloseOnSelect:!0,value:Q==null?void 0:Q.filter(t=>t!=="Sales-Plant"),onChange:(t,n)=>{ce([le,...n.filter(s=>s!==le)]),pt({id:nt,field:"views",value:n})},getOptionDisabled:t=>t===le,renderOption:(t,n,{selected:s})=>{var m;const o=de.find(R=>(R==null?void 0:R.material)===(We==null?void 0:We.materialNumber)),c=((m=o==null?void 0:o.views)==null?void 0:m.includes(n))||!1;return d("li",{...t,children:[e(ns,{checked:s||n=="Basic Data",sx:{marginRight:1}}),n," ",c?"(extended)":""]})},renderTags:(t,n)=>t.map((s,o)=>{var j;const{key:c,...m}=n({index:o}),R=de.find(G=>(G==null?void 0:G.material)===(We==null?void 0:We.materialNumber)),v=((j=R==null?void 0:R.views)==null?void 0:j.includes(s))||!1;return e(eo,{label:`${s} ${v?"(extended)":""}`,...m,disabled:s===le},c)}),renderInput:t=>e(Wn,{...t,label:"Select Views"})}),e(qe,{variant:"contained",disabled:ze,size:"small",onClick:()=>El(),children:"Select all"})]})}),d(Nn,{children:[e(qe,{onClick:()=>{Ut(!1)},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),e(qe,{onClick:()=>{Ut(!1),pt({id:nt,field:"views",value:Q})},variant:"contained",children:"OK"})]})]})}),_t&&d(qn,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:Vn,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[d(Zn,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[e(rs,{fontSize:"small"}),C?e("span",{children:"Select org data for copy"}):e("span",{children:"Select org data to be extended"}),e(Lt,{onClick:Vn,sx:{position:"absolute",right:15},children:e(cs,{})})]}),e(An,{sx:{padding:0},children:e(to,{component:ss,children:d(no,{children:[e(so,{children:d(Hs,{children:[!C&&e(Ge,{align:"center",children:"S NO."}),e(Ge,{align:"center",children:"Sales Org"}),e(Ge,{align:"center",children:"Distribution Channel"}),e(Ge,{align:"center",children:"Plant"}),e(Ge,{align:"center",children:"Storage Location"}),(i==null?void 0:i.Region)!==cn.EUR&&e(Ge,{align:"center",children:"Warehouse"}),e(Ge,{align:"center",children:"MRP Profile"}),(ue==null?void 0:ue.length)>1&&!C&&e(Ge,{align:"center",children:"Action"})]})}),e(lo,{children:Gn==null?void 0:Gn.map((t,n)=>{var s,o,c,m,R,v,j,G,ae,W,Ne,ke;return d(Hs,{sx:{padding:"12px"},children:[!C&&e(Ge,{children:e(Me,{variant:"body2",children:n+1})}),e(Ge,{children:e(Pt,{options:Ze["Sales Organization"],value:C?U==null?void 0:U.salesOrg:t==null?void 0:t.salesOrg,onChange:ee=>yl("Sales Organization",ee,n),placeholder:"Select Sales Org",disabled:ze,isFieldError:!1,minWidth:165,isLoading:S["Sales Organization"]})}),e(Ge,{children:e(Pt,{options:C?(o=U==null?void 0:U.dc)==null?void 0:o.options:(s=t.dc)==null?void 0:s.options,value:C?(m=U==null?void 0:U.dc)==null?void 0:m.value:(c=t.dc)==null?void 0:c.value,onChange:ee=>C?Qe(Fe=>{var Pe;return{...Fe,dc:{value:ee,options:(Pe=U==null?void 0:U.dc)==null?void 0:Pe.options}}}):Dl(ee,n),placeholder:"Select DC",disabled:ze,isFieldError:!1,minWidth:165,isLoading:S["Distribution Channel"][n]})}),e(Ge,{children:e(Pt,{options:Ze.Plant||[],value:C?(v=U==null?void 0:U.plant)==null?void 0:v.value:(R=t.plant)==null?void 0:R.value,onChange:ee=>Ll(ee,n),placeholder:"Select Plant",disabled:ze,isFieldError:!1,minWidth:165,isLoading:S.Plant[n]})}),e(Ge,{children:e(Pt,{options:C?(G=U==null?void 0:U.sloc)==null?void 0:G.options:(j=t==null?void 0:t.sloc)==null?void 0:j.options,value:C?(W=U==null?void 0:U.sloc)==null?void 0:W.value:(ae=t==null?void 0:t.sloc)==null?void 0:ae.value,onChange:ee=>C?Qe(Fe=>{var Pe;return{...Fe,sloc:{value:ee,options:(Pe=U==null?void 0:U.sloc)==null?void 0:Pe.options}}}):kl(ee,n),placeholder:"Select Sloc",disabled:ze,isFieldError:!1,minWidth:165,isLoading:S["Storage Location"][n]})}),(i==null?void 0:i.Region)!==cn.EUR&&e(Ge,{children:e(Pt,{options:Ze.warehouse||[],value:C?(ke=U==null?void 0:U.warehouse)==null?void 0:ke.value:(Ne=t==null?void 0:t.warehouse)==null?void 0:Ne.value,onChange:ee=>C?Qe(Fe=>{var Pe;return{...Fe,warehouse:{value:ee,options:(Pe=U==null?void 0:U.warehouse)==null?void 0:Pe.options}}}):Pl(ee,n),placeholder:"Select Warehouse",disabled:ze,isFieldError:!1,minWidth:165,isLoading:S.warehouse[n]})}),e(Ge,{children:e(Pt,{options:Ze["Mrp Profile"]||[],value:C?U==null?void 0:U.mrpProfile:t.mrpProfile,onChange:ee=>C?Qe(Fe=>({...Fe,mrpProfile:ee})):ql(ee,n),placeholder:"Select MRP Profile",disabled:ze,isFieldError:!1,minWidth:165,isLoading:S["Mrp Profile"]})}),ue.length>1&&d(Ge,{align:"right",children:[e(Lt,{size:"small",color:"primary",disabled:ze,onClick:()=>{k(!0),re({orgRowLength:ue.length,copyFor:n})},style:{display:n===0?"none":"inline-flex"},children:e(Bs,{})}),e(Lt,{style:{display:n===0?"none":"inline-flex"},size:"small",color:"error",onClick:()=>wl(n),children:e(Vs,{})})]})]},n)})})]})})}),d(Nn,{sx:{justifyContent:"flex-end",gap:.5},children:[!C&&e(qe,{onClick:Hl,disabled:ze,variant:"contained",children:"+ Add"}),e(qe,{onClick:()=>{if(gt(!1),ue[0].plant&&(pt({id:nt,field:"orgData",value:ue}),!C)){En(ue);const n=D==null?void 0:D.map(s=>(s==null?void 0:s.id)===nt?{...s,orgData:ue}:s);_(Tn(n))}const t=ue.filter(n=>{var s,o;return(o=(s=n.plant)==null?void 0:s.value)==null?void 0:o.code}).map(n=>{var s,o;return(o=(s=n.plant)==null?void 0:s.value)==null?void 0:o.code});t.length>0&&vl(t),C&&(kt(n=>{const s=n.findIndex(o=>o.id===U.id);return s!==-1?n.map((o,c)=>c===s?{...o,...U}:o):[...n,U]}),Fl(U,ue,i,Q))},variant:"contained",children:"Ok"})]})]}),q&&e(mr,{open:q,onClose:()=>k(!1),title:oo.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:Vt,lengthOfOrgRow:H,materialID:ye,orgRows:ue}),Gt&&e(Un,{openSnackBar:tn,alertMsg:Gt,alertType:en,handleSnackBarClose:()=>nn(!1)}),e(jr,{openSearchMat:an,materialOptions:dt,handleMatInputChange:Rl,inputState:Ue,setOpenSearchMat:$,dropDownData:Ze,AddCopiedMaterial:Cl}),e(ro,{})]})},Jn=({number:f,label:g})=>d("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:12,background:"#fff",borderRadius:8,boxShadow:"0 5px 15px rgba(0, 0, 0, 0.05)",transition:"transform 0.3s ease",animation:"fadeIn 0.5s ease-out forwards",minHeight:80},children:[e("div",{className:"stat-number",children:f}),e("div",{className:"stat-label",children:g})]}),zr=({data:f})=>{let g=0,A=Object.keys(f).length,_=0,y=0;const{t:Z}=Xt();Object.values(f).forEach(i=>{var F;const p=i.workflowDetails;g+=2,_+=(p.requestor_sla||0)+(p.mdmApprover_sla||0),y+=2,(F=p.workflowTaskDetailsByLevel)==null||F.forEach(w=>{var h;const x=(h=Object.values(w))==null?void 0:h[0];g+=x.length,y+=x.length,x.forEach(J=>{_+=J.taskSla||0})})});const E=y?(_/y).toFixed(1):0;return d("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(180px, 1fr))",gap:16,margin:"16px 0",padding:8,borderRadius:8},children:[e(Jn,{number:A,label:Z("Workflow Groups")}),e(Jn,{number:g,label:Z("Total Tasks")}),e(Jn,{number:E,label:Z("Avg SLA (Days)")})]})};var gs={},Xr=xt;Object.defineProperty(gs,"__esModule",{value:!0});var al=gs.default=void 0,Yr=Xr(St()),Jr=Et;al=gs.default=(0,Yr.default)((0,Jr.jsx)("path",{d:"M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2M1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2"}),"ShoppingCart");var ps={},Kr=xt;Object.defineProperty(ps,"__esModule",{value:!0});var Hn=ps.default=void 0,Qr=Kr(St()),Zr=Et;Hn=ps.default=(0,Qr.default)((0,Zr.jsx)("path",{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m2 14H7v-2h7zm3-4H7v-2h10zm0-4H7V7h10z"}),"Assignment");var ms={},ei=xt;Object.defineProperty(ms,"__esModule",{value:!0});var dl=ms.default=void 0,ti=ei(St()),ni=Et;dl=ms.default=(0,ti.default)((0,ni.jsx)("path",{d:"m3.5 18.49 6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"}),"ShowChart");var Ss={},si=xt;Object.defineProperty(Ss,"__esModule",{value:!0});var cl=Ss.default=void 0,li=si(St()),oi=Et;cl=Ss.default=(0,li.default)((0,oi.jsx)("path",{d:"M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m-9-2V7H4v3H1v2h3v3h2v-3h3v-2zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"}),"PersonAdd");var xs={},ri=xt;Object.defineProperty(xs,"__esModule",{value:!0});var ul=xs.default=void 0,ii=ri(St()),zs=Et;ul=xs.default=(0,ii.default)([(0,zs.jsx)("path",{d:"M14 7h-4c-1.1 0-2 .9-2 2v6h2v7h4v-7h2V9c0-1.1-.9-2-2-2"},"0"),(0,zs.jsx)("circle",{cx:"12",cy:"4",r:"2"},"1")],"Man");const ai=f=>f.includes("Storage")?e(So,{}):f.includes("Buyer")?e(al,{}):f.includes("Planner")?e(Hn,{}):f.includes("Regulatory")?e(rs,{}):f.includes("Product")?e(Lr,{}):f.includes("Plant")?e(dl,{}):f.includes("Data")?e(Ir,{}):f.includes("Requestor")?e(cl,{}):f.includes("Steward")?e(xo,{}):f.includes("Manager")?e(ul,{}):e(Hn,{}),di=({id:f,task:g,onClick:A})=>{const _="#1976d2";return d("div",{id:f,onClick:A,className:"task",style:{borderRadius:12,marginBottom:8,background:"#fff",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.08)",cursor:"pointer",transition:"transform 0.1s ease",maxWidth:230,padding:8,borderLeft:`4px solid ${_}`,position:"relative"},children:[e("div",{style:{position:"absolute",left:-12,top:12,width:16,height:16,borderRadius:"50%",background:"#fff",border:`3px solid ${_}`,boxSizing:"border-box",zIndex:2}}),d("div",{style:{display:"flex",alignItems:"start",gap:8,paddingTop:"16px",paddingBottom:"16px"},children:[e("div",{style:{color:"#1976d2",fontSize:24,display:"flex",alignItems:"center"},children:ai(g.name)}),e("div",{style:{fontWeight:600},children:g.name}),d("div",{style:{marginLeft:"auto",color:"#3498db",fontSize:14,background:"#e1f0fa",paddingLeft:"10px",paddingRight:"10px",borderRadius:"20px"},children:[g.sla,"d"]})]})]})},hl=({label:f,tasks:g,onTaskClick:A})=>d("div",{style:{display:"flex",flexDirection:"column",alignItems:"stretch",gap:0,padding:12},children:[e("div",{style:{marginBottom:8},children:e("span",{className:"level-label",style:{fontWeight:600,fontSize:16,display:"block",textAlign:"center",border:`${f=="Requestor"&&"2px solid #90EE90"}`},children:f})}),e("div",{style:{display:"flex",flexDirection:"column",gap:8,position:"relative"},children:g.map(_=>e(di,{id:_.id,task:_,onClick:()=>A(_)},_.id))})]});hl.propTypes={label:In.string.isRequired,tasks:In.arrayOf(In.object).isRequired,onTaskClick:In.func.isRequired};const ci=({containerRef:f,sourceTargets:g})=>{const A=l.useRef(null),[_,y]=l.useState({width:0,height:0});return l.useEffect(()=>{const Z=()=>{if(f.current){const E=f.current.getBoundingClientRect();y({width:E.width,height:E.height})}};return Z(),window.addEventListener("resize",Z),()=>window.removeEventListener("resize",Z)},[f]),l.useEffect(()=>{if(!f.current||!A.current)return;const Z=A.current;Z.innerHTML="";const E=document.createElementNS("http://www.w3.org/2000/svg","defs"),i=document.createElementNS("http://www.w3.org/2000/svg","marker");i.setAttribute("id","arrowhead"),i.setAttribute("markerWidth","10"),i.setAttribute("markerHeight","7"),i.setAttribute("refX","9"),i.setAttribute("refY","3.5"),i.setAttribute("orient","auto");const p=document.createElementNS("http://www.w3.org/2000/svg","polygon");p.setAttribute("points","0 0, 10 3.5, 0 7"),p.setAttribute("fill","#3498db"),i.appendChild(p),E.appendChild(i),Z.appendChild(E),g.forEach(({fromId:F,toId:w})=>{const x=document.getElementById(F),h=document.getElementById(w);if(!x||!h)return;const J=x.getBoundingClientRect(),te=h.getBoundingClientRect(),X=f.current.getBoundingClientRect(),he=J.right-X.left,be=J.top+J.height/2-X.top,xe=te.left-X.left,M=te.top+te.height/2-X.top,Ae=document.createElementNS("http://www.w3.org/2000/svg","path"),se=Math.max(50,Math.abs(xe-he)/2),u=`M ${he},${be} C ${he+se},${be} ${xe-se},${M} ${xe},${M}`;Ae.setAttribute("d",u),Ae.setAttribute("stroke","#bdc3c7"),Ae.setAttribute("stroke-width","2"),Ae.setAttribute("fill","none"),Ae.setAttribute("marker-end","url(#arrowhead)"),Z.appendChild(Ae)})},[f,g,_]),e("svg",{ref:A,width:_.width,height:_.height,style:{position:"absolute",top:5,left:1.5,pointerEvents:"none",zIndex:0}})},ui=({groupName:f,groupData:g,onTaskClick:A})=>{var p;const _=l.useRef(null),[y,Z]=l.useState([]),E=[],i=(F,w,x,h)=>({type:F,label:w,tasks:x.map((J,te)=>({...J,id:`${f}-level${h}-task${te}`}))});return E.push(i("requestor",g.requestorTaskLevelName,[{name:g.requestorTaskName,sla:g.requestor_sla,group:g.requestorTaskGroup,approver:"Requester",level:g.requestorTaskLevelName,status:"Pending"}],0)),(p=g==null?void 0:g.workflowTaskDetailsByLevel)==null||p.forEach((F,w)=>{const x=Object.keys(F)[0],h=F[x];h.length>0&&E.push(i("workflow",`Level ${h[0].workflowApprovalLevel}: ${h[0].workflowLevelName}`,h.map(J=>({name:J.workflowTaskName,sla:J.taskSla,group:J.workflowTaskGroup,approver:J.taskApprover,level:J.workflowLevelName,status:"In Progress"})),w+1))}),E.push(i("mdm",g.mdmTaskLevelName,[{name:g.mdmTaskName,sla:g.mdmApprover_sla,group:g.mdmTaskGroup,approver:g.mdmApprover_RecipientUsers,level:g.mdmTaskLevelName,status:"Not Started"}],E.length)),l.useEffect(()=>{const F=[];for(let w=0;w<E.length-1;w++){const x=E[w],h=E[w+1];x.tasks.forEach(J=>{h.tasks.length>0&&F.push({fromId:J.id,toId:h.tasks[0].id})})}Z(F)},[f]),d("div",{style:{display:"flex",flexDirection:"row",alignItems:"stretch",width:"100%",borderRadius:8,padding:16,minHeight:120,position:"relative",gap:"24px"},ref:_,children:[E==null?void 0:E.map((F,w)=>e(hl,{type:F.type,label:F.label,tasks:F.tasks,onTaskClick:A},w)),e(ci,{containerRef:_,sourceTargets:y})]})};var Es={},hi=xt;Object.defineProperty(Es,"__esModule",{value:!0});var fl=Es.default=void 0,fi=hi(St()),gi=Et;fl=Es.default=(0,fi.default)((0,gi.jsx)("path",{d:"M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"}),"KeyboardArrowDown");var Cs={},pi=xt;Object.defineProperty(Cs,"__esModule",{value:!0});var gl=Cs.default=void 0,mi=pi(St()),Si=Et;gl=Cs.default=(0,mi.default)((0,Si.jsx)("path",{d:"M7.41 15.41 12 10.83l4.59 4.58L18 14l-6-6-6 6z"}),"KeyboardArrowUp");const xi=({groupName:f,groupData:g,materialTypes:A,onTaskClick:_})=>{var i,p,F,w,x,h,J,te;const[y,Z]=l.useState(!1),E=()=>Z(!y);return l.useEffect(()=>{},[y]),d("div",{style:{border:"1px solid #e0e0e0",borderRadius:8,marginBottom:16,background:"#fff",boxShadow:"0 2px 8px rgba(0,0,0,0.04)",overflow:"hidden",transition:"box-shadow 0.2s"},children:[d("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",cursor:"pointer",backgroundImage:"linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",borderBottom:"1px solid #e0e0e0"},onClick:E,children:[d("div",{children:[d("div",{style:{fontWeight:600,fontSize:18,color:(p=(i=Re)==null?void 0:i.primary)==null?void 0:p.main},children:[f,` (${A.join(", ")})`]}),d("div",{style:{fontSize:14,color:(w=(F=Re)==null?void 0:F.primary)==null?void 0:w.main,marginTop:2},children:[g==null?void 0:g.mdmTaskGroup," • ",g==null?void 0:g.mdmTaskLevelName]})]}),e("div",{style:{fontSize:28,color:"#fff",transition:"transform 0.2s",display:"flex",alignItems:"center",justifyContent:"center"},children:y?e(gl,{style:{color:(h=(x=Re)==null?void 0:x.primary)==null?void 0:h.main,fontSize:32}}):e(fl,{style:{color:(te=(J=Re)==null?void 0:J.primary)==null?void 0:te.main,fontSize:32}})})]}),!y&&e("div",{style:{padding:16,background:"#fff"},children:e(ui,{groupName:f,groupData:g,onTaskClick:_})})]})},Ei=f=>{const g=new Date,A=new Date(g);return A.setDate(g.getDate()+f),A.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})},Ci=({task:f,onClose:g})=>e("div",{className:"modal",onClick:A=>A.target.classList.contains("modal")&&g(),children:d("div",{className:"modal-content",children:[d("div",{className:"modal-header",children:[d("div",{className:"modal-title",style:{display:"flex",alignItems:"center",gap:8},children:[e(Hn,{style:{color:"#fff"}}),e("span",{children:f.name})]}),e("div",{className:"modal-close",onClick:g,style:{cursor:"pointer"},children:e(cs,{style:{color:"#fff"}})})]}),d("div",{className:"modal-body",children:[d("div",{className:"task-detail",children:[e("div",{className:"detail-label",children:"Task Group:"}),e("div",{className:"detail-value",children:f.group})]}),d("div",{className:"task-detail",children:[e("div",{className:"detail-label",children:"Approver:"}),e("div",{className:"detail-value",children:f.approver})]}),d("div",{className:"task-detail",children:[e("div",{className:"detail-label",children:"Level:"}),e("div",{className:"detail-value",children:f.level})]}),d("div",{className:"task-detail",children:[e("div",{className:"detail-label",children:"SLA:"}),e("div",{className:"detail-value",children:d("span",{className:"sla-badge sla-normal",children:[f.sla," days"]})})]}),d("div",{className:"task-detail",children:[e("div",{className:"detail-label",children:"Due Date:"}),e("div",{className:"detail-value",children:Ei(f.sla)})]}),d("div",{className:"task-detail",children:[e("div",{className:"detail-label",children:"Status:"}),e("div",{className:"detail-value",children:f.status})]})]})]})}),Ti=({data:f})=>{var E;const[g,A]=l.useState(null),{t:_}=Xt(),y=i=>{A(i)},Z=()=>{A(null)};return d("div",{children:[e(Me,{align:"left",variant:"h4",component:"h2",gutterBottom:!0,children:_("Workflow Details")}),e(zr,{data:f}),e("div",{className:"workflow-container",children:(E=Object==null?void 0:Object.entries(f))==null?void 0:E.map(([i,p])=>e(xi,{groupName:i,groupData:p==null?void 0:p.workflowDetails,materialTypes:p==null?void 0:p.materialTypes,onTaskClick:y},i))}),g&&e(Ci,{task:g,onClose:Z})]})};function bi({label:f,checked:g=!1,onChange:A,id:_}){return e(es,{control:e(ns,{id:_,checked:g,onChange:A,sx:{color:"primary.main","&.Mui-checked":{color:"primary.main"}}}),label:f,sx:{ml:0,".MuiFormControlLabel-label":{fontSize:14,color:"#4B5563",cursor:"pointer"}}})}const Ai=({initialReqScreen:f,isreqBench:g})=>{const A=mn(),y=new URLSearchParams(A.search).get("RequestId"),Z=Y(se=>se.payload),E=Y(se=>se.payload.payloadData),[i,p]=l.useState(!1),[F,w]=l.useState(),[x,h]=l.useState("success"),[J,te]=l.useState(!1),{t:X}=Xt(),{customError:he}=On(),{createPayloadFromReduxState:be}=ll({initialReqScreen:f,isReqBench:g}),{changePayloadForTemplate:xe}=ol(E==null?void 0:E.TemplateName),M=()=>{p(!1)},Ae=()=>{var K,T,le,Q,ce,de,B,D,V,ge,He,Le,Ht,Ct,It,Ie,dt;const se=(E==null?void 0:E.RequestType)===((K=L)==null?void 0:K.CHANGE)||(E==null?void 0:E.RequestType)===((T=L)==null?void 0:T.CHANGE_WITH_UPLOAD)?xe(!!y):be(Z),u={materialDetails:se,dtName:zn((Q=(le=se[0])==null?void 0:le.Torequestheaderdata)==null?void 0:Q.RequestType).dtName,version:zn((de=(ce=se[0])==null?void 0:ce.Torequestheaderdata)==null?void 0:de.RequestType).version,requestId:((D=(B=se[0])==null?void 0:B.Torequestheaderdata)==null?void 0:D.RequestId)||"",scenario:(He=zn((ge=(V=se[0])==null?void 0:V.Torequestheaderdata)==null?void 0:ge.RequestType))==null?void 0:He.scenario,templateName:(E==null?void 0:E.RequestType)===((Le=L)==null?void 0:Le.CHANGE)||(E==null?void 0:E.RequestType)===((Ht=L)==null?void 0:Ht.CHANGE_WITH_UPLOAD)?(It=(Ct=se[0])==null?void 0:Ct.Torequestheaderdata)==null?void 0:It.TemplateName:"",matlType:"ALL",region:((dt=(Ie=se[0])==null?void 0:Ie.Torequestheaderdata)==null?void 0:dt.Region)||""},P=z=>{if((z==null?void 0:z.size)>0){const pe=URL.createObjectURL(z),r=document.createElement("a");r.href=pe,r.setAttribute("download",`Material_Preview_${new Date().getTime()}.xlsx`),document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(pe)}},ne=z=>{he(z),w(z==null?void 0:z.message),h("error"),p(!0)};Te(`/${Ce}${at.EXCEL.EXPORT_PREVIEW_EXCEL}`,"postandgetblob",P,ne,u)};return d(it,{item:!0,md:12,sx:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #E0E0E0",boxShadow:"0px 1px 4px rgba(0, 0, 0, 0.1)",p:"10px"},children:[e(Me,{sx:{fontWeight:"bold",mb:"6px"},children:X("Master data details")}),e(I,{sx:{backgroundColor:"#FAFAFA",borderRadius:"8px",boxShadow:"none"},children:d(I,{sx:{padding:"8px"},children:[e(Me,{align:"left",variant:"h6",component:"h2",children:X("Please download the excel sheet to view all the material data.")}),d(I,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",gap:1,mt:2},children:[e(qe,{variant:"contained",startIcon:e(il,{sx:{fontSize:28,animation:"downloadBounce 2s ease-in-out infinite",filter:"drop-shadow(0 2px 4px rgba(255,255,255,0.3))"}}),onClick:Ae,sx:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:"-100%",width:"100%",height:"100%",background:"linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)",transition:"left 0.5s"},"&:hover::before":{left:"100%"},"&:hover":{background:"linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)",transform:"translateY(-3px) scale(1.02)",boxShadow:"0 12px 25px rgba(102, 126, 234, 0.4), 0 0 20px rgba(118, 75, 162, 0.3)"},"&:active":{transform:"translateY(-1px) scale(0.98)",boxShadow:"0 6px 15px rgba(102, 126, 234, 0.3)"},transition:"all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)",borderRadius:3,py:1.8,px:3,textTransform:"none",fontSize:"1.1rem",fontWeight:600,boxShadow:"0 8px 20px rgba(102, 126, 234, 0.3), 0 0 15px rgba(118, 75, 162, 0.2)",display:"flex",alignItems:"center",gap:1.5,border:"1px solid rgba(255, 255, 255, 0.1)",backdropFilter:"blur(10px)",color:"#ffffff",letterSpacing:"0.5px",minWidth:"180px","@keyframes downloadBounce":{"0%, 100%":{transform:"translateY(0) rotate(0deg)",filter:"drop-shadow(0 2px 4px rgba(255,255,255,0.3))"},"25%":{transform:"translateY(-3px) rotate(-2deg)",filter:"drop-shadow(0 4px 8px rgba(255,255,255,0.4))"},"50%":{transform:"translateY(-6px) rotate(0deg)",filter:"drop-shadow(0 6px 12px rgba(255,255,255,0.5))"},"75%":{transform:"translateY(-3px) rotate(2deg)",filter:"drop-shadow(0 4px 8px rgba(255,255,255,0.4))"}},"&::after":{content:'""',position:"absolute",top:"50%",left:"50%",width:"0",height:"0",background:"radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)",borderRadius:"50%",transform:"translate(-50%, -50%)",transition:"width 0.6s, height 0.6s",pointerEvents:"none"},"&:active::after":{width:"300px",height:"300px"}},children:d(I,{sx:{display:"flex",alignItems:"center",gap:1,position:"relative",zIndex:1},children:[X("Download Excel"),e(I,{component:"span",sx:{fontSize:"0.8rem",opacity:.8,fontWeight:400,ml:.5},children:"(.xlsx)"})]})}),e(bi,{label:X("I have reviewed all material details."),checked:J,onChange:()=>te(!J)})]})]})}),e(Un,{openSnackBar:i,alertMsg:F,alertType:x,handleSnackBarClose:M})]})},Ni=()=>{const{t:f}=Xt();return d(I,{sx:{p:3,maxWidth:1400,mx:"auto"},children:[e(Me,{variant:"h6",sx:{mb:3,color:"#666"},children:f("Workflow Details")}),d(I,{sx:{display:"flex",gap:6,mb:4,justifyContent:"flex-start",maxWidth:600},children:[d(I,{sx:{textAlign:"center"},children:[e(ie,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),e(ie,{variant:"text",width:100,height:20,sx:{mt:1,mx:"auto"}})]}),d(I,{sx:{textAlign:"center"},children:[e(ie,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),e(ie,{variant:"text",width:80,height:20,sx:{mt:1,mx:"auto"}})]}),d(I,{sx:{textAlign:"center"},children:[e(ie,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),e(ie,{variant:"text",width:120,height:20,sx:{mt:1,mx:"auto"}})]})]}),d(Eo,{expanded:!0,sx:{bgcolor:"#37474f",color:"white","&:before":{display:"none"},borderRadius:1,overflow:"hidden"},children:[e(Co,{sx:{bgcolor:"#37474f","& .MuiAccordionSummary-content":{alignItems:"center"}},children:d(I,{children:[e(ie,{variant:"text",width:180,height:24,sx:{bgcolor:"rgba(255,255,255,0.2)"}}),e(ie,{variant:"text",width:220,height:16,sx:{bgcolor:"rgba(255,255,255,0.1)",mt:.5}})]})}),d(To,{sx:{bgcolor:"#f5f5f5",p:0},children:[e(I,{sx:{display:"grid",gridTemplateColumns:"repeat(6, 1fr)",gap:2,bgcolor:"#fff",borderBottom:"1px solid #e0e0e0",p:2},children:["Requestor","Level 1: Data Entry","Level 2: Additional Master Data","Level 3: Cost","Level 4: Record Approval","Final Creation"].map((g,A)=>e(I,{sx:{textAlign:"center"},children:e(ie,{variant:"text",width:"80%",height:20,sx:{mx:"auto"}})},A))}),e(I,{sx:{p:3,bgcolor:"#fafafa"},children:d(I,{sx:{display:"grid",gridTemplateColumns:"repeat(6, 1fr)",gap:2,alignItems:"flex-start"},children:[e(I,{sx:{display:"flex",flexDirection:"column",gap:2},children:[1,2,3,4,5,6].map((g,A)=>d(I,{sx:{position:"relative"},children:[e(un,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:e(hn,{sx:{p:2},children:d(I,{sx:{display:"flex",alignItems:"center",gap:1},children:[e(ie,{variant:"circular",width:24,height:24}),d(I,{sx:{flex:1},children:[e(ie,{variant:"text",width:"70%",height:16}),e(ie,{variant:"text",width:"40%",height:12,sx:{mt:.5}})]}),e(ie,{variant:"text",width:30,height:20})]})})}),A<5&&e(I,{sx:{position:"absolute",right:-8,top:"50%",width:16,height:2,bgcolor:"#2196f3",zIndex:1,transform:"translateY(-50%)"}})]},A))}),e(I,{sx:{display:"flex",flexDirection:"column",gap:2},children:e(un,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:e(hn,{sx:{p:2},children:d(I,{sx:{display:"flex",alignItems:"center",gap:1},children:[e(ie,{variant:"circular",width:24,height:24}),d(I,{sx:{flex:1},children:[e(ie,{variant:"text",width:"80%",height:16}),e(ie,{variant:"text",width:"50%",height:12,sx:{mt:.5}})]}),e(ie,{variant:"text",width:30,height:20})]})})})}),e(I,{sx:{display:"flex",flexDirection:"column",gap:2},children:[1,2].map((g,A)=>e(un,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:e(hn,{sx:{p:2},children:d(I,{sx:{display:"flex",alignItems:"center",gap:1},children:[e(ie,{variant:"circular",width:24,height:24}),d(I,{sx:{flex:1},children:[e(ie,{variant:"text",width:"75%",height:16}),e(ie,{variant:"text",width:"45%",height:12,sx:{mt:.5}})]}),e(ie,{variant:"text",width:30,height:20})]})})},A))}),e(I,{sx:{display:"flex",flexDirection:"column",gap:2},children:e(un,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:e(hn,{sx:{p:2},children:d(I,{sx:{display:"flex",alignItems:"center",gap:1},children:[e(ie,{variant:"circular",width:24,height:24}),d(I,{sx:{flex:1},children:[e(ie,{variant:"text",width:"85%",height:16}),e(ie,{variant:"text",width:"35%",height:12,sx:{mt:.5}})]}),e(ie,{variant:"text",width:30,height:20})]})})})}),e(I,{sx:{display:"flex",flexDirection:"column",gap:2},children:e(un,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:e(hn,{sx:{p:2},children:d(I,{sx:{display:"flex",alignItems:"center",gap:1},children:[e(ie,{variant:"circular",width:24,height:24}),d(I,{sx:{flex:1},children:[e(ie,{variant:"text",width:"90%",height:16}),e(ie,{variant:"text",width:"55%",height:12,sx:{mt:.5}})]}),e(ie,{variant:"text",width:30,height:20})]})})})}),e(I,{sx:{display:"flex",flexDirection:"column",gap:2},children:e(un,{sx:{bgcolor:"#f5f5f5",minHeight:60,border:"2px solid #ccc",borderRadius:2},children:e(hn,{sx:{p:2},children:d(I,{sx:{display:"flex",alignItems:"center",gap:1},children:[e(ie,{variant:"circular",width:24,height:24,sx:{bgcolor:"#bdbdbd"}}),d(I,{sx:{flex:1},children:[e(ie,{variant:"text",width:"75%",height:16,sx:{bgcolor:"#bdbdbd"}}),e(ie,{variant:"text",width:"40%",height:12,sx:{mt:.5,bgcolor:"#bdbdbd"}})]}),e(ie,{variant:"text",width:30,height:20,sx:{bgcolor:"#bdbdbd"}})]})})})})]})})]})]})]})},vi=f=>{var de;const g=Y(B=>B.payload),{customError:A}=On(),_=mn(),y=new URLSearchParams(_.search),Z=Y(B=>B.request.materialRows),E=Y(B=>B.userManagement.taskData),i=Y(B=>B.applicationConfig),p=y.get("reqBench"),F=y.get("RequestId"),w=Y(B=>B.request.requestHeader),{showSnackbar:x}=bo(),[h,J]=l.useState(null),[te,X]=l.useState(!1),he=!(E!=null&&E.taskId)&&!p,be=Y(B=>B.tabsData.requestHeaderData),{t:xe}=Xt(),M=Y(B=>B.payload.payloadData),{createPayloadFromReduxState:Ae}=ll({initialReqScreen:he,isReqBench:p}),{changePayloadForTemplate:se}=ol(M==null?void 0:M.TemplateName),u=Y(B=>B.payload.filteredButtons),{filteredButtons:P}=xr(E,i,vn,ls),{extendFilteredButtons:ne}=sl(E,i,vn,ls),K=M==null?void 0:M.RequestStatus,T=K===Ye.DRAFT||K===Ye.DRAFT_IN_CAPS||K===Ye.VALIDATED_REQUESTOR||K===Ye.VALIDATION_FAILED_REQUESTOR||K===Ye.UPLOAD_SUCCESSFUL;let le;const Q=[qt.HANDLE_SEND_BACK,qt.HANDLE_VALIDATE,qt.HANDLE_CORRECTION];(M==null?void 0:M.RequestType)===L.CREATE||(M==null?void 0:M.RequestType)===L.CREATE_WITH_UPLOAD?le=kn(P,[...Q,qt.HANDLE_VALIDATE1]):(M==null?void 0:M.RequestType)===L.EXTEND||(M==null?void 0:M.RequestType)===L.EXTEND_WITH_UPLOAD?le=kn(ne,Q):(M==null?void 0:M.RequestType)===L.CHANGE||(M==null?void 0:M.RequestType)===L.CHANGE_WITH_UPLOAD?le=kn(u,Q):le=[];const ce=!pn.includes(f==null?void 0:f.requestStatus)||F&&!p;return l.useEffect(()=>{var B,D;if(T){const V=(M==null?void 0:M.RequestType)===((B=L)==null?void 0:B.CHANGE)||(M==null?void 0:M.RequestType)===((D=L)==null?void 0:D.CHANGE_WITH_UPLOAD)?se(!!F):Ae(g);X(!0);const ge=Le=>{Le.statusCode===Xe.STATUS_200?(J(Le==null?void 0:Le.body),X(!1)):(x(Le==null?void 0:Le.message,"error"),X(!1))},He=Le=>{A(Le),X(!1)};Te(`/${Ce}${at.MASS_ACTION.WORKFLOW_DETAILS_BIFURCATION}`,"post",ge,He,V)}},[]),d(Je,{children:[d(_n,{spacing:2,children:[Object.entries(be).map(([B,D])=>d(it,{item:!0,md:12,sx:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #E0E0E0",boxShadow:"0px 1px 4px rgba(0, 0, 0, 0.1)",...Ao,pt:"10px"},children:[e(Me,{sx:{fontWeight:"bold",mb:"6px"},children:xe("Request Details")}),e(I,{sx:{backgroundColor:"#FAFAFA",padding:"10px",pl:"0px",pr:"0px",borderRadius:"8px",boxShadow:"none"},children:e(it,{container:!0,spacing:2,children:D.filter(V=>V.visibility!=="Hidden").sort((V,ge)=>V.sequenceNo-ge.sequenceNo).map(V=>{let ge=(w==null?void 0:w[V==null?void 0:V.jsonName])||(M==null?void 0:M[V==null?void 0:V.jsonName])||"",He="";return Array.isArray(ge)?He=ge.join(", "):ge instanceof Date||typeof ge=="object"&&ge instanceof Object&&ge.toString().includes("GMT")?He=new Date(ge).toLocaleString():He=ge,ge=He,ge&&ge!==null&&ge!==""&&e(it,{item:!0,md:3,children:d("div",{style:{padding:"12px",backgroundColor:"#ffffff",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",transition:"all 0.3s ease"},children:[e(zt,{title:xe(V==null?void 0:V.fieldName)||"Field Name",children:d(Me,{variant:"body1",sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},children:[xe(V==null?void 0:V.fieldName)||"Field Name",((V==null?void 0:V.visibility)==="Required"||(V==null?void 0:V.visibility)==="MANDATORY")&&e("span",{style:{color:"#d32f2f",marginLeft:"2px"},children:"*"})]})}),e(zt,{title:ge||"--",children:e("div",{style:{fontSize:"0.8rem",color:"#333333",marginTop:"4px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},children:e("span",{style:{fontWeight:500,color:"grey",letterSpacing:"0.5px",wordSpacing:"1px"},children:ge||"--"})})})]})},V==null?void 0:V.id)})})})]},B)),e(Ai,{initialReqScreen:he,isreqBench:p}),T&&e(Je,{children:h&&!te?e(Ti,{data:h}):e(Ni,{})})]}),(!ce||F&&!p||p&&pn.includes(f==null?void 0:f.requestStatus))&&e(I,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:e(us,{activeTab:Er.PREVIEW,submitForApprovalDisabled:!No(Z),filteredButtons:le,childRequestHeaderData:(de=g==null?void 0:g[M==null?void 0:M.selectedMaterialID])==null?void 0:de.Tochildrequestheaderdata})})]})},_i={AdditionalData:{fieldName:["Material","AltUnit","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Alternative Unit of Measure","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},BasicData:{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},MRPData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},PurchasingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},SalesData:{fieldName:["Material","SalesOrg","DistrChan","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Sales Org","Distribution Channel","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},SalesPlantData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},SalesGeneralData:{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},PurchasingGeneralData:{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},WarehouseData:{fieldName:["Material","WhseNo","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Warehouse","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},DescriptionData:{fieldName:["Material","Langu","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Language","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},AdditionalEANData:{fieldName:["Material","AltUnit","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Alternative Unit of Measure","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},CostingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},AccountingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},WorkSchedulingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},TaxClassificationData:{fieldName:["Material","Country","TaxType","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Country","Tax Type","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},Oi={FinanceCostData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},Ri=({open:f,closeModal:g,requestId:A,requestType:_})=>{const{customError:y}=On(),[Z,E]=l.useState(!0),[i,p]=l.useState(null),F=mn(),x=new URLSearchParams(F.search.split("?")[1]).get("RequestId"),[h,J]=l.useState(()=>{var P;const u=x!=null&&x.includes("FCA")?Oi:_i;return(P=Object.entries(u))==null?void 0:P.map(([ne])=>({label:ne,columns:u[ne],rows:[]}))}),[te,X]=l.useState(()=>(h==null?void 0:h.length)>0?{number:0,label:h[0].label}:{number:0,label:""}),he=(u,P)=>{X({number:P,label:h[P].label})},be={position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"80%",height:"auto",bgcolor:"#fff",boxShadow:4,p:2},xe=()=>{g(!1)};l.useEffect(()=>{(async()=>{if(f&&!i)try{const P=await M(A,_);p(P)}catch(P){y(Qn.FETCH_CHANGELOG_ERROR,P)}})()},[f,A]),l.useEffect(()=>{if(i)try{J(u=>u==null?void 0:u.map(P=>{const ne=vo(_o,P.label),K=i[P.label]||[];return{...P,rows:K==null?void 0:K.map(T=>({id:Oo(),...T,Material:Xn(T==null?void 0:T.ObjectNo,1),SAPValue:Dn(T==null?void 0:T.SAPValue),PreviousValue:Dn(T==null?void 0:T.PreviousValue),CurrentValue:Dn(T==null?void 0:T.CurrentValue),ChangedOn:Dn(T==null?void 0:T.ChangedOn),...(ne==null?void 0:ne.length)>0&&{[ne[0]]:Xn(T==null?void 0:T.ObjectNo,2)},...(ne==null?void 0:ne.length)>1&&{[ne[1]]:Xn(T==null?void 0:T.ObjectNo,3)}}))}}))}catch(u){y(Qn.CHANGE_LOG_MESSAGE,u)}},[i]);const M=u=>{var K;E(!0);const P=`/${Ce}/${(K=at)==null?void 0:K.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA}`;let ne={ChildRequestId:u};return new Promise((T,le)=>{Te(P,"post",de=>{var B;if((de==null?void 0:de.statusCode)===Xe.STATUS_200&&((B=de==null?void 0:de.body)==null?void 0:B.length)>0){const D=Do(de==null?void 0:de.body);E(!1),T(D)}else E(!1),T([])},de=>{E(!1),y(de),le(de)},ne)})},Ae=new Date,se={convertJsonToExcel:()=>{const u=h==null?void 0:h.map(P=>{var K;const ne=(K=P==null?void 0:P.columns)==null?void 0:K.fieldName.map((T,le)=>({header:P==null?void 0:P.columns.headerName[le],key:T}));return{sheetName:P==null?void 0:P.label,fileName:x!=null&&x.includes("FCA")?`Finance Costing Changelog Data-${os(Ae).format("DD-MMM-YYYY")}`:`Create Changelog Data-${os(Ae).format("DD-MMM-YYYY")}`,columns:ne,rows:P==null?void 0:P.rows}});Io(u)},button:()=>e(qe,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>se.convertJsonToExcel(),children:"Download"})};return d(Je,{children:[Z&&e(gn,{blurLoading:Z,loaderMessage:ds.CHANGELOG_LOADING}),e(Lo,{open:f,onClose:xe,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:d(I,{sx:be,children:[e(_n,{children:d(it,{item:!0,md:12,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[d(I,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[e(Ro,{sx:{color:Re.black.dark,fontSize:"20px","&:hover":{transform:"rotate(360deg)",transition:"0.9s"},textAlign:"center",marginTop:"4px"}}),e(Me,{id:"modal-modal-title",variant:"subtitle1",fontSize:"16px",fontWeight:"bold",sx:{color:Re.black.dark},children:"Change Log"})]}),d(I,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[e(zt,{title:"Export Table",children:e(Lt,{sx:yo,onClick:se.convertJsonToExcel,children:e(Js,{iconName:"IosShare"})})}),e(Lt,{sx:{padding:"0 0 0 5px"},onClick:xe,children:e(cs,{})})]})]})}),e(as,{value:te==null?void 0:te.number,onChange:he,variant:"scrollable",scrollButtons:"auto","aria-label":"modal tabs",children:h==null?void 0:h.map((u,P)=>e(bn,{label:u.label.replace(Mo.ADDING_SPACE," $1").trim()},P))}),e("div",{className:"tab-content",style:{position:"relative",height:"100%",marginTop:16},children:h==null?void 0:h.map((u,P)=>{var ne,K,T,le;return(te==null?void 0:te.number)===P&&e(Me,{id:`modal-tab-content-${P}`,sx:{mt:1},children:e(it,{item:!0,sx:{position:"relative"},children:e(_n,{children:e(tl,{rows:u==null?void 0:u.rows,columns:(K=(ne=u==null?void 0:u.columns)==null?void 0:ne.fieldName)==null?void 0:K.map((Q,ce)=>{var de;return{field:Q,headerName:(de=u==null?void 0:u.columns)==null?void 0:de.headerName[ce],flex:1,minWidth:100}}),getRowIdValue:"id",pageSize:(le=(T=u==null?void 0:u.columns)==null?void 0:T.fieldName)==null?void 0:le.length,autoHeight:!0,scrollbarSize:10,sx:{"& .MuiDataGrid-row:hover":{backgroundColor:`${Re.primary.light}40`},backgroundColor:Re.primary.white}})})})},P)})})]})})]})};var Ts={},yi=xt;Object.defineProperty(Ts,"__esModule",{value:!0});var pl=Ts.default=void 0,Mi=yi(St()),Li=Et;pl=Ts.default=(0,Mi.default)((0,Li.jsx)("path",{d:"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M17 13l-5 5-5-5h3V9h4v4z"}),"CloudDownload");const Ii=({handleDownload:f,setEnableDocumentUpload:g,enableDocumentUpload:A,handleUploadMaterial:_})=>{const{t:y}=Xt();return d(it,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"12px",border:"1px solid #E0E0E0",mt:1,boxShadow:"0px 8px 24px rgba(48, 38, 185, 0.12)",padding:"1.5rem 2rem"},children:[e(it,{container:!0,alignItems:"center",sx:{mb:2},children:e(Me,{sx:{fontSize:"12px",fontWeight:"700"},children:y("Excel Operations")})}),d(it,{container:!0,spacing:2,sx:{display:"flex",justifyContent:"center",position:"relative"},children:[e(it,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center"},children:d(ss,{elevation:0,onClick:f,sx:{width:"100%",maxWidth:"280px",height:"200px",borderRadius:"16px",padding:"1.5rem",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",position:"relative",overflow:"hidden",cursor:"pointer",transition:"all 0.3s ease","&:hover":{backgroundColor:"rgba(63, 140, 218, 0.1)"}},children:[e(I,{sx:{backgroundColor:"rgba(63, 140, 218, 0.2)",borderRadius:"50%",padding:"16px",marginBottom:"16px"},children:e(pl,{sx:{fontSize:64,color:"#1976d2",filter:"drop-shadow(0px 4px 6px rgba(7, 31, 54, 0.3))"}})}),e(Me,{sx:{fontSize:"16px",fontWeight:"600",color:"#0D47A1",mb:1},children:y("Download Excel")}),e(Me,{sx:{fontSize:"12px",color:"#1565C0",textAlign:"center"},children:y("Download Excel if you have not downloaded yet")})]})}),e(wo,{orientation:"vertical",flexItem:!0,sx:{position:"absolute",height:"80%",top:"10%",left:"50%",display:{xs:"none",md:"block"}}}),e(it,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center"},children:d(ss,{elevation:0,onClick:()=>g(!0),sx:{width:"100%",maxWidth:"280px",height:"200px",borderRadius:"16px",padding:"1.5rem",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",position:"relative",overflow:"hidden",cursor:"pointer",transition:"all 0.3s ease","&:hover":{backgroundColor:"rgba(55, 43, 224, 0.1)"}},children:[e(I,{sx:{backgroundColor:"rgba(55, 43, 224, 0.2)",borderRadius:"50%",padding:"16px",marginBottom:"16px"},children:e(ko,{sx:{fontSize:64,color:"#3026B9",filter:"drop-shadow(0px 4px 6px rgba(58, 48, 199, 0.4))"}})}),e(Me,{sx:{fontSize:"16px",fontWeight:"600",color:"#3026B9",mb:1},children:y("Upload Excel")}),e(Me,{sx:{fontSize:"12px",color:"#5148C2",textAlign:"center"},children:y("Upload your Excel after doing the necessary changes")})]})})]}),A&&e(wr,{artifactId:"",artifactName:"",setOpen:g,handleUpload:_})]})},Di=f=>{var se;const g=Fn(),[A,_]=l.useState(!1),y=Y(u=>u.payload.fcRows),Z=Y(u=>u.payload.unselectedRows),E=Y(u=>u.paginationData),i=Y(u=>u.payload.filteredButtons),p=Y(u=>u.userManagement.taskData),F=Y(u=>u.payload.selectedRows),w=Y(u=>u.payload.payloadData),x=(w==null?void 0:w.RequestType)||"",{getButtonsDisplay:h}=Cr(),{getNextDisplayDataForCreate:J}=nl(),te=mn(),he=new URLSearchParams(te.search).get("reqBench");let be=te.state;l.useEffect(()=>{p!=null&&p.ATTRIBUTE_1&&h()},[p]),l.useEffect(()=>{var u;if((y==null?void 0:y.length)>0&&!(he&&!((u=pn)!=null&&u.includes(be==null?void 0:be.reqStatus)))){const P=new Set(Z.map(K=>K.id)),ne=y.filter(K=>!P.has(K.id)).map(K=>K.id);g(Pn(ne))}return()=>{g(Pn([])),g(Fs([]))}},[y,g]);const xe=u=>{g(Pn(u));const P=y==null?void 0:y.filter(ne=>!u.includes(ne.id));g(Fs(P))},M=[{field:"FinanceCostingId",headerName:"ID",flex:1,hide:!0},{field:"RequestId",headerName:"Req ID",flex:1.7,editable:!1},{field:"RequestType",headerName:"Req Type",flex:1.2,editable:!1},{field:"Requester",headerName:"Requestor",flex:1.6,editable:!1},{field:"CreatedOn",headerName:"Created On(SAP)",flex:1.3,editable:!1,valueFormatter:u=>u.value?os(u.value).format("DD MMM YYYY"):""},{field:"Material",headerName:"Material Number",flex:1.3,editable:!1},{field:"MatlType",headerName:"Material Type",flex:1,editable:!1},{field:"Plant",headerName:"Plant",flex:1,editable:!1},{field:"FStdPrice",headerName:"Standard Price",flex:1,editable:!1},{field:"IntlPoPrice",headerName:"Initial PO Price",flex:1,editable:!1,valueFormatter:u=>u.value?Number(u.value).toFixed(2):""},{field:"PryVendor",headerName:"Primary Vendor",flex:1.3,editable:!1},{field:"FlagForBOM",headerName:"Flag For BOM",flex:1,editable:!1},{field:"VolInEA",headerName:"Volume EA",flex:1,editable:!1,valueFormatter:u=>u.value?Number(u.value).toFixed(2):""},{field:"VolInCA",headerName:"Volume CA",flex:1,editable:!1,valueFormatter:u=>u.value?Number(u.value).toFixed(2):""},{field:"VolInCAR",headerName:"Volume Carton",flex:1,editable:!1,valueFormatter:u=>u.value?Number(u.value).toFixed(2):""},{field:"NoOfUnitForCA",headerName:"Number Of Unit For CA",flex:1,editable:!1,valueFormatter:u=>u.value?Number(u.value).toFixed(0):""},{field:"NoOfUnitForCT",headerName:"Number Of Unit For CT",flex:1,editable:!1,valueFormatter:u=>u.value?Number(u.value).toFixed(0):""}],Ae=u=>{g(el(u))};return l.useEffect(()=>{var u;(E==null?void 0:E.page)!==0&&x===((u=L)==null?void 0:u.FINANCE_COSTING)&&J()},[E==null?void 0:E.page]),d(Je,{children:[e(tl,{isLoading:A,module:"FinanceCosting",width:"100%",title:"Finance Costing Details",rows:y,columns:M,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!(he&&!((se=pn)!=null&&se.includes(be==null?void 0:be.reqStatus))),disableSelectionOnClick:!0,tempheight:"calc(100vh - 300px)",selectionModel:F,onRowsSelectionHandler:xe,rowCount:(E==null?void 0:E.totalElements)||0,pageSize:100,onPageChange:u=>Ae(u)}),e(us,{filteredButtons:i,setCompleted:f==null?void 0:f.setCompleted})]})},oa=()=>{var kt,U,Qe,Qt,Zt,We,Ot,en,Bt,Vt,tn,nn,Rt,xn,Gt,sn,ln,on,rn,an;const[f,g]=l.useState(!1),[A,_]=l.useState([]),[y,Z]=l.useState(!1),[E,i]=l.useState([]),[p,F]=l.useState(!1),[w,x]=l.useState(!1),[h,J]=l.useState(""),[te,X]=l.useState(!1),[he,be]=l.useState([]),[xe,M]=l.useState(!1),[Ae,se]=l.useState(!1),[u,P]=l.useState(""),[ne,K]=l.useState(),[T,le]=l.useState(""),[Q,ce]=l.useState(!1),[de,B]=l.useState(""),[D,V]=l.useState("success"),[ge,He]=l.useState(!1),[Le,Ht]=l.useState(!1),[Ct,It]=l.useState(!1),[Ie,dt]=l.useState(!1),z=Fn(),pe=Y($=>$.applicationConfig),r=Y($=>$.payload.payloadData),Ue=Y($=>{var C;return(C=$.request.requestHeader)==null?void 0:C.requestId}),Dt=Y($=>$.request.requestHeader.requestType),De=Y($=>{var C;return(C=$.userManagement)==null?void 0:C.taskData}),{getDtCall:Tt,dtData:bt}=Po(),$e=Xs(),[Yt,Jt]=l.useState(!0),ct=Y($=>$.request.tabValue),{t:ve}=Xt(),{getRequestHeaderTemplate:ut}=Tr(),je=[ve("Request Header"),ve("Material List"),ve("Attachments & Remarks"),ve("Preview")],[At,Nt]=l.useState([!1]),Ft=$=>{z(wn($))},Ze=mn(),O=Ze.state,et=((kt=Ze.state)==null?void 0:kt.isChildRequest)??!1,we=new URLSearchParams(Ze.search.split("?")[1]).get("RequestId"),ht=new URLSearchParams(Ze.search),oe=ht.get("RequestId"),_e=ht.get("RequestType"),wt=ht.get("reqBench"),{getDisplayData:Ut}=kr(),nt=()=>{M(!0)},Ke=()=>{M(!1)},ft=()=>{se(!0)},vt=$=>{se($)},_t=()=>{T==="success"?$e("/requestBench"):Ke()},gt=()=>{Z(!0)},ye=$=>{let C="";_e===L.CREATE_WITH_UPLOAD?C="getAllMaterialsFromExcel":_e===L.EXTEND_WITH_UPLOAD?C="getAllMaterialsFromExcelForMassExtend":C="getAllMaterialsFromExcelForMassChange",B("Initiating Excel Upload"),ce(!0);const me=new FormData;[...$].forEach(Se=>me.append("files",Se)),me.append("dtName",_e===L.CREATE_WITH_UPLOAD||_e===L.EXTEND_WITH_UPLOAD?"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG":"MDG_MAT_CHANGE_TEMPLATE"),me.append("version",_e===L.CREATE_WITH_UPLOAD||_e===L.EXTEND_WITH_UPLOAD?"v1":"v5"),me.append("requestId",we?we.slice(3):""),me.append("region",r!=null&&r.Region?r==null?void 0:r.Region:"US"),me.append("matlType","ALL");const Oe=Se=>{var a;(Se==null?void 0:Se.statusCode)===Xe.STATUS_200?(X(!1),ce(!1),B(""),$e((a=Cn)==null?void 0:a.REQUEST_BENCH)):(X(!1),ce(!1),K(Se==null?void 0:Se.message),B(""),V("error"),lt())},tt=Se=>{ce(!1),K(Se==null?void 0:Se.message),B(""),V("error"),lt()};Te(`/${Ce}/massAction/${C}`,"postformdata",Oe,tt,me)};l.useEffect(()=>((async()=>{var C;if(oe){const me=dr(Yn.CURRENT_TASK,!0,{}),Oe=_e||(De==null?void 0:De.ATTRIBUTE_2)||(me==null?void 0:me.ATTRIBUTE_2);await Ut(oe,Oe,wt,De,O),(_e===L.CHANGE_WITH_UPLOAD&&!((C=O==null?void 0:O.material)!=null&&C.length)||_e===L.CREATE_WITH_UPLOAD||_e===L.EXTEND_WITH_UPLOAD)&&((O==null?void 0:O.reqStatus)===Ye.DRAFT||(O==null?void 0:O.reqStatus)===Ye.UPLOAD_FAILED)?(z(wn(0)),F(!1),x(!1)):(z(wn(1)),F(!0),x(!0)),Ht(!0)}else z(wn(0))})(),()=>{z(qo([])),z(Ho()),z(Fo()),z(Uo()),z($o()),z(jo()),z(ts({})),z(Bo({data:{}})),z(Vo([])),z(Go([])),z(Ys({})),z(Wo()),z(Pn([])),z(zo([])),z(Xo({})),Us(Yn.CURRENT_TASK),Us(Yn.ROLE)}),[we,z]);function $t($){let C={decisionTableId:null,decisionTableName:ur.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":$}]};Tt(C)}l.useEffect(()=>{r!=null&&r.Region&&$t(r==null?void 0:r.Region)},[r==null?void 0:r.Region]),l.useEffect(()=>{var $,C;if(bt){const Oe=[...Yo((C=($=bt==null?void 0:bt.result)==null?void 0:$[0])==null?void 0:C.MDG_MAT_REGION_DIVISION_MAPPING)].sort((tt,Se)=>tt.code.localeCompare(Se.code));z(fn({keyName:"Division",data:Oe})),Jt(!1),B(ds.DT_LOADING)}},[bt]),l.useEffect(()=>(ut(),jt(),z(Tn([])),z(fn({keyName:"Region",data:Jo})),z(fn({keyName:"DiversionControlFlag",data:Ko})),()=>{z(Qo({}))}),[]),l.useEffect(()=>{p&&Nt([!0])},[p]),l.useEffect(()=>{J(Zo("MAT"))},[]);const jt=()=>{let $={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};g(!0);const C=Oe=>{var tt,Se,a,S;if(g(!1),Oe.statusCode===200){let b=(Se=(tt=Oe==null?void 0:Oe.data)==null?void 0:tt.result[0])==null?void 0:Se.MDG_ATTACHMENTS_ACTION_TYPE,q=[];b==null||b.map((H,re)=>{var fe={id:re};q.push(fe)}),i(q);const k=((S=(a=Oe==null?void 0:Oe.data)==null?void 0:a.result[0])==null?void 0:S.MDG_ATTACHMENTS_ACTION_TYPE)||[];be(k)}},me=Oe=>{console.log(Oe)};pe.environment==="localhost"?Te(`/${vn}/rest/v1/invoke-rules`,"post",C,me,$):Te(`/${vn}/v1/invoke-rules`,"post",C,me,$)},st=()=>{var Se,a,S,b,q,k;const $=oe!=null&&oe.includes("FCA")?at.EXCEL.DOWNLOAD_EXCEL_FINANCE:at.EXCEL.DOWNLOAD_EXCEL_MAT;B("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),ce(!0);let C={massSchedulingId:r==null?void 0:r.RequestId},me={dtName:(r==null?void 0:r.RequestType)===((Se=L)==null?void 0:Se.CHANGE)||(r==null?void 0:r.RequestType)===((a=L)==null?void 0:a.CHANGE_WITH_UPLOAD)?"MDG_MAT_CHANGE_TEMPLATE":"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:(r==null?void 0:r.RequestType)===((S=L)==null?void 0:S.CHANGE)||(r==null?void 0:r.RequestType)===((b=L)==null?void 0:b.CHANGE_WITH_UPLOAD)?"v4":"v1",requestId:(r==null?void 0:r.RequestId)||Ue||"",scenario:(r==null?void 0:r.RequestType)===((q=L)==null?void 0:q.CHANGE)||(r==null?void 0:r.RequestType)===((k=L)==null?void 0:k.CHANGE_WITH_UPLOAD)?"Change with Upload":"Create with Upload",templateName:(r==null?void 0:r.TemplateName)||"",region:(r==null?void 0:r.Region)||"",matlType:"ALL"};const Oe=H=>{const re=URL.createObjectURL(H),fe=document.createElement("a");fe.href=re,fe.setAttribute("download",`${r!=null&&r.TemplateName?r==null?void 0:r.TemplateName:oe!=null&&oe.includes("FCA")?L.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild(fe),fe.click(),document.body.removeChild(fe),URL.revokeObjectURL(re),ce(!1),B(""),K(`${r!=null&&r.TemplateName?r==null?void 0:r.TemplateName:oe!=null&&oe.includes("FCA")?L.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx has been exported successfully.`),V("success"),lt()},tt=()=>{};Te(`/${Ce}${$}`,"postandgetblob",Oe,tt,oe!=null&&oe.includes("FCA")?C:me)},lt=()=>{He(!0)},ue=()=>{He(!1)},Be=()=>{var $,C,me;we&&!wt?$e(($=Cn)==null?void 0:$.MY_TASK):wt?$e((C=Cn)==null?void 0:C.REQUEST_BENCH):!we&&!wt&&$e((me=Cn)==null?void 0:me.MASTER_DATA)},Sn=()=>{dt(!1)};return d(Je,{children:[Yt&&e(gn,{blurLoading:Q,loaderMessage:de}),d(I,{sx:{padding:2},children:[d(it,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[Ue||we?d(Me,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(vr,{sx:{fontSize:"1.5rem"}}),ve("Request Header ID"),": ",e("span",{children:Ue?$s(Dt,Ue):we})]}):e("div",{style:{flex:1}}),ct===1&&d(I,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[e(qe,{variant:"outlined",size:"small",title:ve("Download Error Report"),disabled:!oe,onClick:()=>{$e(`/requestBench/errorHistory?RequestId=${oe||""}`,{state:{display:!0,childRequest:et}})},color:"primary",children:e(er,{sx:{padding:"2px"}})}),(r==null?void 0:r.RequestType)===L.CREATE||(r==null?void 0:r.RequestType)===L.EXTEND||(r==null?void 0:r.RequestType)===L.EXTEND_WITH_UPLOAD||(r==null?void 0:r.RequestType)===L.CREATE_WITH_UPLOAD||we!=null&&we.includes("FCA")?e(qe,{variant:"outlined",disabled:!oe,size:"small",onClick:()=>It(!0),title:we!=null&&we.includes("FCA")?ve("Finance Costing Change Log"):ve("Create Change Log"),children:e(js,{sx:{padding:"2px"}})}):e(qe,{variant:"outlined",disabled:!oe,size:"small",onClick:ft,title:ve("Change Log"),children:e(js,{sx:{padding:"2px"}})}),e(qe,{variant:"outlined",disabled:!oe,size:"small",onClick:st,title:ve("Export Excel"),children:e(Dr,{sx:{padding:"2px"}})})]}),Ae&&e(tr,{open:!0,closeModal:vt,requestId:Ue||we.slice(3),requestType:r==null?void 0:r.RequestType}),Ct&&e(Ri,{open:!0,closeModal:()=>It(!1),requestId:Ue||we.slice(3),requestType:r==null?void 0:r.RequestType})]}),(r==null?void 0:r.TemplateName)&&d(Me,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(rl,{sx:{fontSize:"1.5rem"}}),ve("Template Name"),": ",e("span",{children:r==null?void 0:r.TemplateName})]}),e(Lt,{onClick:()=>{var $,C;if(wt&&!(($=pn)!=null&&$.includes(r==null?void 0:r.RequestStatus))){$e((C=Cn)==null?void 0:C.REQUEST_BENCH);return}dt(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:ve("Back"),children:e(nr,{sx:{fontSize:"25px",color:"#000000"}})}),e(lr,{nonLinear:!0,activeStep:ct,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:je.map(($,C)=>e(sr,{completed:At[C],children:e(Pr,{color:"error",disabled:C===1&&!p||C===2&&!w||C===3&&!w,onClick:()=>Ft(C),sx:{fontSize:"50px",fontWeight:"bold"},children:e("span",{style:{fontSize:"15px",fontWeight:"bold"},children:$})})},$))}),e(Zs,{dialogState:xe,openReusableDialog:nt,closeReusableDialog:Ke,dialogTitle:u,dialogMessage:ne,handleDialogConfirm:Ke,dialogOkText:"OK",handleOk:_t,dialogSeverity:T}),e(gn,{blurLoading:Q,loaderMessage:de}),ct===0&&d(Je,{children:[e(br,{setIsSecondTabEnabled:F,setIsAttachmentTabEnabled:x,requestStatus:O!=null&&O.reqStatus?O==null?void 0:O.reqStatus:Ye.ENABLE_FOR_FIRST_TIME,downloadClicked:y,setDownloadClicked:Z}),(_e===L.CHANGE_WITH_UPLOAD||_e===L.CREATE_WITH_UPLOAD||_e===L.EXTEND_WITH_UPLOAD)&&((O==null?void 0:O.reqStatus)==Ye.DRAFT&&!((U=O==null?void 0:O.material)!=null&&U.length)||(O==null?void 0:O.reqStatus)==Ye.UPLOAD_FAILED)&&e(Ii,{handleDownload:gt,setEnableDocumentUpload:X,enableDocumentUpload:te,handleUploadMaterial:ye}),((r==null?void 0:r.RequestType)===((Qe=L)==null?void 0:Qe.CHANGE)||(r==null?void 0:r.RequestType)===((Qt=L)==null?void 0:Qt.CHANGE_WITH_UPLOAD))&&!oe&&(r==null?void 0:r.DirectAllowed)!=="X"&&(r==null?void 0:r.DirectAllowed)!==void 0&&d(Me,{sx:{fontSize:"13px",fontWeight:"500",color:(We=(Zt=Re)==null?void 0:Zt.error)==null?void 0:We.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[e(I,{component:"span",sx:{fontWeight:"bold"},children:"Note:"})," ","You are not authorized to Tcode"," ",d(I,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),ct===1&&((r==null?void 0:r.RequestType)===((Ot=L)==null?void 0:Ot.CREATE)||(De==null?void 0:De.ATTRIBUTE_2)===((en=L)==null?void 0:en.CREATE)||_e===((Bt=L)==null?void 0:Bt.CREATE)||_e===((Vt=L)==null?void 0:Vt.CREATE_WITH_UPLOAD)?e(Ar,{requestStatus:O!=null&&O.reqStatus?O==null?void 0:O.reqStatus:Ye.ENABLE_FOR_FIRST_TIME,mandFields:A,addHardCodeData:Le,setIsAttachmentTabEnabled:x,setCompleted:Nt}):(r==null?void 0:r.RequestType)===((tn=L)==null?void 0:tn.EXTEND)||(De==null?void 0:De.ATTRIBUTE_2)===((nn=L)==null?void 0:nn.EXTEND)||(De==null?void 0:De.ATTRIBUTE_2)===((Rt=L)==null?void 0:Rt.EXTEND_WITH_UPLOAD)||_e===((xn=L)==null?void 0:xn.EXTEND)||_e===((Gt=L)==null?void 0:Gt.EXTEND_WITH_UPLOAD)?e(Wr,{requestStatus:O!=null&&O.reqStatus?O==null?void 0:O.reqStatus:Ye.ENABLE_FOR_FIRST_TIME,mandFields:A,addHardCodeData:Le,setIsAttachmentTabEnabled:x,setCompleted:Nt}):(r==null?void 0:r.RequestType)===((sn=L)==null?void 0:sn.FINANCE_COSTING)||(De==null?void 0:De.ATTRIBUTE_2)===((ln=L)==null?void 0:ln.FINANCE_COSTING)||_e===((on=L)==null?void 0:on.FINANCE_COSTING)?e(Di,{setCompleted:Nt}):e(_r,{setIsAttachmentTabEnabled:!0,setCompleted:Nt,downloadClicked:y,setDownloadClicked:Z})),ct===2&&e(Nr,{requestStatus:O!=null&&O.reqStatus?O==null?void 0:O.reqStatus:Ye.ENABLE_FOR_FIRST_TIME,attachmentsData:he,requestIdHeader:Ue?$s(Dt,Ue):we,pcNumber:h}),ct===3&&e(I,{sx:{width:"100%",overflow:"auto"},children:e(vi,{requestStatus:O!=null&&O.reqStatus?O==null?void 0:O.reqStatus:Ye.ENABLE_FOR_FIRST_TIME})})]}),e(Un,{openSnackBar:ge,alertMsg:ne,alertType:D,handleSnackBarClose:ue}),Ie&&d(or,{isOpen:Ie,titleIcon:e(cr,{size:"small",sx:{color:(an=(rn=Re)==null?void 0:rn.secondary)==null?void 0:an.amber,fontSize:"20px"}}),Title:ve("Warning"),handleClose:Sn,children:[e(An,{sx:{mt:2},children:ve(rr.LEAVE_PAGE_MESSAGE)}),d(Nn,{children:[e(qe,{variant:"outlined",size:"small",sx:{...ir},onClick:Sn,children:ve("No")}),e(qe,{variant:"contained",size:"small",sx:{...ar},onClick:Be,children:ve("Yes")})]})]})]})};export{oa as default};
