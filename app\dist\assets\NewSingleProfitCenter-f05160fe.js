import{l as dn,i as b,r as s,sR as $,iz as Gn,a as n,j as c,T as C,a3 as p,g_ as j,$ as u,hy as Kn,hA as Qn,gR as Xn,hJ as Yn,w as B,hL as H,hP as un,h_ as ee,B as V,F as _,u as Zn,b as yn,ix as Dn,sS as pn,hZ as et,ht as nt,J as tt,i7 as De,fV as pe,h1 as en,W as ce,hI as nn,fX as tn,gQ as rn,fH as sn,a1 as R,ho as it,h8 as rt,hp as on,hv as st,i8 as ot,sT as lt,ic as ct,id as dt,ie as ut,P as at,hE as ht,gW as Q,ih as ln,hn as ft,i6 as mt,ik as cn}from"./index-fdfa25a0.js";import{d as gt}from"./dayjs.min-774e293a.js";import{A as Ct}from"./AdapterDayjs-cd3745c6.js";import{C as xt}from"./CompCodeProfitCenter-a8989840.js";import"./AutoCompleteType-3a9c9c9d.js";import"./isBetween-fe8614a5.js";import"./useChangeLogUpdate-3699f77c.js";function ne(t){var F,e,O,J;const i=dn();let x=b(o=>o.profitCenter.errorFields);var d=t.field.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").replaceAll("%","").split(" ").join("");const f=b(o=>o.profitCenter.singlePCPayload),a=gt();s.useEffect(()=>{var o;((o=t.field)==null?void 0:o.fieldType)==="Calendar"&&i($({keyName:d,data:a}))},[]);const v=o=>{const h=P=>{console.log("value",P),i(un({keyName:"Region",data:P.body}))},T=P=>{console.log(P,"error in dojax")};B(`/${H}/data/getRegionBasedOnCountry?country=${o==null?void 0:o.code}`,"get",h,T)};s.useEffect(()=>{var o,h;(((o=t==null?void 0:t.field)==null?void 0:o.visibility)==="0"||((h=t==null?void 0:t.field)==null?void 0:h.visibility)==="Required")&&i(Gn(d))},[]),console.log("props",t);const S=b(o=>o.AllDropDown.dropDown);if(((F=t.field)==null?void 0:F.fieldType)==="Input")return n(u,{item:!0,md:2,children:t.field.visibility==="Hidden"?null:c(j,{children:[c(C,{variant:"body2",color:"#777",children:[t.field.fieldName,t.field.visibility==="Required"||t.field.visibility==="0"?n("span",{style:{color:"red"},children:"*"}):""]}),n(p,{size:"small",type:t.field.dataType==="QUAN"?"number":"",placeholder:`Enter ${t.field.fieldName}`,inputProps:{maxLength:t.field.maxLength},value:f[d],onChange:(o,h)=>{const T=o.target.value;Object.keys(f).length>0?(console.log("0"),T.length>0&&T[0]===" "?(console.log("1"),i($({keyName:d,data:T.trimStart()}))):(console.log("2"),i($({keyName:d,data:T.toUpperCase()})))):(console.log("3"),i($({keyName:d,data:T.trimStart()})))},required:t.field.visibility==="Required"||t.field.visibility==="0",error:x.includes(d)})]})});if(((e=t.field)==null?void 0:e.fieldType)==="Drop Down")return n(u,{item:!0,md:2,children:t.field.visibility==="Hidden"?null:c(j,{children:[c(C,{variant:"body2",color:"#777",children:[t.field.fieldName,t.field.visibility==="Required"||t.field.visibility==="0"?n("span",{style:{color:"red"},children:"*"}):""]}),n(Kn,{sx:{height:"31px"},fullWidth:!0,size:"small",value:f[d],onChange:(o,h)=>{t.field.fieldName==="Country/Reg."&&v(h),i($({keyName:d,data:h}))},options:S[d]??[],required:t.field.visibility==="0"||t.field.visibility==="Required",getOptionLabel:o=>`${o==null?void 0:o.code} - ${o==null?void 0:o.desc}`,renderOption:(o,h)=>n("li",{...o,children:c(C,{style:{fontSize:12},children:[h==null?void 0:h.code," - ",h==null?void 0:h.desc]})}),renderInput:o=>n(p,{...o,variant:"outlined",placeholder:`Select ${t.field.fieldName}`,error:x.includes(d)})})]})});if(((O=t.field)==null?void 0:O.fieldType)==="Radio Button")return c(u,{item:!0,md:2,children:[c(C,{variant:"body2",color:"#777",children:[t.field.fieldName,t.field.visibility==="Required"||t.field.visibility==="0"?n("span",{style:{color:"red"},children:"*"}):""]}),n(Qn,{sx:{padding:0},error:x.includes(d),checked:f[d]==!0,onChange:o=>{i($({keyName:d,data:o.target.checked}))}})]});if(((J=t.field)==null?void 0:J.fieldType)==="Calendar")return n(u,{item:!0,md:2,children:c(j,{children:[c(C,{variant:"body2",color:"#777",children:[t.field.fieldName,t.field.visibility==="Required"||t.field.visibility==="0"?n("span",{style:{color:"red"},children:"*"}):""]}),n(Xn,{dateAdapter:Ct,children:n(Yn,{slotProps:{textField:{size:"small"}},value:f[d],defaultValue:a,onChange:o=>i($({keyName:d,data:o})),onError:x.includes(d),required:t.field.visibility==="0"||t.field.visibility==="Required"})})]})})}const bt=t=>{let i=Object==null?void 0:Object.entries(t==null?void 0:t.basicDataTabDetails);console.log("basic",i);const[x,d]=s.useState([]);return s.useEffect(()=>{d(i==null?void 0:i.map(f=>{var a,v;return c(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ee},children:[n(u,{container:!0,children:n(C,{sx:{fontSize:"12px",fontWeight:"700"},children:f[0]})}),n(V,{children:n(u,{container:!0,spacing:1,children:(v=(a=[...f[1]].filter(S=>(S==null?void 0:S.visibility)!="Hidden"))==null?void 0:a.sort((S,F)=>(S==null?void 0:S.sequenceNo)-(F==null?void 0:F.sequenceNo)))==null?void 0:v.map(S=>n(ne,{field:S,dropDownData:t==null?void 0:t.dropDownData}))})})]})}))},[t==null?void 0:t.basicDataTabDetails]),n(_,{children:x})},St=t=>{let i=Object==null?void 0:Object.entries(t==null?void 0:t.indicatorsTabDetails);console.log("PROFITCENTER",i);const[x,d]=s.useState([]);return s.useEffect(()=>{d(i==null?void 0:i.map(f=>c(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ee},children:[n(u,{container:!0,children:n(C,{sx:{fontSize:"12px",fontWeight:"700"},children:f[0]})}),n(V,{children:n(u,{container:!0,spacing:1,children:[...f[1]].filter(a=>a.visibility!="Hidden").sort((a,v)=>a.sequenceNo-v.sequenceNo).map(a=>n(ne,{field:a,dropDownData:t.dropDownData}))})})]})))},[t==null?void 0:t.indicatorsTabDetails]),n(_,{children:x})},Pt=t=>{let i=Object==null?void 0:Object.entries(t==null?void 0:t.addressTabDetails);console.log("basic",i);const[x,d]=s.useState([]);return s.useEffect(()=>{d(i==null?void 0:i.map(f=>c(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ee},children:[n(u,{container:!0,children:n(C,{sx:{fontSize:"12px",fontWeight:"700"},children:f[0]})}),n(V,{children:n(u,{container:!0,spacing:1,children:[...f[1]].filter(a=>a.visibility!="Hidden").sort((a,v)=>a.sequenceNo-v.sequenceNo).map(a=>n(ne,{field:a,dropDownData:t.dropDownData}))})})]})))},[t==null?void 0:t.addressTabDetails]),n(_,{children:x})},vt=t=>{let i=Object==null?void 0:Object.entries(t==null?void 0:t.communicationTabDetails);console.log("basic",i);const[x,d]=s.useState([]);return s.useEffect(()=>{d(i==null?void 0:i.map(f=>c(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ee},children:[n(u,{container:!0,children:n(C,{sx:{fontSize:"12px",fontWeight:"700"},children:f[0]})}),n(V,{children:n(u,{container:!0,spacing:1,children:[...f[1]].filter(a=>a.visibility!="Hidden").sort((a,v)=>a.sequenceNo-v.sequenceNo).map(a=>n(ne,{field:a,dropDownData:t.dropDownData}))})})]})))},[t==null?void 0:t.communicationTabDetails]),n(_,{children:x})},Ot=()=>{var Te,ke,we,Ne,Ae,Ee,Re,Fe,Ie,Be,Oe,qe,Le,Me,$e,je,Ve,ze,We,He,_e,Je,Ue,Ge,Ke,Qe,Xe,Ye;s.useState(0);const i=Zn().state;console.log("displaydata",i);const x=yn(),d=dn(),f=b(r=>r.profitCenter.profitCenterBasicData),a=b(r=>r.profitCenter.profitCenterCompCodes),v=b(r=>r.profitCenter.profitCenterIndicators),S=b(r=>r.profitCenter.profitCenterAddress),F=b(r=>r.profitCenter.profitCenterCommunication);b(r=>r.profitCenter.profitCenterHistory);const e=b(r=>r.profitCenter.singlePCPayload);console.log("payloadData",e);const[O,J]=s.useState(0),[o,h]=s.useState(!1),[T,P]=s.useState(""),[an,w]=s.useState(!1),[Tt,q]=s.useState(!0),[hn,L]=s.useState(!1),[kt,M]=s.useState(!1),[fn,te]=s.useState(!1),[de,z]=s.useState(!0),[mn,X]=s.useState(!1),[gn,U]=s.useState(!1),[Y,Cn]=s.useState([]),[xn,ue]=s.useState(!1),[bn,ae]=s.useState(!1),[Sn,ie]=s.useState(!0),[re,Pn]=s.useState(""),[vn,Tn]=s.useState(!1),[G,he]=s.useState(""),[kn,wn]=s.useState(!1),[Nn,fe]=s.useState(!0),[An,me]=s.useState(!1),[En,Rn]=s.useState("");let N=b(r=>r.userManagement.userData);const K=b(r=>r.AllDropDown.dropDown),ge=["BASIC DATA","COMPANY CODE","INDICATOR","ADDRESS","COMMUNICATION","ATTACHMENTS & COMMENTS"];console.log("isLoading",de);const se=()=>{te(!0)},Fn=()=>{bn?(te(!1),ae(!1)):(te(!1),x("/masterDataCockpit/profitCenter"))},W=()=>{U(!0)},In=()=>{Se()?J(m=>m+1):Pe()},Bn=()=>{ie(!0),Se()?J(m=>m-1):Pe()},oe=()=>{Tn(!1)},Ce=(r,m)=>{const l=r.target.value;if(l.length>0&&l[0]===" ")he(l.trimStart());else{let A=l.toUpperCase();he(A)}},xe=()=>{y(),Vn()},On=b(r=>r.profitCenter.singlePCPayload),qn=b(r=>r.profitCenter.requiredFields),Z=b(r=>r.AllDropDown.dropDown.CompCodeBasedOnControllingArea);console.log("rows",Z);var le={ProfitCenterID:"",RequestID:"",TaskId:"",Action:"I",TaskStatus:"",ReqCreatedBy:N==null?void 0:N.user_id,ReqCreatedOn:"",RequestStatus:"",Remarks:G||"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",MassRequestStatus:"",PrctrName:e!=null&&e.Name?e==null?void 0:e.Name:"",LongText:e!=null&&e.LongText?e==null?void 0:e.LongText:"",InChargeUser:(Te=e==null?void 0:e.UserResponsible)!=null&&Te.code?(ke=e==null?void 0:e.UserResponsible)==null?void 0:ke.code:"",InCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",PrctrHierGrp:(we=e==null?void 0:e.ProfitCtrGroup)!=null&&we.code?(Ne=e==null?void 0:e.ProfitCtrGroup)==null?void 0:Ne.code:"",Segment:(Ae=e==null?void 0:e.Segment)!=null&&Ae.code?(Ee=e==null?void 0:e.Segment)==null?void 0:Ee.code:"",LockInd:(e==null?void 0:e.Lockindicator)===!0?"X":"",Template:(Re=e==null?void 0:e.FormPlanningTemp)!=null&&Re.code?(Fe=e==null?void 0:e.FormPlanningTemp)==null?void 0:Fe.code:"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",Name2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",Name3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",Name4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",Street:e!=null&&e.Street?e==null?void 0:e.Street:"",City:e!=null&&e.City?e==null?void 0:e.City:"",District:e!=null&&e.District?e==null?void 0:e.District:"",Country:(Ie=e==null?void 0:e.CountryReg)!=null&&Ie.code?(Be=e==null?void 0:e.CountryReg)==null?void 0:Be.code:"",Taxjurcode:(Oe=e==null?void 0:e.TaxJur)!=null&&Oe.code?(qe=e==null?void 0:e.TaxJur)==null?void 0:qe.code:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",PobxPcd:e!=null&&e.POBoxPCode?e==null?void 0:e.POBoxPCode:"",Region:(Le=e==null?void 0:e.Region)!=null&&Le.code?(Me=e==null?void 0:e.Region)==null?void 0:Me.code:"",Langu:($e=e==null?void 0:e.Language)!=null&&$e.code?(je=e==null?void 0:e.Language)==null?void 0:je.code:"EN",Telephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",Telephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",Telebox:e!=null&&e.Telebox?e==null?void 0:e.Telebox:"",Telex:e!=null&&e.Telex?e==null?void 0:e.Telex:"",FaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",Teletex:e!=null&&e.Teletex?e==null?void 0:e.Teletex:"",Printer:e!=null&&e.Printername?e==null?void 0:e.Printername:"",DataLine:e!=null&&e.Dataline?e==null?void 0:e.Dataline:"",ProfitCenter:`P${(Ve=i==null?void 0:i.companyCode)==null?void 0:Ve.newCompanyCode.code}${(ze=i==null?void 0:i.profitCenterName)==null?void 0:ze.newProfitCenterName}`?`P${(We=i==null?void 0:i.companyCode)==null?void 0:We.newCompanyCode.code}${(He=i==null?void 0:i.profitCenterName)==null?void 0:He.newProfitCenterName}`:"",ControllingArea:(Je=(_e=i==null?void 0:i.controllingArea)==null?void 0:_e.newControllingArea)!=null&&Je.code?(Ge=(Ue=i==null?void 0:i.controllingArea)==null?void 0:Ue.newControllingArea)==null?void 0:Ge.code:"",ValidfromDate:e!=null&&e.AnalysisPeriodFrom?"/Date("+Date.parse(e==null?void 0:e.AnalysisPeriodFrom)+")/":"",ValidtoDate:e!=null&&e.AnalysisPeriodTo?"/Date("+Date.parse(e==null?void 0:e.AnalysisPeriodTo)+")/":"",Testrun:Nn,Countryiso:"",LanguIso:"",Logsystem:"",ToCompanycode:Z==null?void 0:Z.map(r=>({CompCodeID:"",CompanyName:r==null?void 0:r.companyName,AssignToPrctr:r.assigned,CompCode:r.companyCodes}))};const[be,Ln]=s.useState(0),Mn=(r,m)=>{const l=E=>{d(un({keyName:r,data:E.body})),Ln(I=>I+1)},A=E=>{console.log(E)};B(`/${H}/data/${m}`,"get",l,A)},$n=()=>{var r,m;(m=(r=ln)==null?void 0:r.profitCenter)==null||m.map(l=>{Mn(l==null?void 0:l.keyName,l==null?void 0:l.endPoint)})},jn=()=>{var r,m;be==((m=(r=ln)==null?void 0:r.profitCenter)==null?void 0:m.length)?z(!1):z(!0)};s.useEffect(()=>{jn()},[be]),s.useEffect(()=>{$n()},[]),s.useEffect(()=>{d(Dn())},[]),s.useEffect(()=>{d(pn(Y))},[Y]),s.useEffect(()=>{Pn(et("PC"))},[]);const Se=()=>ft(On,qn,Cn),Pe=()=>{ue(!0)},Vn=()=>{z(!0);const r=l=>{if(z(!1),l.statusCode===200){h("Create"),P(`Profit Center has been Submitted for review NPS${l.body}`),w("success"),q(!1),L(!0),se(),M(!0);const A={artifactId:re,createdBy:N==null?void 0:N.emailId,artifactType:"ProfitCenter",requestId:`NPS${l==null?void 0:l.body}`},E=k=>{console.log("Second API success",k)},I=k=>{console.error("Second API error",k)};B(`/${cn}/documentManagement/updateDocRequestId`,"post",E,I,A)}else h("Create"),L(!1),P("Creation Failed"),w("danger"),q(!1),M(!0),W();handleClose()},m=l=>{console.log(l)};B(`/${H}/alter/profitCenterSubmitForReview`,"post",r,m,le)},zn=()=>{w(!1),w(!1),W(),h("Confirm"),P("Do You Want to Save as Draft ?"),wn(!0),Rn("proceed")},Wn=()=>{z(!0);const r=l=>{if(z(!1),l.statusCode===200){console.log("success"),U(!1),h("Create"),P(`Profit Center has been saved with creation ID NPS${l.body}`),w("success"),q(!1),L(!0),se(),M(!0);const A={artifactId:re,createdBy:N==null?void 0:N.emailId,artifactType:"ProfitCenter",requestId:`NPS${l==null?void 0:l.body}`},E=k=>{console.log("Second API success",k)},I=k=>{console.error("Second API error",k)};B(`/${cn}/documentManagement/updateDocRequestId`,"post",E,I,A)}else U(!1),h("Save"),L(!1),P("Failed Saving the Data "),w("danger"),q(!1),M(!0),W();handleClose()},m=l=>{console.log(l)};B(`/${H}/alter/profitCenterAsDraft`,"post",r,m,le)},Hn=()=>{fe(!1),me(!0)},y=()=>{fe(!0),me(!1)},_n=()=>{var I,k;X(!0);const r={coArea:((k=(I=i==null?void 0:i.controllingArea)==null?void 0:I.newControllingArea)==null?void 0:k.code)||"",name:e!=null&&e.Name?e==null?void 0:e.Name.toUpperCase():""},m=g=>{var D,Ze,ye;g.statusCode===201?(h("Create"),h("Create"),P("All Data has been Validated. Profit Center can be Sent for Review"),w("success"),q(!1),L(!0),se(),M(!0),ae(!0),(r.coArea!==""||r.name!=="")&&B(`/${H}/alter/fetchPCDescriptionDupliChk`,"post",l,A,r)):(X(!1),h("Error"),L(!1),P(`${(D=g==null?void 0:g.body)!=null&&D.message[0]?(Ze=g==null?void 0:g.body)==null?void 0:Ze.message[0]:(ye=g==null?void 0:g.body)==null?void 0:ye.value}`),w("danger"),q(!1),M(!0),W())},l=g=>{g.body.length===0||!g.body.some(D=>D.toUpperCase()===r.name)?(ie(!1),X(!1)):(X(!1),h("Duplicate Check"),L(!1),P("There is a direct match for the Profit Center name. Please change the name."),w("danger"),q(!1),M(!0),W(),ie(!0))},A=g=>{console.log(g)},E=g=>{console.log(g)};B(`/${H}/alter/validateSingleProfitCenter`,"post",m,E,le)},Jn=r=>{switch(r){case 0:return n(bt,{basicDataTabDetails:f,dropDownData:K});case 1:return n(xt,{compCodesTabDetails:a,dropDownData:K});case 2:return n(St,{indicatorsTabDetails:v,dropDownData:K});case 3:return n(Pt,{addressTabDetails:S,dropDownData:K});case 4:return n(vt,{communicationTabDetails:F,dropDownData:K});case 5:return n(mt,{title:"ProfitCenter",useMetaData:!1,artifactId:re,artifactName:"ProfitCenter"});default:return"Unknown step"}},Un=()=>{ue(!1)},ve=()=>{U(!1)};return n(_,{children:de===!0?n(nt,{}):c("div",{children:[n(tt,{dialogState:gn,openReusableDialog:W,closeReusableDialog:ve,dialogTitle:o,dialogMessage:T,handleDialogConfirm:ve,dialogOkText:"OK",showExtraButton:kn,dialogSeverity:an,showCancelButton:!0,handleDialogReject:()=>{U(!1)},handleExtraText:En,handleExtraButton:Wn}),Y.length!=0&&n(De,{openSnackBar:xn,alertMsg:"Please fill the following Field: "+Y.join(", "),handleSnackBarClose:Un}),hn&&n(De,{openSnackBar:fn,alertMsg:T,handleSnackBarClose:Fn}),c(pe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:vn,onClose:oe,children:[c(en,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(C,{variant:"h6",children:"Remarks"}),n(ce,{sx:{width:"max-content"},onClick:oe,children:n(nn,{})})]}),n(tn,{sx:{padding:".5rem 1rem"},children:n(j,{children:n(V,{sx:{minWidth:400},children:n(rn,{sx:{height:"auto"},fullWidth:!0,children:n(p,{sx:{backgroundColor:"#F5F5F5"},value:G,onChange:Ce,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),c(sn,{sx:{display:"flex",justifyContent:"end"},children:[n(R,{sx:{width:"max-content",textTransform:"capitalize"},onClick:oe,children:"Cancel"}),n(R,{className:"button_primary--normal",type:"save",onClick:xe,variant:"contained",children:"Submit"})]})]}),c(pe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:An,onClose:y,children:[c(en,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(C,{variant:"h6",children:"Remarks"}),n(ce,{sx:{width:"max-content"},onClick:y,children:n(nn,{})})]}),n(tn,{sx:{padding:".5rem 1rem"},children:n(j,{children:n(V,{sx:{minWidth:400},children:n(rn,{sx:{height:"auto"},fullWidth:!0,children:n(p,{sx:{backgroundColor:"#F5F5F5"},value:G,onChange:Ce,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),c(sn,{sx:{display:"flex",justifyContent:"end"},children:[n(R,{sx:{width:"max-content",textTransform:"capitalize"},onClick:y,children:"Cancel"}),n(R,{className:"button_primary--normal",type:"save",value:G,onClick:xe,variant:"contained",children:"Submit"})]})]}),n(it,{sx:{color:"#fff",zIndex:r=>r.zIndex.drawer+1},open:mn,children:n(rt,{color:"inherit"})}),n(u,{container:!0,style:{...on,backgroundColor:"#FAFCFF"},children:c(u,{sx:{width:"inherit"},children:[n(u,{item:!0,md:7,style:{padding:"16px",display:"flex"},children:c(u,{item:!0,md:5,sx:{display:"flex"},children:[n(u,{children:n(ce,{color:"primary","aria-label":"upload picture",component:"label",sx:st,children:n(ot,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{x("/masterDataCockpit/profitCenter"),d(lt())}})})}),c(u,{children:[n(C,{variant:"h3",children:n("strong",{children:"Create Profit Center"})}),n(C,{variant:"body2",color:"#777",children:"This view creates a new Profit Center"})]})]})}),n(u,{container:!0,style:{padding:"0 1rem 0 1rem"},children:c(u,{container:!0,sx:on,children:[n(u,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:c(V,{width:"70%",sx:{marginLeft:"40px",marginBottom:"20px"},children:[n(u,{item:!0,sx:{paddingTop:"2px !important"},children:c(j,{flexDirection:"row",children:[n("div",{style:{width:"10%"},children:n(C,{variant:"body2",color:"#777",children:"Profit Center"})}),c(C,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": P",(Ke=i==null?void 0:i.companyCode)==null?void 0:Ke.newCompanyCode.code,(Qe=i==null?void 0:i.profitCenterName)==null?void 0:Qe.newProfitCenterName]})]})}),n(u,{item:!0,sx:{paddingTop:"2px !important"},children:c(j,{flexDirection:"row",children:[n("div",{style:{width:"10%"},children:n(C,{variant:"body2",color:"#777",children:"Controlling Area"})}),c(C,{variant:"body2",fontWeight:"bold",children:[":"," ",(Ye=(Xe=i==null?void 0:i.controllingArea)==null?void 0:Xe.newControllingArea)==null?void 0:Ye.code]})]})})]})}),n(u,{container:!0,children:n(ct,{activeStep:O,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},children:ge.map((r,m)=>n(dt,{children:n(ut,{sx:{fontWeight:"700"},children:r})},r))})}),n(u,{container:!0,children:Jn(O)})]})})]})}),n(at,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:c(ht,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(R,{variant:"contained",size:"small",sx:{...Q,mr:1},onClick:zn,children:"Save As Draft"}),n(R,{variant:"contained",size:"small",sx:{...Q,mr:1},onClick:Bn,disabled:O===0,children:"Back"}),O===ge.length-1?c(_,{children:[n(R,{variant:"contained",size:"small",sx:{...Q,mr:1},onClick:_n,children:"Validate"}),n(R,{variant:"contained",size:"small",sx:{...Q,mr:1},onClick:Hn,disabled:Sn,children:"Submit For Review"})]}):n(R,{variant:"contained",size:"small",sx:{...Q,mr:1},onClick:In,children:"Next"})]})})]})})};export{Ot as default};
