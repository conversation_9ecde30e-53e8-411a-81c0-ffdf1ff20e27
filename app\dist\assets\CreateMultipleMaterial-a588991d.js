import{r as v,b as D,j as r,hl as w,a as n,g_ as b,hC as h,$ as o,hA as C,hp as x,W as O,hv as A,i8 as L,T as g,hE as B,a1 as I,gW as R,P}from"./index-fdfa25a0.js";const _=()=>{var c,p;const[T,F]=v.useState(!1),[f,K]=v.useState("1"),s=D(),l={body:{controlingArea:"TZUS",tableData:[{costCenter:"tuk1",validFrom:null,validTo:null,"Basic Data":{Names:[{fieldName:"Name",sequenceNo:1,fieldType:"Input",maxLength:20,dataType:"String",viewName:"Basic Data",cardName:"Names",cardSeq:1,visibility:"Optional",value:"WATER"},{fieldName:"Description",sequenceNo:2,fieldType:"Input",maxLength:40,dataType:"String",viewName:"Basic Data",cardName:"Names",cardSeq:1,visibility:"Optional",value:"wear"}],"Basic Data":[{fieldName:"User Responsible",sequenceNo:3,fieldType:"Input",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Person Responsible",sequenceNo:4,fieldType:"Input",maxLength:20,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Department",sequenceNo:5,fieldType:"Input",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Cost Center Category",sequenceNo:6,fieldType:"Drop Down",maxLength:1,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Comp Code",sequenceNo:7,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Hierarchy area",sequenceNo:8,fieldType:"Drop Down",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Business Area",sequenceNo:9,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Functional Area",sequenceNo:10,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Currency",sequenceNo:11,fieldType:"",maxLength:5,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Profit Center",sequenceNo:12,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""}]},Control:{Control:[{fieldName:"Record Quantity",sequenceNo:1,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Actual primary costs",sequenceNo:2,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"  Plan primary costs",sequenceNo:3,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:" Commitment Update",sequenceNo:4,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Act. secondary costs",sequenceNo:5,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Plan Secondary Costs",sequenceNo:6,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Actual Revenue",sequenceNo:7,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Plan Revenue",sequenceNo:8,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""}]},Templates:{"Formula planning":[{fieldName:"Acty-Indep. FormPlng Temp",sequenceNo:1,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Formula planning",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Acty-Dep. Form.Plng Temp.",sequenceNo:2,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Formula planning",cardSeq:1,visibility:"Optional",value:""}],"Activity and Business Process Allocation":[{fieldName:"Acty-Indep. Alloc. Temp",sequenceNo:3,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Activity and Business Process Allocation",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Acty-Dep. Alloc. Template",sequenceNo:4,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Activity and Business Process Allocation",cardSeq:2,visibility:"Optional",value:""}],"Actual Statistical Key Figures":[{fieldName:"Templ.: Act. Stat. Key Figure",sequenceNo:5,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Actual Statistical Key Figures",cardSeq:3,visibility:"Optional",value:""},{fieldName:"Templ.: Act. Stat. Key Figure",sequenceNo:6,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Actual Statistical Key Figures",cardSeq:3,visibility:"Optional",value:""}],"Overhead rates":[{fieldName:"Costing Sheet",sequenceNo:7,fieldType:"Drop Down",maxLength:6,dataType:"String",viewName:"Templates",cardName:"Overhead rates",cardSeq:4,visibility:"Optional",value:""}]},Address:{"Address Data":[{fieldName:"Title",sequenceNo:1,fieldType:"Input",maxLength:15,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 1",sequenceNo:2,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 2",sequenceNo:3,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 3",sequenceNo:4,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 4",sequenceNo:5,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Street",sequenceNo:6,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Location",sequenceNo:7,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"District",sequenceNo:8,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Country/Reg",sequenceNo:9,fieldType:"Drop Down",maxLength:3,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Jurisdiction",sequenceNo:10,fieldType:"Drop Down",maxLength:15,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"PO Box",sequenceNo:11,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Postal Code",sequenceNo:12,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"PO Box Postcod",sequenceNo:13,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Region",sequenceNo:14,fieldType:"Drop Down",maxLength:3,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""}]},Communication:{"Communication Data":[{fieldName:"Language Key",sequenceNo:1,fieldType:"Drop Down",maxLength:2,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telephone 1",sequenceNo:2,fieldType:"Input",maxLength:16,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telephone 2",sequenceNo:3,fieldType:"Input",maxLength:16,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telebox number",sequenceNo:4,fieldType:"Input",maxLength:15,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telex number",sequenceNo:5,fieldType:"Input",maxLength:30,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Fax Number",sequenceNo:6,fieldType:"Input",maxLength:31,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Teletex number",sequenceNo:7,fieldType:"Input",maxLength:30,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Printer Destination",sequenceNo:8,fieldType:"Input",maxLength:4,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Data line",sequenceNo:9,fieldType:"Input",maxLength:14,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""}]}},{costCenter:"tuk2",validFrom:null,validTo:null,"Basic Data":{Names:[{fieldName:"Name",sequenceNo:1,fieldType:"Input",maxLength:20,dataType:"String",viewName:"Basic Data",cardName:"Names",cardSeq:1,visibility:"Optional",value:"FIRE"},{fieldName:"Description",sequenceNo:2,fieldType:"Input",maxLength:40,dataType:"String",viewName:"Basic Data",cardName:"Names",cardSeq:1,visibility:"Optional",value:"drink"}],"Basic Data":[{fieldName:"User Responsible",sequenceNo:3,fieldType:"Input",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:"a"},{fieldName:"Person Responsible",sequenceNo:4,fieldType:"Input",maxLength:20,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Department",sequenceNo:5,fieldType:"Input",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Cost Center Category",sequenceNo:6,fieldType:"Drop Down",maxLength:1,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:"f"},{fieldName:"Comp Code",sequenceNo:7,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:"g"},{fieldName:"Hierarchy area",sequenceNo:8,fieldType:"Drop Down",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:"h"},{fieldName:"Business Area",sequenceNo:9,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Functional Area",sequenceNo:10,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Currency",sequenceNo:11,fieldType:"",maxLength:5,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:"l"},{fieldName:"Profit Center",sequenceNo:12,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:"q"}]},Control:{Control:[{fieldName:"Record Quantity",sequenceNo:1,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:"w"},{fieldName:"Actual primary costs",sequenceNo:2,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:"e"},{fieldName:"  Plan primary costs",sequenceNo:3,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:"r"},{fieldName:" Commitment Update",sequenceNo:4,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:"t"},{fieldName:"Act. secondary costs",sequenceNo:5,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:"y"},{fieldName:"Plan Secondary Costs",sequenceNo:6,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:"u"},{fieldName:"Actual Revenue",sequenceNo:7,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:"i"},{fieldName:"Plan Revenue",sequenceNo:8,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:"o"}]},Templates:{"Formula planning":[{fieldName:"Acty-Indep. FormPlng Temp",sequenceNo:1,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Formula planning",cardSeq:1,visibility:"Optional",value:"p"},{fieldName:"Acty-Dep. Form.Plng Temp.",sequenceNo:2,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Formula planning",cardSeq:1,visibility:"Optional",value:"z"}],"Activity and Business Process Allocation":[{fieldName:"Acty-Indep. Alloc. Temp",sequenceNo:3,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Activity and Business Process Allocation",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Acty-Dep. Alloc. Template",sequenceNo:4,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Activity and Business Process Allocation",cardSeq:2,visibility:"Optional",value:""}],"Actual Statistical Key Figures":[{fieldName:"Templ.: Act. Stat. Key Figure",sequenceNo:5,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Actual Statistical Key Figures",cardSeq:3,visibility:"Optional",value:""},{fieldName:"Templ.: Act. Stat. Key Figure",sequenceNo:6,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Actual Statistical Key Figures",cardSeq:3,visibility:"Optional",value:""}],"Overhead rates":[{fieldName:"Costing Sheet",sequenceNo:7,fieldType:"Drop Down",maxLength:6,dataType:"String",viewName:"Templates",cardName:"Overhead rates",cardSeq:4,visibility:"Optional",value:"n"}]},Address:{"Address Data":[{fieldName:"Title",sequenceNo:1,fieldType:"Input",maxLength:15,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"mq"},{fieldName:"Name 1",sequenceNo:2,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"q"},{fieldName:"Name 2",sequenceNo:3,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"q"},{fieldName:"Name 3",sequenceNo:4,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"q"},{fieldName:"Name 4",sequenceNo:5,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"w"},{fieldName:"Street",sequenceNo:6,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"e"},{fieldName:"Location",sequenceNo:7,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"District",sequenceNo:8,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"ty"},{fieldName:"Country/Reg",sequenceNo:9,fieldType:"Drop Down",maxLength:3,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"u"},{fieldName:"Jurisdiction",sequenceNo:10,fieldType:"Drop Down",maxLength:15,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"e"},{fieldName:"PO Box",sequenceNo:11,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"ty"},{fieldName:"Postal Code",sequenceNo:12,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"r"},{fieldName:"PO Box Postcod",sequenceNo:13,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"f"},{fieldName:"Region",sequenceNo:14,fieldType:"Drop Down",maxLength:3,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:"df"}]},Communication:{"Communication Data":[{fieldName:"Language Key",sequenceNo:1,fieldType:"Drop Down",maxLength:2,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:"g"},{fieldName:"Telephone 1",sequenceNo:2,fieldType:"Input",maxLength:16,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:"fd"},{fieldName:"Telephone 2",sequenceNo:3,fieldType:"Input",maxLength:16,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telebox number",sequenceNo:4,fieldType:"Input",maxLength:15,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:"gd"},{fieldName:"Telex number",sequenceNo:5,fieldType:"Input",maxLength:30,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:"d"},{fieldName:"Fax Number",sequenceNo:6,fieldType:"Input",maxLength:31,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Teletex number",sequenceNo:7,fieldType:"Input",maxLength:30,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Printer Destination",sequenceNo:8,fieldType:"Input",maxLength:4,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Data line",sequenceNo:9,fieldType:"Input",maxLength:14,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:"f"}]}},{costCenter:"tuk3",validFrom:null,validTo:null,"Basic Data":{Names:[{fieldName:"Name",sequenceNo:1,fieldType:"Input",maxLength:20,dataType:"String",viewName:"Basic Data",cardName:"Names",cardSeq:1,visibility:"Optional",value:"CLOTH"},{fieldName:"Description",sequenceNo:2,fieldType:"Input",maxLength:40,dataType:"String",viewName:"Basic Data",cardName:"Names",cardSeq:1,visibility:"Optional",value:"wash"}],"Basic Data":[{fieldName:"User Responsible",sequenceNo:3,fieldType:"Input",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Person Responsible",sequenceNo:4,fieldType:"Input",maxLength:20,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Department",sequenceNo:5,fieldType:"Input",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Cost Center Category",sequenceNo:6,fieldType:"Drop Down",maxLength:1,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Comp Code",sequenceNo:7,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Hierarchy area",sequenceNo:8,fieldType:"Drop Down",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Business Area",sequenceNo:9,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Functional Area",sequenceNo:10,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Currency",sequenceNo:11,fieldType:"",maxLength:5,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Profit Center",sequenceNo:12,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""}]},Control:{Control:[{fieldName:"Record Quantity",sequenceNo:1,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Actual primary costs",sequenceNo:2,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"  Plan primary costs",sequenceNo:3,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:" Commitment Update",sequenceNo:4,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Act. secondary costs",sequenceNo:5,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Plan Secondary Costs",sequenceNo:6,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Actual Revenue",sequenceNo:7,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Plan Revenue",sequenceNo:8,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""}]},Templates:{"Formula planning":[{fieldName:"Acty-Indep. FormPlng Temp",sequenceNo:1,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Formula planning",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Acty-Dep. Form.Plng Temp.",sequenceNo:2,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Formula planning",cardSeq:1,visibility:"Optional",value:""}],"Activity and Business Process Allocation":[{fieldName:"Acty-Indep. Alloc. Temp",sequenceNo:3,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Activity and Business Process Allocation",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Acty-Dep. Alloc. Template",sequenceNo:4,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Activity and Business Process Allocation",cardSeq:2,visibility:"Optional",value:""}],"Actual Statistical Key Figures":[{fieldName:"Templ.: Act. Stat. Key Figure",sequenceNo:5,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Actual Statistical Key Figures",cardSeq:3,visibility:"Optional",value:""},{fieldName:"Templ.: Act. Stat. Key Figure",sequenceNo:6,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Actual Statistical Key Figures",cardSeq:3,visibility:"Optional",value:""}],"Overhead rates":[{fieldName:"Costing Sheet",sequenceNo:7,fieldType:"Drop Down",maxLength:6,dataType:"String",viewName:"Templates",cardName:"Overhead rates",cardSeq:4,visibility:"Optional",value:""}]},Address:{"Address Data":[{fieldName:"Title",sequenceNo:1,fieldType:"Input",maxLength:15,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 1",sequenceNo:2,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 2",sequenceNo:3,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 3",sequenceNo:4,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 4",sequenceNo:5,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Street",sequenceNo:6,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Location",sequenceNo:7,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"District",sequenceNo:8,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Country/Reg",sequenceNo:9,fieldType:"Drop Down",maxLength:3,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Jurisdiction",sequenceNo:10,fieldType:"Drop Down",maxLength:15,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"PO Box",sequenceNo:11,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Postal Code",sequenceNo:12,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"PO Box Postcod",sequenceNo:13,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Region",sequenceNo:14,fieldType:"Drop Down",maxLength:3,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""}]},Communication:{"Communication Data":[{fieldName:"Language Key",sequenceNo:1,fieldType:"Drop Down",maxLength:2,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telephone 1",sequenceNo:2,fieldType:"Input",maxLength:16,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telephone 2",sequenceNo:3,fieldType:"Input",maxLength:16,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telebox number",sequenceNo:4,fieldType:"Input",maxLength:15,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telex number",sequenceNo:5,fieldType:"Input",maxLength:30,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Fax Number",sequenceNo:6,fieldType:"Input",maxLength:31,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Teletex number",sequenceNo:7,fieldType:"Input",maxLength:30,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Printer Destination",sequenceNo:8,fieldType:"Input",maxLength:4,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Data line",sequenceNo:9,fieldType:"Input",maxLength:14,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""}]}},{costCenter:"tuk4",validFrom:null,validTo:null,"Basic Data":{Names:[{fieldName:"Name",sequenceNo:1,fieldType:"Input",maxLength:20,dataType:"String",viewName:"Basic Data",cardName:"Names",cardSeq:1,visibility:"Optional",value:"Fabric"},{fieldName:"Description",sequenceNo:2,fieldType:"Input",maxLength:40,dataType:"String",viewName:"Basic Data",cardName:"Names",cardSeq:1,visibility:"Optional",value:"stitch"}],"Basic Data":[{fieldName:"User Responsible",sequenceNo:3,fieldType:"Input",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Person Responsible",sequenceNo:4,fieldType:"Input",maxLength:20,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Department",sequenceNo:5,fieldType:"Input",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Cost Center Category",sequenceNo:6,fieldType:"Drop Down",maxLength:1,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Comp Code",sequenceNo:7,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Hierarchy area",sequenceNo:8,fieldType:"Drop Down",maxLength:12,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Business Area",sequenceNo:9,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Functional Area",sequenceNo:10,fieldType:"Drop Down",maxLength:4,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Currency",sequenceNo:11,fieldType:"",maxLength:5,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Profit Center",sequenceNo:12,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Basic Data",cardName:"Basic Data",cardSeq:2,visibility:"Optional",value:""}]},Control:{Control:[{fieldName:"Record Quantity",sequenceNo:1,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Actual primary costs",sequenceNo:2,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"  Plan primary costs",sequenceNo:3,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:" Commitment Update",sequenceNo:4,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Act. secondary costs",sequenceNo:5,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Plan Secondary Costs",sequenceNo:6,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Actual Revenue",sequenceNo:7,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Plan Revenue",sequenceNo:8,fieldType:"Radio Button",maxLength:1,dataType:"String",viewName:"Control",cardName:"Control",cardSeq:1,visibility:"Optional",value:""}]},Templates:{"Formula planning":[{fieldName:"Acty-Indep. FormPlng Temp",sequenceNo:1,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Formula planning",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Acty-Dep. Form.Plng Temp.",sequenceNo:2,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Formula planning",cardSeq:1,visibility:"Optional",value:""}],"Activity and Business Process Allocation":[{fieldName:"Acty-Indep. Alloc. Temp",sequenceNo:3,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Activity and Business Process Allocation",cardSeq:2,visibility:"Optional",value:""},{fieldName:"Acty-Dep. Alloc. Template",sequenceNo:4,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Activity and Business Process Allocation",cardSeq:2,visibility:"Optional",value:""}],"Actual Statistical Key Figures":[{fieldName:"Templ.: Act. Stat. Key Figure",sequenceNo:5,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Actual Statistical Key Figures",cardSeq:3,visibility:"Optional",value:""},{fieldName:"Templ.: Act. Stat. Key Figure",sequenceNo:6,fieldType:"Drop Down",maxLength:10,dataType:"String",viewName:"Templates",cardName:"Actual Statistical Key Figures",cardSeq:3,visibility:"Optional",value:""}],"Overhead rates":[{fieldName:"Costing Sheet",sequenceNo:7,fieldType:"Drop Down",maxLength:6,dataType:"String",viewName:"Templates",cardName:"Overhead rates",cardSeq:4,visibility:"Optional",value:""}]},Address:{"Address Data":[{fieldName:"Title",sequenceNo:1,fieldType:"Input",maxLength:15,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 1",sequenceNo:2,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 2",sequenceNo:3,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 3",sequenceNo:4,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Name 4",sequenceNo:5,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Street",sequenceNo:6,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Location",sequenceNo:7,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"District",sequenceNo:8,fieldType:"Input",maxLength:35,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Country/Reg",sequenceNo:9,fieldType:"Drop Down",maxLength:3,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Jurisdiction",sequenceNo:10,fieldType:"Drop Down",maxLength:15,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"PO Box",sequenceNo:11,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Postal Code",sequenceNo:12,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"PO Box Postcod",sequenceNo:13,fieldType:"Input",maxLength:10,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Region",sequenceNo:14,fieldType:"Drop Down",maxLength:3,dataType:"String",viewName:"Address",cardName:"Address Data",cardSeq:1,visibility:"Optional",value:""}]},Communication:{"Communication Data":[{fieldName:"Language Key",sequenceNo:1,fieldType:"Drop Down",maxLength:2,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telephone 1",sequenceNo:2,fieldType:"Input",maxLength:16,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telephone 2",sequenceNo:3,fieldType:"Input",maxLength:16,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telebox number",sequenceNo:4,fieldType:"Input",maxLength:15,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Telex number",sequenceNo:5,fieldType:"Input",maxLength:30,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Fax Number",sequenceNo:6,fieldType:"Input",maxLength:31,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Teletex number",sequenceNo:7,fieldType:"Input",maxLength:30,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Printer Destination",sequenceNo:8,fieldType:"Input",maxLength:4,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""},{fieldName:"Data line",sequenceNo:9,fieldType:"Input",maxLength:14,dataType:"String",viewName:"Communication",cardName:"Communication Data",cardSeq:1,visibility:"Optional",value:""}]}}]}};l.body.tableData.map((e,a)=>console.log("funncy",e.costCenter,a));const S=(c=l==null?void 0:l.body)==null?void 0:c.tableData.map(e=>{if(console.log("type",typeof e,e),typeof e=="object"){console.log("item",e);let a=Object.entries(e);console.log("aray",a,a.length);let d=[];for(let t=0;t<a.length;t++)console.log("binay",t,a[t],typeof a[t][1]),typeof a[t][1]=="object"&&a[t][1]!=null&&(console.log("binay p",a[t][1]),d.push(Object.entries(a[t][1])));return d}else return null});console.log("nihar");const m=(p=S.filter(e=>e!=null).map(e=>e.flat()).map(e=>(console.log("priti",e),e.map(a=>a[1]))).map(e=>e.flat()))==null?void 0:p.map((e,a)=>{var d,t,N,u,y;return console.log("initialRow",e,a),{id:a,costCenter:l.body.tableData[a].costCenter,controllingArea:l.body.controlingArea,description:(d=e==null?void 0:e.find(i=>(i==null?void 0:i.fieldName)==="Description"))==null?void 0:d.value,personResponsible:(t=e==null?void 0:e.find(i=>(i==null?void 0:i.fieldName)==="Person Responsible"))==null?void 0:t.value,companyCode:(N=e==null?void 0:e.find(i=>(i==null?void 0:i.fieldName)==="Comp Code"))==null?void 0:N.value,profitCenter:(u=e==null?void 0:e.find(i=>(i==null?void 0:i.fieldName)==="Profit Center"))==null?void 0:u.value,costCenterCategory:(y=e==null?void 0:e.find(i=>(i==null?void 0:i.fieldName)==="Cost Center Category"))==null?void 0:y.value,validFrom:l.body.tableData[a].validFrom,validTo:l.body.tableData[a].validTo}});console.log("walker",m);const q=[{field:"id",headerName:"",editable:!1,flex:1,renderCell:e=>n(C,{checked:e.value===!0})},{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"description",headerName:"Description",editable:!1,flex:1},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"costCenterCategory",headerName:"Cost Center Category",editable:!1,flex:1},{field:"validFrom",headerName:"Valid From",editable:!1,flex:1},{field:"validTo",headerName:"Valid To",editable:!1,flex:1}];return r("div",{children:[r("div",{style:{...w,backgroundColor:"#FAFCFF"},children:[n(o,{item:!0,sx:x,children:r(o,{item:!0,md:5,sx:{display:"flex"},children:[n(o,{children:n(O,{color:"primary","aria-label":"upload picture",component:"label",sx:A,children:n(L,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{s("/masterDataCockpit/materialMaster/materialSingle"),dispatch(clearPayload()),dispatch(clearOrgData())}})})}),r(o,{children:[n(g,{variant:"h3",children:n("strong",{children:"Create Multiple Materials"})}),n(g,{variant:"body2",color:"#777",children:"This view creates multiple material"})]})]})}),n(o,{item:!0,sx:{position:"relative"},children:n(b,{children:n(h,{isLoading:T,width:"100%",title:"Material Master List ("+m.length+")",rows:m,columns:q,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,callback_onRowSingleClick:e=>{console.log("paramss",e);const a=e.row.costCenter,d=l.body.tableData.find(t=>t.costCenter===a);console.log(d,"pppp"),s(`/masterDataCockpit/costCenter/createMultipleCostCenter/editMultipleCostCenter/${a}`,{state:{rowViewData:d,selectedRow:e.row}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})})]}),n(P,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(B,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:f,children:n(I,{variant:"contained",size:"small",sx:{...R},children:"Submit for Approval"})})})]})};export{_ as default};
