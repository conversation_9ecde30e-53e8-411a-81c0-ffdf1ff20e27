import{ah as te,ai as he,aj as be,r as m,j as g,h_ as ke,a as o,T as M,$ as y,B as Q,F as ne,i as se,hf as Be,E as Ce,gK as I,gM as ve,A as Ee,Z as Fe,P as qe,M as Ke,N as Je,O as ye,Q as G,S as Qe,f as me,g as Me,e as Xe,b as Ze,u as Ye,l as ce,k as De,w as D,iZ as s,hp as en,W as nn,hv as on,i8 as ln,g_ as de,aa as rn,ab as sn,G as z,he as dn,ms as le,jj as O,lQ as tn,mt as hn,hg as re}from"./index-fdfa25a0.js";import{d as bn}from"./Description-5b38f787.js";import{F as un}from"./FilterField-6f6e20f9.js";import{u as xn}from"./useMaterialFieldConfig-6dda1d2a.js";import{G as Ie}from"./GenericViewGeneral-4b82ad14.js";import gn from"./AdditionalData-a38f586a.js";import{m as an}from"./makeStyles-1dfd4db4.js";import"./useChangeLogUpdate-3699f77c.js";import"./AutoCompleteType-3a9c9c9d.js";import"./dayjs.min-774e293a.js";import"./AdapterDayjs-cd3745c6.js";import"./isBetween-fe8614a5.js";import"./DeleteOutline-1f72afa8.js";import"./useCustomDtCall-04c3c72a.js";var ue={},fn=he;Object.defineProperty(ue,"__esModule",{value:!0});var Re=ue.default=void 0,pn=fn(te()),Sn=be;Re=ue.default=(0,pn.default)((0,Sn.jsx)("path",{d:"M12 7V3H2v18h20V7zM6 19H4v-2h2zm0-4H4v-2h2zm0-4H4V9h2zm0-4H4V5h2zm4 12H8v-2h2zm0-4H8v-2h2zm0-4H8V9h2zm0-4H8V5h2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8zm-2-8h-2v2h2zm0 4h-2v2h2z"}),"Business");var xe={},Tn=he;Object.defineProperty(xe,"__esModule",{value:!0});var Pe=xe.default=void 0,An=Tn(te()),yn=be;Pe=xe.default=(0,An.default)((0,yn.jsx)("path",{d:"M20 2H4c-1 0-2 .9-2 2v3.01c0 .72.43 1.34 1 1.69V20c0 1.1 1.1 2 2 2h14c.9 0 2-.9 2-2V8.7c.57-.35 1-.97 1-1.69V4c0-1.1-1-2-2-2m-5 12H9v-2h6zm5-7H4V4l16-.02z"}),"Inventory");var ge={},In=he;Object.defineProperty(ge,"__esModule",{value:!0});var _e=ge.default=void 0,Nn=In(te()),ie=be;_e=ge.default=(0,Nn.default)([(0,ie.jsx)("path",{d:"m12 2-5.5 9h11z"},"0"),(0,ie.jsx)("circle",{cx:"17.5",cy:"17.5",r:"4.5"},"1"),(0,ie.jsx)("path",{d:"M3 13.5h8v8H3z"},"2")],"Category");const Ne=d=>{let S=(d==null?void 0:d.basicDataTabDetails)&&(Object==null?void 0:Object.entries(d==null?void 0:d.basicDataTabDetails));const[v,a]=m.useState([]);return m.useEffect(()=>{a(S==null?void 0:S.map(f=>{var w;return g(y,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ke},children:[o(y,{container:!0,children:o(M,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:f[0]})}),o(Q,{children:o(y,{container:!0,spacing:1,paddingBottom:1,children:(w=[...f[1]].filter(p=>p.visibility!="Hidden").sort((p,x)=>p.sequenceNo-x.sequenceNo))==null?void 0:w.map(p=>o(un,{disabled:d==null?void 0:d.disabled,field:p,dropDownData:d.dropDownData,materialID:d==null?void 0:d.materialID,viewName:d==null?void 0:d.activeViewTab,plantData:d==null?void 0:d.plantData},p.fieldName))})})]},f[0])}))},[d==null?void 0:d.basicDataTabDetails,d.activeViewTab,d==null?void 0:d.materialID]),o(ne,{children:v})},Cn=({materialID:d})=>{var f,w;const S=se(p=>{var x,i,k,U;return(U=(k=(i=(x=p.payload[d])==null?void 0:x.payloadData)==null?void 0:i.TaxData)==null?void 0:k.TaxData)==null?void 0:U.UniqueTaxDataSet}),[v,a]=m.useState((f=Be)==null?void 0:f.TAXDATA_LOADING);return m.useEffect(()=>{let p;return(S==null?void 0:S.length)===0&&(p=setTimeout(()=>{a(Ce.NO_DATA_AVAILABLE)},500)),()=>{p&&clearTimeout(p)}},[S]),!S||S.length===0?o(M,{sx:{textAlign:"center",marginTop:"10px"},children:v}):g(Me,{sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(w=I)==null?void 0:w.primary.white},defaultExpanded:!0,children:[o(Ee,{expandIcon:o(ve,{}),sx:{backgroundColor:I.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:I.hover.hoverbg}},children:o(M,{variant:"h6",sx:{fontWeight:"bold"},children:"Tax Classification"})}),o(me,{children:g(Fe,{component:qe,sx:{maxWidth:"100%"},children:[o(M,{variant:"h6",sx:{p:1,fontWeight:"bold",textAlign:"center"},children:"Tax Data"}),g(Ke,{children:[o(Je,{children:g(ye,{sx:{backgroundColor:I.primary.whiteSmoke},children:[o(G,{sx:{fontWeight:"bold"},children:"Country"}),o(G,{sx:{fontWeight:"bold"},children:"Tax Type"}),o(G,{sx:{fontWeight:"bold"},children:"Tax Class"}),o(G,{sx:{fontWeight:"bold"},children:"Description"})]})}),o(Qe,{children:S.map(({Country:p,TaxType:x,SelectedTaxClass:i},k)=>g(ye,{children:[o(G,{sx:{fontWeight:"bold"},children:p}),o(G,{sx:{fontWeight:"bold"},children:x}),o(G,{children:(i==null?void 0:i.TaxClass)||"N/A"}),o(G,{children:(i==null?void 0:i.TaxClassDesc)||"N/A"})]},`${p}-${x}-${k}`))})]})]})})]},"Tax_Classification_Static")},ee=({label:d,value:S,labelWidth:v="25%",centerWidth:a="5%",icon:f})=>g(de,{flexDirection:"row",alignItems:"center",children:[f&&o("div",{style:{marginRight:"10px"},children:f}),o(M,{variant:"body2",color:I.secondary.grey,style:{width:v},children:d}),o(M,{variant:"body2",fontWeight:"bold",sx:{width:a,textAlign:"center"},children:":"}),o(M,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:S||""})]}),Hn=()=>{var fe,pe,Se;const{fetchMaterialFieldConfig:d}=xn(),S=an(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),v=se(r=>r.payload),a=se(r=>r.tabsData.allTabsData),{t:f}=Xe(),w=Ze();S();const p=Ye(),x=ce(),i=p.state,{customError:k}=De(),U=[],[b,Le]=m.useState([]),[$e,Oe]=m.useState({}),[We,ae]=m.useState(!1),[Ge,we]=m.useState(""),[u,Ue]=m.useState(0),[He,oe]=m.useState(null);m.useEffect(()=>{Ve(),d(i==null?void 0:i.materialType.split(" - ")[0])},[]);const Ve=()=>{ae(!0);const r={materialNo:i==null?void 0:i.Number},e={"Basic Data":"Toclientdata",Sales:"Tosalesdata",Purchasing:"Toplantdata",Costing:"Toplantdata",Accounting:"Toaccountingdata",MRP:"Toplantdata",Warehouse:"Towarehousedata","Sales-Plant":"Tosalesdata","Work Scheduling":"Toplantdata"};function h(l){if(!Array.isArray(l))return{UniqueTaxDataSet:[]};const A=new Map;return l.forEach(T=>{const H=T==null?void 0:T.Depcountry;for(let R=1;R<=9;R++){const V=T[`TaxType${R}`],N=T[`Taxclass${R}`];if(V&&N!==void 0){const P=`${H}-${V}`,_=N==="1"?"Taxable":"Exempt";A.has(P)||A.set(P,{Country:H,TaxType:V,TaxClasses:[],SelectedTaxClass:{TaxClass:N,TaxClassDesc:_}});const t=A.get(P);t.TaxClasses.some(L=>L.TaxClass===N)||t.TaxClasses.push({TaxClass:N,TaxClassDesc:_}),t.SelectedTaxClass={TaxClass:N,TaxClassDesc:_}}}}),{UniqueTaxDataSet:Array.from(A.values())}}const Te=l=>{var A,T,H,R,V,N,P,_,t,W,L,j,B,F;if(l!=null&&l.body){const $=((A=l.body[0].ViewNames)==null?void 0:A.split(",").map(n=>n.trim()))||[],q=h((T=l==null?void 0:l.body[0])==null?void 0:T.Tocontroldata),K=[],c={};$.forEach(n=>{const E=e[n]??"";E!==""&&l.body[0][E]&&(K.push(n),c[n]=l.body[0][E])}),$.includes("Sales")&&(K.push("Sales-Plant"),c["Sales-Plant"]=l.body[0].Toplantdata),K.push("Additional Data"),Le(K),Oe(c);const C=(H=l==null?void 0:l.body[0])==null?void 0:H.Toclientdata;if(C){if(C.Pvalidfrom){const n=le(C.Pvalidfrom);C.Pvalidfrom=n?n.toISOString().split("T")[0]:""}if(C.Svalidfrom){const n=le(C.Svalidfrom);C.Svalidfrom=n?n.toISOString().split("T")[0]:""}}x(O({materialID:(R=l==null?void 0:l.body[0])==null?void 0:R.Material,viewID:"Basic Data",itemID:"basic",data:C})),x(O({materialID:(V=l==null?void 0:l.body[0])==null?void 0:V.Material,viewID:s.PURCHASING_GENERAL,itemID:s.PURCHASING_GENERAL,data:(N=l==null?void 0:l.body[0])==null?void 0:N.Toclientdata})),x(O({materialID:(P=l==null?void 0:l.body[0])==null?void 0:P.Material,viewID:s.SALES_GENERAL,itemID:s.SALES_GENERAL,data:(_=l==null?void 0:l.body[0])==null?void 0:_.Toclientdata})),x(tn({materialID:(t=l==null?void 0:l.body[0])==null?void 0:t.Material,data:(L=(W=l==null?void 0:l.body[0])==null?void 0:W.Touomdata)==null?void 0:L.map((n,E)=>{var J,Ae;return{...n,id:`${n.Material}-${E}`,uomId:`${n.Material}-${E}`,xValue:(n==null?void 0:n.Denominatr)||"",aUnit:(n==null?void 0:n.AltUnit)||"",measureUnitText:"",yValue:(n==null?void 0:n.Numerator)||"",eanUpc:(n==null?void 0:n.EanUpc)||"",eanCategory:(n==null?void 0:n.EanCat)||"",length:n==null?void 0:n.Length,width:n==null?void 0:n.Width,height:n==null?void 0:n.Height,unitsOfDimension:(n==null?void 0:n.UnitDim)||"",volume:(n==null?void 0:n.Volume)||"",volumeUnit:(n==null?void 0:n.Volumeunit)||"",grossWeight:(n==null?void 0:n.GrossWt)||"",netWeight:E===0&&(((Ae=(J=l==null?void 0:l.body[0])==null?void 0:J.Toclientdata)==null?void 0:Ae.NetWeight)||(n==null?void 0:n.NetWeight))||"",weightUnit:(n==null?void 0:n.UnitOfWt)||""}})})),x(hn({materialID:(j=l==null?void 0:l.body[0])==null?void 0:j.Material,data:(B=l==null?void 0:l.body[0])==null?void 0:B.Tomaterialdescription.map((n,E)=>({id:E+1,language:n==null?void 0:n.Langu,materialDescription:n==null?void 0:n.MatlDesc}))})),x(O({materialID:(F=l==null?void 0:l.body[0])==null?void 0:F.Material,viewID:"TaxData",itemID:"TaxData",data:q||{TaxData:{UniqueTaxDataSet:[]}}})),ae(!1),we("")}},Y=()=>{};D(`/${z}/data/displayLimitedMaterialData`,"post",Te,Y,r)},je=(r,e)=>{Ue(e),oe(null)},ze=(r,e,h)=>(Te,Y)=>{var N,P,_;if(h===s.COSTING){let t={materialNo:e==null?void 0:e.Material,plant:e==null?void 0:e.Plant};e==null||e.Plant;let W={materialNo:e==null?void 0:e.Material,valArea:e==null?void 0:e.Plant},L=`/${z}/${re.ACCORDION_API.PLANT}`,j=`/${z}/${re.ACCORDION_API.ACCOUNTING}`;D(L,"post",$=>{const q=$==null?void 0:$.body[0].Toplantdata[0];D(j,"post",C=>{var J;const n=(J=C==null?void 0:C.body[0])==null?void 0:J.Toaccountingdata[0],E={...q,...n};x(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:e==null?void 0:e.Plant,data:E}))},()=>{},W)},()=>{},t),oe(Y?r:null);return}let l={},A="",T="";h===s.PURCHASING||h===s.MRP||h===s.WORKSCHEDULING||h===s.SALES_PLANT?(l={materialNo:e==null?void 0:e.Material,plant:e==null?void 0:e.Plant},T=e==null?void 0:e.Plant,A=`/${z}/data/displayLimitedPlantData`):h===s.WAREHOUSE?(l={materialNo:e==null?void 0:e.Material,whNumber:e==null?void 0:e.WhseNo},T=e==null?void 0:e.WhseNo,A=`/${z}/${re.ACCORDION_API.WAREHOUSE}`):h===s.ACCOUNTING?(l={materialNo:e==null?void 0:e.Material,valArea:e==null?void 0:e.ValArea},T=e==null?void 0:e.ValArea,A=`/${z}/data/displayLimitedAccountingData`):h===s.SALES&&(l={materialNo:e==null?void 0:e.Material,salesOrg:e==null?void 0:e.SalesOrg,distChnl:e==null?void 0:e.DistrChan},T=`${e==null?void 0:e.SalesOrg}-${e==null?void 0:e.DistrChan}`,A=`/${z}/data/displayLimitedSalesData`);const H=t=>{var W,L,j,B,F,$;if(h===s.PURCHASING||h===s.MRP||h===s.WORKSCHEDULING||h===s.SALES_PLANT)x(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:e==null?void 0:e.Plant,data:{...t==null?void 0:t.body[0].Toplantdata[0],ProdProf:((W=t==null?void 0:t.body[0].Toplantdata[0])==null?void 0:W.Prodprof)||""}}));else if(h===s.ACCOUNTING)x(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:e==null?void 0:e.ValArea,data:(L=t==null?void 0:t.body[0])==null?void 0:L.Toaccountingdata[0]}));else if(h===s.WAREHOUSE)x(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:e==null?void 0:e.WhseNo,data:(j=t==null?void 0:t.body[0])==null?void 0:j.Towarehousedata[0]}));else if(h===s.SALES){if((F=(B=t==null?void 0:t.body[0])==null?void 0:B.Tosalesdata[0])!=null&&F.ValidFrom){const q=le(t.body[0].Tosalesdata[0].ValidFrom);t.body[0].Tosalesdata[0].ValidFrom=q?q.toISOString().split("T")[0]:""}x(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:`${e==null?void 0:e.SalesOrg}-${e==null?void 0:e.DistrChan}`,data:($=t==null?void 0:t.body[0])==null?void 0:$.Tosalesdata[0]}))}},R=t=>{k(t)};!((_=(P=(N=v==null?void 0:v[e==null?void 0:e.Material])==null?void 0:N.payloadData)==null?void 0:P[h])!=null&&_[T])&&D(A,"post",H,R,l),oe(Y?r:null)},X=a!=null&&a.hasOwnProperty(s.SALES_GENERAL)?Object.entries(a[s.SALES_GENERAL]):[],Z=a!=null&&a.hasOwnProperty(s.PURCHASING_GENERAL)?Object.entries(a[s.PURCHASING_GENERAL]):[];return g("div",{style:{backgroundColor:"#FAFCFF"},children:[o(y,{container:!0,sx:en,children:o(y,{item:!0,md:12,sx:{padding:"16px",display:"flex"},children:g(y,{md:9,sx:{display:"flex"},children:[o(nn,{color:"primary",sx:on,onClick:()=>w(-1),children:o(ln,{sx:{fontSize:"25px",color:"#000000"}})}),g(y,{item:!0,md:12,children:[o(M,{variant:"h3",children:o("strong",{children:f("Display Material")})}),o(M,{variant:"body2",color:"#777",children:f("This view displays the details of the materials")})]})]})})}),g(y,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",sx:{justifyContent:"space-between",alignItems:"center",paddingLeft:"29px",backgroundColor:I.basic.lighterGrey,borderRadius:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[g(de,{width:"48%",spacing:1,sx:{padding:"10px 15px",borderRight:"1px solid #eaedf0"},children:[o(y,{item:!0,children:o(ee,{label:f("Material"),value:(i==null?void 0:i.Number)||"",labelWidth:"35%",icon:o(Pe,{sx:{color:I.blue.indigo,fontSize:"20px"}})})}),o(y,{item:!0,children:o(ee,{label:f("Industry Sector"),value:(i==null?void 0:i.indSector)||"",labelWidth:"35%",icon:o(Re,{sx:{color:I.blue.indigo,fontSize:"20px"}})})})]}),g(de,{width:"48%",spacing:1,marginRight:"-10%",sx:{padding:"10px 15px"},children:[o(y,{item:!0,children:o(ee,{label:f("Material Type"),value:(i==null?void 0:i.materialType)||"",labelWidth:"35%",icon:o(_e,{sx:{color:I.blue.indigo,fontSize:"20px"}})})}),o(y,{item:!0,children:o(ee,{label:f("Material Description"),value:(i==null?void 0:i.materialDesc)||"",labelWidth:"35%",icon:o(bn,{sx:{color:I.blue.indigo,fontSize:"20px"}})})})]})]}),o(y,{children:a&&b.length>0?o(Q,{sx:{marginTop:"30px",border:"1px solid #e0e0e0",padding:"16px",background:I.primary.white},children:g(Q,{sx:{marginTop:"-10px",marginLeft:"5px"},children:[o(rn,{value:u,onChange:je,"aria-label":"material tabs",sx:{top:100,zIndex:1e3,background:"#fafcff",marginLeft:"20px",marginBottom:"-20px"},children:b.map((r,e)=>o(sn,{label:f(r)},e))}),o(Q,{sx:{padding:2,marginTop:2},children:b[u]==="Basic Data"&&v?o(Ne,{disabled:!0,materialID:i==null?void 0:i.Number,dropDownData:U,basicDataTabDetails:a["Basic Data"],activeViewTab:"Basic Data",plantData:"basic"}):b[u]==="Additional Data"?o(gn,{disableCheck:!0,materialID:i==null?void 0:i.Number}):g(ne,{children:[b[u]===s.SALES&&g(ne,{children:[o(Cn,{materialID:i==null?void 0:i.Number}),(X==null?void 0:X.length)>0&&o(Ie,{materialID:i==null?void 0:i.Number,GeneralFields:X,disabled:!0,dropDownData:U,viewName:(fe=s)==null?void 0:fe.SALES_GENERAL})]}),b[u]===s.PURCHASING&&g(ne,{children:[" ",(Z==null?void 0:Z.length)>0&&o(Ie,{materialID:i==null?void 0:i.Number,GeneralFields:Z,disabled:!0,dropDownData:U,viewName:(pe=s)==null?void 0:pe.PURCHASING_GENERAL})]}),(Se=$e[b[u]])==null?void 0:Se.map((r,e)=>g(Me,{sx:{marginBottom:"20px",boxShadow:3},expanded:He===e,onChange:ze(e,r,b[u]),children:[o(Ee,{expandIcon:o(ve,{}),sx:{backgroundColor:"#f5f5f5",borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:"#e0e0e0"}},children:o(M,{variant:"h6",sx:{fontWeight:"bold"},children:b[u]===s.PURCHASING||b[u]===s.COSTING||b[u]===s.MRP||b[u]===s.WORKSCHEDULING||b[u]===s.SALES_PLANT?`Plant - ${r==null?void 0:r.Plant}`:b[u]===s.SALES?`Sales Org - ${r==null?void 0:r.SalesOrg} ,  Distribution Channel - ${r==null?void 0:r.DistrChan}`:b[u]===s.ACCOUNTING?`Plant - ${r==null?void 0:r.ValArea}`:b[u]===s.WAREHOUSE?`Warehouse - ${r==null?void 0:r.WhseNo}`:`${r==null?void 0:r.Material}`})}),v&&o(me,{sx:{padding:"16px"},children:o(M,{sx:{fontSize:"0.875rem",color:"#555"},children:o(Ne,{disabled:!0,materialID:i==null?void 0:i.Number,dropDownData:U,basicDataTabDetails:a[b[u]],activeViewTab:b[u],plantData:b[u]==="Sales"?`${r==null?void 0:r.SalesOrg}-${r==null?void 0:r.DistrChan}`:b[u]==="Purchasing"?`${r==null?void 0:r.Plant}`:b[u]==="Accounting"?`${r==null?void 0:r.ValArea}`:b[u]==="Warehouse"?`${r==null?void 0:r.WhseNo}`:`${r==null?void 0:r.Plant}`})})})]},e))]})})]})}):o(Q,{sx:{marginTop:"30px",border:`1px solid ${I.secondary.grey}`,padding:"16px",background:`${I.primary.white}`,textAlign:"center"},children:o("span",{children:Ce.NO_DATA_AVAILABLE})})}),o(dn,{blurLoading:We,loaderMessage:Ge})]})};export{Hn as default};
