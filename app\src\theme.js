import { createTheme } from "@mui/material";
import { makeStyles } from "@mui/styles";

export const themesPalette = new Map([
  [
    "blueTheme",
    {
      themeKey: "blueTheme",
      themeLabel: "Admiral Blue",
      primary: {
        main: "#3026B9",
        light: "#E8E7FA",
      },
      secondary: {
        main: "#082940",
        light: "#082940",
      },
      background: {
        default: "#FAFAFF",
        paper: "#FFFFFF",
      },
      text: {
        primary: "#1D1D11",
        secondary: "#000000",
      },
      gradient: {
        default: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      },
    },
  ],
  [
    "redTheme",
    {
      themeKey: "redTheme",
      themeLabel: "Scarlet Red",
      primary: {
        main: "#b9262b",
        light: "#fae7e9",
      },
      secondary: {
        main: "#40080c",
        light: "#40080c",
      },
      background: {
        default: "#fffafa",
        paper: "#FFFFFF",
      },
      text: {
        primary: "#1D1D11",
        secondary: "#000000",
      },
      gradient: {
        default: "linear-gradient(135deg,rgb(214, 110, 141) 0%,rgb(224, 14, 95) 100%)",
      },
    },
  ],
  [
    "darkTheme",
    {
      themeKey: "darkTheme",
      themeLabel: "Midnight Grey",
      primary: {
        main: "#969696",
        light: "#edebeb",
      },
      secondary: {
        main: "#212121",
        light: "#212121",
      },
      background: {
        default: "#fffafa",
        paper: "#FFFFFF",
      },
      text: {
        primary: "#1D1D11",
        secondary: "#000000",
      },
      gradient: {
        default: "linear-gradient(135deg,rgb(157, 157, 163) 0%,rgb(93, 92, 94) 100%)",
      },
    },
  ],
  [
    "greenTheme",
    {
      themeKey: "greenTheme",
      themeLabel: "Forest Green",
      primary: {
        main: "#35b926",
        light: "#cdf7da",
      },
      secondary: {
        main: "#084009",
        light: "#084009",
      },
      background: {
        default: "#fafffb",
        paper: "#FFFFFF",
      },
      text: {
        primary: "#1D1D11",
        secondary: "#000000",
      },
      gradient: {
        default: "linear-gradient(135deg,rgb(24, 143, 60) 0%,rgb(183, 240, 196) 100%)",
      },
    },
  ],
]);

export const customTheme = (theme) =>
  createTheme({
    palette: {
      primary: {
        main: theme.primary.main,
        light: theme.primary.light,
      },
      secondary: {
        main: theme.secondary.main,
        light: theme.secondary.light,
      },
      background: {
        default: theme.background.default,
        paper: theme.background.paper,
      },
      positiveAccent: {
        fontColor: "#073323",
        primary: "#61DAAE",
        secondary: "#27CF92",
      },
      negativeAccesnt: {
        fontColor: "#431919",
        primary: "#ED8A88",
        secondary: "#ED4F4C",
      },
      grey: {
        0: "#FFFFFF",
        10: "#F5F5F5",
        50: "#EEEEEE",
        100: "#E0E0E0",
        200: "#BDBDBD",
        300: "#9E9E9E",
        400: "#757575",
        500: "#616161",
        600: "#3F3F3F",
        700: "#424242",
        800: "#323232",
        900: "#000000",
      },
      text: {
        primary: theme.text.primary,
        secondary: theme.text.secondary,
      },
      gradient: {
        default: theme.gradient.default,
      },
    },
    typography: {
      h3: { fontSize: "20px", fontWeight: "600", color: "#1d1d1d" },
      h4: { fontSize: "18px", fontWeight: "600", color: "#1d1d1d" },
      h5: { fontSize: "16px", fontWeight: "600", color: "#1d1d1d" },
      h6: { fontSize: "14px", fontWeight: "600", color: "#1d1d1d" },
      body1: { fontSize: "14px" },
      body2: { fontSize: "12px" },
      caption: { fontSize: "10px" },
    },
    shape: {
      borderRadius: 4,
    },
    components: {
      MuiButtonBase: {
        defaultProps: {
          disableRipple: true,
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            fontWeight: 400,
            textTransform: "none",
            boxShadow: "none",
            "&.Mui-disabled": {
              backgroundColor: theme.grey?.[50] || "#f0f0f0",
              color: theme.text?.secondary || "#888",
            },
          },
          containedPrimary: {
            backgroundColor: theme.primary.main,
            "&:hover": {
              backgroundColor: theme.primary.main,
              boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
            },
          },
          containedSecondary: {
            backgroundColor: theme.primary.dark || theme.primary.main,
            color: theme.primary.main,
            "&:hover": {
              backgroundColor: theme.primary.dark || theme.primary.main,
              boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
            },
          },
          sizeSmall: {
            height: "2rem",
            fontSize: "14px",
          },
          sizeMedium: {
            height: "2.25rem",
            fontSize: "14px",
          },
          sizeLarge: {
            height: "2.5rem",
            fontSize: "16px",
          },
        },
      },
    },
  });

export const useCustomStyles = makeStyles((theme) => {
  return {
    customButton: {
      fontWeight: "400 !important",
      boxShadow: "none !important",
      "& .MuiButton-sizeSmall": {
        height: `${theme.spacing(4)} !important`,
        fontSize: "14 !important",
      },
      "& .MuiButton-sizeMedium": {
        height: theme.spacing(4.5),
        fontSize: 14,
      },
      "& .MuiButton-sizeLarge": {
        height: theme.spacing(5),
        fontSize: 16,
      },
      "&.MuiButton-containedSecondary": {
        color: theme.palette.primary.main,
        backgroundColor: theme.palette.primary.dark,
        "&:hover": {
          backgroundColor: theme.palette.primary.dark,
          boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
        },
      },
      "&.MuiButton-containedPrimary": {
        backgroundColor: `${theme.palette.primary.main} !important`,
        color: "#fff",
        "&:hover": {
          backgroundColor: theme.palette.primary.dark || theme.palette.primary.main,
          boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
        },
      },

      "&.Mui-disabled": {
        backgroundColor: `${theme.palette.grey[50]} !important`,
      },
      "&.MuiButton-containedAction": {
        color: theme.palette.primary.main,
        backgroundColor: theme.palette.primary.light,
        "&:hover": {
          backgroundColor: theme.palette.primary.light,
          boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
        },
      },
    },
    customAutoComplete: {
      "& .MuiAutocomplete-option, .MuiAutocomplete-popper": {
        zIndex: 4,
      },
      "& .MuiOutlinedInput-input": {
        zIndex: 2,
        padding: `${theme.spacing(0, 0, 0, 1)} !important`,
      },
      "& .MuiInputBase-sizeSmall": {
        height: theme.spacing(4),
        fontSize: 14,
      },
      "& .MuiInputBase-sizeMedium": {
        height: theme.spacing(5),
        fontSize: 14,
      },
      "& .MuiInputBase-sizeLarge": {
        height: theme.spacing(6),
        fontSize: 16,
      },
      "& .MuiAutocomplete-endAdornment": {
        zIndex: 2,
        "& .MuiIconButton-root": {
          background: "transparent",
          color: "#495057",
        },
      },
      "&. MuiOutlinedInput-notchedOutline": {
        zIndex: "1",
        border: "1px solid #ced4da",
        "& legend": {
          width: "0%",
        },
      },
    },
    option: {
      "& .MuiAutocomplete-option, .MuiAutocomplete-popper": {
        zIndex: 4,
      },
      zIndex: 4,
    },
  };
});
