import{b as pe,l as he,r,u as ue,i as g,ig as fe,j as n,$ as i,hp as ge,a as t,i7 as me,T as h,ia as xe,B as k,g_ as G,id as Ce,ie as ye,ic as be,h_ as ve,gZ as Se,hE as H,a1 as u,gW as y,P as R,w as I,hL as U,ix as P,hn as ke,W as Pe,hv as Be,i8 as Ae,gU as Z,i9 as Ee,ib as Ne,hP as J}from"./index-fdfa25a0.js";import{E as je}from"./EditFieldForMassProfitCenter-d87ebb29.js";import{C as De}from"./CompCodeProfitCenter-a8989840.js";const _e=()=>{var W,q;const N=pe(),c=he();r.useState({});const[j,D]=r.useState(0),[f,K]=r.useState(!1),[we,Q]=r.useState(!0);r.useState(0);const[X,Y]=r.useState([]),[d,b]=r.useState(0),[ee,w]=r.useState(!1);r.useState([]);const[$,te]=r.useState([]),[ae,F]=r.useState(!1),p=ue(),oe=g(e=>e.profitCenter.profitCenterCompCodes);console.log("location",p.state),g(e=>e.initialData.EditMultipleMaterial),g(e=>e.initialData.MultipleMaterial);const m=p.state.tabsData.viewData,l=p.state.rowData,ie=p.state.requestNumber,v=m["Comp Codes"]["Company Code Assignment for Profit Center"];g(e=>e.payload);let B=g(e=>e.edit.payload);console.log(B,"singlePCPayloadAfterChange");let A=g(e=>e.profitCenter.requiredFields);console.log(A,"required_field_for_data");const ne=()=>{const e=o=>{c(J({keyName:"ProfitCtrGroup",data:o.body}))},a=o=>{console.log(o)};I(`/${U}/data/getProfitCtrGroup?controllingArea=${l==null?void 0:l.controllingArea}`,"get",e,a)},le=e=>{console.log("compcode",e);const a=s=>{console.log("value",s),c(J({keyName:"Region",data:s.body}))},o=s=>{console.log(s,"error in dojax")};I(`/${U}/data/getRegionBasedOnCountry?country=${e}`,"get",a,o)};r.useEffect(()=>{c(fe(z))},[]),r.useEffect(()=>{var e;Y(_.zip(v[0].value,v[1].value,v[2].value).map((a,o)=>{var s,S,C,O;return{id:o,companyCodes:(s=a[0])!=null&&s.split("$$$")[0]?(S=a[0])==null?void 0:S.split("$$$")[0]:"",companyName:(C=a[1])!=null&&C.split("$$$")[0]?(O=a[1])==null?void 0:O.split("$$$")[0]:"",assigned:a[2]?a[2]:""}})),le((e=m.Address["Address Data"].find(a=>(a==null?void 0:a.fieldName)==="Country/Reg."))==null?void 0:e.value),ne()},[]);const re=()=>{K(!0),Q(!1)},M=()=>{const e=V();f?e?(b(a=>a-1),c(P())):L():(b(a=>a-1),c(P()))},T=()=>{const e=V();f?e?(b(a=>a+1),c(P())):L():(b(a=>a+1),c(P()))},L=()=>{F(!0)};console.log("datafromprevious",m,v);const x=Object.entries(m).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]);console.log("tabsArray",x,d);const E=Object.entries(m).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),z={};E.map(e=>{e.forEach((a,o)=>{a.forEach((s,S)=>{S!==0&&s.forEach(C=>{z[C.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=C.value})})})});const se=e=>{w(e)},de=()=>{w(!0)};console.log(B,A,"========deeced"),console.log(d,"activeStep");const V=()=>ke(B,A,te),ce=()=>{F(!1)};return n("div",{children:[n(i,{container:!0,style:{...ge,backgroundColor:"#FAFCFF"},children:[$.length!=0&&t(me,{openSnackBar:ae,alertMsg:"Please fill the following Field: "+$.join(", "),handleSnackBarClose:ce}),n(i,{sx:{width:"inherit"},children:[n(i,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[t(i,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:t(Pe,{color:"primary","aria-label":"upload picture",component:"label",sx:Be,children:t(Ae,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{N("/masterDataCockpit/profitCenter/createMultipleProfitCenter")}})})}),n(i,{md:10,children:[t(h,{variant:"h3",children:n("strong",{children:["Multiple Profit Centers: ",l.profitCenter," "]})}),t(h,{variant:"body2",color:"#777",children:"This view dispalys detail of Multiple Profit Centers"})]}),(W=p==null?void 0:p.state)!=null&&W.requestNumber?t(i,{md:.5,sx:{display:"flex",justifyContent:"flex-end"},children:t(u,{variant:"outlined",size:"small",sx:Z,onClick:de,title:"Change Log",children:t(Ee,{sx:{padding:"2px"},fontSize:"small"})})}):t(i,{md:.5,sx:{display:"flex",justifyContent:"flex-end"}}),ee&&t(xe,{open:!0,closeModal:se,requestId:ie,requestType:"Mass",pageName:"profitCenter",controllingArea:l.controllingArea,centerName:l.costCenter}),f?"":t(i,{md:1.5,sx:{display:"flex",justifyContent:"flex-end"},children:t(i,{item:!0,children:n(u,{variant:"outlined",size:"small",sx:Z,onClick:re,children:["Change",t(Ne,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),t(i,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:n(k,{width:"70%",sx:{marginLeft:"40px"},children:[t(i,{item:!0,sx:{paddingTop:"2px !important"},children:n(G,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(h,{variant:"body2",color:"#777",children:"Profit Center"})}),n(h,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",l==null?void 0:l.profitCenter]})]})}),t(i,{item:!0,sx:{paddingTop:"2px !important"},children:n(G,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(h,{variant:"body2",color:"#777",children:"Controlling Area"})}),n(h,{variant:"body2",fontWeight:"bold",children:[": ",l==null?void 0:l.controllingArea]})]})})]})}),n(i,{container:!0,style:{padding:"16px"},children:[t(be,{activeStep:d,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:x.map((e,a)=>t(Ce,{children:t(ye,{sx:{fontWeight:"700"},children:e})},e))}),t(i,{container:!0,children:E&&((q=E[d])==null?void 0:q.map((e,a)=>d===2?t(De,{compCodesTabDetails:oe,displayCompCode:X}):t(k,{sx:{width:"100%"},children:n(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ve},children:[t(i,{container:!0,children:t(h,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),t(k,{children:t(k,{sx:{width:"100%"},children:t(Se,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:t(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(o=>(console.log("inneritem",o),t(je,{activeTabIndex:d,fieldGroup:e[0],selectedRowData:l.profitCenter,pcTabs:x,label:o.fieldName,value:o.value,length:o.maxLength,visibility:o.visibility,onSave:s=>handleFieldSave(o.fieldName,s),isEditMode:f,type:o.fieldType,field:o})))})})})})]})},a)))})]})]})]}),f?t(R,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(H,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:j,onChange:e=>{D(e)},children:[t(u,{size:"small",sx:{...y,mr:1},variant:"contained",onClick:()=>{N("/masterDataCockpit/profitCenter/createMultipleProfitCenter")},children:"Save"}),t(u,{variant:"contained",size:"small",sx:{...y,mr:1},onClick:M,disabled:d===0,children:"Back"}),t(u,{variant:"contained",size:"small",sx:{...y,mr:1},onClick:T,disabled:d===x.length-1,children:"Next"})]})}):"",f?"":t(R,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(H,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:j,onChange:e=>{D(e)},children:[t(u,{variant:"contained",size:"small",sx:{...y,mr:1},onClick:M,disabled:d===0,children:"Back"}),t(u,{variant:"contained",size:"small",sx:{...y,mr:1},onClick:T,disabled:d===x.length-1,children:"Next"})]})})]})};export{_e as default};
