import{r as d,b as Tt,l as Dt,u as Ft,cK as Ds,i as Z,hZ as wt,j as p,fV as te,h1 as ne,a as n,fH as re,hl as It,hp as Nt,$ as E,W as x,hv as Bt,i8 as kt,T as g,h as Pt,kf as Et,F as _,fX as ae,a0 as Fs,g_ as le,B as ie,i6 as qt,hC as be,fh as $t,h$ as Lt,i0 as Ot,i1 as _t,i2 as zt,i3 as jt,i4 as Rt,i5 as Vt,hd as ce,a1 as T,hD as Ut,hE as Wt,gU as de,gW as L,P as Jt,ii as Kt,ij as Xt,w as D,hK as I,ih as ws,ik as Se,hI as Me,gQ as Is,a3 as Ns,J as Ht,i7 as Gt,h8 as Yt,ho as Qt,hY as Zt,hP as xt,t7 as en}from"./index-fdfa25a0.js";const ln=()=>{var Ue,We;const[Te,z]=d.useState(!0),[Bs,S]=d.useState(!1),[ks,on]=d.useState("1"),[V,Ps]=d.useState([]);d.useState([]);const ue=Tt(),De=Dt(),r=Ft().state,[Fe,m]=d.useState(""),[Es,pe]=d.useState(!1),[tn,f]=d.useState(!1),[qs,C]=d.useState(!1),[$s,v]=d.useState(!1),[nn,y]=d.useState(!0),[Ls,A]=d.useState(!1),[Os,we]=d.useState(!1),[_s,Ie]=d.useState(!1),[zs,Ne]=d.useState(!1),[Be,ke]=d.useState(!0);Ds.useState(0);const[he,Pe]=d.useState("");Ds.useState({});const[Ce,js]=d.useState([]),[ge,Rs]=d.useState([]),[Vs,Ee]=d.useState(!1),[me,K]=d.useState(!0),[qe,Us]=d.useState(!0),[ee,Ws]=d.useState(""),[X,Js]=d.useState([]),[fe,Ks]=d.useState({}),[Xs,Hs]=d.useState([]),[Gs,ve]=d.useState(!1),[oe,$e]=d.useState([]),[Ys,ye]=d.useState(!1),k=()=>{pe(!0)},Qs=()=>{Ys?(pe(!1),ye(!1)):(pe(!1),ue("/masterDataCockpit/costCenter"))},Zs=()=>{Ee(!0)},Le=()=>{Ee(!1)},F=Z(s=>s.costCenter.MultipleCostCenterData);console.log(F,"MultipleCostCenter");const U=Z(s=>s.appSettings);console.log("massCostCenterRowData",r);let a=Z(s=>s.userManagement.taskData),o=Z(s=>s.userManagement.userData),xs=Z(s=>{var l;return(l=s.userManagement.entitiesAndActivities)==null?void 0:l["Cost Center"]});const et=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:s=>p(_,{children:[n(Kt,{index:s.row.id,name:s.row.docName}),n(Xt,{index:s.row.id,name:s.row.docName})]})}],ot=s=>{console.log("selected",s),Js(s),s.length>0?(K(!0),console.log("selectedIds1",s)):K(!1),console.log("selectedIds",s),Ps(s)},st=()=>{var i,e,b,M;z(!0);let s={};(a==null?void 0:a.processDesc)==="Mass Change"?s={massCreationId:"",massChangeId:a!=null&&a.subject?(i=a==null?void 0:a.subject)==null?void 0:i.slice(3):r==null?void 0:r.requestId.slice(3),screenName:"Change"}:(a==null?void 0:a.processDesc)==="Mass Create"?s={massCreationId:a!=null&&a.subject?(e=a==null?void 0:a.subject)==null?void 0:e.slice(3):r==null?void 0:r.requestId.slice(3),massChangeId:"",screenName:"Create"}:(r==null?void 0:r.requestType)==="Mass Create"?s={massCreationId:(b=r==null?void 0:r.requestId)==null?void 0:b.slice(3),massChangeId:"",screenName:"Create"}:(r==null?void 0:r.requestType)==="Mass Change"&&(s={massCreationId:"",massChangeId:(M=r==null?void 0:r.requestId)==null?void 0:M.slice(3),screenName:"Change"});const l=B=>{z(!1),De(Zt(B==null?void 0:B.body))},t=B=>{console.log(B)};D(`/${I}/data/displayMassCostCenter`,"post",l,t,s)},q=()=>{Ie(!1)},[Ae,tt]=d.useState(0),nt=(s,l)=>{const t=e=>{De(xt({keyName:s,data:e.body})),tt(b=>b+1)},i=e=>{console.log(e)};D(`/${I}/data/${l}`,"get",t,i)},rt=()=>{var s,l;(l=(s=ws)==null?void 0:s.costCenter)==null||l.map(t=>{nt(t==null?void 0:t.keyName,t==null?void 0:t.endPoint)})},at=()=>{var s,l;Ae==((l=(s=ws)==null?void 0:s.costCenter)==null?void 0:l.length)?(z(!1),console.log("apiCount",Ae)):z(!0)};d.useEffect(()=>{Ws(wt("CC"))},[]),d.useEffect(()=>{at()},[Ae]),d.useEffect(()=>{rt()},[]),d.useEffect(()=>{if((F==null?void 0:F.length)===0)st();else return},[]),console.log("newPayload",fe,X),d.useEffect(()=>{const s={};let l=[];console.log("userdata",o==null?void 0:o.role,F),(o==null?void 0:o.role)=="MDM Steward"||(o==null?void 0:o.role)=="Approver"?(console.log("userrole",o==null?void 0:o.role,F),l=F):l=F==null?void 0:F.filter((t,i)=>X==null?void 0:X.includes(i)),l.forEach(t=>{(t==null?void 0:t.controllingArea)in s?s[t.controllingArea].push(t):s[t.controllingArea]=[t]}),Ks(s),console.log("temparray",l,s)},[F,X]);const j=F==null?void 0:F.map((s,l)=>{var e,b,M,B,W,u,N,$,R,H,G,Y,Q;const t=s,i=((e=s==null?void 0:s.viewData)==null?void 0:e["Basic Data"])||{};return{id:l,costCenter:t.costCenter,controllingArea:t.controllingArea,name:((M=(b=i.Names)==null?void 0:b.find(h=>(h==null?void 0:h.fieldName)==="Name"))==null?void 0:M.value)||"",description:((W=(B=i.Names)==null?void 0:B.find(h=>(h==null?void 0:h.fieldName)==="Description"))==null?void 0:W.value)||"",personResponsible:((N=(u=i["Basic Data"])==null?void 0:u.find(h=>(h==null?void 0:h.fieldName)==="Person Responsible"))==null?void 0:N.value)||"",companyCode:((R=($=i["Basic Data"])==null?void 0:$.find(h=>(h==null?void 0:h.fieldName)==="Company Code"))==null?void 0:R.value)||"",profitCenter:((G=(H=i["Basic Data"])==null?void 0:H.find(h=>(h==null?void 0:h.fieldName)==="Profit Center"))==null?void 0:G.value)||"",costCenterCategory:((Q=(Y=i["Basic Data"])==null?void 0:Y.find(h=>(h==null?void 0:h.fieldName)==="Cost Center Category"))==null?void 0:Q.value)||"",validFrom:t.validFrom,validTo:t.validTo}}),lt=[{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1,renderCell:s=>{const l=oe.find(t=>t.costCenter===s.value);return console.log(l,"isDirectMatch"),console.log(s,"params"),l&&l.code===400?n(g,{sx:{fontSize:"12px",color:"red"},children:s.value}):n(g,{sx:{fontSize:"12px"},children:s.value})}},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"name",headerName:"Name",editable:!1,flex:1,renderCell:s=>{const l=Xs.includes(s.row.profitCenterName);return n(g,{sx:{fontSize:"12px",color:l?"red":"inherit"},children:s.value})}},{field:"description",headerName:"Description",editable:!1,flex:1},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"costCenterCategory",headerName:"Cost Center Category",editable:!1,flex:1},{field:"validFrom",headerName:"Valid From",editable:!1,flex:1,renderCell:s=>n(g,{sx:{fontSize:"12px"},children:ce(s.row.validFrom).format(U==null?void 0:U.dateFormat)})},{field:"validTo",headerName:"Valid To",editable:!1,flex:1,renderCell:s=>n(g,{sx:{fontSize:"12px"},children:ce(s.row.validTo).format(U==null?void 0:U.dateFormat)})}],c=(s,l)=>{const t=s==null?void 0:s.find(i=>(i==null?void 0:i.fieldName)===l);return t?t.value:""},it=()=>{let s=a!=null&&a.subject?a==null?void 0:a.subject:r==null?void 0:r.requestId,l=t=>{var i=[];t.documentDetailDtoList.forEach(e=>{var b={id:e.documentId,docType:e.fileType,docName:e.fileName,uploadedOn:ce(e.docCreationDate).format(U.date),uploadedBy:e.createdBy};i.push(b)}),js(i)};D(`/${Se}/documentManagement/getDocByRequestId/${s}`,"get",l)},ct=()=>{let s=a!=null&&a.subject?a==null?void 0:a.subject:r==null?void 0:r.requestId,l=i=>{console.log("commentsdata",i);var e=[];i.body.forEach(b=>{var M={id:b.requestId,comment:b.comment,user:b.createdByUser,createdAt:b.updatedAt};e.push(M)}),Rs(e),console.log("commentrows",e)},t=i=>{console.log(i)};D(`/${I}/activitylog/fetchTaskDetailsForRequestId?requestId=${s}`,"get",l,t)};d.useEffect(()=>{it(),ct()},[]);var w=(Ue=Object.keys(fe))==null?void 0:Ue.map(s=>{var l,t,i;return console.log("payloadmapping",s),{TaskId:a!=null&&a.taskId?a==null?void 0:a.taskId:"",CostCenterHeaderID:(l=F[0])==null?void 0:l.costCenterHeaderId,ControllingArea:s??"",Testrun:me,Action:(a==null?void 0:a.processDesc)==="Mass Create"?"I":(a==null?void 0:a.processDesc)==="Mass Change"||(r==null?void 0:r.requestType)==="Mass Change"?"U":(r==null?void 0:r.requestType)==="Mass Create"?"I":"",ReqCreatedBy:o!=null&&o.user_id?o==null?void 0:o.user_id:"",ReqCreatedOn:o!=null&&o.createdOn?"/Date("+(o==null?void 0:o.createdOn)+")/":"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",Remarks:he,MassCreationId:(a==null?void 0:a.processDesc)==="Mass Create"?(t=a==null?void 0:a.subject)==null?void 0:t.slice(3):"",MassEditId:(a==null?void 0:a.processDesc)==="Mass Change"?(i=a==null?void 0:a.subject)==null?void 0:i.slice(3):r==null?void 0:r.requestId.slice(3),MassDeleteId:"",RequestType:"",MassRequestStatus:"",Toitem:fe[s].map(e=>{var b,M,B,W,u,N,$,R,H,G,Y,Q,h,Je,Ke,Xe,He,Ge,Ye,Qe,Ze,xe,eo,oo,so,to,no,ro,ao,lo,io,co,uo,po,ho,Co,go,mo,fo,vo,yo,Ao,bo,So,Mo,To,Do,Fo,wo,Io,No,Bo,ko,Po,Eo,qo,$o,Lo,Oo,_o,zo,jo,Ro,Vo,Uo,Wo,Jo,Ko,Xo,Ho,Go,Yo,Qo,Zo,xo,es,os,ss,ts,ns,rs,as,ls,is,cs,ds,us,ps,hs,Cs,gs,ms,fs,vs,ys,As,bs,Ss,Ms,Ts;return console.log("x",e),{CostCenterID:e!=null&&e.costCenterId?e==null?void 0:e.costCenterId:"",Costcenter:e!=null&&e.costCenter?e==null?void 0:e.costCenter:"",ValidFrom:e!=null&&e.validFrom?e==null?void 0:e.validFrom:"",ValidTo:e!=null&&e.validTo?e==null?void 0:e.validTo:"",PersonInCharge:c((M=(b=e==null?void 0:e.viewData)==null?void 0:b["Basic Data"])==null?void 0:M["Basic Data"],"Person Responsible"),CostcenterType:c((W=(B=e==null?void 0:e.viewData)==null?void 0:B["Basic Data"])==null?void 0:W["Basic Data"],"Cost Center Category"),CostctrHierGrp:c((N=(u=e==null?void 0:e.viewData)==null?void 0:u["Basic Data"])==null?void 0:N["Basic Data"],"Hierarchy Area"),BusArea:c((R=($=e==null?void 0:e.viewData)==null?void 0:$["Basic Data"])==null?void 0:R["Basic Data"],"Business Area"),CompCode:c((G=(H=e==null?void 0:e.viewData)==null?void 0:H["Basic Data"])==null?void 0:G["Basic Data"],"Company Code"),Currency:c((Q=(Y=e==null?void 0:e.viewData)==null?void 0:Y["Basic Data"])==null?void 0:Q["Basic Data"],"Currency"),ProfitCtr:c((Je=(h=e==null?void 0:e.viewData)==null?void 0:h["Basic Data"])==null?void 0:Je["Basic Data"],"Profit Center"),Name:c((Xe=(Ke=e==null?void 0:e.viewData)==null?void 0:Ke["Basic Data"])==null?void 0:Xe.Names,"Name"),Descript:c((Ge=(He=e==null?void 0:e.viewData)==null?void 0:He["Basic Data"])==null?void 0:Ge.Names,"Description"),PersonInChargeUser:c((Qe=(Ye=e==null?void 0:e.viewData)==null?void 0:Ye["Basic Data"])==null?void 0:Qe.Names,"User Responsible"),RecordQuantity:c((xe=(Ze=e==null?void 0:e.viewData)==null?void 0:Ze.Control)==null?void 0:xe.Control,"Record Quantity")===!0?"X":"",LockIndActualPrimaryCosts:c((oo=(eo=e==null?void 0:e.viewData)==null?void 0:eo.Control)==null?void 0:oo.Control,"Actual Primary Costs")===!0?"X":"",LockIndPlanPrimaryCosts:c((to=(so=e==null?void 0:e.viewData)==null?void 0:so.Control)==null?void 0:to.Control,"Plan Primary Costs")===!0?"X":"",LockIndActSecondaryCosts:c((ro=(no=e==null?void 0:e.viewData)==null?void 0:no.Control)==null?void 0:ro.Control,"Act. secondary Costs")===!0?"X":"",LockIndPlanSecondaryCosts:c((lo=(ao=e==null?void 0:e.viewData)==null?void 0:ao.Control)==null?void 0:lo.Control,"Plan Secondary Costs")===!0?"X":"",LockIndActualRevenues:c((co=(io=e==null?void 0:e.viewData)==null?void 0:io.Control)==null?void 0:co.Control,"Actual Revenue")===!0?"X":"",LockIndPlanRevenues:c((po=(uo=e==null?void 0:e.viewData)==null?void 0:uo.Control)==null?void 0:po.Control,"Plan Revenue")===!0?"X":"",LockIndCommitmentUpdate:c((Co=(ho=e==null?void 0:e.viewData)==null?void 0:ho.Control)==null?void 0:Co.Control,"Commitment Update")===!0?"X":"",ConditionTableUsage:"",Application:"",CstgSheet:c((mo=(go=e==null?void 0:e.viewData)==null?void 0:go.Templates)==null?void 0:mo["Overhead rates"],"Costing Sheet"),ActyIndepTemplate:c((vo=(fo=e==null?void 0:e.viewData)==null?void 0:fo.Templates)==null?void 0:vo["Formula planning"],"Acty-Indep. Form Plng Temp"),ActyDepTemplate:c((Ao=(yo=e==null?void 0:e.viewData)==null?void 0:yo.Templates)==null?void 0:Ao["Formula planning"],"Acty-Dep. Form Plng Temp"),AddrTitle:c((So=(bo=e==null?void 0:e.viewData)==null?void 0:bo.Address)==null?void 0:So["Address Data"],"Title"),AddrName1:c((To=(Mo=e==null?void 0:e.viewData)==null?void 0:Mo.Address)==null?void 0:To["Address Data"],"Name 1"),AddrName2:c((Fo=(Do=e==null?void 0:e.viewData)==null?void 0:Do.Address)==null?void 0:Fo["Address Data"],"Name 2"),AddrName3:c((Io=(wo=e==null?void 0:e.viewData)==null?void 0:wo.Address)==null?void 0:Io["Address Data"],"Name 3"),AddrName4:c((Bo=(No=e==null?void 0:e.viewData)==null?void 0:No.Address)==null?void 0:Bo["Address Data"],"Name 4"),AddrStreet:c((Po=(ko=e==null?void 0:e.viewData)==null?void 0:ko.Address)==null?void 0:Po["Address Data"],"Street"),AddrCity:c((qo=(Eo=e==null?void 0:e.viewData)==null?void 0:Eo.Address)==null?void 0:qo["Address Data"],"Location"),AddrDistrict:c((Lo=($o=e==null?void 0:e.viewData)==null?void 0:$o.Address)==null?void 0:Lo["Address Data"],"District"),AddrCountry:c((_o=(Oo=e==null?void 0:e.viewData)==null?void 0:Oo.Address)==null?void 0:_o["Address Data"],"Country/Reg"),AddrCountryIso:"",AddrTaxjurcode:c((jo=(zo=e==null?void 0:e.viewData)==null?void 0:zo.Address)==null?void 0:jo["Address Data"],"Jurisdiction"),AddrPoBox:c((Vo=(Ro=e==null?void 0:e.viewData)==null?void 0:Ro.Address)==null?void 0:Vo["Address Data"],"PO Box"),AddrPostlCode:c((Wo=(Uo=e==null?void 0:e.viewData)==null?void 0:Uo.Address)==null?void 0:Wo["Address Data"],"Postal Code"),AddrPobxPcd:c((Ko=(Jo=e==null?void 0:e.viewData)==null?void 0:Jo.Address)==null?void 0:Ko["Address Data"],"PO Box Post Cod"),AddrRegion:c((Ho=(Xo=e==null?void 0:e.viewData)==null?void 0:Xo.Address)==null?void 0:Ho["Address Data"],"Region"),TelcoLangu:"",TelcoLanguIso:c((Yo=(Go=e==null?void 0:e.viewData)==null?void 0:Go.Communication)==null?void 0:Yo["Communication Data"],"Language Key"),TelcoTelephone:c((Zo=(Qo=e==null?void 0:e.viewData)==null?void 0:Qo.Communication)==null?void 0:Zo["Communication Data"],"Telephone 1"),TelcoTelephone2:c((es=(xo=e==null?void 0:e.viewData)==null?void 0:xo.Communication)==null?void 0:es["Communication Data"],"Telephone 2"),TelcoTelebox:c((ss=(os=e==null?void 0:e.viewData)==null?void 0:os.Communication)==null?void 0:ss["Communication Data"],"Telebox Number"),TelcoTelex:c((ns=(ts=e==null?void 0:e.viewData)==null?void 0:ts.Communication)==null?void 0:ns["Communication Data"],"Telex Number"),TelcoFaxNumber:c((as=(rs=e==null?void 0:e.viewData)==null?void 0:rs.Communication)==null?void 0:as["Communication Data"],"Fax Number"),TelcoTeletex:c((is=(ls=e==null?void 0:e.viewData)==null?void 0:ls.Communication)==null?void 0:is["Communication Data"],"Teletex Number"),TelcoPrinter:c((ds=(cs=e==null?void 0:e.viewData)==null?void 0:cs.Communication)==null?void 0:ds["Communication Data"],"Printer Destination"),TelcoDataLine:c((ps=(us=e==null?void 0:e.viewData)==null?void 0:us.Communication)==null?void 0:ps["Communication Data"],"Data Line"),ActyDepTemplateAllocCc:c((Cs=(hs=e==null?void 0:e.viewData)==null?void 0:hs.Templates)==null?void 0:Cs["Activity and Business Process Allocation"],"Acty-Dep. Alloc Template"),ActyDepTemplateSk:c((ms=(gs=e==null?void 0:e.viewData)==null?void 0:gs.Templates)==null?void 0:ms["Actual Statistical Key Figures"],"Templ.: Act. Stat. Key Figure"),ActyIndepTemplateAllocCc:c((vs=(fs=e==null?void 0:e.viewData)==null?void 0:fs.Templates)==null?void 0:vs["Activity and Business Process Allocation"],"Acty-Indep. Alloc Temp"),ActyIndepTemplateSk:c((As=(ys=e==null?void 0:e.viewData)==null?void 0:ys.Templates)==null?void 0:As["Actual Statistical Key Figures"],"Templ.: Act. Stat. Key Figure"),AvcActive:!1,AvcProfile:"",BudgetCarryingCostCtr:"",CurrencyIso:"",Department:c((Ss=(bs=e==null?void 0:e.viewData)==null?void 0:bs["Basic Data"])==null?void 0:Ss["Basic Data"],"Department"),FuncArea:c((Ts=(Ms=e==null?void 0:e.viewData)==null?void 0:Ms["Basic Data"])==null?void 0:Ts["Basic Data"],"Functional Area"),FuncAreaFixAssigned:"",FuncAreaLong:"",Fund:"",FundFixAssigned:"",GrantFixAssigned:"",GrantId:"",JvEquityTyp:"",JvJibcl:"",JvJibsa:"",JvOtype:"",JvRecInd:"",JvVenture:"",Logsystem:""}})}});console.log("rishav",w);const P=()=>{we(!0)},se=()=>{Ie(!0)},J=()=>{K(!1),Ne(!0)},Oe=(s,l)=>{const t=s.target.value;if(t.length>0&&t[0]===" ")Pe(t.trimStart());else{let i=t.toUpperCase();Pe(i)}},O=()=>{K(!0),Ne(!1)},dt=()=>{(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(r==null?void 0:r.requestType)==="Mass Create"?(q(),At()):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(r==null?void 0:r.requestType)==="Mass Create"?(q(),St()):(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(r==null?void 0:r.requestType)==="Mass Change"?(q(),bt()):((o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(r==null?void 0:r.requestType)==="Mass Change")&&(q(),Mt())},ut=()=>{(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(r==null?void 0:r.requestType)==="Mass Create"?(S(!0),O(),pt()):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(r==null?void 0:r.requestType)==="Mass Create"?(S(!0),O(),Ct()):(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(r==null?void 0:r.requestType)==="Mass Create"?(S(!0),O(),ft()):(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(r==null?void 0:r.requestType)==="Mass Change"?(S(!0),O(),ht()):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(r==null?void 0:r.requestType)==="Mass Change"?(S(!0),O(),gt()):((o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(r==null?void 0:r.requestType)==="Mass Change")&&(S(!0),O(),vt())},pt=()=>{console.log("paylaod",w);const l=i=>{S(!1),i.statusCode===200?(console.log("success"),C("Create"),m(`Mass Cost Center Submitted for Approval with ID NCM${i.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(C("Error"),A(!1),m("Failed Submitting Mass Cost Center for Approval"),v("danger"),y(!1),f(!0),P())},t=i=>{console.log("error")};D(`/${I}/massAction/costCentersApprovalSubmit`,"post",l,t,w)},ht=()=>{console.log("paylaod",w);const l=i=>{S(!1),console.log("kdfljsald"),i.statusCode===200?(console.log("success"),C("Create"),m(`Mass Cost Center Submitted for Approval with ID CCM${i.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(C("Error"),A(!1),m("Failed Submitting Mass Cost Center for Approval"),v("danger"),y(!1),f(!0),P())},t=i=>{console.log("error")};D(`/${I}/massAction/changeCostCentersApprovalSubmit`,"post",l,t,w)},Ct=()=>{console.log("paylaod",w);const l=i=>{S(!1),i.statusCode===201?(console.log("success"),C("Create"),m("Mass Cost Center Approved & SAP Syndication Completed"),v("success"),y(!1),A(!0),k(),f(!0)):(C("Error"),A(!1),m("Failed Approving Mass Cost Center "),v("danger"),y(!1),f(!0),P())},t=i=>{console.log("error")};D(`/${I}/massAction/createCostCentersApproved`,"post",l,t,w)},gt=()=>{console.log("paylaod",w);const l=i=>{S(!1),i.statusCode===201?(console.log("success"),C("Create"),m("Mass Cost Center Change Approved & SAP Syndication Completed"),v("success"),y(!1),A(!0),k(),f(!0)):(C("Error"),A(!1),m("Failed Approving Mass Cost Center Change"),v("danger"),y(!1),f(!0),P())},t=i=>{console.log("error")};D(`/${I}/massAction/changeCostCentersApproved`,"post",l,t,w)},_e=()=>{we(!1)},mt=()=>{},ft=()=>{j.filter((t,i)=>V.includes(i));const s=t=>{if(S(!1),t.statusCode===200){console.log("success"),C("Create"),m(`Mass Cost Center Sent for Review with ID NCM${t.body}`),v("success"),y(!1),A(!0),k(),f(!0);const i={artifactId:ee,createdBy:o==null?void 0:o.emailId,artifactType:"CostCenter",requestId:`NCM${t==null?void 0:t.body}`},e=M=>{console.log("Second API success",M)},b=M=>{console.error("Second API error",M)};D(`/${Se}/documentManagement/updateDocRequestId`,"post",e,b,i)}else C("Error"),A(!1),m("Failed Submitting the Cost Center for Review "),v("danger"),y(!1),f(!0),P()},l=t=>{console.log("error")};D(`/${I}/massAction/costCentersSubmitForReview`,"post",s,l,w)},vt=()=>{j.filter((t,i)=>V.includes(i));const s=t=>{if(S(!1),t.statusCode===200){console.log("success"),C("Create"),m(`Mass Cost Center Change Sent for Review with ID CCM${t.body}`),v("success"),y(!1),A(!0),k(),f(!0);const i={artifactId:ee,createdBy:o==null?void 0:o.emailId,artifactType:"CostCenter",requestId:`CCM${t==null?void 0:t.body}`},e=M=>{console.log("Second API success",M)},b=M=>{console.error("Second API error",M)};D(`/${Se}/documentManagement/updateDocRequestId`,"post",e,b,i)}else C("Error"),A(!1),m("Failed Submitting the Mass Cost Center for Review "),v("danger"),y(!1),f(!0),P()},l=t=>{console.log("error")};D(`/${I}/massAction/changeCostCentersSubmitForReview`,"post",s,l,w)},ze=()=>{S(!0);const s=t=>{t.statusCode===400?($e(t.body),ve(!0),S(!1)):(Us(!1),C("Create"),console.log("success"),C("Create"),m("All Data has been Validated. Cost Center can be Sent for Review"),v("success"),y(!1),A(!0),k(),f(!0),ye(!0),S(!1))},l=t=>{console.log(t)};D(`/${I}/massAction/validateMassCostCenter`,"post",s,l,w)},yt=(s,l)=>s.every(t=>!l.includes(t)),je=()=>{S(!0);const s=j.filter((u,N)=>V.includes(N));console.log("selectedData",s);const l=[],t=[];s.map(u=>{var N={coArea:u==null?void 0:u.controllingArea,name:u==null?void 0:u.name};l.push(N),t.push(u==null?void 0:u.name.toUpperCase())});const i=[];F.map(u=>{var N,$;($=(N=u==null?void 0:u.viewData)==null?void 0:N["Basic Data"])==null||$.Names.map(R=>{R.fieldName==="Name"&&i.push(R.value.toUpperCase())})}),console.log(i,t,"arrayofviewand");let e=yt(i,t);console.log("duplicateCheckPayload",l);const b=u=>{u.statusCode===400?($e(u.body),ve(!0),S(!1)):(C("Create"),console.log("success"),C("Create"),m("All Data has been Validated. Cost Center can be Sent for Review"),v("success"),y(!1),A(!0),k(),f(!0),ye(!0),S(!1),(l.coArea!==""||l.name!=="")&&((r==null?void 0:r.requestType)==="Mass Change"&&!e?S(!1):D(`/${I}/alter/fetchCCDescriptionsDupliChk`,"post",M,B,l)))},M=u=>{console.log("dataaaa",u),u.body.length===0||!u.body.some(N=>l.some($=>$.name.toUpperCase()===N.matches[0]))?(S(!1),ke(!1),K(!0)):(S(!1),C("Duplicate Check"),A(!1),m("There is a direct match for the Cost Center name."),v("danger"),y(!1),f(!0),P(),ke(!0),Hs(directMatches))},B=u=>{console.log(u)},W=u=>{console.log(u)};D(`/${I}/massAction/validateMassCostCenter`,"post",b,W,w)},At=()=>{j.filter((t,i)=>V.includes(i));const s=t=>{z(!1),t.statusCode===200?(console.log("success"),C("Create"),m(`Cost Center Submitted for Correction with ID NCM${t.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(C("Error"),A(!1),m("Failed Submitting Cost Center for Correction"),v("danger"),y(!1),f(!0),P()),q()},l=t=>{console.log(t)};D(`/${I}/massAction/costCentersSendForCorrection`,"post",s,l,w)},bt=()=>{j.filter((t,i)=>V.includes(i));const s=t=>{z(en),t.statusCode===200?(console.log("success"),C("Create"),m(`Cost Center Submitted for Correction with ID CCM${t.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(C("Error"),A(!1),m("Failed Submitting Cost Center for Correction"),v("danger"),y(!1),f(!0),P()),q()},l=t=>{console.log(t)};D(`/${I}/massAction/changeCostCentersSendForCorrection`,"post",s,l,w)},St=()=>{console.log("isLoading7",Te),j.filter((t,i)=>V.includes(i));const s=t=>{z(!1),t.statusCode===200?(console.log("success"),C("Create"),m(`Cost Center Submitted for Correction with ID NCM${t.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(C("Error"),A(!1),m("Failed Submitting Cost Center for Correction"),v("danger"),y(!1),f(!0),P()),q()},l=t=>{console.log(t)};D(`/${I}/massAction/costCentersSendForReview`,"post",s,l,w)},Mt=()=>{j.filter((t,i)=>V.includes(i));const s=t=>{z(!1),t.statusCode===200?(console.log("success"),C("Create"),m(`Cost Center Submitted for Correction with ID CCM${t.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(C("Error"),A(!1),m("Failed Submitting Cost Center for Correction"),v("danger"),y(!1),f(!0),P()),q()},l=t=>{console.log(t)};D(`/${I}/massAction/changeCostCentersSendForReview`,"post",s,l,w)},Re=()=>{ve(!1)},Ve=(We=oe==null?void 0:oe.filter(s=>(s==null?void 0:s.code)===400))==null?void 0:We.map((s,l)=>{var t;if(s.code===400)return{id:l,costCenter:s==null?void 0:s.costCenter,error:(t=s==null?void 0:s.status)==null?void 0:t.message}});return p(_,{children:[p("div",{style:{backgroundColor:"#FAFCFF"},children:[p(te,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:_s,onClose:q,children:[p(ne,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(g,{variant:"h6",children:"Remarks"}),n(x,{sx:{width:"max-content"},onClick:q,children:n(Me,{})})]}),n(ae,{sx:{padding:".5rem 1rem"},children:n(le,{children:n(ie,{sx:{minWidth:400},children:n(Is,{sx:{height:"auto"},fullWidth:!0,children:n(Ns,{sx:{backgroundColor:"#F5F5F5"},onChange:Oe,value:he,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),p(re,{sx:{display:"flex",justifyContent:"end"},children:[n(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:q,children:"Cancel"}),n(T,{className:"button_primary--normal",type:"save",onClick:dt,variant:"contained",children:"Submit"})]})]}),p(te,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:zs,onClose:O,children:[p(ne,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(g,{variant:"h6",children:"Remarks"}),n(x,{sx:{width:"max-content"},onClick:O,children:n(Me,{})})]}),n(ae,{sx:{padding:".5rem 1rem"},children:n(le,{children:n(ie,{sx:{minWidth:400},children:n(Is,{sx:{height:"auto"},fullWidth:!0,children:n(Ns,{sx:{backgroundColor:"#F5F5F5"},onChange:Oe,value:he,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),p(re,{sx:{display:"flex",justifyContent:"end"},children:[n(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:O,children:"Cancel"}),n(T,{className:"button_primary--normal",type:"save",onClick:ut,variant:"contained",children:"Submit"})]})]}),p(te,{open:Gs,fullWidth:!0,onClose:Re,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[p(ne,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(g,{variant:"h6",color:"red",children:"Errors"}),n(x,{sx:{width:"max-content"},onClick:Re,children:n(Me,{})})]}),n(ae,{sx:{padding:".5rem 1rem"},children:Ve&&n(be,{isLoading:Te,width:"100%",rows:Ve,columns:[{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}],pageSize:10,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),n(re,{sx:{display:"flex",justifyContent:"end"}})]}),n(Ht,{dialogState:Os,openReusableDialog:P,closeReusableDialog:_e,dialogTitle:qs,dialogMessage:Fe,handleDialogConfirm:_e,dialogOkText:"OK",handleExtraButton:mt,dialogSeverity:$s}),Ls&&n(Gt,{openSnackBar:Es,alertMsg:Fe,handleSnackBarClose:Qs}),p("div",{style:{...It,backgroundColor:"#FAFCFF"},children:[n(E,{container:!0,sx:Nt,children:p(E,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[p(E,{item:!0,md:11,sx:{display:"flex"},children:[n(E,{children:n(x,{color:"primary","aria-label":"upload picture",component:"label",sx:Bt,children:n(kt,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{ue(-1)}})})}),a.processDesc==="Mass Create"?p(E,{children:[n(g,{variant:"h3",children:n("strong",{children:"Create Multiple Cost Center"})}),n(g,{variant:"body2",color:"#777",children:"This view displays list of uploaded Cost Centers"})]}):(r==null?void 0:r.requestType)==="Mass Create"?p(E,{children:[n(g,{variant:"h3",children:n("strong",{children:"Create Multiple Cost Center"})}),n(g,{variant:"body2",color:"#777",children:"This view displays list of Cost Centers"})]}):(r==null?void 0:r.requestType)==="Mass Change"?p(E,{children:[n(g,{variant:"h3",children:n("strong",{children:"Change Multiple Cost Center"})}),n(g,{variant:"body2",color:"#777",children:"This view displays list of Cost Centers"})]}):(a==null?void 0:a.processDesc)==="Mass Change"?p(E,{children:[n(g,{variant:"h3",children:n("strong",{children:"Change Multiple Cost Center"})}),n(g,{variant:"body2",color:"#777",children:"This view displays list of Cost Centers"})]}):""]}),n(E,{item:!0,md:1,sx:{display:"flex"},children:n(Pt,{title:"Uploaded documents",arrow:!0,children:n(x,{onClick:Zs,children:n(Et,{})})})}),p(te,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Vs,onClose:Le,children:[n(ne,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:(o==null?void 0:o.role)==="MDM Steward"?n(_,{children:n(g,{variant:"h6",children:"Add Attachment"})}):""}),n(ae,{sx:{padding:".5rem 1rem"},children:p(Fs,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[(o==null?void 0:o.role)==="Finance"?n(le,{children:n(ie,{sx:{minWidth:400},children:n(qt,{title:"CostCenter",useMetaData:!1,artifactId:ee,artifactName:"CostCenter"})})}):"",n(E,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(g,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!Ce.length&&n(be,{width:"100%",rows:Ce,columns:et,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action",artifactId:ee}),!Ce.length&&n(g,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(g,{variant:"h6",children:"Comments"}),!!ge.length&&n($t,{sx:{[`& .${Lt.root}:before`]:{flex:0,padding:0}},children:ge.map(s=>p(Ot,{children:[p(_t,{children:[n(zt,{children:n(jt,{sx:{color:"#757575"}})}),n(Rt,{})]}),n(Vt,{sx:{py:"12px",px:2},children:n(Fs,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(ie,{sx:{padding:"1rem"},children:p(le,{spacing:1,children:[n(E,{sx:{display:"flex",justifyContent:"space-between"},children:n(g,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:ce(s.createdAt).format("DD MMM YYYY")})}),n(g,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:s.user}),n(g,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:s.comment})]})})})})]}))}),!ge.length&&n(g,{variant:"body2",children:"No Comments Found"}),n("br",{})]})}),n(re,{children:n(T,{onClick:Le,children:"Close"})})]})]})}),n(E,{item:!0,sx:{position:"relative"},children:n(be,{width:"100%",rows:j,columns:lt,pageSize:10,getRowIdValue:"id",hideFooter:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,checkboxSelection:(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(r==null?void 0:r.requestType)==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(r==null?void 0:r.requestType)==="Mass Change",onRowsSelectionHandler:ot,callback_onRowSingleClick:s=>{const l=s.row.costCenter,t=F.find(i=>i.costCenter===l);console.log(t,"yo"),ue(`/masterDataCockpit/costCenter/massCostCenterTableRequestBench/displayMultipleCostCenterRequestBench/${l}`,{state:{rowData:s.row,requestNumber:r==null?void 0:r.requestId.slice(3),tabsData:t,requestbenchRowData:r}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})]}),Ut(xs,"Cost Center","ChangeCC")?n(Jt,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:p(Wt,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:ks,children:[(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(r==null?void 0:r.requestType)==="Mass Create"?p(_,{children:[n(T,{variant:"outlined",size:"small",sx:{button_Outlined:de,mr:1},onClick:se,children:"Correction"}),n(T,{variant:"contained",size:"small",sx:{...L},onClick:J,children:"Submit For Approval"})]}):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(r==null?void 0:r.requestType)==="Mass Create"?p(_,{children:[n(T,{variant:"outlined",size:"small",sx:{button_Outlined:de,mr:1},onClick:se,children:"Correction"}),n(T,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:ze,children:"Validate"}),n(T,{variant:"contained",size:"small",sx:{...L},onClick:J,disabled:qe,children:"Approve"})]}):(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(r==null?void 0:r.requestType)==="Mass Create"?p(_,{children:[n(T,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:je,disabled:!me,children:"Validate"}),n(T,{variant:"contained",size:"small",sx:{...L},onClick:J,disabled:Be,children:"Submit For Review"})]}):"",(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(r==null?void 0:r.requestType)==="Mass Change"?p(_,{children:[n(T,{variant:"outlined",size:"small",sx:{button_Outlined:de,mr:1},onClick:se,children:"Correction"}),n(T,{variant:"contained",size:"small",sx:{...L},onClick:J,children:"Submit For Approval"})]}):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(r==null?void 0:r.requestType)==="Mass Change"?p(_,{children:[n(T,{variant:"outlined",size:"small",sx:{button_Outlined:de,mr:1},onClick:se,children:"Correction"}),n(T,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:ze,children:"Validate"}),n(T,{variant:"contained",size:"small",sx:{...L},onClick:J,disabled:qe,children:"Approve"})]}):(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(r==null?void 0:r.requestType)==="Mass Change"?p(_,{children:[n(T,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:je,disabled:!me,children:"Validate"}),n(T,{variant:"contained",size:"small",sx:{...L},onClick:J,disabled:Be,children:"Submit For Review"})]}):""]})}):""]}),n(Qt,{sx:{color:"#fff",zIndex:s=>s.zIndex.drawer+1},open:Bs,children:n(Yt,{color:"inherit"})})]})};export{ln as default};
