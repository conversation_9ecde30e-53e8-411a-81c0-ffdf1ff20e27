import{i as S,r as n,y as p,w as c,hj as a,hg as i}from"./index-fdfa25a0.js";const _=()=>{const l=S(s=>s.applicationConfig),[u,D]=n.useState(null),[C,e]=n.useState(null);return{getDtCall:async(s,E="")=>{try{const o=t=>{(t==null?void 0:t.statusCode)===p.STATUS_200&&D({data:t==null?void 0:t.data,customParam:E})},r=t=>{e(t)};l.environment==="localhost"?await c(`/${a}${i.INVOKE_RULES.LOCAL}`,"post",o,r,s):await c(`/${a}${i.INVOKE_RULES.PROD}`,"post",o,r,s)}catch(o){e(o)}},dtData:u,error:C}};export{_ as u};
