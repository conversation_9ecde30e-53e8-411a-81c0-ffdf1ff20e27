import{r as x,l as cr,b as ar,u as dr,i as H,hZ as hr,w as N,hK as I,a as t,j as l,h_ as ce,T as p,B as w,gZ as ae,$ as d,hC as Ht,i0 as Kt,i1 as Jt,i2 as Xt,i3 as Yt,i4 as Qt,i5 as Zt,a0 as Se,g_ as J,hN as Ae,i6 as mr,F as K,ht as pr,J as ur,i7 as Gt,fV as Ce,h1 as ke,W as Te,hI as We,fX as Ne,gQ as G,a3 as de,fH as Fe,a1 as a,ho as xr,h8 as fr,hp as gr,hv as br,i8 as vr,hD as ye,gU as W,ib as Ie,ic as Sr,id as Ar,ie as Cr,P as O,hE as V,gW as f,hy as kr,gR as Dt,gS as Rt,hJ as Pt,ig as Tr,ih as en,hs as Ee,ii as Nr,ij as Fr,hn as yr,il as Ir,hP as te,ik as Oe}from"./index-fdfa25a0.js";import{<PERSON> as he}from"./EditableFieldForCostCenter-7fa6d38f.js";const Or=()=>{var dt,ht,mt,pt,ut,xt,ft,gt,bt,vt,St,At,Ct,kt,Tt,Nt,Ft,yt,It,Et,Bt,zt,jt,$t,Mt,qt,Lt,Wt,Ot,Vt,_t;const[F,Ve]=x.useState(!1);x.useState(0);const[ne,tn]=x.useState(!0);x.useState({});const[T,nn]=x.useState([]),[m,me]=x.useState(0),[_e,rn]=x.useState([]),[z,on]=x.useState(),[sn,Er]=x.useState(!1);x.useState([]);const[ln,g]=x.useState(!1),[cn,S]=x.useState(!1),[we,b]=x.useState(""),[an,v]=x.useState(!1),[Br,A]=x.useState(!0),[zr,C]=x.useState(!1),[dn,k]=x.useState(!0),[hn,pe]=x.useState(!1),[mn,Be]=x.useState(!1),[pn,ze]=x.useState(!1),[un,Ue]=x.useState(!1),[X,He]=x.useState(""),[D,jr]=x.useState([]),[xn,Ke]=x.useState(!1),[je,$e]=x.useState(!0),[fn,Je]=x.useState(!1),[R,$r]=x.useState([]),[gn,Me]=x.useState(!1),[bn,re]=x.useState(!0),[vn,Xe]=x.useState(!1),[Sn,An]=x.useState(""),[ue,Cn]=x.useState(""),[Mr,kn]=x.useState([]),[Ye,Tn]=x.useState([]),[Nn,Qe]=x.useState(!1),_=cr(),Ze=ar(),Fn=dr(),P=H(r=>r.appSettings);let xe=H(r=>{var s;return(s=r.userManagement.entitiesAndActivities)==null?void 0:s["Cost Center"]}),yn=H(r=>{var s;return(s=r==null?void 0:r.initialData)==null?void 0:s.IWMMyTask}),c=H(r=>r.userManagement.userData),n=Fn.state,h=H(r=>r.userManagement.taskData),In=H(r=>r.costCenter.requiredFields);const e=H(r=>r.edit.payload);let En=H(r=>r.edit.payload);console.log("ccroewdata",new Date(`${z==null?void 0:z.ValidFrom} GMT-0000`).toUTCString()),console.log("costCenterDetails",_e),console.log(X,"Remarks");const oe=H(r=>{var s;return(s=r.costCenter)==null?void 0:s.costCenterViewData});H(r=>r.costCenter.requiredFields),console.log("costCenterViewData",oe);var $={TaskId:h!=null&&h.taskId?h==null?void 0:h.taskId:"",CostCenterHeaderID:z!=null&&z.costCenterHeaderId?z==null?void 0:z.costCenterHeaderId:"",ControllingArea:(ht=(dt=n==null?void 0:n.controllingAreaData)==null?void 0:dt.newControllingArea)!=null&&ht.code?(pt=(mt=n==null?void 0:n.controllingAreaData)==null?void 0:mt.newControllingArea)==null?void 0:pt.code:"",Testrun:bn,Action:"I",ReqCreatedBy:c==null?void 0:c.user_id,ReqCreatedOn:"",RequestStatus:"",CreationId:h!=null&&h.subject?h==null?void 0:h.subject.slice(3):"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",MassRequestStatus:"",Remarks:X||"",Toitem:[{CostCenterID:z!=null&&z.costCenterId?z==null?void 0:z.costCenterId:"",ValidFrom:(ut=n==null?void 0:n.validFromDate)!=null&&ut.newValidFromDate?"/Date("+Date.parse((xt=n==null?void 0:n.validFromDate)==null?void 0:xt.newValidFromDate)+")/":"",ValidTo:(ft=n==null?void 0:n.validToDate)!=null&&ft.newValidToDate?"/Date("+Date.parse((gt=n==null?void 0:n.validToDate)==null?void 0:gt.newValidToDate)+")/":"",Costcenter:`${(vt=(bt=n==null?void 0:n.companyCode)==null?void 0:bt.newCompanyCode)==null?void 0:vt.code}${(St=n==null?void 0:n.costCenterName)==null?void 0:St.newCostCenterName}`?`${(Ct=(At=n==null?void 0:n.companyCode)==null?void 0:At.newCompanyCode)==null?void 0:Ct.code}${(kt=n==null?void 0:n.costCenterName)==null?void 0:kt.newCostCenterName}`:"",PersonInCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",CostcenterType:e!=null&&e.CostCenterCategory?e==null?void 0:e.CostCenterCategory:"",CostctrHierGrp:"TUK1-PRODU",BusArea:e!=null&&e.BusinessArea?e==null?void 0:e.BusinessArea:"",CompCode:(Nt=(Tt=n==null?void 0:n.companyCode)==null?void 0:Tt.newCompanyCode)!=null&&Nt.code?(yt=(Ft=n==null?void 0:n.companyCode)==null?void 0:Ft.newCompanyCode)==null?void 0:yt.code:"TUK1",Currency:e!=null&&e.Currency?e==null?void 0:e.Currency:"",ProfitCtr:e!=null&&e.ProfitCenter?e==null?void 0:e.ProfitCenter:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Descript:e!=null&&e.Description?e==null?void 0:e.Description:"",PersonInChargeUser:e!=null&&e.UserResponsible?e==null?void 0:e.UserResponsible:"",RecordQuantity:(e==null?void 0:e.RecordQuantity)===!0?"X":"",LockIndActualPrimaryCosts:(e==null?void 0:e.ActualPrimaryCosts)===!0?"X":"",LockIndPlanPrimaryCosts:(e==null?void 0:e.PlanPrimaryCosts)===!0?"X":"",LockIndActSecondaryCosts:(e==null?void 0:e.ActsecondaryCosts)===!0?"X":"",LockIndPlanSecondaryCosts:(e==null?void 0:e.PlanSecondaryCosts)===!0?"X":"",LockIndActualRevenues:(e==null?void 0:e.ActualRevenue)===!0?"X":"",LockIndPlanRevenues:(e==null?void 0:e.PlanRevenue)===!0?"X":"",LockIndCommitmentUpdate:(e==null?void 0:e.CommitmentUpdate)===!0?"X":"",ConditionTableUsage:"",Application:"",CstgSheet:e!=null&&e.CostingSheet?e==null?void 0:e.CostingSheet:"",ActyIndepTemplate:e!=null&&e.ActyIndepFromPlngTemp?e==null?void 0:e.ActyIndepFromPlngTemp:"",ActyDepTemplate:e!=null&&e.ActyDepFromPlngTemp?e==null?void 0:e.ActyDepFromPlngTemp:"",AddrTitle:e!=null&&e.Title?e==null?void 0:e.Title:"",AddrName1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",AddrName2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",AddrName3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",AddrName4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",AddrStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",AddrCity:e!=null&&e.Location?e==null?void 0:e.Location:"",AddrDistrict:e!=null&&e.District?e==null?void 0:e.District:"",AddrCountry:e!=null&&e.CountryReg?e==null?void 0:e.CountryReg:"",AddrCountryIso:"",AddrTaxjurcode:e!=null&&e.Jurisdiction?e==null?void 0:e.Jurisdiction:"",AddrPoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",AddrPostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",AddrPobxPcd:e!=null&&e.POBoxPostCod?e==null?void 0:e.POBoxPostCod:"",AddrRegion:e!=null&&e.Region?e==null?void 0:e.Region:"",TelcoLangu:"",TelcoLanguIso:e!=null&&e.LanguageKey?e==null?void 0:e.LanguageKey:"",TelcoTelephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",TelcoTelephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",TelcoTelebox:e!=null&&e.TeleboxNumber?e==null?void 0:e.TeleboxNumber:"",TelcoTelex:e!=null&&e.TelexNumber?e==null?void 0:e.TelexNumber:"",TelcoFaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",TelcoTeletex:e!=null&&e.TeletexNumber?e==null?void 0:e.TeletexNumber:"",TelcoPrinter:e!=null&&e.PrinterDestination?e==null?void 0:e.PrinterDestination:"",TelcoDataLine:e!=null&&e.DataLine?e==null?void 0:e.DataLine:"",ActyDepTemplateAllocCc:e!=null&&e.ActyDepAllocTemp?e==null?void 0:e.ActyDepAllocTemp:"",ActyDepTemplateSk:e!=null&&e.TempActStatKeyFigure?e==null?void 0:e.TempActStatKeyFigure:"",ActyIndepTemplateAllocCc:e!=null&&e.ActyIndepAllocTemp?e==null?void 0:e.ActyIndepAllocTemp:"",ActyIndepTemplateSk:e!=null&&e.TempActStatKeyFigure?e==null?void 0:e.TempActStatKeyFigure:"",AvcActive:!1,AvcProfile:"",BudgetCarryingCostCtr:"",CurrencyIso:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",FuncArea:e!=null&&e.FunctionalArea?e==null?void 0:e.FunctionalArea:"",FuncAreaFixAssigned:"",FuncAreaLong:"",Fund:"",FundFixAssigned:"",GrantFixAssigned:"",GrantId:"",JvEquityTyp:"",JvJibcl:"",JvJibsa:"",JvOtype:"",JvRecInd:"",JvVenture:"",Logsystem:""}]};console.log("taskData",yn),console.log("taskRowDetails",h);const Bn=()=>{Qe(!1)},Ge=()=>yr(En,In,Tn),zn=r=>{console.log("compcode",r);const s=i=>{console.log("value",i),_(te({keyName:"Region",data:i.body}))},o=i=>{console.log(i,"error in dojax")};N(`/${I}/data/getRegionBasedOnCountry?country=${r}`,"get",s,o)},jn=r=>{let s=[];for(const o in r){if(r.hasOwnProperty(o)){for(const i in r[o])if(r[o].hasOwnProperty(i)){const u=r[o][i];for(const y of u)if(y.visibility==="0"||y.visibility==="Required"){console.log(y.fieldName,"field.fieldName");let B=y.fieldName.replace(/\s/g,"");s.push(B)}}}kn(i=>({...i,error_field_arr:s}))}},$n=()=>{var i,u,y,B,ie,se,E,Z,le;var r={id:n!=null&&n.reqStatus?n==null?void 0:n.id:"",costCenter:(u=(i=n==null?void 0:n.costCenter)==null?void 0:i.newCostCenter)!=null&&u.code?(B=(y=n==null?void 0:n.costCenter)==null?void 0:y.newCostCenter)==null?void 0:B.code:n!=null&&n.costCenter?(ie=n==null?void 0:n.costCenter)==null?void 0:ie.split(" ")[0]:"",controllingArea:(E=(se=n==null?void 0:n.controllingAreaDataCopy)==null?void 0:se.newControllingAreaCopyFrom)!=null&&E.code?(le=(Z=n==null?void 0:n.controllingAreaDataCopy)==null?void 0:Z.newControllingAreaCopyFrom)==null?void 0:le.code:"",reqStatus:n!=null&&n.reqStatus?n==null?void 0:n.reqStatus:"Approved",screenName:"Create"};const s=Y=>{const ee=Y.body.viewData,sr=Y.body;console.log("ccdata",Y.body),_(Ir(ee)),_n(ee["Basic Data"]["Basic Data"].find(U=>(U==null?void 0:U.fieldName)==="Company Code").value),zn(ee.Address["Address Data"].find(U=>(U==null?void 0:U.fieldName)==="Country/Reg").value);const wt=Object.keys(ee);nn(wt);const lr=["Attachment & Documents"];T.concat(lr);const Ut=wt.map(U=>({category:U,data:ee[U],setIsEditMode:Ve}));rn(Ut),jn(ee),on(sr),console.log("mappedData",Ut,z)},o=Y=>{console.log(Y)};N(`/${I}/data/displayCostCenter`,"post",s,o,r)},Mn=()=>{T[m],console.log("costCenterViewData",oe);let r=Object.entries(oe);console.log("viewDataArray",r);const s={};r.map(o=>{console.log("bottle",o[1]);let i=Object.entries(o[1]);return console.log("notebook",i),i.forEach(u=>{u[1].forEach(y=>{s[y.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=y.value})}),o}),console.log("toSetArray",s),_(Tr(s))},[De,qn]=x.useState(0),Ln=(r,s)=>{const o=u=>{_(te({keyName:r,data:u.body})),qn(y=>y+1)},i=u=>{console.log(u)};N(`/${I}/data/${s}`,"get",o,i)},Wn=()=>{var r,s;(s=(r=en)==null?void 0:r.costCenter)==null||s.map(o=>{Ln(o==null?void 0:o.keyName,o==null?void 0:o.endPoint)})},On=()=>{var r,s;De==((s=(r=en)==null?void 0:r.costCenter)==null?void 0:s.length)?k(!1):k(!0)};x.useEffect(()=>{On()},[De]),x.useEffect(()=>{Cn(hr("CC"))},[]),x.useEffect(()=>{Wn(),$n(),wn(),Un(),Hn()},[]),x.useEffect(()=>{oe.length!==0&&Mn()},[oe]);const qe=()=>{Be(!1)},Vn=()=>{un?(ze(!1),Ue(!1)):(ze(!1),Ze("/masterDataCockpit/costCenter"))},M=()=>{$e(!0);const r=Ge();F?r?(me(s=>s-1),_(Ee())):Re():(me(s=>s-1),_(Ee()))},q=()=>{const r=Ge();F?r?(me(s=>s+1),_(Ee())):Re():(me(s=>s+1),_(Ee()))},Re=()=>{Qe(!0)},_n=r=>{console.log("compcode",r);const s=i=>{console.log("value",i),_(te({keyName:"Currency",data:i.body}))},o=i=>{console.log(i,"error in dojax")};N(`/${I}/data/getCurrency?companyCode=${r}`,"get",s,o)},wn=()=>{var o,i;const r=u=>{_(te({keyName:"HierarchyArea",data:u.body}))},s=u=>{console.log(u)};N(`/${I}/data/getHierarchyArea?controllingArea=${(i=(o=n==null?void 0:n.controllingAreaDataCopy)==null?void 0:o.newControllingAreaCopyFrom)==null?void 0:i.code}`,"get",r,s)},Un=()=>{var o,i;const r=u=>{_(te({keyName:"CompCode",data:u.body}))},s=u=>{console.log(u)};N(`/${I}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${(i=(o=n==null?void 0:n.controllingAreaDataCopy)==null?void 0:o.newControllingAreaCopyFrom)==null?void 0:i.code}`,"get",r,s)},Hn=()=>{var o,i;const r=u=>{_(te({keyName:"ProfitCenter",data:u.body}))},s=u=>{console.log(u)};N(`/${I}/data/getProfitCenterAsPerControllingArea?controllingArea=${(i=(o=n==null?void 0:n.controllingAreaDataCopy)==null?void 0:o.newControllingAreaCopyFrom)==null?void 0:i.code}`,"get",r,s)},fe=()=>{Ve(!0),tn(!1)},Pe=()=>{k(!0),Zn()},et=()=>{k(!0),Gn()},tt=()=>{k(!0),Dn()},nt=()=>{k(!0),Rn()},rt=()=>{Pn()},ot=()=>{k(!0),tr()},it=()=>{k(!0),nr()},j=()=>{Be(!0)},L=()=>{ze(!0)},st=()=>{var y,B,ie,se;pe(!0);const r={coArea:(B=(y=n==null?void 0:n.controllingAreaData)==null?void 0:y.newControllingArea)!=null&&B.code?(se=(ie=n==null?void 0:n.controllingAreaData)==null?void 0:ie.newControllingArea)==null?void 0:se.code:"",name:e!=null&&e.Name?e==null?void 0:e.Name.toUpperCase():""},s=E=>{var Z,le,Y;E.statusCode===201?(g("Create"),g("Create"),b("All Data has been Validated. Cost Center can be Sent for Review"),v("success"),A(!1),S(!0),L(),C(!0),Ue(!0),(r.coArea!==""||r.name!=="")&&N(`/${I}/alter/fetchCCDescriptionDupliChk`,"post",o,i,r)):(g("Error"),S(!1),b(`${(Z=E==null?void 0:E.body)!=null&&Z.message[0]?(le=E==null?void 0:E.body)==null?void 0:le.message[0]:(Y=E==null?void 0:E.body)==null?void 0:Y.value}`),v("danger"),A(!1),C(!0),j(),pe(!1))},o=E=>{E.body.length===0||!E.body.some(Z=>Z.toUpperCase()===r.name)?(pe(!1),$e(!1),re(!1)):(pe(!1),g("Duplicate Check"),S(!1),b("There is a direct match for the Cost Center name. Please change the name."),v("danger"),A(!1),C(!0),j(),$e(!0))},i=E=>{console.log(E)},u=E=>{console.log(E)};N(`/${I}/alter/validateCostCenter`,"post",s,u,$)},Kn=()=>{(c==null?void 0:c.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Create"||(h==null?void 0:h.processDesc)==="Create")?(k(!0),Jn()):(c==null?void 0:c.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Change"||(h==null?void 0:h.processDesc)==="Change")?(k(!0),Xn()):(c==null?void 0:c.role)==="Approver"&&((n==null?void 0:n.requestType)==="Create"||(h==null?void 0:h.processDesc)==="Create")?(k(!0),Yn()):(c==null?void 0:c.role)==="Approver"&&((n==null?void 0:n.requestType)==="Change"||(h==null?void 0:h.processDesc)==="Change")&&(k(!0),Qn())},Jn=()=>{const r=o=>{k(!1),o.statusCode===200?(console.log("success"),g("Create"),b(`Cost Center Submitted for Correction with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(g("Error"),S(!1),b("Failed Submitting Cost Center for Correction"),v("danger"),A(!1),C(!0),j()),Q()},s=o=>{console.log(o)};console.log("remarkssssssssss",X),N(`/${I}/alter/costCenterSendForCorrection`,"post",r,s,$)},Xn=()=>{const r=o=>{k(!1),o.statusCode===200?(console.log("success"),g("Create"),b(`Cost Center Submitted for Correction with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(g("Error"),S(!1),b("Failed Submitting Cost Center for Correction"),v("danger"),A(!1),C(!0),j()),Q()},s=o=>{console.log(o)};console.log("hsdfjgdh",$),N(`/${I}/alter/changeCostCenterSendForCorrection`,"post",r,s,$)},Yn=()=>{const r=o=>{k(!1),o.statusCode===200?(console.log("success"),g("Create"),b(`Cost Center Submitted for Correction with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(g("Error"),S(!1),b("Failed Submitting Cost Center for Correction"),v("danger"),A(!1),C(!0),j()),Q()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterSendForReview`,"post",r,s,$)},Qn=()=>{const r=o=>{k(!1),o.statusCode===200?(console.log("success"),g("Create"),b(`Cost Center Submitted for Correction with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(g("Error"),S(!1),b("Failed Submitting Cost Center for Correction"),v("danger"),A(!1),C(!0),j()),Q()},s=o=>{console.log(o)};console.log("remarksssaaaa",X),N(`/${I}/alter/changeCostCenterSendForReview`,"post",r,s,$)},lt=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:r=>l(K,{children:[t(Nr,{index:r.row.id,name:r.row.docName}),t(Fr,{index:r.row.id,name:r.row.docName})]})}],ct=T.map(r=>{const s=_e.filter(o=>{var i;return((i=o.category)==null?void 0:i.split(" ")[0])==(r==null?void 0:r.split(" ")[0])});if(s.length!=0)return{category:r==null?void 0:r.split(" ")[0],data:s[0].data}}).map((r,s)=>{if((r==null?void 0:r.category)=="Basic"&&m==0)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(i=>(console.log("fieldDatatttt",i),t(he,{length:i.maxLength,label:i.fieldName,data:e,value:i.value,visibility:i.visibility,onSave:u=>handleFieldSave(i.fieldName,u),isEditMode:F,type:i.fieldType,field:i,taskRequestId:n==null?void 0:n.requestId})))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Control"&&m==1)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(i=>t(he,{data:e,label:i.fieldName,value:(i==null?void 0:i.value)==="X",onSave:u=>handleFieldSave(i.fieldName,u),visibility:i.visibility,isEditMode:F,type:i.fieldType,taskRequestId:n==null?void 0:n.requestId},i.fieldName))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Templates"&&m==2)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(i=>t(he,{label:i.fieldName,data:e,value:i.value,onSave:u=>handleFieldSave(i.fieldName,u),isEditMode:F,visibility:i.visibility,type:i.fieldType,taskRequestId:n==null?void 0:n.requestId},i.fieldName))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Address"&&m==3)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(i=>t(he,{label:i.fieldName,data:e,value:i.value,onSave:u=>handleFieldSave(i.fieldName,u),isEditMode:F,type:i.fieldType,visibility:i.visibility,taskRequestId:n==null?void 0:n.requestId},i.fieldName))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Communication"&&m==4)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(i=>t(he,{label:i.fieldName,data:e,value:i.value,onSave:u=>handleFieldSave(i.fieldName,u),isEditMode:F,visibility:i.visibility,type:i.fieldType,taskRequestId:n==null?void 0:n.requestId},i.fieldName))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Attachments"&&m==5)return[t(K,{children:F?l(K,{children:[t(mr,{title:"CostCenter",useMetaData:!1,artifactId:ue,artifactName:"CostCenter"}),l(Se,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[t(d,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:t(p,{variant:"h6",children:t("strong",{children:"Attachments"})})}),!!D.length&&t(Ht,{width:"100%",rows:D,columns:lt,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!D.length&&t(p,{variant:"body2",children:"No Attachments Found"}),t("br",{}),t(p,{variant:"h6",children:"Comments"}),!!R.length&&t(Timeline,{sx:{[`& .${timelineItemClasses.root}:before`]:{flex:0,padding:0}},children:R.map(o=>l(Kt,{children:[l(Jt,{children:[t(Xt,{children:t(Yt,{sx:{color:"#757575"}})}),t(Qt,{})]}),t(Zt,{sx:{py:"12px",px:2},children:t(Se,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:t(w,{sx:{padding:"1rem"},children:l(J,{spacing:1,children:[t(d,{sx:{display:"flex",justifyContent:"space-between"},children:t(p,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ae(o.createdAt).format("DD MMM YYYY")})}),t(p,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),t(p,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!R.length&&t(p,{variant:"body2",children:"No Comments Found"}),t("br",{})]})]}):l(Se,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[t(d,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:t(p,{variant:"h6",children:t("strong",{children:"Attachments"})})}),!!D.length&&t(Ht,{width:"100%",rows:D,columns:lt,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!D.length&&t(p,{variant:"body2",children:"No Attachments Found"}),t("br",{}),t(p,{variant:"h6",children:"Comments"}),!!R.length&&t(Timeline,{sx:{[`& .${timelineItemClasses.root}:before`]:{flex:0,padding:0}},children:R.map(o=>l(Kt,{children:[l(Jt,{children:[t(Xt,{children:t(Yt,{sx:{color:"#757575"}})}),t(Qt,{})]}),t(Zt,{sx:{py:"12px",px:2},children:t(Se,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:t(w,{sx:{padding:"1rem"},children:l(J,{spacing:1,children:[t(d,{sx:{display:"flex",justifyContent:"space-between"},children:t(p,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ae(o.createdAt).format("DD MMM YYYY")})}),t(p,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),t(p,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!R.length&&t(p,{variant:"body2",children:"No Comments Found"}),t("br",{})]})})]}),Zn=()=>{const r=o=>{k(!1),o.statusCode===200?(console.log("success"),g("Create"),b(`Cost Center Submitted for Approval with ID CCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(g("Approve"),S(!1),b("Failed Submitting Cost Center"),v("danger"),A(!1),C(!0),j()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/changeCostCenterApprovalSubmit`,"post",r,s,$)},Gn=()=>{const r=o=>{k(!1),o.statusCode===200?(console.log("success"),g("Create"),b(`Cost Center Submitted for Approval with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(g("Error"),S(!1),b("Failed Submitting Cost Center"),v("danger"),A(!1),C(!0),j()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterApprovalSubmit`,"post",r,s,$)},Dn=()=>{const r=o=>{if(k(!1),o.statusCode===200){console.log("success"),g("Create"),b(`Cost Center Submitted For Review with ID NCS${o.body} `),v("success"),A(!1),S(!0),L(),C(!0);const i={artifactId:ue,createdBy:c==null?void 0:c.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},u=B=>{console.log("Second API success",B)},y=B=>{console.error("Second API error",B)};N(`/${Oe}/documentManagement/updateDocRequestId`,"post",u,y,i)}else g("Error"),S(!1),b("Failed Submitting Cost Center"),v("danger"),A(!1),C(!0),j();handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterSubmitForReview`,"post",r,s,$)},Rn=()=>{const r=o=>{k(!1),o.statusCode===200?(console.log("success"),g("Create"),b(`Cost Center Submitted for Review with ID NCR${o.body} `),v("success"),A(!1),S(!0),L(),C(!0)):(g("Error"),S(!1),b("Failed Saving the Data"),v("danger"),A(!1),C(!0),j()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterSubmitForReview`,"post",r,s,$)},Pn=()=>{v(!1),j(),g("Confirm"),b("Do You Want to Save as Draft ?"),Me(!0),An("proceed")},er=()=>{qe(),k(!0);const r=o=>{if(k(!1),o.statusCode===200){console.log("success"),g("Create"),b(`Cost Center Saved As Draft with ID NCS${o.body} `),v("success"),A(!1),S(!0),L(),C(!0);const i={artifactId:ue,createdBy:c==null?void 0:c.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},u=B=>{console.log("Second API success",B)},y=B=>{console.error("Second API error",B)};N(`/${Oe}/documentManagement/updateDocRequestId`,"post",u,y,i)}else g("Error"),S(!1),b("Failed Saving Cost Center"),v("danger"),A(!1),C(!0),j();handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterAsDraft`,"post",r,s,$)},tr=()=>{const r=o=>{k(!1),o.statusCode===201?(console.log("success"),g("Create"),b(`${o.message}`),v("success"),A(!1),S(!0),L(),C(!0)):(g("Error"),S(!1),b("Failed Approving Cost Center"),v("danger"),A(!1),C(!0),j()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/changeCostCenterApproved`,"post",r,s,$)},nr=()=>{const r=o=>{k(!1),o.statusCode===201?(console.log("success"),g("Create"),b(`${o.message}`),v("success"),A(!1),S(!0),L(),C(!0)):(g("Error"),S(!1),b("Failed Approving the Cost Center"),v("danger"),A(!1),C(!0),j()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/createCostCenterApproved`,"post",r,s,$)},rr=()=>{Xe(!0)},ge=()=>{Xe(!1)},be=()=>{re(!1),Ke(!0)},Q=()=>{re(!0),Ke(!1)},Le=(r,s)=>{const o=r.target.value;if(o.length>0&&o[0]===" ")He(o.trimStart());else{let i=o.toUpperCase();He(i)}},ve=()=>{re(!0),Je(!1)},or=()=>{re(!1),Je(!0)},ir=()=>{k(!0);const r=o=>{if(k(!1),o.statusCode===200){g("Create"),b(`Cost Center has been Submitted for review NCS${o.body}`),Me(!1),v("success"),A(!1),S(!0),L(),C(!0),ve();const i={artifactId:ue,createdBy:c==null?void 0:c.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},u=B=>{console.log("Second API success",B)},y=B=>{console.error("Second API error",B)};N(`/${Oe}/documentManagement/updateDocRequestId`,"post",u,y,i)}else g("Create"),S(!1),b("Creation Failed"),Me(!1),v("danger"),A(!1),C(!0),j();handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterSubmitForReview`,"post",r,s,$)},at=()=>{ge(),ir()};return console.log("factorsarray",T),t(K,{children:dn===!0?t(pr,{}):l("div",{style:{backgroundColor:"#FAFCFF"},children:[t(ur,{dialogState:mn,openReusableDialog:j,closeReusableDialog:qe,dialogTitle:ln,dialogMessage:we,handleDialogConfirm:qe,dialogOkText:"OK",dialogSeverity:an,showCancelButton:!0,handleDialogReject:()=>{Be(!1)},handleExtraText:Sn,handleExtraButton:er,showExtraButton:gn}),cn&&t(Gt,{openSnackBar:pn,alertMsg:we,handleSnackBarClose:Vn}),Ye.length!=0&&t(Gt,{openSnackBar:Nn,alertMsg:"Please fill the following Field: "+Ye.join(", "),handleSnackBarClose:Bn}),l(Ce,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:xn,onClose:Q,children:[t(d,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:t(d,{item:!0,children:l(ke,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(p,{variant:"h6",children:"Remarks"}),t(Te,{sx:{width:"max-content"},onClick:Q,children:t(We,{})})]})})}),t(Ne,{sx:{padding:".5rem 1rem"},children:t(J,{children:t(w,{sx:{minWidth:400},children:t(G,{sx:{height:"auto"},fullWidth:!0,children:t(de,{sx:{backgroundColor:"#F5F5F5"},onChange:Le,multiline:!0,value:X,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),l(Fe,{sx:{display:"flex",justifyContent:"end"},children:[t(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Q,children:"Cancel"}),t(a,{className:"button_primary--normal",type:"save",onClick:Kn,variant:"contained",children:"Submit"})]})]}),l(Ce,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:fn,onClose:ve,children:[l(ke,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(p,{variant:"h6",children:"Remarks"}),t(Te,{sx:{width:"max-content"},onClick:ve,children:t(We,{})})]}),t(Ne,{sx:{padding:".5rem 1rem"},children:t(J,{children:t(w,{sx:{minWidth:400},children:t(G,{sx:{height:"auto"},fullWidth:!0,children:t(de,{sx:{backgroundColor:"#F5F5F5"},value:X,onChange:Le,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(Fe,{sx:{display:"flex",justifyContent:"end"},children:[t(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ve,children:"Cancel"}),t(a,{className:"button_primary--normal",type:"save",onClick:at,variant:"contained",children:"Submit"})]})]}),l(Ce,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:vn,onClose:ge,children:[l(ke,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(p,{variant:"h6",children:"Remarks"}),t(Te,{sx:{width:"max-content"},onClick:ge,children:t(We,{})})]}),t(Ne,{sx:{padding:".5rem 1rem"},children:t(J,{children:t(w,{sx:{minWidth:400},children:t(G,{sx:{height:"auto"},fullWidth:!0,children:t(de,{sx:{backgroundColor:"#F5F5F5"},onChange:Le,value:X,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(Fe,{sx:{display:"flex",justifyContent:"end"},children:[t(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ge,children:"Cancel"}),t(a,{className:"button_primary--normal",type:"save",onClick:at,variant:"contained",children:"Submit"})]})]}),t(xr,{sx:{color:"#fff",zIndex:r=>r.zIndex.drawer+1},open:hn,children:t(fr,{color:"inherit"})}),l(d,{container:!0,sx:gr,children:[l(d,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[l(d,{md:9,sx:{display:"flex"},children:[t(d,{children:t(Te,{color:"primary","aria-label":"upload picture",component:"label",sx:br,children:t(vr,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{Ze("/masterDataCockpit/costCenter")}})})}),l(d,{children:[F?l(d,{item:!0,md:12,children:[t(p,{variant:"h3",children:t("strong",{children:"Create Cost Center: "})}),t(p,{variant:"body2",color:"#777",children:"This view creates Cost Center from existing Cost Center"})]}):"",ne?l(d,{item:!0,md:12,children:[t(p,{variant:"h3",children:t("strong",{children:"Display Cost Center "})}),t(p,{variant:"body2",color:"#777",children:"This view displays the details of the Cost Center"})]}):""]})]}),t(d,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:ye(xe,"Cost Center","ChangeCC")&&((c==null?void 0:c.role)==="Super User"&&(n!=null&&n.requestType)&&ne?t(d,{gap:1,sx:{display:"flex"},children:t(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:t(K,{children:t(d,{item:!0,children:l(a,{variant:"outlined",size:"small",sx:W,onClick:fe,children:["Fill Details",t(Ie,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&(n!=null&&n.requestType)&&ne?t(d,{gap:1,sx:{display:"flex"},children:t(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:t(K,{children:t(d,{item:!0,children:l(a,{variant:"outlined",size:"small",sx:W,onClick:fe,children:["Fill Details",t(Ie,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Super User"&&!(n!=null&&n.requestType)&&ne?t(d,{gap:1,sx:{display:"flex"},children:t(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:t(K,{children:t(d,{item:!0,children:l(a,{variant:"outlined",size:"small",sx:W,onClick:fe,children:["Change",t(Ie,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&!(n!=null&&n.requestType)&&ne?t(d,{gap:1,sx:{display:"flex"},children:t(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:t(K,{children:t(d,{item:!0,children:l(a,{variant:"outlined",size:"small",sx:W,onClick:fe,children:["Change",t(Ie,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")})]}),l(d,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[l(d,{item:!0,md:10,sx:{marginLeft:"40px"},children:[t(d,{item:!0,sx:{paddingTop:"2px !important"},children:l(J,{flexDirection:"row",children:[t("div",{style:{width:"12%"},children:t(p,{variant:"body2",color:"#777",children:"Cost Center"})}),l(p,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",`${(Et=(It=n==null?void 0:n.companyCode)==null?void 0:It.newCompanyCode)==null?void 0:Et.code}${(Bt=n==null?void 0:n.costCenterName)==null?void 0:Bt.newCostCenterName}`?`${(jt=(zt=n==null?void 0:n.companyCode)==null?void 0:zt.newCompanyCode)==null?void 0:jt.code}${($t=n==null?void 0:n.costCenterName)==null?void 0:$t.newCostCenterName}`:""]})]})}),t(d,{item:!0,sx:{paddingTop:"2px !important"},children:l(J,{flexDirection:"row",children:[t("div",{style:{width:"12%"},children:t(p,{variant:"body2",color:"#777",children:"Controlling Area"})}),l(p,{variant:"body2",fontWeight:"bold",children:[":"," ",(qt=(Mt=n==null?void 0:n.controllingAreaData)==null?void 0:Mt.newControllingArea)!=null&&qt.code?(Wt=(Lt=n==null?void 0:n.controllingAreaData)==null?void 0:Lt.newControllingArea)==null?void 0:Wt.code:""]})]})})]}),l(d,{item:!0,md:2,sx:{marginLeft:"40px"},children:[t(d,{item:!0,sx:{paddingTop:"2px !important"},children:l(J,{flexDirection:"row",children:[t("div",{style:{width:"50%"},children:t(p,{variant:"body2",color:"#777",children:"Valid From"})}),l(p,{variant:"body2",fontWeight:"bold",children:[":"," ",Ae((Ot=n==null?void 0:n.validFromDate)==null?void 0:Ot.newValidFromDate).format(P==null?void 0:P.dateFormat)]})]})}),t(d,{item:!0,sx:{paddingTop:"2px !important"},children:l(J,{flexDirection:"row",children:[t("div",{style:{width:"50%"},children:t(p,{variant:"body2",color:"#777",children:"Valid To"})}),l(p,{variant:"body2",fontWeight:"bold",children:[":"," ",Ae((Vt=n==null?void 0:n.validToDate)==null?void 0:Vt.newValidToDate).format(P==null?void 0:P.dateFormat)]}),t(p,{variant:"body2",fontWeight:"bold"})]})})]})]}),l(d,{container:!0,style:{marginLeft:25},children:[t(Sr,{activeStep:m,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:T.map((r,s)=>t(Ar,{children:t(Cr,{sx:{fontWeight:"700"},children:r})},r))}),ct&&((_t=ct[m])==null?void 0:_t.map((r,s)=>t(w,{sx:{mb:2,width:"100%"},children:t(p,{variant:"body2",children:r})},s)))]})]}),l(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[ye(xe,"Cost Center","ChangeCC")&&(!(n!=null&&n.requestType)&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):""),ye(xe,"Cost Center","ChangeCC")&&((c==null?void 0:c.role)==="Super User"&&!(n!=null&&n.requestType)&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",submit:!0,for:!0,review:!0,sx:{...f,mr:1},onClick:rt,children:"Save As Draft"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),m===T.length-1?l(K,{children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:st,children:"Validate"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:or,children:"Submit For Review"})]}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&!(n!=null&&n.requestType)&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:rt,children:"Save As Draft"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),m===T.length-1?l(K,{children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:st,children:"Validate"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:rr,disabled:je,children:"Submit For Review"})]}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):""),ye(xe,"Cost Center","ChangeCC")&&((c==null?void 0:c.role)==="Super User"&&(n==null?void 0:n.requestType)==="Create"&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{button_Outlined:W,mr:1},onClick:it,children:"Approve"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:et,children:"Submit For Approval"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(n==null?void 0:n.requestType)==="Change"&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{button_Outlined:W,mr:1},onClick:ot,children:"Approve"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Pe,children:"Submit For Approval"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Create"||(h==null?void 0:h.processDesc)==="Create")&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:be,children:"Correction"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:et,children:"Submit For Approval"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((n==null?void 0:n.requestType)==="Change"||(h==null?void 0:h.processDesc)==="Change")&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:be,children:"Correction"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Pe,children:"Submit For Approval"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((n==null?void 0:n.requestType)==="Create"||(h==null?void 0:h.processDesc)==="Create")&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:be,children:"Correction"}),t(a,{variant:"contained",size:"small",sx:{button_Outlined:W,mr:1},onClick:it,children:"Approve"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((n==null?void 0:n.requestType)==="Change"||(h==null?void 0:h.processDesc)==="Change")&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:be,children:"Correction"}),t(a,{variant:"contained",size:"small",sx:{button_Outlined:W,mr:1},onClick:ot,children:"Approve"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(n==null?void 0:n.requestType)==="Create"&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),m===T.length-1?t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:nt,children:"Submit For Review"}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(n==null?void 0:n.requestType)==="Change"&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),m===T.length-1?t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:tt,children:"Submit For Review"}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((n==null?void 0:n.requestType)==="Create"||(h==null?void 0:h.processDesc)==="Create")&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),m===T.length-1?t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:nt,disabled:je,children:"Submit For Review"}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((n==null?void 0:n.requestType)==="Change"||(h==null?void 0:h.processDesc)==="Change")&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:m===0,children:"Back"}),m===T.length-1?t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:tt,disabled:je,children:"Submit For Review"}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:m===T.length-1,children:"Next"})]})}):"")]}),l(Ce,{open:sn,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[t(ke,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:t(p,{variant:"h6",children:"New Cost Center"})}),t(Ne,{sx:{padding:".5rem 1rem"},children:l(d,{container:!0,spacing:1,children:[l(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[l(p,{children:["Cost Center",t("span",{style:{color:"red"},children:"*"})]}),t(G,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:t(de,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",placeholder:"Enter Cost Center Name",required:!0})})]}),l(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[l(p,{children:["Controlling Area",t("span",{style:{color:"red"},children:"*"})]}),t(G,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:t(kr,{sx:{height:"42px"},required:"true",size:"small",renderInput:r=>t(de,{sx:{fontSize:"12px !important"},...r,variant:"outlined",placeholder:"Select Cost Center"})})})]}),l(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[l(p,{children:["Valid From",t("span",{style:{color:"red"},children:"*"})]}),t(G,{fullWidth:!0,sx:{margin:".5em 0px"},children:t(Dt,{dateAdapter:Rt,children:t(Pt,{slotProps:{textField:{size:"small"}}})})})]}),l(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[l(p,{children:["Valid To",t("span",{style:{color:"red"},children:"*"})]}),t(G,{fullWidth:!0,sx:{margin:".5em 0px"},children:t(Dt,{dateAdapter:Rt,children:t(Pt,{slotProps:{textField:{size:"small"}}})})})]})]})}),l(Fe,{sx:{display:"flex",justifyContent:"end"},children:[t(a,{sx:{width:"max-content",textTransform:"capitalize"},children:"Cancel"}),t(a,{className:"button_primary--normal",type:"save",variant:"contained",children:"Proceed"})]})]})]})})};export{Or as default};
