import{ah as V,ai as $,aj as B,r as _,kJ as Te,l as De,i as y,u as me,jY as P,j7 as Q,hZ as Ae,w as m,j as G,hj as E,G as Ee,T as Ie,a as p,aa as he,ab as H,B as Y,kK as F,kL as fe,k5 as Ne,jn as ve,kM as Se,kN as Ge,kO as be,kP as Ce,kQ as Oe,kR as ge}from"./index-fdfa25a0.js";import{n as ye,R as Pe,m as He,A as Re}from"./PermIdentityOutlined-42e26a93.js";import{d as je}from"./CommentOutlined-075abc93.js";import"./FilterField-6f6e20f9.js";import"./useChangeLogUpdate-3699f77c.js";import"./AutoCompleteType-3a9c9c9d.js";import"./dayjs.min-774e293a.js";import"./AdapterDayjs-cd3745c6.js";import"./isBetween-fe8614a5.js";import"./useFinanceCostingRows-2aab0ea4.js";import"./FilterChangeDropdown-22e6e937.js";import"./DeleteOutlineOutlined-9e9a8646.js";import"./useMaterialFieldConfig-6dda1d2a.js";import"./Description-5b38f787.js";import"./GenericTabs-8e261948.js";import"./useCustomDtCall-04c3c72a.js";import"./GenericViewGeneral-4b82ad14.js";import"./AdditionalData-a38f586a.js";import"./DeleteOutline-1f72afa8.js";import"./makeStyles-1dfd4db4.js";import"./AttachFile-1c547195.js";import"./Timeline-bb89efb4.js";var R={},Le=$;Object.defineProperty(R,"__esModule",{value:!0});var W=R.default=void 0,xe=Le(V()),ze=B;W=R.default=(0,xe.default)((0,ze.jsx)("path",{d:"M11 7h6v2h-6zm0 4h6v2h-6zm0 4h6v2h-6zM7 7h2v2H7zm0 4h2v2H7zm0 4h2v2H7zM20.1 3H3.9c-.5 0-.9.4-.9.9v16.2c0 .*******.9h16.2c.4 0 .9-.5.9-.9V3.9c0-.5-.5-.9-.9-.9M19 19H5V5h14z"}),"ListAltOutlined");var j={},Ue=$;Object.defineProperty(j,"__esModule",{value:!0});var w=j.default=void 0,ke=Ue(V()),Qe=B;w=j.default=(0,ke.default)((0,Qe.jsx)("path",{d:"M3 13h2v-2H3zm0 4h2v-2H3zm0-8h2V7H3zm4 4h14v-2H7zm0 4h14v-2H7zM7 7v2h14V7zm-4 6h2v-2H3zm0 4h2v-2H3zm0-8h2V7H3zm4 4h14v-2H7zm0 4h14v-2H7zM7 7v2h14V7z"}),"ListOutlined");const pa=()=>{const[Ye,b]=_.useState(!1),[J,q]=_.useState([]),[K,L]=_.useState(!1),[X,x]=_.useState(!1),[Z,ee]=_.useState(""),[ae,te]=_.useState([]),[Fe,se]=_.useState([]),[Ve,le]=_.useState([]),[re,oe]=_.useState(!1),{RequestId:N}=Te(),l=De(),C=y(t=>t.applicationConfig),v=y(t=>t.request.requestHeader.requestId),I=y(t=>t.request.tabValue),ie=(t,s)=>{l(P(s))};me();const ne=()=>{let t={decisionTableId:null,decisionTableName:"MDG_GI_MATERIAL_QUESTIONS",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_GI_SCENARIO":"Create"}],systemFilters:null,systemOrders:null,filterString:null};b(!0);const s=e=>{var n,c;if(e.statusCode===200){const u=((c=(n=e==null?void 0:e.data)==null?void 0:n.result[0])==null?void 0:c.MDG_GI_QUESTIONS_ACTION_TYPE)||[];le(u),u.map(a=>{a==null||a.MDG_GI_INPUT_OPTION}),u.map(a=>{(a==null?void 0:a.MDG_GI_INPUT_OPTION)==="Radio Button"?((a==null?void 0:a.MDG_GI_VISIBILITY)===" Mandatory"&&(console.log("insidevisibility"),F(a.MDG_GI_QUESTION_TYPE.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""))),(a==null?void 0:a.MDG_GI_QUESTION_TYPE)!=="Choose Priority Level"?l(setSingleMaterialPayload({keyName:a.MDG_GI_QUESTION_TYPE.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:"No"})):l(setSingleMaterialPayload({keyName:a.MDG_GI_QUESTION_TYPE.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:"Medium"}))):((a==null?void 0:a.MDG_GI_VISIBILITY)===" Mandatory"&&l(F(a.MDG_GI_QUESTION_TYPE.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""))),l(setSingleMaterialPayload({keyName:a.MDG_GI_QUESTION_TYPE.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:""})))}),l(fe(u))}},r=e=>{console.log(e)};C.environment==="localhost"?m(`/${E}/rest/v1/invoke-rules`,"post",s,r,t):m(`/${E}/v1/invoke-rules`,"post",s,r,t)},ce=()=>{const t={massCreationId:N.slice(3),massChangeId:"",screenName:"Mass Create",dtName:"MDG_MAT_FIELD_CONFIG",version:"v2"},s=e=>{var T;const n=e.body,c=Ne(n);l(ve({data:c}));const u=Object.keys(c).filter(d=>!isNaN(Number(d))),a={};u.forEach(d=>{a[d]=c[d]}),l(Q((T=Object.values(a))==null?void 0:T.map(d=>d.headerData)))},r=e=>{console.error("Error fetching data:",e)};m(`/${Ee}/data/displayMassMaterial`,"post",s,r,t)};_.useEffect(()=>{N&&(l(P(1)),ce(),L(!0),x(!0),oe(!0))},[N,l]),_.useEffect(()=>{Me(),ne(),de(),l(P(0)),l(Q([]))},[]),_.useEffect(()=>{ee(Ae("MAT"))},[]);const de=()=>{let t={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Mass Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};b(!0);const s=e=>{var n,c,u,a;if(b(!1),e.statusCode===200){let T=(c=(n=e==null?void 0:e.data)==null?void 0:n.result[0])==null?void 0:c.MDG_ATTACHMENTS_ACTION_TYPE,d=[];T==null||T.map((S,M)=>{var k={id:M};d.push(k)}),se(d);const O=((a=(u=e==null?void 0:e.data)==null?void 0:u.result[0])==null?void 0:a.MDG_ATTACHMENTS_ACTION_TYPE)||[];te(O)}},r=e=>{console.log(e)};C.environment==="localhost"?m(`/${E}/rest/v1/invoke-rules`,"post",s,r,t):m(`/${E}/v1/invoke-rules`,"post",s,r,t)};function z(t,s){return t.reduce((r,e)=>((r[e[s]]=r[e[s]]||[]).push(e),r),{})}function _e(t){for(let s=0;s<t.length;s++){let r=t[s];r.cards=r.cards.filter(e=>e.cardDetails.some(n=>n.visibility!=="Hidden")),r.cards.length===0&&(t.splice(s,1),s--)}return t}let U=[];const Me=()=>{let t={decisionTableId:null,decisionTableName:"MDG_MAT_FIELD_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":"Mass Create"}],systemFilters:null,systemOrders:null,filterString:null};const s=e=>{var n,c;if(e.statusCode===200){let a=((c=(n=e==null?void 0:e.data)==null?void 0:n.result[0])==null?void 0:c.MDG_MAT_FIELD_DETAILS_ACTION_TYPE).sort((o,A)=>o.MDG_MAT_VIEW_SEQUENCE-A.MDG_MAT_VIEW_SEQUENCE);const T=z(a,"MDG_MAT_VIEW_NAME");let d=[];Object.entries(T).forEach(([o,A])=>{let h=z(A,"MDG_MAT_CARD_NAME"),D=[];Object.entries(h).forEach(([g,f])=>{f.sort((i,ue)=>i.MDG_MAT_SEQUENCE_NO-ue.MDG_MAT_SEQUENCE_NO);let pe=f.map(i=>({fieldName:i.MDG_MAT_UI_FIELD_NAME,sequenceNo:i.MDG_MAT_SEQUENCE_NO,fieldType:i.MDG_MAT_FIELD_TYPE,maxLength:i.MDG_MAT_MAX_LENGTH,dataType:i.MDG_MAT_DATA_TYPE,viewName:i.MDG_MAT_VIEW_NAME,cardName:i.MDG_MAT_CARD_NAME,cardSeq:i.MDG_MAT_CARD_SEQUENCE,value:i.MDG_MAT_DEFAULT_VALUE,visibility:i.MDG_MAT_VISIBILITY,jsonName:i.MDG_MAT_JSON_FIELD_NAME}));D.push({cardName:g,cardSeq:f[0].MDG_MAT_CARD_SEQUENCE,cardDetails:pe})}),D.sort((g,f)=>g.cardSeq-f.cardSeq),console.log("cardssss",D),d.push({viewName:o,cards:D})});let O=_e(d),S={};S.viewData=O,console.log("dattt10",S);let M={};S.viewData.forEach(o=>{let A={};o.cards.forEach(h=>{A[h.cardName]=h.cardDetails,o.viewName!=="Request Header"&&h.cardDetails.forEach(D=>{D.visibility==="0"&&U.push(D.jsonName)})}),M[o.viewName]=A}),q(U),Object.keys(M).map(o=>{o==="Basic Data"?(l(Se(M["Basic Data"])),console.log(M["Basic Data"],"basicDataTabs")):o==="Sales"?l(Ge(M.Sales)):o==="Purchasing"?l(be(M.Purchasing)):o==="MRP"?l(Ce(M.MRP)):o==="Accounting"?l(Oe(M.Accounting)):o==="Request Header"&&l(ge(M["Request Header"]))})}},r=e=>{console.log(e)};C.environment==="localhost"?m(`/${E}/rest/v1/invoke-rules`,"post",s,r,t):m(`/${E}/v1/invoke-rules`,"post",s,r,t)};return G(Y,{sx:{padding:2},children:[v&&G(Ie,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[p(ye,{sx:{fontSize:"1.5rem"}}),"Request ID: ",G("span",{style:{fontWeight:"bold"},children:["NMM",v]})]}),G(he,{value:I,onChange:ie,sx:{mb:.8},"aria-label":"request form tabs",children:[p(H,{icon:p(w,{}),iconPosition:"start",label:"Request Header",sx:{fontSize:12,fontWeight:"bold"}}),p(H,{icon:p(W,{}),iconPosition:"start",label:"Material List",sx:{fontSize:12,fontWeight:"bold"},disabled:!K}),p(H,{icon:p(je,{}),iconPosition:"start",label:"Attachments & Comments",sx:{fontSize:12,fontWeight:"bold"},disabled:!X})]}),I===0&&p(Pe,{setIsSecondTabEnabled:L,setIsAttachmentTabEnabled:x}),I===1&&p(He,{mandFields:J,addHardCodeData:re}),I===2&&p(Re,{disabled:!0,attachmentsData:ae,requestIdHeader:v||N,pcNumber:Z}),I===3&&p(Y,{children:"Request Change History Content"})]})};export{pa as default};
