import{K as O,r as u,j as t,a as e,T as n,B as o,P as f,M as x,N as v,O as d,Q as i,S as C,U as D,V as w,W as m,X as R,Y as U,Z as A,_ as H,$ as y,a0 as K,a1 as F,a2 as L,a3 as T,a4 as W,a5 as j,a6 as Q,a7 as X,a8 as Y,a9 as Z,aa as _,ab as P,ac as q,ad as ee}from"./index-fdfa25a0.js";const ae=[{id:"USR-1001",name:"<PERSON>",email:"<EMAIL>",department:"Engineering",status:"Active",lastLogin:"2025-03-10T14:32:45",avatar:"https://randomuser.me/api/portraits/men/32.jpg",roles:[{id:1,name:"Developer",description:"Access to development tools and environments"},{id:2,name:"Project Manager",description:"Can manage project settings and team assignments"}],features:[{id:1,name:"Code Repository",access:!0,category:"Development"},{id:2,name:"CI/CD Pipeline",access:!0,category:"Development"},{id:3,name:"User Administration",access:!1,category:"Administration"},{id:4,name:"Billing",access:!1,category:"Finance"},{id:5,name:"Reports",access:!0,category:"Analytics"},{id:6,name:"API Gateway",access:!0,category:"Development"}]},{id:"USR-1002",name:"Sarah Williams",email:"<EMAIL>",department:"Marketing",status:"Active",lastLogin:"2025-03-11T09:15:22",avatar:"https://randomuser.me/api/portraits/women/44.jpg",roles:[{id:3,name:"Marketing Manager",description:"Manage marketing campaigns and content"}],features:[{id:1,name:"Code Repository",access:!1,category:"Development"},{id:2,name:"CI/CD Pipeline",access:!1,category:"Development"},{id:3,name:"User Administration",access:!1,category:"Administration"},{id:4,name:"Billing",access:!0,category:"Finance"},{id:5,name:"Reports",access:!0,category:"Analytics"},{id:6,name:"API Gateway",access:!1,category:"Development"}]},{id:"USR-1003",name:"Michael Chen",email:"<EMAIL>",department:"IT Administration",status:"Inactive",lastLogin:"2025-02-28T16:45:30",avatar:"https://randomuser.me/api/portraits/men/67.jpg",roles:[{id:4,name:"System Administrator",description:"Full system access and configuration"},{id:5,name:"Security Officer",description:"Manage security policies and audits"}],features:[{id:1,name:"Code Repository",access:!0,category:"Development"},{id:2,name:"CI/CD Pipeline",access:!0,category:"Development"},{id:3,name:"User Administration",access:!0,category:"Administration"},{id:4,name:"Billing",access:!0,category:"Finance"},{id:5,name:"Reports",access:!0,category:"Analytics"},{id:6,name:"API Gateway",access:!0,category:"Development"}]},{id:"USR-1004",name:"Jessica Lee",email:"<EMAIL>",department:"Finance",status:"Active",lastLogin:"2025-03-09T11:22:33",avatar:"https://randomuser.me/api/portraits/women/22.jpg",roles:[{id:6,name:"Finance Manager",description:"Access to financial data and reporting"}],features:[{id:1,name:"Code Repository",access:!1,category:"Development"},{id:2,name:"CI/CD Pipeline",access:!1,category:"Development"},{id:3,name:"User Administration",access:!1,category:"Administration"},{id:4,name:"Billing",access:!0,category:"Finance"},{id:5,name:"Reports",access:!0,category:"Analytics"},{id:6,name:"API Gateway",access:!1,category:"Development"}]}],k=r=>{const l=new Date(r);return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(l)},B=r=>r.split(" ").map(l=>l[0]).join("").toUpperCase().substring(0,2),z=r=>{let l=0;for(let c=0;c<r.length;c++)l=r.charCodeAt(c)+((l<<5)-l);let p="#";for(let c=0;c<3;c++){const b=l>>c*8&255;p+=`00${b.toString(16)}`.slice(-2)}return p},ne=()=>{const r=O(),[l,p]=u.useState(0),[c,b]=u.useState(""),[g,M]=u.useState(""),[s,I]=u.useState(null),G=(a,h)=>{p(h)},$=a=>{b(a.target.value)},N=a=>{M(a.target.value)},S=a=>{I(a)},E=()=>{I(null)},V=ae.filter(a=>a.name.toLowerCase().includes(g.toLowerCase())||a.email.toLowerCase().includes(g.toLowerCase())||a.department.toLowerCase().includes(g.toLowerCase())),J=s?s.features.filter(a=>a.name.toLowerCase().includes(c.toLowerCase())||a.category.toLowerCase().includes(c.toLowerCase())):[];return t(o,{sx:{width:"100%",height:"100vh",display:"flex",flexDirection:"column"},children:[e(o,{sx:{p:3,borderBottom:`1px solid ${r.palette.divider}`},children:e(n,{variant:"h4",component:"h1",fontWeight:"500",children:"User Management"})}),t(o,{sx:{display:"flex",flex:1,overflow:"hidden"},children:[t(o,{sx:{width:s?"50%":"100%",transition:"width 0.3s ease",p:3,borderRight:s?`1px solid ${r.palette.divider}`:"none",overflow:"auto",height:"100%"},children:[t(o,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[e(T,{size:"small",placeholder:"Search users",value:g,onChange:N,InputProps:{startAdornment:e(W,{position:"start",children:e(j,{})})},sx:{width:300}}),e(F,{variant:"contained",startIcon:e(L,{}),color:"primary",sx:{borderRadius:2},children:"Add New User"})]}),e(A,{component:f,sx:{borderRadius:2,boxShadow:r.shadows[2]},children:t(x,{sx:{minWidth:650},"aria-label":"users table",children:[e(v,{sx:{backgroundColor:r.palette.action.hover},children:t(d,{children:[e(i,{children:e(n,{fontWeight:"bold",children:"User"})}),e(i,{children:e(n,{fontWeight:"bold",children:"Department"})}),e(i,{children:e(n,{fontWeight:"bold",children:"Status"})}),e(i,{children:e(n,{fontWeight:"bold",children:"Last Login"})}),e(i,{align:"right",children:e(n,{fontWeight:"bold",children:"Actions"})})]})}),e(C,{children:V.map(a=>t(d,{hover:!0,onClick:()=>S(a),sx:{cursor:"pointer",backgroundColor:s&&s.id===a.id?r.palette.action.selected:"inherit"},children:[e(i,{component:"th",scope:"row",children:t(o,{display:"flex",alignItems:"center",children:[e(D,{sx:{mr:2,width:40,height:40,bgcolor:z(a.name)},children:B(a.name)}),t(o,{children:[e(n,{fontWeight:"medium",children:a.name}),e(n,{variant:"body2",color:"text.secondary",children:a.email})]})]})}),e(i,{children:a.department}),e(i,{children:e(w,{label:a.status,size:"small",color:a.status==="Active"?"success":"default"})}),e(i,{children:k(a.lastLogin)}),t(i,{align:"right",children:[e(m,{color:"primary",size:"small",onClick:h=>{h.stopPropagation(),S(a)},children:e(R,{})}),e(m,{color:"error",size:"small",onClick:h=>{h.stopPropagation()},children:e(U,{})})]})]},a.id))})]})})]}),s&&t(o,{sx:{width:"50%",p:3,overflow:"auto",height:"100%",position:"relative"},children:[e(m,{sx:{position:"absolute",top:r.spacing(2),right:r.spacing(2),zIndex:1},onClick:E,children:e(H,{})}),e(K,{sx:{mb:4,borderRadius:2,overflow:"visible",boxShadow:r.shadows[2]},children:t(o,{sx:{p:3,display:"flex",alignItems:"flex-start"},children:[e(D,{sx:{width:80,height:80,mr:3,border:`2px solid ${r.palette.primary.main}`,bgcolor:z(s.name),fontSize:"2rem"},children:B(s.name)}),t(o,{sx:{flexGrow:1},children:[t(o,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[t(o,{children:[e(n,{variant:"h5",component:"h2",fontWeight:"500",children:s.name}),e(n,{variant:"body1",color:"text.secondary",gutterBottom:!0,children:s.email})]}),t(o,{children:[e(w,{label:s.status,color:s.status==="Active"?"success":"default",sx:{fontWeight:"medium"}}),e(m,{color:"primary",sx:{ml:1},children:e(R,{})})]})]}),t(y,{container:!0,spacing:3,sx:{mt:1},children:[t(y,{item:!0,xs:12,md:6,children:[e(n,{variant:"body2",color:"text.secondary",children:"Department"}),e(n,{variant:"body1",children:s.department})]}),t(y,{item:!0,xs:12,md:6,children:[e(n,{variant:"body2",color:"text.secondary",children:"Last Login"}),e(n,{variant:"body1",children:k(s.lastLogin)})]}),t(y,{item:!0,xs:12,md:6,children:[e(n,{variant:"body2",color:"text.secondary",children:"User ID"}),e(n,{variant:"body1",children:s.id})]})]})]})]})}),e(o,{sx:{borderBottom:1,borderColor:"divider",mb:3},children:t(_,{value:l,onChange:G,"aria-label":"user management tabs",textColor:"primary",indicatorColor:"primary",children:[e(P,{icon:e(q,{}),iconPosition:"start",label:"Roles",id:"tab-0"}),e(P,{icon:e(ee,{}),iconPosition:"start",label:"Feature Access",id:"tab-1"})]})}),e("div",{role:"tabpanel",hidden:l!==0,children:l===0&&t(o,{children:[t(o,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e(n,{variant:"h6",component:"h3",children:"Assigned Roles"}),e(F,{variant:"contained",startIcon:e(L,{}),color:"primary",sx:{borderRadius:2},children:"Assign Role"})]}),e(A,{component:f,sx:{borderRadius:2,boxShadow:r.shadows[2]},children:t(x,{"aria-label":"roles table",children:[e(v,{sx:{backgroundColor:r.palette.action.hover},children:t(d,{children:[e(i,{children:e(n,{fontWeight:"bold",children:"Role Name"})}),e(i,{children:e(n,{fontWeight:"bold",children:"Description"})}),e(i,{align:"right",children:e(n,{fontWeight:"bold",children:"Actions"})})]})}),e(C,{children:s.roles.map(a=>t(d,{children:[e(i,{component:"th",scope:"row",children:e(n,{fontWeight:"medium",children:a.name})}),e(i,{children:a.description}),e(i,{align:"right",children:e(m,{color:"error",size:"small",children:e(U,{})})})]},a.id))})]})})]})}),e("div",{role:"tabpanel",hidden:l!==1,children:l===1&&t(o,{children:[t(o,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[e(n,{variant:"h6",component:"h3",children:"Feature Permissions"}),e(T,{size:"small",placeholder:"Search features",value:c,onChange:$,InputProps:{startAdornment:e(W,{position:"start",children:e(j,{})})},sx:{width:250,borderRadius:2}})]}),e(A,{component:f,sx:{borderRadius:2,boxShadow:r.shadows[2]},children:t(x,{"aria-label":"features table",children:[e(v,{sx:{backgroundColor:r.palette.action.hover},children:t(d,{children:[e(i,{children:e(n,{fontWeight:"bold",children:"Feature Name"})}),e(i,{children:e(n,{fontWeight:"bold",children:"Category"})}),e(i,{align:"center",children:e(n,{fontWeight:"bold",children:"Access Status"})}),e(i,{align:"right",children:e(n,{fontWeight:"bold",children:"Toggle Access"})})]})}),e(C,{children:J.map(a=>t(d,{children:[e(i,{component:"th",scope:"row",children:e(n,{fontWeight:"medium",children:a.name})}),e(i,{children:e(w,{label:a.category,size:"small",variant:"outlined",color:"primary"})}),e(i,{align:"center",children:a.access?t(o,{display:"flex",alignItems:"center",justifyContent:"center",children:[e(Q,{color:"success",fontSize:"small",sx:{mr:.5}}),e(n,{variant:"body2",color:"success.main",children:"Granted"})]}):t(o,{display:"flex",alignItems:"center",justifyContent:"center",children:[e(X,{color:"error",fontSize:"small",sx:{mr:.5}}),e(n,{variant:"body2",color:"error.main",children:"Denied"})]})}),e(i,{align:"right",children:e(Y,{control:e(Z,{checked:a.access,color:"primary"}),label:""})})]},a.id))})]})})]})})]})]})]})};export{ne as default};
