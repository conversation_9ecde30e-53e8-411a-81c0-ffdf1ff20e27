import{r as i,i as A,l as H,iz as N,is as q,a as t,$ as S,j as g,T as C,hN as W,hA as O,g_ as z,hy as _,a3 as T,gR as G,gS as J,hJ as K,F as M,w as Q,hL as X,hP as Y}from"./index-fdfa25a0.js";function Z(a,n){return Array.isArray(n)&&n.find(l=>l.code===a)||""}const V=({label:a,value:n,length:x,data:l,visibility:c,onSave:v,isEditMode:F,type:o,taskRequestId:I})=>{var k,$;const[D,h]=i.useState(n),[w,B]=i.useState(!1),u=A(e=>e.AllDropDown.dropDown),f=H();Z(D,u),A(e=>e.edit.payload);const P=A(e=>e.appSettings);let R=A(e=>e.userManagement.taskData);const[y,E]=i.useState(!1),L=R==null?void 0:R.subject;console.log("editField",a,n,l);const j={label:a,value:D,type:o,visibility:c};let s=a.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");i.useEffect(()=>{(c==="0"||c==="Required")&&f(N(s))},[]),i.useEffect(()=>{h(n),(a==="Analysis Period From"||a==="Analysis Period To")&&h(parseInt(n.replace("/Date(","").replace(")/","")))},[n]);const d=e=>{f(q({keyname:s.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:e}))};i.useEffect(()=>{if(a==="Analysis Period From"||a==="Analysis Period To"||a==="Created On"){const e=parseInt(n.replace("/Date(","").replace(")/",""));f(q({keyname:s.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:e}))}},[]);const U=e=>{const r=m=>{console.log("value",m),f(Y({keyName:"Region",data:m.body}))},p=m=>{console.log(m,"error in dojax")};Q(`/${X}/data/getRegionBasedOnCountry?country=${e==null?void 0:e.code}`,"get",r,p)};return console.log("editedvalue",D),t(M,{children:F?c==="Hidden"?null:t(S,{item:!0,children:t(z,{children:F?g("div",{children:[g(C,{variant:"body2",color:"#777",children:[a," ",c==="Required"||c==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),o==="Drop Down"?t(_,{options:u[s]??[],value:l[s]&&((k=u[s])==null?void 0:k.filter(e=>e.code===l[s]))&&(($=u[s])==null?void 0:$.filter(e=>e.code===l[s])[0])||"",onChange:(e,r,p)=>{a==="Country/Reg."&&U(r),d(r==null?void 0:r.code),h(r==null?void 0:r.code),B(!0),d(p==="clear"?"":r==null?void 0:r.code),(c==="Required"||c==="0")&&r.length===null&&E(!0)},getOptionLabel:e=>(console.log("optionn",e),e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??""),renderOption:(e,r)=>t("li",{...e,children:t(C,{style:{fontSize:12},children:`${r==null?void 0:r.code} - ${r==null?void 0:r.desc}`})}),renderInput:e=>t(T,{...e,variant:"outlined",placeholder:`Select ${j.label}`,size:"small",label:null,error:y})}):o==="Input"?t(T,{variant:"outlined",size:"small",fullWidth:!0,value:l[s].toUpperCase(),placeholder:`Enter ${j.label}`,inputProps:{maxLength:x},onChange:e=>{const r=e.target.value;if(r.length>0&&r[0]===" ")d(r.trimStart());else{let p=r.toUpperCase();d(p)}(c==="Required"||c==="0")&&r.length<=0&&E(!0),h(r.toUpperCase())},error:y}):o==="Calendar"?t(G,{dateAdapter:J,children:t(K,{slotProps:{textField:{size:"small"}},value:l[s],placeholder:"Select Date Range",maxDate:new Date(9999,12,31),onChange:e=>{d(e),h(e)}})}):o==="Radio Button"?t(S,{item:!0,md:2,children:t(O,{sx:{padding:0},checked:l[s],onChange:(e,r)=>{d(r),h(r)}})}):""]}):""})}):I&&c==="Hidden"||L&&c==="Hidden"?null:t(S,{item:!0,children:g(z,{children:[g(C,{variant:"body2",color:"#777",children:[a,c==="Required"||c==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),g(C,{variant:"body2",fontWeight:"bold",children:[a==="Analysis Period From"||a==="Analysis Period To"?W(l[s]).format(P==null?void 0:P.dateFormat):l[s],o==="Radio Button"?t(O,{sx:{padding:0},checked:l[s],disabled:!0}):""]})]})})})};export{V as E};
