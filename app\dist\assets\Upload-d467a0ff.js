import{r as c,j as s,a as e,h8 as j,iA as ae,a1 as J,fV as oe,h1 as te,T as l,hI as ne,W as z,fX as se,B as F,g_ as b,iB as ie,iC as de,F as q,iD as X,iE as le,iF as ce,hW as he,i as Q,e as pe,hd as fe,gK as T,V as ge,ii as ue,ij as me,iG as xe,$ as x,hp as be,hC as ye,iH as Ce,iI as Z,iJ as we,w as V,iK as ee,hg as re,i7 as ve}from"./index-fdfa25a0.js";import{d as Se,a as De}from"./AttachFile-1c547195.js";const Ie=({onFilesAdded:y=()=>{},maxFiles:U=5,maxFileSize:g=500,acceptedTypes:v=".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",currentFileCount:E=0,loading:A=!1})=>{const[C,B]=c.useState(!1),[d,f]=c.useState([]),[O,w]=c.useState(!1),[M,S]=c.useState([]),[t,_]=c.useState(!1),[N,u]=c.useState(0),[$,P]=c.useState(!1),p=()=>{w(!0),f([]),S([]),u(0)},D=()=>{t||(w(!1),f([]),S([]),u(0))},W=a=>{a.preventDefault(),B(!0)},m=()=>{B(!1)},G=a=>{a.preventDefault(),B(!1);const o=Array.from(a.dataTransfer.files);if(d.length+o.length>5){X("cannot feygdsfvs","error");return}I(o)},I=a=>{let o=[];a.forEach(i=>{i.id=he(),o.push(i)}),f(i=>[...i,...o])},L=()=>{t||document.getElementById("fileButton").click()},H=a=>{t||f(o=>o.filter(i=>i.id!==a))},K=async()=>{if(d.length!==0){_(!0),u(0);try{const a=setInterval(()=>{u(i=>i>=90?(clearInterval(a),90):i+10)},200),o=await y(d);clearInterval(a),u(100),setTimeout(()=>{D()},500)}catch{S(["Upload failed. Please try again."]),u(0)}finally{_(!1)}}};let r=()=>{let a=0;d.forEach(o=>{a+=o.length}),a>5e9?(X("more size","error"),P(!0)):P(!1)};c.useEffect(()=>{r()},[d]);const n=a=>{if(a===0)return"0 Bytes";const o=1024,i=["Bytes","KB","MB","GB"],h=Math.floor(Math.log(a)/Math.log(o));return parseFloat((a/Math.pow(o,h)).toFixed(2))+" "+i[h]};return s(q,{children:[e(J,{variant:"contained",onClick:p,startIcon:A?e(j,{size:16,color:"inherit"}):e(ae,{}),disabled:A,sx:{backgroundColor:"#1976d2",color:"#fff",textTransform:"capitalize",borderRadius:"5px",padding:"10px 20px","&:hover":{backgroundColor:"#1565c0"},"&:disabled":{backgroundColor:"#ccc"},boxShadow:"0px 4px 10px rgba(0, 0, 0, 0.2)"},children:A?"Loading...":"Upload Files"}),s(oe,{fullWidth:!0,maxWidth:"sm",open:O,onClose:D,disableEscapeKeyDown:t,sx:{"& .MuiDialog-paper":{borderRadius:"12px",padding:"1rem"}},children:[s(te,{sx:{padding:"1rem 1.5rem"},children:[e(l,{variant:"h6",sx:{fontWeight:500},children:"Upload Files"}),e(z,{"aria-label":"close",onClick:D,disabled:t,sx:{position:"absolute",right:12,top:10,color:t?"#ccc":"#666"},children:e(ne,{})})]}),s(se,{sx:{padding:"1.5rem"},children:[t&&s(F,{sx:{mb:2},children:[s(b,{direction:"row",justifyContent:"space-between",alignItems:"center",sx:{mb:1},children:[e(l,{variant:"body2",children:"Uploading files..."}),s(l,{variant:"body2",children:[N,"%"]})]}),e(ie,{variant:"determinate",value:N,sx:{height:8,borderRadius:4,backgroundColor:"#e0e0e0","& .MuiLinearProgress-bar":{borderRadius:4}}})]}),M.length>0&&e(F,{sx:{mb:2},children:M.map((a,o)=>s(l,{variant:"body2",color:"error",sx:{mb:.5},children:["• ",a]},o))}),s(F,{sx:{width:"100%",border:`2px dashed ${C?"#3b30c8":"#d0d5dd"}`,borderRadius:"8px",padding:"2rem",backgroundColor:C?"#f8f9ff":"#fafbff",transition:"all 0.3s ease",cursor:t?"not-allowed":"pointer",minHeight:"200px",display:"flex",alignItems:"center",justifyContent:"center",opacity:t?.6:1},onDragOver:t?void 0:W,onDragLeave:t?void 0:m,onDrop:t?void 0:G,onClick:t?void 0:L,children:[s(b,{alignItems:"center",spacing:1,children:[t?e(j,{sx:{color:"#3b30c8"}}):e(de,{sx:{fontSize:48,color:"#3b30c8"}}),e(l,{variant:"body1",sx:{color:t?"#999":"#344054"},children:t?"Uploading...":"Drag and drop files here"}),!t&&s(q,{children:[e(l,{variant:"body2",color:"primary",sx:{cursor:"pointer",textDecoration:"underline","&:hover":{color:"#3b30c8"}},children:"or click to browse"}),s(l,{variant:"caption",color:"textSecondary",children:["Max ",U," files, ",g,"MB each"]})]})]}),e("input",{id:"fileButton",multiple:!0,accept:v,type:"file",onChange:a=>{const o=Array.from(a.target.files);if(d.length+o.length>5){X("Cannot upload more than 5 files","error");return}I(o),a.target.value=""},style:{display:"none"},disabled:t})]}),d.length>0&&s(F,{sx:{maxHeight:"200px",overflowY:"auto",marginTop:"1.5rem",padding:"1rem",backgroundColor:"#fff",borderRadius:"8px",border:"1px solid #eee"},children:[s(l,{variant:"subtitle2",sx:{mb:1,fontWeight:500},children:["Selected Files (",d.length,")"]}),e(b,{spacing:1,children:d==null?void 0:d.map(a=>{var o,i,h;return(i=(o=a==null?void 0:a.name)==null?void 0:o.split(".").pop())==null||i.toLowerCase(),s(F,{sx:{display:"flex",alignItems:"center",padding:"0.75rem",backgroundColor:"#f8f9fa",borderRadius:"6px",border:"1px solid #e9ecef"},children:[e("img",{style:{width:"24px",height:"24px",marginRight:"0.75rem"},src:le[(h=a.name)==null?void 0:h.split(".")[1]]}),e(l,{variant:"body1",sx:{flexGrow:1},children:a.name}),e(F,{sx:{flex:1,minWidth:0},children:e(l,{variant:"caption",color:"textSecondary",children:n(a.size)})}),e(z,{size:"small",onClick:()=>H(a.id),disabled:t,sx:{color:t?"#ccc":"#666","&:hover":{color:t?"#ccc":"#d32f2f",backgroundColor:t?"transparent":"rgba(211, 47, 47, 0.04)"}},children:e(ce,{fontSize:"small"})})]},a.id)})})]}),s(b,{direction:"row",spacing:2,justifyContent:"flex-end",sx:{mt:3},children:[e(J,{variant:"outlined",onClick:D,disabled:t,sx:{color:t?"#ccc":"#666",borderColor:t?"#ccc":"#ddd"},children:"Cancel"}),e(J,{variant:"contained",onClick:K,disabled:d.length===0||t,startIcon:t?e(j,{size:16,color:"inherit"}):void 0,sx:{backgroundColor:"#1976d2","&:hover":{backgroundColor:"#1565c0"},"&:disabled":{backgroundColor:"#ccc"}},children:t?`Uploading... ${N}%`:`Upload ${d.length} file${d.length!==1?"s":""}`})]})]})]})]})},ke=y=>{var v,E;const U=((E=(v=y==null?void 0:y.split("."))==null?void 0:v.pop())==null?void 0:E.toLowerCase())||"",g={fontSize:"small",sx:{mr:1}};switch(U){case"xlsx":case"xls":case"csv":return e(Se,{sx:{color:"#1f7a1f"}});case"pdf":return e(we,{...g,sx:{color:"#d32f2f"}});case"doc":case"docx":return e(Z,{...g,sx:{color:"#1976d2"}});case"ppt":case"pptx":return e(Z,{...g,sx:{color:"#ff9800"}});default:return e(Ce,{...g,sx:{color:"#666"}})}},Ee=({onFilesChange:y=()=>{},maxFiles:U=5,maxFileSize:g=500,acceptedTypes:v=".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",disabled:E=!1,title:A=""})=>{const[C,B]=c.useState([]),[d,f]=c.useState(!1),[O,w]=c.useState(!1),[M,S]=c.useState(""),[t,_]=c.useState("success"),[N,u]=c.useState({}),$=Q(r=>r.userManagement.userData),P=Q(r=>r.appSettings),{t:p}=pe(),D=async()=>new Promise((r,n)=>{var i,h;const a=k=>{r(k)},o=k=>{r(k)};V(`/${ee}${(h=(i=re)==null?void 0:i.DOCUMENT_CONFIGURATION_APIS)==null?void 0:h.GET_FILES_LIST_API}?adminEmail=${$==null?void 0:$.emailId}`,"get",a,o)}),W=async r=>new Promise((n,a)=>{var k,Y;const o=new FormData;r.forEach((R,Fe)=>{o.append("files",R)});const i=R=>{n(R)},h=R=>{n(data)};V(`/${ee}${(Y=(k=re)==null?void 0:k.DOCUMENT_CONFIGURATION_APIS)==null?void 0:Y.UPLOAD_FILES_API}`,"postformdata",i,h,o)}),m=(r,n="success")=>{S(r),_(n),w(!0)},G=()=>{w(!1)},I={primary:"#1976d2",light:"#f5f5f5",accent:"#e0e0e0",text:"#333",secondaryText:"#666",background:"#fff"};c.useEffect(()=>{L()},[]);const L=async()=>{var r;f(!0);try{const n=await D();if(((r=n==null?void 0:n.responseMessage)==null?void 0:r.status)==="Success"){const a=n.documentDetailDtoList.map(o=>({id:o.documentId,docType:o.fileType,docName:o.fileName,uploadedOn:fe(o.docCreationDate).format(P.dateFormat),uploadedBy:o.createdBy,attachmentType:o.attachmentType,documentViewUrl:o.documentViewUrl}));B(a),y(a)}else m(`Error fetching files: ${n.error}`,"error")}catch(n){m(`Error fetching files: ${n.message}`,"error")}finally{f(!1)}},H=async r=>{f(!0);try{const n=await W(r);n.success?(await L(),m(`${r.length} file(s) uploaded successfully`,"success")):m(`Upload failed: ${n.error}`,"error")}catch(n){m(`Upload error: ${n.message}`,"error")}finally{f(!1)}},K=[{field:"id",headerName:"Document ID",flex:1.2,hideable:!1,hidden:!0},{field:"attachmentType",headerName:p("Attachment Type"),flex:1.5,renderCell:r=>{var n;return e(ge,{label:r.value,size:"small",sx:{backgroundColor:(n=T)==null?void 0:n.reportTile.lightBlue,color:T.primary.lightPlus,fontWeight:"medium"}})}},{field:"docName",headerName:p("Document Name"),flex:2,renderCell:r=>s(b,{direction:"row",spacing:1,alignItems:"center",children:[ke(r.value),e(l,{variant:"body2",children:r.value})]})},{field:"uploadedOn",headerName:p("Uploaded On"),flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:p("Uploaded By"),sortable:!1,flex:1},{field:"view",headerName:p("View"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",renderCell:r=>{var n,a;return e(z,{size:"small",sx:{color:T.icon.matView,"&:hover":{backgroundColor:"rgba(2, 136, 209, 0.1)"}},children:e(ue,{index:r.row.id,name:((n=r==null?void 0:r.row)==null?void 0:n.docName)||((a=r==null?void 0:r.row)==null?void 0:a.fileName),documentViewUrl:r.row.documentViewUrl})})}},{field:"action",headerName:p("Action"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:r=>{var n,a,o,i,h;return s(b,{direction:"row",spacing:0,children:[e(z,{size:"small",sx:{color:(n=T)==null?void 0:n.icon.matDownload,"&:hover":{backgroundColor:"rgba(46, 125, 50, 0.1)"}},children:e(me,{index:r.row.id,name:((a=r==null?void 0:r.row)==null?void 0:a.docName)||((o=r==null?void 0:r.row)==null?void 0:o.fileName)})}),e(z,{size:"small",sx:{color:(i=T)==null?void 0:i.icon.delete,"&:hover":{backgroundColor:"rgba(211, 47, 47, 0.1)"}},children:e(xe,{index:r.row.id,name:r.row.docName||((h=r==null?void 0:r.row)==null?void 0:h.fileName),setSnackbar:w,setopenSnackbar:w,setMessageDialogMessage:S,handleSnackbarOpen:m,setDownloadLoader:u,DownloadLoader:N})})]})}}];return s("div",{children:[s(x,{container:!0,spacing:2,sx:{padding:"35px",pt:"40px"},children:[s(x,{container:!0,sx:be,children:[s(x,{item:!0,md:5,xs:12,children:[e(l,{variant:"h3",children:e("strong",{children:p("Documents Configuration")})}),e(l,{variant:"body2",color:T.secondary.grey,children:p("This view displays the list of Documents uploaded for AI")})]}),e(x,{item:!0,md:7,xs:12,sx:{display:"flex"},children:e(x,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,mt:0})})]}),s(x,{item:!0,md:12,sx:{backgroundColor:"#fff",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",paddingRight:"16px",mt:"20px"},children:[s(x,{container:!0,sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",alignItems:"center"},children:[e(l,{variant:"h6",children:e("strong",{children:A})}),!E&&e(Ie,{onFilesAdded:H,maxFiles:U,maxFileSize:g,acceptedTypes:v,currentFileCount:C.length,loading:d})]}),d&&C.length===0?s(b,{alignItems:"center",spacing:2,sx:{py:4},children:[e(j,{}),e(l,{variant:"body2",color:I.secondaryText,children:"Loading files..."})]}):C.length>0?e(x,{sx:{padding:"15px"},children:e(ye,{width:"100%",rows:C,columns:K,hideFooter:!1,getRowIdValue:"id",autoHeight:!0,disableSelectionOnClick:!0,stopPropagation_Column:"action",title:p("Documents Uploaded")})}):s(b,{alignItems:"center",spacing:2,sx:{py:"25vh"},children:[e(De,{sx:{fontSize:40,color:I.accent,transform:"rotate(90deg)"}}),e(l,{variant:"body2",color:I.secondaryText,children:p("No Files Added")})]})]})]}),O&&e(ve,{openSnackBar:O,alertMsg:M,alertType:t,handleSnackBarClose:G})]})};export{Ee as default};
