import{l as Ka,u as ie,i as ta,r as q,lP as Y,a as n,hf as le,T as Q,j as w,gK as $,gM as Fa,A as za,Z as ne,P as de,M as ce,N as se,O as ka,Q as z,S as Te,j6 as ue,f as Ya,g as Za,iZ as l,k as oe,e as Ae,lK as Ee,j4 as O,mf as aa,lO as Ea,h_ as Ha,$ as ea,B as Sa,F as ua,h8 as Se,w as ja,G as qa,hg as Wa,E as Ba}from"./index-fdfa25a0.js";import{F as $a}from"./FilterField-6f6e20f9.js";import{u as he}from"./useCustomDtCall-04c3c72a.js";import{u as Ie}from"./useChangeLogUpdate-3699f77c.js";import{G as Xa}from"./GenericViewGeneral-4b82ad14.js";const me="1",De=({materialID:a,selectedMaterialNumber:I})=>{var H,na;const Z=Ka(),E=ie(),ia=new URLSearchParams(E.search).get("RequestId"),C=ta(t=>t.payload.payloadData),W=ta(t=>{var b,g,L,r;return((r=(L=(g=(b=t.payload[a])==null?void 0:b.payloadData)==null?void 0:g.TaxData)==null?void 0:L.TaxData)==null?void 0:r.TaxDataSet)||[]}),{updateChangeLog:ha}=Ie(),la=q.useRef(!1);q.useEffect(()=>{if(!W.length||la.current)return;let t=!1;const b=W.map(g=>{var L;if(!g.SelectedTaxClass){const r=(L=g.options)==null?void 0:L.find(J=>J.code===me);if(r)return t=!0,{...g,SelectedTaxClass:{TaxClass:r.code,TaxClassDesc:r.desc}}}return g});t&&(Z(Y({materialID:a,viewID:"TaxData",itemID:"TaxData",keyName:"TaxDataSet",data:b})),la.current=!0)},[W,Z,a]);const Ta=(t,b,g,L)=>{const r=[...W];r[t]={...r[t],SelectedTaxClass:b},Z(Y({materialID:a,viewID:"TaxData",itemID:"TaxData",keyName:"TaxDataSet",data:r})),ia&&ha({materialID:I,viewName:l.TAX_DATA,plantData:`${g}-${L}`,fieldName:"Tax Class",jsonName:"SelectedTaxClass",currentValue:b.TaxClass,requestId:C==null?void 0:C.RequestId,childRequestId:ia})};return W.length===0?n(Q,{sx:{textAlign:"center",marginTop:"10px"},children:(H=le)==null?void 0:H.TAXDATA_LOADING}):w(Za,{sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(na=$)==null?void 0:na.primary.white},children:[n(za,{expandIcon:n(Fa,{}),sx:{backgroundColor:$.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:$.hover.hoverbg}},children:n(Q,{variant:"h6",sx:{fontWeight:"bold"},children:"Tax Classification"})}),n(Ya,{children:w(ne,{component:de,children:[n(Q,{variant:"h6",sx:{p:1,fontWeight:"bold",textAlign:"center"},children:"Tax Data"}),w(ce,{children:[n(se,{children:w(ka,{sx:{backgroundColor:"#f5f5f5"},children:[n(z,{sx:{fontWeight:"bold"},children:"Country"}),n(z,{sx:{fontWeight:"bold"},children:"Tax Type"}),n(z,{sx:{fontWeight:"bold"},children:"Tax Class"}),n(z,{sx:{fontWeight:"bold"},children:"Description"})]})}),n(Te,{children:W.map(({Country:t,TaxType:b,options:g=[],SelectedTaxClass:L},r)=>{const J=L?{code:L.TaxClass,desc:L.TaxClassDesc}:{code:"",desc:""};return w(ka,{children:[n(z,{sx:{fontWeight:"bold"},children:t}),n(z,{sx:{fontWeight:"bold"},children:b}),n(z,{children:n(ue,{options:g,value:J,onChange:da=>{const Aa=da?{TaxClass:da.code,TaxClassDesc:da.desc}:null;Ta(r,Aa,t,b)},placeholder:"SELECT TAX CLASS",minWidth:200})}),n(z,{children:J.desc})]},`${t}-${b}`)})})]})]})})]},"Tax_Classification")},Ge=a=>{var Ia,ma;const I=ta(i=>i.payload),Z=ta(i=>{var e;return(e=i==null?void 0:i.request)==null?void 0:e.materialRows}),E=(ma=(Ia=I==null?void 0:I[a.materialID])==null?void 0:Ia.headerData)==null?void 0:ma.orgData;ta(i=>i.userManagement.taskData);const[oa,ia]=q.useState({}),C=ta(i=>{var e;return(e=i.payload.payloadData)==null?void 0:e.RequestType}),[W,ha]=q.useState([]),[la,Ta]=q.useState([]),H=Ka(),{getDtCall:na,dtData:t}=he(),{customError:b}=oe(),{t:g}=Ae();function L(i,e){const T={plant:"Plant",salesOrg:"SalesOrg",dc:"Distribution Channel",sloc:"Storage Location",mrpProfile:"MRP Profile",warehouse:"Warehouse"},d=i.split("-");return e.map((c,s)=>{const S=T[c],_=d[s]||"N/A";return`${S} - ${_}`}).join(", ")}const r=i=>[...new Set(i)].join("$^$"),J=i=>{const e=new Map;return i.forEach(({CountryName:T,Country:d})=>{e.set(d,T)}),Array.from(e,([T,d])=>({Country:T,CountryName:d}))},da=i=>i.map(({Country:e})=>e).join("$^$"),Aa=i=>{const e=`/${qa}${Wa.TAX_DATA.GET_COUNTRY_SALESORG}`;ja(e,"post",s=>{const S=s==null?void 0:s.body,_=J(S),N=da(_);Qa(N)},s=>{b(Ba.NO_DATA_AVAILABLE)},{salesOrg:i})},Qa=i=>{const e=`/${qa}${Wa.TAX_DATA.GET_TAX_COUNTRY}`;ja(e,"post",s=>{var A,u,D,f;const S=s==null?void 0:s.body,_=((f=(D=(u=(A=I[a==null?void 0:a.materialID])==null?void 0:A.payloadData)==null?void 0:u.TaxData)==null?void 0:D.TaxData)==null?void 0:f.TaxDataSet)||[],N={},x=S.filter(o=>o.TaxType);x.forEach(({TaxClass:o,TaxClassDesc:y})=>{N[o]=y});const m=_.map(o=>{const y=x.filter(P=>P.TaxType===o.TaxType&&P.Country===o.Country).map(P=>({code:P.TaxClass,desc:P.TaxClassDesc}));let h=o.SelectedTaxClass;return h&&N[h.TaxClass]&&(h={...h,TaxClassDesc:N[h.TaxClass]}),{...o,options:y,SelectedTaxClass:h}});x.forEach(({TaxType:o,SequenceNo:y,Country:h,TaxClass:P,TaxClassDesc:M})=>{if(!m.some(j=>j.TaxType===o&&j.Country===h)){const j=x.filter(v=>v.TaxType===o&&v.Country===h).map(v=>({code:v.TaxClass,desc:v.TaxClassDesc}));m.push({TaxType:o,SequenceNo:y,Country:h,options:j,SelectedTaxClass:null})}}),H(Y({materialID:(a==null?void 0:a.materialID)||"",keyName:"TaxDataSet",data:m,viewID:"TaxData",itemID:"TaxData"}))},s=>{b(Ba.NO_DATA_AVAILABLE)},{country:i})},p=Z==null?void 0:Z.find(i=>(i==null?void 0:i.id)===a.materialID);q.useEffect(()=>{var i,e,T,d,c,s,S,_,N;if(E){const x=!!((e=(i=I[a.materialID])==null?void 0:i.headerData)!=null&&e.refMaterialData),m=Ee(E,(T=I==null?void 0:I[a.materialID])==null?void 0:T.payloadData,a==null?void 0:a.materialID,H);ia(m),!x&&!a.isDisplay&&m.hasOwnProperty(l.SALES)&&((d=a==null?void 0:a.selectedViews)!=null&&d.includes(l.SALES))&&m[l.SALES].reduxCombinations.forEach((A,u)=>{C!==O.EXTEND&&pa({comb:A,dt:aa.SALES_DIV_PRICE_MAPPING},E[u])}),(!x&&((c=a==null?void 0:a.selectedViews)!=null&&c.includes(l.SALES))||(s=a==null?void 0:a.selectedViews)!=null&&s.includes(l.ACCOUNTING)||(S=a==null?void 0:a.selectedViews)!=null&&S.includes(l.COSTING))&&E.forEach((A,u)=>{C!==O.EXTEND&&!a.isDisplay&&ae({combinations:m,index:u,dt:aa.REG_PLNT_INSPSTK_MAPPING},A)}),x&&Ja(m,(N=(_=I[a.materialID])==null?void 0:_.headerData)==null?void 0:N.refMaterialData)}else ia({})},[E]),q.useEffect(()=>{if(E){const i=[...new Set(E==null?void 0:E.map(T=>{var d;return(d=T.salesOrg)==null?void 0:d.code}))],e=r(i);Aa(e)}},[E,a==null?void 0:a.callGetCountryBasedonSalesOrg]),q.useEffect(()=>{var i,e,T,d,c,s,S,_,N,x,m,A,u,D,f,o,y,h,P,M,X,j,v,sa,R,V,U,K,F,Da,_a,Na,xa,Ca,Pa,Ga,ba,ra,ya,Ra,ga,La,fa,Ma,Oa,wa,va,Va,Ua;if(t){if(((i=t.customParam)==null?void 0:i.dt)===aa.SALES_DIV_PRICE_MAPPING&&((e=a==null?void 0:a.selectedViews)!=null&&e.includes(l.SALES))){const B=(d=Object.keys((T=t==null?void 0:t.data)==null?void 0:T.result[0]))!=null&&d.length?(_=(S=(s=(c=t==null?void 0:t.data)==null?void 0:c.result)==null?void 0:s[0])==null?void 0:S.MDG_MAT_SALESDIV_PRCICEGRP_MAPPING[0])==null?void 0:_.MDG_MAT_MATERIAL_PRICING_GROUP:"";C!==O.EXTEND&&C!==O.CREATE_WITH_UPLOAD&&B&&ca((N=t.customParam)==null?void 0:N.comb,"MatPrGrp",B,"Sales")}else if(((x=t.customParam)==null?void 0:x.dt)===aa.REG_PLNT_INSPSTK_MAPPING){let B=(m=t.customParam)==null?void 0:m.combinations,G=(A=t.customParam)==null?void 0:A.org;if(B!=null&&B.hasOwnProperty(l.SALES)&&((u=a==null?void 0:a.selectedViews)!=null&&u.includes(l.SALES))){const k=(f=Object.keys((D=t==null?void 0:t.data)==null?void 0:D.result[0]))!=null&&f.length?(P=(h=(y=(o=t==null?void 0:t.data)==null?void 0:o.result)==null?void 0:y[0])==null?void 0:h.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:P.MDG_MAT_ITEM_CAT_GROUP:"";C!==O.EXTEND&&C!==O.CREATE_WITH_UPLOAD&&k&&ca(((M=G==null?void 0:G.salesOrg)==null?void 0:M.code)+"-"+((j=(X=G==null?void 0:G.dc)==null?void 0:X.value)==null?void 0:j.code),"ItemCat",k,"Sales")}if(B.hasOwnProperty(l.PURCHASING)&&((v=a==null?void 0:a.selectedViews)!=null&&v.includes(l.PURCHASING))){const k=(R=Object.keys((sa=t==null?void 0:t.data)==null?void 0:sa.result[0]))!=null&&R.length?(F=(K=(U=(V=t==null?void 0:t.data)==null?void 0:V.result)==null?void 0:U[0])==null?void 0:K.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:F.MDG_MAT_POST_TO_INSP_STOCK:"";C!==O.EXTEND&&C!==O.CREATE_WITH_UPLOAD&&k&&ca((_a=(Da=G==null?void 0:G.plant)==null?void 0:Da.value)==null?void 0:_a.code,"IndPostToInspStock",k,"Purchasing")}if(B.hasOwnProperty(l.ACCOUNTING)&&((Na=a==null?void 0:a.selectedViews)!=null&&Na.includes(l.ACCOUNTING))){const k=(Ca=Object.keys((xa=t==null?void 0:t.data)==null?void 0:xa.result[0]))!=null&&Ca.length?(ra=(ba=(Ga=(Pa=t==null?void 0:t.data)==null?void 0:Pa.result)==null?void 0:Ga[0])==null?void 0:ba.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:ra.MDG_MAT_PRICE_UNIT:"";C!==O.EXTEND&&C!==O.CREATE_WITH_UPLOAD&&k&&ca((Ra=(ya=G==null?void 0:G.plant)==null?void 0:ya.value)==null?void 0:Ra.code,"PriceUnit",k,"Accounting")}if(B.hasOwnProperty(l.COSTING)&&((ga=a==null?void 0:a.selectedViews)!=null&&ga.includes(l.COSTING))){const k=(fa=Object.keys((La=t==null?void 0:t.data)==null?void 0:La.result[0]))!=null&&fa.length?(va=(wa=(Oa=(Ma=t==null?void 0:t.data)==null?void 0:Ma.result)==null?void 0:Oa[0])==null?void 0:wa.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:va.MDG_MAT_COSTING_LOT_SIZE:"";C!==O.EXTEND&&C!==O.CREATE_WITH_UPLOAD&&k&&ca((Ua=(Va=G==null?void 0:G.plant)==null?void 0:Va.value)==null?void 0:Ua.code,"Lotsizekey",k,"Costing")}}}},[t]);const Ja=(i,e)=>{var d;let T=(d=I[a.materialID])==null?void 0:d.payloadData;Object.keys(i).forEach(c=>{var S;let s=(S=i[c])==null?void 0:S.reduxCombinations;s==null||s.forEach(_=>{var N,x,m,A,u,D,f,o,y,h,P,M,X,j,v,sa;c!==l.BASIC_DATA&&((N=e==null?void 0:e.copyPayload)!=null&&N.payloadData[c])&&!((x=T[c])!=null&&x[_])&&((A=Object.keys((m=e==null?void 0:e.copyPayload)==null?void 0:m.payloadData[c]))==null||A.forEach(R=>{var U;let V=Ea(R,(U=e==null?void 0:e.copyPayload)==null?void 0:U.payloadData[c][R],a==null?void 0:a.allTabsData[c]);V&&H(Y({materialID:a==null?void 0:a.materialID,viewID:c,itemID:_,keyName:R,data:V}))}),c===l.SALES&&(H(Y({materialID:a==null?void 0:a.materialID,viewID:l.TAX_DATA,itemID:l.TAX_DATA,data:(f=(D=(u=e==null?void 0:e.copyPayload)==null?void 0:u.payloadData)==null?void 0:D.TaxData)==null?void 0:f.TaxData})),(P=Object.keys((h=(y=(o=e==null?void 0:e.copyPayload)==null?void 0:o.payloadData)==null?void 0:y[l.SALES_GENERAL])==null?void 0:h[l.SALES_GENERAL]))==null||P.forEach(R=>{var U,K,F;let V=Ea(R,(F=(K=(U=e==null?void 0:e.copyPayload)==null?void 0:U.payloadData[l.SALES_GENERAL])==null?void 0:K[l.SALES_GENERAL])==null?void 0:F[R],a==null?void 0:a.allTabsData[l.SALES_GENERAL]);V&&H(Y({materialID:a==null?void 0:a.materialID,viewID:l.SALES_GENERAL,itemID:l.SALES_GENERAL,keyName:R,data:V}))})),c===l.PURCHASING&&((X=(M=e==null?void 0:e.copyPayload)==null?void 0:M.payloadData)!=null&&X[l.PURCHASING_GENERAL])&&((sa=Object.keys((v=(j=e==null?void 0:e.copyPayload)==null?void 0:j.payloadData[l.PURCHASING_GENERAL])==null?void 0:v[l.PURCHASING_GENERAL]))==null||sa.forEach(R=>{var U,K,F;let V=Ea(R,(F=(K=(U=e==null?void 0:e.copyPayload)==null?void 0:U.payloadData[l.PURCHASING_GENERAL])==null?void 0:K[l.PURCHASING_GENERAL])==null?void 0:F[R],a==null?void 0:a.allTabsData[l.PURCHASING_GENERAL]);V&&H(Y({materialID:a==null?void 0:a.materialID,viewID:l.PURCHASING_GENERAL,itemID:l.PURCHASING_GENERAL,keyName:R,data:V}))})))})})},pa=(i,e)=>{var d,c;let T={decisionTableId:null,decisionTableName:aa.SALES_DIV_PRICE_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_SALES_ORG":(d=e==null?void 0:e.salesOrg)==null?void 0:d.code,"MDG_CONDITIONS.MDG_MAT_DIVISION":(c=I==null?void 0:I.payloadData)==null?void 0:c.Division}]};i.org=e,na(T,i)},ae=(i,e)=>{var d,c,s;let T={decisionTableId:null,decisionTableName:aa.REG_PLNT_INSPSTK_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(d=I==null?void 0:I.payloadData)==null?void 0:d.Region,"MDG_CONDITIONS.MDG_MAT_PLANT":(s=(c=e==null?void 0:e.plant)==null?void 0:c.value)==null?void 0:s.code}]};i.org=e,na(T,i)};q.useEffect(()=>{let i=E!=null&&E.length?E==null?void 0:E.map(()=>!1):[];Ta(i)},[a.activeViewTab,E]);const ee=(i,e,T)=>(d,c)=>{Ta(s=>({...s,[T]:c}))},ca=(i,e,T,d)=>{H(Y({materialID:(a==null?void 0:a.materialID)||"",keyName:e||"",data:T??null,viewID:d,itemID:i}))},te=q.useMemo(()=>{var _,N,x,m;const i=oa[a.activeViewTab]||{},{displayCombinations:e=[],reduxCombinations:T=[],requiredKeys:d=[]}=i,c=Object.entries((a==null?void 0:a.basicDataTabDetails)||{}),s=(_=a.allTabsData)!=null&&_.hasOwnProperty(l.SALES_GENERAL)?Object.entries(a.allTabsData[l.SALES_GENERAL]):[],S=(N=a.allTabsData)!=null&&N.hasOwnProperty(l.PURCHASING_GENERAL)?Object.entries(a.allTabsData[l.PURCHASING_GENERAL]):[];return a.activeViewTab==="Basic Data"?c.map(A=>{var u;return w(ea,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${(u=a==null?void 0:a.missingValidationPlant)!=null&&u.includes(l.BASIC_DATA)&&!(p!=null&&p.validated)?$.error.dark:$.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Ha},children:[n(ea,{container:!0,children:n(Q,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:A[0]})}),n(Sa,{children:n(ea,{container:!0,spacing:1,children:[...A[1]].filter(D=>D.visibility!=="Hidden").sort((D,f)=>D.sequenceNo-f.sequenceNo).map(D=>n($a,{disabled:a==null?void 0:a.disabled,field:D,dropDownData:a.dropDownData,materialID:a==null?void 0:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,viewName:a==null?void 0:a.activeViewTab,plantData:"basic"},D.fieldName))})})]},A[0])}):e.length?w(ua,{children:[a.activeViewTab===l.SALES&&w(ua,{children:[n(De,{materialID:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber}),(s==null?void 0:s.length)>0&&n(Xa,{materialID:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,GeneralFields:s,disabled:a.disabled,dropDownData:a.dropDownData,viewName:(x=l)==null?void 0:x.SALES_GENERAL})]}),a.activeViewTab===l.PURCHASING&&w(ua,{children:[" ",(S==null?void 0:S.length)>0&&n(Xa,{materialID:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,GeneralFields:S,disabled:a.disabled,dropDownData:a.dropDownData,viewName:(m=l)==null?void 0:m.PURCHASING_GENERAL})]}),e.map((A,u)=>{var D,f,o,y,h;return w(Za,{sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(D=a==null?void 0:a.missingValidationPlant)!=null&&D.includes(A)&&!(p!=null&&p.validated)?(o=(f=$)==null?void 0:f.error)==null?void 0:o.dark:(y=$)==null?void 0:y.primary.white},onChange:ee(A,d,u),expanded:la[u],children:[n(za,{expandIcon:n(Fa,{}),sx:{backgroundColor:$.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:$.hover.hoverbg}},children:n(Q,{variant:"h6",sx:{fontWeight:"bold"},children:L(A,d)})}),n(Ya,{children:((h=W[u])==null?void 0:h.value)===1?w(Sa,{sx:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"200px"},children:[" ",n(Se,{})]}):c.map(P=>w(ea,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Ha},children:[n(ea,{container:!0,children:n(Q,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:g(P[0])})}),n(Sa,{children:n(ea,{container:!0,spacing:1,children:[...P[1]].filter(M=>M.visibility!=="Hidden").sort((M,X)=>M.sequenceNo-X.sequenceNo).map(M=>n($a,{disabled:a==null?void 0:a.disabled,field:M,dropDownData:a.dropDownData,materialID:a==null?void 0:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,viewName:a==null?void 0:a.activeViewTab,plantData:T[u]},M.fieldName))})})]},P[0]))})]},u)})]}):n(Q,{variant:"body2",sx:{margin:"20px",color:"gray"},children:"No Org Data selected."})},[oa,a.activeViewTab,a.basicDataTabDetails,W,a.materialID,a.missingValidationPlant,la]);return n(ua,{children:te})};export{Ge as G};
