import{i as re,l as po,u as rn,j4 as I,w as ze,hj as qo,hg as ye,kS as Zu,kR as eh,k as Qo,r as E,k0 as Kr,kT as ve,iM as ae,hP as jo,G as Re,y as zt,k1 as dd,k2 as pn,k3 as kl,o as $n,p as Gn,jC as _i,jX as tn,hW as Ls,jM as _n,jN as th,kU as sh,ah as An,ai as bn,aj as wn,fF as ln,fV as on,gK as De,a1 as St,kV as oh,h as Xt,j as te,h1 as On,a,T as wt,_ as nh,W as ds,gQ as $i,iR as ud,B as Tt,a8 as Bl,iS as $l,kW as Mc,kX as Oc,fX as Uo,fH as Ho,E as Vo,hA as Bn,V as Fn,kG as rh,F as $s,a3 as En,h8 as lh,hy as Gl,iL as ih,b as Jr,iT as ql,iN as ah,iO as hd,iP as ch,kp as vc,kY as dh,aa as fd,ab as Fl,R as yc,iQ as Ni,he as Yn,af as so,i7 as Vn,hM as gd,iD as hi,kZ as fi,k_ as uh,jt as Wl,k$ as Td,l0 as hh,l1 as pd,l2 as Ed,l3 as Lc,jZ as oi,H as ni,j$ as Si,k4 as Ul,t as fh,l4 as xc,k5 as gh,l5 as Th,l6 as ph,e as Qr,l7 as Eh,g_ as io,l8 as Mi,j5 as Vr,l9 as Ch,jh as zr,i8 as Dc,g as mh,A as Ah,gM as bh,f as wh,la as Pc,lb as Yr,lc as Ih,kA as sn,j2 as Xo,ld as gi,j6 as to,hd as Wn,gR as Rh,gS as _h,hJ as Nh,hC as Cd,jW as Gi,gU as md,le as jl,gW as Fi,lf as Sh,lg as Mh,lh as Oi,li as Oh,lj as vh,lk as Ot,Q as Js,Z as Ad,ll as yh,P as Wi,M as bd,S as wd,O as vi,lm as Lh,iV as Id,iZ as Y,kj as xh,kz as Dh,ln as Ph,m as qh,hE as Uh,ji as Bs,lo as Rd,J as _d,hI as Nd,lp as Hh,jL as Ti,lq as kh,iY as ji,j9 as zi,jE as Bh,lr as $h,ls as qc,lt as Gh,j1 as yi,$ as Rs,h_ as Sd,lu as Fh,lv as Wh,lw as jh,jY as Uc,km as zh,jU as Yh,jT as Kh,j3 as Li,q as Xh,lx as Vh,jF as Jh,ly as Qh,lz as Zh,lA as zl,lB as ef,lC as tf,lD as Nn,lE as Do,lF as pi,lG as Ei,lH as Hc,lI as Ci,lJ as Dl,lK as sf,lL as mi,iW as Yi,j8 as Md,lM as of,i_ as nf,i$ as rf,j0 as lf,js as Mn,lN as af,lO as cf,lP as kc,lQ as Bc,ja as $r,iX as Yo,jb as df,jc as Gr,j7 as Po,lR as $c,iU as Gc,lS as uf,lT as hf,N as ff,lU as Fr,lV as gf,lW as Ai,jd as bi,lX as Tf,jf as Fc,jg as pf,lY as Ef,je as Cf,lZ as Wc,l_ as mf,l$ as Af,jj as Wr,ii as bf,ij as wf,iG as If,i6 as Rf,i0 as _f,i1 as Nf,i2 as Sf,m0 as jc,i4 as Mf,i5 as Of,a0 as vf,gZ as yf,U as Lf,m1 as xf,hi as Df,iH as Pf,iI as zc,iJ as qf,m2 as Uf,ik as Yc}from"./index-fdfa25a0.js";import{F as Hf}from"./FilterField-6f6e20f9.js";import{a as kf,b as Od,u as Bf}from"./useFinanceCostingRows-2aab0ea4.js";import{d as $f,F as Gf}from"./FilterChangeDropdown-22e6e937.js";import{d as Xr}from"./DeleteOutlineOutlined-9e9a8646.js";import{u as vd}from"./useMaterialFieldConfig-6dda1d2a.js";import{d as xi}from"./Description-5b38f787.js";import{G as Ff}from"./GenericTabs-8e261948.js";import Wf from"./AdditionalData-a38f586a.js";import{m as jf}from"./makeStyles-1dfd4db4.js";import{a as zf,d as Yf}from"./AttachFile-1c547195.js";import{T as Kf}from"./Timeline-bb89efb4.js";const Xf=()=>{const e=re(h=>h.payload.payloadData),t=re(h=>h.applicationConfig),n=re(h=>{var le;return(le=h.userManagement)==null?void 0:le.taskData}),o=po(),c=rn(),C=new URLSearchParams(c.search).get("RequestType");return{getRequestHeaderTemplate:()=>{var K,f,X;let h={decisionTableId:null,decisionTableName:"MDG_MAT_REQUEST_HEADER_CONFIG",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(n==null?void 0:n.ATTRIBUTE_2)===((K=I)==null?void 0:K.FINANCE_COSTING)?(f=I)==null?void 0:f.FINANCE_COSTING:C||(e==null?void 0:e.RequestType)||((X=I)==null?void 0:X.CREATE),"MDG_CONDITIONS.MDG_MAT_REGION":(e==null?void 0:e.Region)||"US"}],systemFilters:null,systemOrders:null,filterString:null};const le=V=>{var H,ce;if(V.statusCode===200){const u={"Header Data":((ce=(H=V==null?void 0:V.data)==null?void 0:H.result[0])==null?void 0:ce.MDG_MAT_REQUEST_HEADER_CONFIG).sort((k,F)=>k.MDG_MAT_SEQUENCE_NO-F.MDG_MAT_SEQUENCE_NO).map(k=>({fieldName:k.MDG_MAT_UI_FIELD_NAME,sequenceNo:k.MDG_MAT_SEQUENCE_NO,fieldType:k.MDG_MAT_FIELD_TYPE,maxLength:k.MDG_MAT_MAX_LENGTH,value:k.MDG_MAT_DEFAULT_VALUE,visibility:k.MDG_MAT_VISIBILITY,jsonName:k.MDG_MAT_JSON_FIELD_NAME}))};o(Zu({tab:"Request Header",data:u})),o(eh(u))}},Ie=V=>{console.log(V)};t.environment==="localhost"?ze(`/${qo}${ye.INVOKE_RULES.LOCAL}`,"post",le,Ie,h):ze(`/${qo}${ye.INVOKE_RULES.PROD}`,"post",le,Ie,h)}}},yd=()=>{const e=re(p=>p.paginationData),t=re(p=>p.payload.changeFieldRows),n=re(p=>p.payload.whseList),o=re(p=>p.payload.matNoList),c=re(p=>p.payload.plantList),s=re(p=>p.payload.changeFieldRowsDisplay),C=re(p=>p.payload.selectedRows),i=po(),{customError:h}=Qo(),[le,Ie]=E.useState({errorText:!1,errorTextMessage:""}),K=async(p,T)=>{var N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae,Q,G,Pe,$e,Be,bs,us,Fs,Ws,ws,Qs,Is,js,Zs,os,Ht,Ft,Pt,ns,Ye,yt,Et,_s,Vt,be,kt,rs,ls,$t,is,ot,Ct,as;i(Kr(!0));let S,g;return p===((N=ve)==null?void 0:N.LOGISTIC)?(g={materialNo:(T==null?void 0:T[(L=ae)==null?void 0:L.MATERIAL_NUM])||"",division:(T==null?void 0:T[(m=ae)==null?void 0:m.DIVISION])||"",plant:(T==null?void 0:T[(O=ae)==null?void 0:O.PLANT])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},S=`/${Re}/${(q=(P=ye)==null?void 0:P.CHG_DISPLAY_REQUESTOR)==null?void 0:q.LOGISTIC}`):p===((U=ve)==null?void 0:U.ITEM_CAT)?(g={materialNo:(T==null?void 0:T[(R=ae)==null?void 0:R.MATERIAL_NUM])||"",salesOrg:(T==null?void 0:T[(v=ae)==null?void 0:v.SALES_ORG])||"",distrChan:(T==null?void 0:T[(b=ae)==null?void 0:b.DIST_CHNL])||"",division:(T==null?void 0:T[(ue=ae)==null?void 0:ue.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},S=`/${Re}/${(Ne=(B=ye)==null?void 0:B.CHG_DISPLAY_REQUESTOR)==null?void 0:Ne.SALES}`):p===((A=ve)==null?void 0:A.MRP)?(g={materialNo:(T==null?void 0:T[(Ae=ae)==null?void 0:Ae.MATERIAL_NUM])||"",mrpCtrler:(T==null?void 0:T[(Q=ae)==null?void 0:Q.MRP_CTRLER])||"",plant:(T==null?void 0:T[(G=ae)==null?void 0:G.PLANT])||"",division:(T==null?void 0:T[(Pe=ae)==null?void 0:Pe.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},S=`/${Re}/${(Be=($e=ye)==null?void 0:$e.CHG_DISPLAY_REQUESTOR)==null?void 0:Be.MRP}`):p===((bs=ve)==null?void 0:bs.UPD_DESC)?(g={materialNo:(T==null?void 0:T[(us=ae)==null?void 0:us.MATERIAL_NUM])||"",division:(T==null?void 0:T[(Fs=ae)==null?void 0:Fs.DIVISION])||"",plant:(T==null?void 0:T[(Ws=ae)==null?void 0:Ws.PLANT])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},S=`/${Re}/${(Qs=(ws=ye)==null?void 0:ws.CHG_DISPLAY_REQUESTOR)==null?void 0:Qs.DESC}`):p===((Is=ve)==null?void 0:Is.WARE_VIEW_2)?(g={materialNo:(T==null?void 0:T[(js=ae)==null?void 0:js.MATERIAL_NUM])||"",whseNo:(T==null?void 0:T[(Zs=ae)==null?void 0:Zs.WAREHOUSE])||"",plant:(T==null?void 0:T[(os=ae)==null?void 0:os.PLANT])||"",division:(T==null?void 0:T[(Ht=ae)==null?void 0:Ht.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},S=`/${Re}/${(Pt=(Ft=ye)==null?void 0:Ft.CHG_DISPLAY_REQUESTOR)==null?void 0:Pt.WAREHOUSE}`):p===((ns=ve)==null?void 0:ns.CHG_STAT)?(g={materialNo:(T==null?void 0:T[(Ye=ae)==null?void 0:Ye.MATERIAL_NUM])||"",salesOrg:(T==null?void 0:T[(yt=ae)==null?void 0:yt.SALES_ORG])||"",distrChan:(T==null?void 0:T[(Et=ae)==null?void 0:Et.DIST_CHNL])||"",division:(T==null?void 0:T[(_s=ae)==null?void 0:_s.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},S=`/${Re}/${(be=(Vt=ye)==null?void 0:Vt.CHG_DISPLAY_REQUESTOR)==null?void 0:be.CHG_STATUS}`):p===((kt=ve)==null?void 0:kt.SET_DNU)&&(g={materialNo:(T==null?void 0:T[(rs=ae)==null?void 0:rs.MATERIAL_NUM])||"",salesOrg:(T==null?void 0:T[(ls=ae)==null?void 0:ls.SALES_ORG])||"",distrChan:(T==null?void 0:T[($t=ae)==null?void 0:$t.DIST_CHNL])||"",division:(T==null?void 0:T[(is=ae)==null?void 0:is.DIVISION])||"",plant:(T==null?void 0:T[(ot=ae)==null?void 0:ot.PLANT])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},S=`/${Re}/${(as=(Ct=ye)==null?void 0:Ct.CHG_DISPLAY_REQUESTOR)==null?void 0:as.SET_DNU}`),new Promise((xs,Wt)=>{ze(S,"post",Le=>{var Ds,zs,Ns,Ss,Jt,Ms,Ys,nt,Qt;i(dd(Le==null?void 0:Le.totalElements)),(Le==null?void 0:Le.totalPages)===1||(Le==null?void 0:Le.currentPage)+1===(Le==null?void 0:Le.totalPages)?(i(pn(Le==null?void 0:Le.totalElements)),i(kl(!0))):i(pn(((Le==null?void 0:Le.currentPage)+1)*(Le==null?void 0:Le.pageSize)));const mt=p===((Ds=ve)==null?void 0:Ds.LOGISTIC)?f(Le==null?void 0:Le.body):p===((zs=ve)==null?void 0:zs.ITEM_CAT)?X(Le==null?void 0:Le.body):p===((Ns=ve)==null?void 0:Ns.MRP)?V(Le==null?void 0:Le.body):p===((Ss=ve)==null?void 0:Ss.UPD_DESC)?$(Le==null?void 0:Le.body):p===((Jt=ve)==null?void 0:Jt.WARE_VIEW_2)?J(Le==null?void 0:Le.body):p===((Ms=ve)==null?void 0:Ms.CHG_STAT)?H(Le==null?void 0:Le.body):p===((Ys=ve)==null?void 0:Ys.SET_DNU)?ce(Le==null?void 0:Le.body):[];if(Array.isArray(mt))i($n([...t,...mt])),i(Gn({...s,[e==null?void 0:e.page]:mt}));else if(typeof mt=="object"&&mt!==null){const ke={...t};(nt=Object==null?void 0:Object.keys(mt))==null||nt.forEach(dt=>{ke[dt]=[...ke[dt]||[],...mt[dt]]}),i($n(ke)),i(Gn({...s,[e==null?void 0:e.page]:mt}))}i(Kr(!1));let hs;if(Array.isArray(mt))hs=mt==null?void 0:mt.map(ke=>ke==null?void 0:ke.id),i(_i([...C,...hs]));else if(typeof mt=="object"&&mt!==null){hs=Object.keys(mt).reduce((dt,Zt)=>{var z;return dt[Zt]=((z=mt[Zt])==null?void 0:z.map(Se=>Se==null?void 0:Se.id))||[],dt},{});const ke={...C};(Qt=Object==null?void 0:Object.keys(hs))==null||Qt.forEach(dt=>{ke[dt]=[...ke[dt]||[],...hs[dt]]}),i(_i(ke))}xs(Le==null?void 0:Le.body)},()=>{var Le;i(Kr(!1)),Wt(new Error((Le=tn)==null?void 0:Le.ERROR_MSG))},g)})},f=p=>{const T=[];let S=1;const g=new Set;return p.forEach(N=>{N.ToLogisticdata.forEach(L=>{g.add(L.Material);const m={...L,id:Ls(),slNo:S++,MatlType:(N==null?void 0:N.MatlType)||""};T.push(m)})}),i(_n([...o,...g])),T},X=p=>{const T=[];let S=1;const g=new Set;return p.forEach(N=>{N.Tosalesdata.forEach(L=>{g.add(L.Material);const m={...L,id:Ls(),slNo:S++,MatlType:(N==null?void 0:N.MatlType)||""};T.push(m)})}),i(_n([...o,...g])),T},V=p=>{const T={"Basic Data":[],"Plant Data":[]};let S=1,g=1;const N=new Set,L=new Set;return p.forEach(m=>{const{Tomrpupdate:O,ToBasicdata:P,Material:q,MatlType:U}=m;N.add(q),T["Basic Data"].push({...P,id:Ls(),slNo:S++,type:"Basic Data",MatlType:U}),O.forEach(R=>{L.add(R==null?void 0:R.Plant),T["Plant Data"].push({...R,id:Ls(),slNo:g++,type:"Plant Data"})})}),i(_n([...o,...N])),i(th([...c,...L])),T},H=p=>{const T={"Basic Data":[],"Plant Data":[],"Sales Data":[]};let S=1,g=1,N=1;const L=new Set;return p.forEach(m=>{const{Tosalesdata:O,ToBasicdata:P,Toplantdata:q,Material:U,MatlType:R}=m;L.add(U),T["Basic Data"].push({...P,id:Ls(),slNo:S++,type:"Basic Data",MatlType:R}),q==null||q.forEach(v=>{T["Plant Data"].push({...v,id:Ls(),slNo:g++,type:"Plant Data"})}),O==null||O.forEach(v=>{T["Sales Data"].push({...v,id:Ls(),slNo:N++,type:"Sales Data"})})}),i(_n([...o,...L])),T},ce=p=>{const T={"Basic Data":[],"Plant Data":[],"Sales Data":[],Description:[]};let S=1,g=1,N=1,L=1;const m=new Set;return p.forEach(O=>{const{Tosalesdata:P,ToBasicdata:q,Toplantdata:U,Tomaterialdescription:R,Material:v,MatlType:b}=O;m.add(v),T["Basic Data"].push({...q,id:Ls(),slNo:S++,type:"Basic Data",MatlType:b}),U==null||U.forEach(ue=>{T["Plant Data"].push({...ue,id:Ls(),slNo:g++,type:"Plant Data"})}),P==null||P.forEach(ue=>{T["Sales Data"].push({...ue,id:Ls(),slNo:N++,type:"Sales Data"})}),R==null||R.forEach(ue=>{T.Description.push({...ue,id:Ls(),slNo:L++,type:"Description"})})}),i(_n([...o,...m])),T},J=p=>{const T=[],S=new Set;let g=1;const N=new Set;p.forEach(m=>{m.ToWarehousedata.forEach(O=>{S.add(O.WhseNo),N.add(O.Material);const P={...O,id:Ls(),slNo:g++,MatlType:(m==null?void 0:m.MatlType)||""};T.push(P)})});const L=[...S];return i(sh(L)),i(_n([...o,...N])),T},$=p=>{const T=[];let S=1;const g=new Set;return p.forEach(N=>{N.Tomaterialdescription.forEach(L=>{g.add(L.Material);const m={...L,id:Ls(),slNo:S++,MatlType:(N==null?void 0:N.MatlType)||""};T.push(m)})}),i(_n([...o,...g])),T};E.useEffect(()=>{(async()=>{if((n==null?void 0:n.length)>0){const T=await u(n);i(jo({keyName:"Unittype1",data:T}))}})()},[n]);const u=async p=>{const T={};for(const S of p){let g={whseNo:S};try{const N=await new Promise(L=>{var m,O;ze(`/${Re}${(O=(m=ye)==null?void 0:m.DEPENDENT_LOOKUPS)==null?void 0:O.UNITTYPE}`,"post",P=>{var q;P.statusCode===((q=zt)==null?void 0:q.STATUS_200)?L(P==null?void 0:P.body):(h("Failed to fetch data"),L([]))},P=>{h(P),L([])},g)});T[S]=N}catch(N){h(N),T[S]=[]}}return T};E.useEffect(()=>{(async()=>{if((c==null?void 0:c.length)>0){const T=await k(c);i(jo({keyName:"Spproctype",data:T}));const S=await F(c);i(jo({keyName:"MrpCtrler",data:S}))}})()},[c]);const k=async p=>{const T={};for(const S of p){let g={plant:S};try{const N=await new Promise(L=>{var m,O;ze(`/${Re}${(O=(m=ye)==null?void 0:m.DATA)==null?void 0:O.GET_SPPROC_TYPE}`,"post",P=>{var q;P.statusCode===((q=zt)==null?void 0:q.STATUS_200)?L(P==null?void 0:P.body):(h("Failed to fetch data"),L([]))},P=>{h(P),L([])},g)});T[S]=N}catch(N){h(N),T[S]=[]}}return T},F=async p=>{const T={};for(const S of p){let g={plant:S};try{const N=await new Promise(L=>{var m,O;ze(`/${Re}${(O=(m=ye)==null?void 0:m.DATA)==null?void 0:O.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",P=>{var q;P.statusCode===((q=zt)==null?void 0:q.STATUS_200)?L(P==null?void 0:P.body):(h("Failed to fetch data"),L([]))},P=>{h(P),L([])},g)});T[S]=N}catch(N){h(N),T[S]=[]}}return T};return{fetchDisplayDataRequestor:K,errorState:le}};var Ki={},Vf=bn;Object.defineProperty(Ki,"__esModule",{value:!0});var Di=Ki.default=void 0,Jf=Vf(An()),Qf=wn;Di=Ki.default=(0,Jf.default)((0,Qf.jsx)("path",{d:"M11 16h2v2h-2zm1-14C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4"}),"HelpOutlineTwoTone");const Zf=ln(on)(({theme:e})=>{var t,n,o,c;return{"& .MuiPaper-root":{borderRadius:"12px",boxShadow:"0 4px 20px rgba(0, 0, 0, 0.1)",border:`1px solid ${(n=(t=De)==null?void 0:t.placeholder)==null?void 0:n.color}`,backgroundColor:(c=(o=De)==null?void 0:o.primary)==null?void 0:c.white,maxWidth:"600px"}}}),eg=ln(St)(({theme:e})=>{var t,n,o,c;return{borderRadius:"8px",padding:"1.2rem 1rem !important",backgroundColor:(n=(t=De)==null?void 0:t.primary)==null?void 0:n.lightPlus,"&:hover":{backgroundColor:(c=(o=De)==null?void 0:o.info)==null?void 0:c.dark,boxShadow:"0 2px 8px rgba(25, 118, 210, 0.3)"},transition:"all 0.2s ease-in-out",textTransform:"none",fontWeight:500}}),Kc=ln(oh)(({theme:e})=>{var t,n;return{borderRadius:"6px",backgroundColor:(n=(t=De)==null?void 0:t.secondary)==null?void 0:n.lightYellow,display:"flex",alignItems:"center","& .MuiAlert-icon":{display:"flex",alignItems:"center",justifyContent:"center"},marginTop:"1rem"}}),Xc=ln(Xt)({maxWidth:"none"}),Ld=({onDownloadTypeChange:e,open:t,downloadType:n,handleDownloadTypeChange:o,onClose:c})=>{var C,i,h,le,Ie,K;const s=n==="systemGenerated"?(C=Vo)==null?void 0:C.SYSTEM_GENERATED_MSG:(i=Vo)==null?void 0:i.EMAIL_DELIVERY_MSG;return te(Zf,{open:t,onClose:c,children:[te(On,{sx:{backgroundColor:(le=(h=De)==null?void 0:h.success)==null?void 0:le.light,padding:"1rem 1.5rem",borderBottom:`1px solid ${(K=(Ie=De)==null?void 0:Ie.primary)==null?void 0:K.whiteSmoke}`,display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[a(wt,{variant:"h6",sx:{fontWeight:600,color:"#333",letterSpacing:"0.2px"},children:"Select Download Option"}),a(ds,{size:"small",onClick:c,children:a(nh,{fontSize:"small"})})]}),a(Uo,{sx:{padding:"1.5rem"},children:te($i,{component:"fieldset",sx:{width:"100%"},children:[te(ud,{"aria-label":"download-option",name:"download-option",value:n,onChange:o,sx:{display:"flex",flexDirection:"row",gap:2,alignItems:"center"},children:[te(Tt,{sx:{flex:1,padding:"0.4rem",borderRadius:"6px",backgroundColor:n==="systemGenerated"?"#f0f4ff":"#ffffff",border:n==="systemGenerated"?"1px solid #1976d2":"1px solid #e0e0e0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:"#f7f9fc"},display:"flex",alignItems:"center",justifyContent:"space-between"},children:[a(Bl,{value:"systemGenerated",control:a($l,{color:"primary",size:"small"}),label:te(Tt,{sx:{display:"flex",alignItems:"center",gap:.5},children:[a(Mc,{sx:{mr:1,color:"#1976d2"}}),a(wt,{sx:{fontWeight:500,color:"#333",fontSize:"0.85rem"},children:"System-Generated"})]}),sx:{flexGrow:1,margin:0}}),a(Xc,{title:a("span",{style:{whiteSpace:"nowrap",fontSize:"12px"},children:"Download Excel file instantly"}),arrow:!0,placement:"top",children:a(Di,{sx:{color:"#1976d2",fontSize:"1.1rem",verticalAlign:"middle",mr:1}})})]}),te(Tt,{sx:{flex:1,padding:"0.4rem",borderRadius:"8px",backgroundColor:n==="mailGenerated"?"#f0f4ff":"#ffffff",border:n==="mailGenerated"?"1px solid #1976d2":"1px solid #e0e0e0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:"#f7f9fc"},display:"flex",alignItems:"center",justifyContent:"space-between"},children:[a(Bl,{value:"mailGenerated",control:a($l,{color:"primary",size:"small"}),label:te(Tt,{sx:{display:"flex",alignItems:"center",gap:.5},children:[a(Oc,{sx:{mr:1,color:"#1976d2"}}),a(wt,{sx:{fontWeight:500,color:"#333",fontSize:"0.85rem"},children:"Mail-Generated"})]}),sx:{flexGrow:1,margin:0}}),a(Xc,{title:a("span",{style:{whiteSpace:"nowrap",fontSize:"12px"},children:"Receive the Excel file via email"}),arrow:!0,placement:"top",children:a(Di,{sx:{color:"#1976d2",fontSize:"1.1rem",verticalAlign:"middle",mr:1}})})]})]}),a(Kc,{severity:"info",children:a(wt,{sx:{fontSize:"0.9rem",color:"#555"},children:s[0]})}),a(Kc,{severity:"info",children:a(wt,{sx:{fontSize:"0.9rem",color:"#555"},children:s[1]})})]})}),a(Ho,{sx:{padding:"0 1.5rem 1.5rem"},children:a(eg,{variant:"contained",onClick:e,startIcon:n==="systemGenerated"?a(Mc,{}):a(Oc,{}),children:n==="systemGenerated"?"Download":"Send Email"})})]})},tg=({param:e,mandatory:t=!1,dropDownData:n,allDropDownData:o,selectedValues:c,inputState:s,handleSelectAll:C,handleSelectionChange:i,handleMatInputChange:h,handleScroll:le,dropdownRef:Ie,errors:K={},formatOptionLabel:f,handlePopoverOpen:X,handlePopoverClose:V,handleMouseEnterPopover:H,handleMouseLeavePopover:ce,isPopoverVisible:J,popoverId:$,popoverAnchorEl:u,popoverRef:k,popoverContent:F,isMaterialNum:p=!1,isLoading:T=!1,isSelectAll:S=!1,singleSelect:g=!1,hasMoreItems:N=!0,totalCount:L=0,loadedCount:m=0})=>{const O=()=>{const U=p?(n==null?void 0:n[e==null?void 0:e.key])||[]:(n==null?void 0:n[e==null?void 0:e.key])||(o==null?void 0:o[e==null?void 0:e.key])||[];return S&&U.length>0&&!g?["Select All",...U]:U},P=()=>{if(!g)return c[e.key]||[];const U=c[e.key];return Array.isArray(U)&&U.length>0?U[0]:null},q=U=>{!N&&p||le(U)};return a(Gl,{multiple:!g,disableListWrap:!0,options:O(),getOptionLabel:U=>typeof U=="string"?U:U==="Select All"?"Select All":f(U),value:g?P():c[e.key]||[],inputValue:p&&!g?s==null?void 0:s.code:void 0,onChange:(U,R)=>{!g&&R.includes("Select All")?C(e.key,O().filter(v=>v!=="Select All")):g?i(e.key,R?[R]:[]):i(e.key,R)},disableCloseOnSelect:!g,ListboxProps:{onScroll:q,ref:Ie},renderOption:(U,R,{selected:v})=>{var B;const ue=R==="Select All"?((B=c[e.key])==null?void 0:B.length)===O().filter(Ne=>Ne!=="Select All").length:v;return te("li",{...U,style:{display:"flex",alignItems:"center",width:"100%",cursor:"pointer"},children:[!g&&a(Bn,{checked:ue,sx:{marginRight:1}}),typeof R=="string"?a("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:R,children:R}):te("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:`${R==null?void 0:R.code}${R!=null&&R.desc?` - ${R==null?void 0:R.desc}`:""}`,children:[a("strong",{children:R==null?void 0:R.code}),R!=null&&R.desc?` - ${R==null?void 0:R.desc}`:""]})]})},renderTags:(U,R)=>{if(g)return null;const v=U.map(b=>typeof b=="string"?b:f(b)).join("<br />");return U.length>1?te($s,{children:[a(Fn,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${f(U[0])}`,...R({index:0})}),a(Fn,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`+${U.length-1}`,onMouseEnter:b=>X(b,v),onMouseLeave:V}),a(rh,{id:$,open:J,anchorEl:u,onClose:V,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},onMouseEnter:H,onMouseLeave:ce,ref:k,sx:{"& .MuiPopover-paper":{backgroundColor:De.primary.whiteSmoke,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"10px",fontSize:"0.875rem",color:De.blue.main,border:"1px solid #ddd"}},children:a(Tt,{sx:{maxHeight:"270px",overflowY:"auto",padding:"5px"},dangerouslySetInnerHTML:{__html:F}})})]}):U.map((b,ue)=>a(Fn,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${f(b)}`,...R({index:ue})}))},renderInput:U=>{var R,v;return a(En,{...U,label:t?te($s,{children:[te("strong",{children:["Select ",e.key]})," ",a("span",{style:{color:(v=(R=De)==null?void 0:R.error)==null?void 0:v.dark},children:"*"})]}):`Select ${e.key}`,variant:"outlined",error:!!K[e.key],helperText:K[e.key],onChange:h||void 0,InputProps:{...U.InputProps,endAdornment:te($s,{children:[T?a(lh,{size:20,sx:{mr:1}}):null,p&&L>0&&te(Tt,{component:"span",sx:{mr:1,fontSize:"0.75rem",color:"text.secondary"},children:[m,"/",L]}),U.InputProps.endAdornment]})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"8px",height:50,boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},"& .MuiInputLabel-root":{fontWeight:500}}})}},e.key)},sg=E.forwardRef(function(t,n){return a(ih,{direction:"down",ref:n,...t})}),og=({open:e,onClose:t,parameters:n,templateName:o,setShowTable:c,allDropDownData:s})=>{var Ut,Kt,Ps,qs,eo,lt,qt;const[C,i]=E.useState({}),[h,le]=E.useState({}),[Ie,K]=E.useState({}),[f,X]=E.useState(""),[V,H]=E.useState(!1),[ce,J]=E.useState("success"),[$,u]=E.useState(!1),[k,F]=E.useState(""),[p,T]=E.useState(""),[S,g]=E.useState(!1),[N,L]=E.useState("systemGenerated"),[m,O]=E.useState([]),[P,q]=E.useState(""),[U,R]=E.useState(!1),v=re(ee=>ee.payload.payloadData),b=re(ee=>ee.request.requestHeader.requestId),ue=re(ee=>ee.payload.dataLoading),B=re(ee=>ee.request.salesOrgDTData),[Ne,A]=E.useState({}),[Ae,Q]=E.useState({[ae.MATERIAL_NUM]:!1,[ae.PLANT]:!1,[ae.SALES_ORG]:!1,[ae.DIVISION]:!1,[ae.DIST_CHNL]:!1,[ae.WAREHOUSE]:!1,[ae.STORAGE_LOC]:!1,[ae.MRP_CTRLER]:!1}),[G,Pe]=E.useState(0),[$e,Be]=E.useState({code:"",desc:""}),[bs,us]=E.useState(null),Fs=E.useRef(null),[Ws,ws]=E.useState(!1),[Qs,Is]=E.useState(null),[js,Zs]=E.useState(""),[os,Ht]=E.useState(!1),Ft=E.useRef(null),Pt=Jr(),ns=po(),{fetchDisplayDataRequestor:Ye}=yd(),[yt,Et]=E.useState(0),[_s,Vt]=E.useState(null),[be,kt]=E.useState([]),[rs,ls]=E.useState(0),[$t,is]=E.useState(0),ot=E.useRef(),Ct=()=>{var ee;(ee=ot==null?void 0:ot.current)==null||ee.click()},as=(Ut=ql[v==null?void 0:v.TemplateName])==null?void 0:Ut.map(ee=>({field:ee.key,headerName:ee.key,editable:!0,flex:2})),xs=200,Wt=E.useCallback(ee=>{ee.preventDefault();const Me=(ee.clipboardData||window.clipboardData).getData("Text").trim().split(`
`).map((Xe,oe)=>{const et=Xe.split("	"),Oe={id:oe+1};return as.forEach((qe,Ze)=>{Oe[qe.field]=et[Ze]||""}),Oe});kt(Me)},[]);E.useEffect(()=>{if(yt===1)return document.addEventListener("paste",Wt),()=>{document.removeEventListener("paste",Wt)}},[yt,Wt]);const uo=(ee,we)=>{Et(we),yt===1&&Vt("handlePasteMaterialData")};`& .${hd.tooltip}`+"";const no={convertJsonToExcel:()=>{let ee=[];as==null||as.forEach(we=>{we.headerName.toLowerCase()!=="action"&&!we.hide&&ee.push({header:we.headerName,key:we.field})}),gd({fileName:"Material Data",columns:ee,rows:be}),is(1)}},Le=(ee,we)=>{Is(ee.currentTarget),Zs(we),Ht(!0)},mt=()=>{Ht(!1)},hs=()=>{Ht(!0)},Ds=()=>{Ht(!1)},Ns=!!Qs?"custom-popover":void 0,Ss=(ee,we)=>{i(M=>({...M,[ee]:we})),we.length>0&&K(M=>({...M,[ee]:""}))};E.useEffect(()=>{le(Ms(C)),ns(ch(Ms(C)))},[C]),E.useEffect(()=>{if(be){let ee=Ys(be);i(ee)}},[be]);const Jt=(ee,we)=>{var Me;const M=((Me=C[ee])==null?void 0:Me.length)===we.length;i(Xe=>({...Xe,[ee]:M?[]:we})),M||K(Xe=>({...Xe,[ee]:""}))},Ms=ee=>{const we={};for(const M in ee)ee.hasOwnProperty(M)&&(we[M]=ee[M].map(Me=>Me.code).join(","));return we},Ys=ee=>{const we={};return ee.forEach(M=>{Object.keys(M).forEach(Me=>{Me!=="id"&&M[Me].trim()!==""&&(we[Me]||(we[Me]=[]),we[Me].push({code:M[Me].trim()}))})}),we},nt=ee=>{const we=fi[ee]||[],M=we==null?void 0:we.filter(Me=>!h[Me]||h[Me].trim()==="");return M.length>0?(R(!0),q(Vo.MANDATORY_FILTER_MD(M.join(", "))),!1):!0},Qt=async()=>{if(nt(o))try{const ee=await Ye(o,h);ee&&ee.length>0?(R(!1),c(!0)):(R(!0),q("No data found for the selected criteria."))}catch{R(!0),q("Error fetching data.")}},ke=()=>{var Xe,oe,et;T("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),X(!0),t();let ee=((Xe=ql[v==null?void 0:v.TemplateName])==null?void 0:Xe.map(Oe=>Oe.key))||[],we={};yt===0?we={materialDetails:[ee.reduce((Oe,qe)=>(Oe[qe]=h!=null&&h[qe]?h==null?void 0:h[qe]:"",Oe),{})],templateHeaders:v!=null&&v.FieldName?(oe=v.FieldName)==null?void 0:oe.join("$^$"):"",requestId:b||(v==null?void 0:v.RequestId)||"",templateName:v!=null&&v.TemplateName?v.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v5",rolePrefix:""}:we={materialDetails:[ee.reduce((Oe,qe)=>(Oe[qe]=be.map(Ze=>{var he;return(he=Ze[qe])==null?void 0:he.trim()}).filter(Ze=>Ze!=="").join(",")||"",Oe),{})],templateHeaders:v!=null&&v.FieldName?(et=v.FieldName)==null?void 0:et.join("$^$"):"",requestId:b||(v==null?void 0:v.RequestId)||"",templateName:v!=null&&v.TemplateName?v.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v5",rolePrefix:""};const M=Oe=>{var he;if((Oe==null?void 0:Oe.size)==0){X(!1),T(""),hi((he=Vo)==null?void 0:he.NO_DATA_FOUND,"error",{position:"top-center",largeWidth:!0}),setTimeout(()=>{var Nt;Pt((Nt=so)==null?void 0:Nt.REQUEST_BENCH)},2600);return}const qe=URL.createObjectURL(Oe),Ze=document.createElement("a");Ze.href=qe,Ze.setAttribute("download",`${v.TemplateName}_Mass Change.xlsx`),document.body.appendChild(Ze),Ze.click(),document.body.removeChild(Ze),URL.revokeObjectURL(qe),X(!1),T(""),H(!0),F(`${v.TemplateName}_Mass Change.xlsx has been downloaded successfully.`),J("success"),Zt(),setTimeout(()=>{var Nt;Pt((Nt=so)==null?void 0:Nt.REQUEST_BENCH)},2600)},Me=()=>{var Oe;X(!1),T(""),hi((Oe=Vo)==null?void 0:Oe.ERR_DOWNLOADING_EXCEL,"error",{position:"top-center"}),setTimeout(()=>{var qe;Pt((qe=so)==null?void 0:qe.REQUEST_BENCH)},2600)};ze(`/${Re}/excel/downloadExcelWithData`,"postandgetblob",M,Me,we)},dt=()=>{var Xe,oe,et;X(!0),t();let ee=((Xe=ql[v==null?void 0:v.TemplateName])==null?void 0:Xe.map(Oe=>Oe.key))||[],we={};yt===0?we={materialDetails:[ee.reduce((Oe,qe)=>(Oe[qe]=h!=null&&h[qe]?h==null?void 0:h[qe]:"",Oe),{})],templateHeaders:v!=null&&v.FieldName?(oe=v.FieldName)==null?void 0:oe.join("$^$"):"",requestId:b||(v==null?void 0:v.RequestId)||"",templateName:v!=null&&v.TemplateName?v.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""}:we={materialDetails:[ee.reduce((Oe,qe)=>(Oe[qe]=be.map(Ze=>{var he;return(he=Ze[qe])==null?void 0:he.trim()}).filter(Ze=>Ze!=="").join(",")||"",Oe),{})],templateHeaders:v!=null&&v.FieldName?(et=v.FieldName)==null?void 0:et.join("$^$"):"",requestId:b||(v==null?void 0:v.RequestId)||"",templateName:v!=null&&v.TemplateName?v.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""};const M=()=>{X(!1),T(""),H(!0),F("Download has been started. You will get the Excel file via email."),J("success"),Zt(),setTimeout(()=>{var Oe;Pt((Oe=so)==null?void 0:Oe.REQUEST_BENCH)},2600)},Me=()=>{X(!1),H(!0),F("Oops! Something went wrong. Please try again later."),J("danger"),Zt(),setTimeout(()=>{var Oe;Pt((Oe=so)==null?void 0:Oe.REQUEST_BENCH)},2600)};ze(`/${Re}/excel/downloadExcelWithDataInMail`,"postandgetblob",M,Me,we)},Zt=()=>{u(!0)},z=()=>{u(!1)},Se=()=>{g(!0)},ne=()=>{g(!1),L("systemGenerated")},ge=ee=>{var we;L((we=ee==null?void 0:ee.target)==null?void 0:we.value)},xe=()=>{N==="systemGenerated"&&(ke(),ne()),N==="mailGenerated"&&(dt(),ne())};E.useEffect(()=>{var M;const{[(M=ae)==null?void 0:M.MATERIAL_NUM]:ee,...we}=C||{};we&&Object.keys(we).length>0&&(Be({code:"",desc:""}),Ke("",!0))},[JSON.stringify({...C,[ae.MATERIAL_NUM]:void 0})]);const pe=ee=>{var Me;const we=(Me=ee.target.value)==null?void 0:Me.toUpperCase();Be({code:we,desc:""}),Pe(0),bs&&clearTimeout(bs);const M=setTimeout(()=>{Ke(we,!0)},500);us(M)},Ke=(ee="",we=!1)=>{var Us,es,Hs,Os,fs,Eo,cs,st,ts,ho,fo,Co,mo,Ks,Rt,Ao,gs,bo,wo,Io,Ro,_o,Z,Ge,Fe,Ve,fe,Qe,ht,it,_t,Bt,We,at;Q(Ee=>({...Ee,[ae.MATERIAL_NUM]:!0}));const M=((es=C[(Us=ae)==null?void 0:Us.DIVISION])==null?void 0:es.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"",Me=((Os=C[(Hs=ae)==null?void 0:Hs.PLANT])==null?void 0:Os.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"",Xe=((Eo=C[(fs=ae)==null?void 0:fs.SALES_ORG])==null?void 0:Eo.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||((cs=B==null?void 0:B.uniqueSalesOrgList)==null?void 0:cs.map(Ee=>Ee.code).join("$^$"))||"",oe=((ts=C[(st=ae)==null?void 0:st.DIST_CHNL])==null?void 0:ts.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"",et=((fo=C[(ho=ae)==null?void 0:ho.MRP_CTRLER])==null?void 0:fo.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"",Oe=((mo=C[(Co=ae)==null?void 0:Co.WAREHOUSE])==null?void 0:mo.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"";let qe="",Ze={materialNo:ee??"",salesOrg:Xe,top:xs,skip:we?0:G};switch(o){case((Ks=ve)==null?void 0:Ks.LOGISTIC):qe=(Ao=(Rt=ye)==null?void 0:Rt.MAT_SEARCH_APIS)==null?void 0:Ao.LOGISTIC,Ze={...Ze,division:M,plant:Me};break;case((gs=ve)==null?void 0:gs.MRP):qe=(wo=(bo=ye)==null?void 0:bo.MAT_SEARCH_APIS)==null?void 0:wo.MRP,Ze={...Ze,division:M,plant:Me,mrpCtlr:et};break;case((Io=ve)==null?void 0:Io.ITEM_CAT):qe=(_o=(Ro=ye)==null?void 0:Ro.MAT_SEARCH_APIS)==null?void 0:_o.SALES,Ze={...Ze,division:M,salesOrg:Xe,distrChan:oe};break;case((Z=ve)==null?void 0:Z.WARE_VIEW_2):qe=(Fe=(Ge=ye)==null?void 0:Ge.MAT_SEARCH_APIS)==null?void 0:Fe.WAREHOUSE,Ze={...Ze,division:M,plant:Me,whseNo:Oe};break;case((Ve=ve)==null?void 0:Ve.CHG_STAT):qe=(Qe=(fe=ye)==null?void 0:fe.MAT_SEARCH_APIS)==null?void 0:Qe.CHG_STATUS,Ze={...Ze,division:M,salesOrg:Xe,distrChan:oe};break;case((ht=ve)==null?void 0:ht.SET_DNU):qe=(_t=(it=ye)==null?void 0:it.MAT_SEARCH_APIS)==null?void 0:_t.SET_DNU,Ze={...Ze,division:M,salesOrg:Xe,distrChan:oe,plant:Me};break;case((Bt=ve)==null?void 0:Bt.UPD_DESC):qe=(at=(We=ye)==null?void 0:We.MAT_SEARCH_APIS)==null?void 0:at.DESC,Ze={...Ze,division:M,plant:Me};break;default:return}const he=Ee=>{(Ee==null?void 0:Ee.statusCode)===zt.STATUS_200?((Ee==null?void 0:Ee.count)!==void 0&&ls(Ee==null?void 0:Ee.count),we?(O(Ee==null?void 0:Ee.body),A(je=>{var Lt;return{...je,[(Lt=ae)==null?void 0:Lt.MATERIAL_NUM]:Ee.body}}),Pe(0)):(O(je=>[...je,...Ee==null?void 0:Ee.body]),A(je=>{var Lt,zo;return{...je,[(Lt=ae)==null?void 0:Lt.MATERIAL_NUM]:[...je[(zo=ae)==null?void 0:zo.MATERIAL_NUM]||[],...Ee.body]}}))):(Ee==null?void 0:Ee.statusCode)===zt.STATUS_414&&(hi(Ee==null?void 0:Ee.message,"error"),O([]),A(je=>{var Lt;return{...je,[(Lt=ae)==null?void 0:Lt.MATERIAL_NUM]:[]}}),ls(0)),Q(je=>({...je,[ae.MATERIAL_NUM]:!1})),ws(!1)},Nt=()=>{ws(!1),Q(Ee=>({...Ee,[ae.MATERIAL_NUM]:!1}))};ws(!0),ze(`/${Re}${qe}`,"post",he,Nt,Ze)},_e=ee=>{const{scrollTop:we,scrollHeight:M,clientHeight:Me}=ee.target;we+Me>=M-10&&!Ws&&!Ae[ae.MATERIAL_NUM]&&m.length<rs&&Pe(Xe=>Xe+xs)};E.useEffect(()=>{G>0&&Ke($e==null?void 0:$e.code,!1)},[G]),E.useEffect(()=>{n==null||n.forEach(ee=>{var we,M;ee.key===((we=ae)==null?void 0:we.SALES_ORG)?A(Me=>({...Me,[ee.key]:(B==null?void 0:B.uniqueSalesOrgList)||[]})):ee.key===((M=ae)==null?void 0:M.PLANT)&&A(Me=>({...Me,[ee.key]:(B==null?void 0:B.uniquePlantList)||[]}))})},[n]),E.useEffect(()=>{var ee,we;if(((ee=B==null?void 0:B.salesOrgData)==null?void 0:ee.length)>0&&!C[(we=ae)==null?void 0:we.SALES_ORG]){A(Me=>{var Xe;return{...Me,[(Xe=ae)==null?void 0:Xe.SALES_ORG]:(B==null?void 0:B.uniqueSalesOrgList)||[]}});const M=vc(B==null?void 0:B.uniqueSalesOrgList,B);A(Me=>{var Xe;return{...Me,[(Xe=ae)==null?void 0:Xe.PLANT]:M}})}},[B]),E.useEffect(()=>{var ee,we,M,Me,Xe,oe,et,Oe,qe,Ze;if(C[(ee=ae)==null?void 0:ee.SALES_ORG]&&C[(we=ae)==null?void 0:we.SALES_ORG].length===0&&(C[(M=ae)==null?void 0:M.DIST_CHNL]=[],C[(Me=ae)==null?void 0:Me.PLANT]=[]),o===((Xe=ve)==null?void 0:Xe.SET_DNU)&&(A(he=>{var Nt;return{...he,[(Nt=ae)==null?void 0:Nt.PLANT]:[]}}),A(he=>{var Nt;return{...he,[(Nt=ae)==null?void 0:Nt.DIST_CHNL]:[]}})),(o===((oe=ve)==null?void 0:oe.ITEM_CAT)||o===((et=ve)==null?void 0:et.CHG_STAT))&&A(he=>{var Nt;return{...he,[(Nt=ae)==null?void 0:Nt.DIST_CHNL]:[]}}),C[(Oe=ae)==null?void 0:Oe.SALES_ORG]&&C[(qe=ae)==null?void 0:qe.SALES_ORG].length>0){It();const he=vc(C[(Ze=ae)==null?void 0:Ze.SALES_ORG],B);A(Nt=>{var Us;return{...Nt,[(Us=ae)==null?void 0:Us.PLANT]:he}})}},[C[(Kt=ae)==null?void 0:Kt.SALES_ORG]]),E.useEffect(()=>{var ee,we,M,Me,Xe,oe,et;if(C[(ee=ae)==null?void 0:ee.PLANT]&&C[(we=ae)==null?void 0:we.PLANT].length===0&&(C[(M=ae)==null?void 0:M.MRP_CTRLER]=[],C[(Me=ae)==null?void 0:Me.WAREHOUSE]=[],A(Oe=>{var qe;return{...Oe,[(qe=ae)==null?void 0:qe.MRP_CTRLER]:[]}}),A(Oe=>{var qe;return{...Oe,[(qe=ae)==null?void 0:qe.WAREHOUSE]:[]}})),C[(Xe=ae)==null?void 0:Xe.PLANT]&&C[(oe=ae)==null?void 0:oe.PLANT].length>0){Gt();const Oe=dh(C[(et=ae)==null?void 0:et.PLANT],B);A(qe=>{var Ze;return{...qe,[(Ze=ae)==null?void 0:Ze.WAREHOUSE]:Oe}})}},[C[(Ps=ae)==null?void 0:Ps.PLANT]]);const It=()=>{var Me,Xe,oe;Q(et=>({...et,[ae.DIST_CHNL]:!0}));let ee={salesOrg:C[(Me=ae)==null?void 0:Me.SALES_ORG]?(oe=C[(Xe=ae)==null?void 0:Xe.SALES_ORG])==null?void 0:oe.map(et=>et==null?void 0:et.code).join("$^$"):""};const we=et=>{A(Oe=>{var qe;return{...Oe,[(qe=ae)==null?void 0:qe.DIST_CHNL]:et.body}}),Q(Oe=>({...Oe,[ae.DIST_CHNL]:!1}))},M=et=>{Q(Oe=>({...Oe,[ae.DIST_CHNL]:!1}))};ze(`/${Re}${ye.DATA.GET_DISTRCHNL}`,"post",we,M,ee)},Gt=()=>{var Me,Xe,oe;Q(et=>({...et,[ae.MRP_CTRLER]:!0}));let ee={plant:C[(Me=ae)==null?void 0:Me.PLANT]?(oe=C[(Xe=ae)==null?void 0:Xe.PLANT])==null?void 0:oe.map(et=>et==null?void 0:et.code).join("$^$"):""};const we=et=>{A(Oe=>{var qe;return{...Oe,[(qe=ae)==null?void 0:qe.MRP_CTRLER]:et.body}}),Q(Oe=>({...Oe,[ae.MRP_CTRLER]:!1}))},M=et=>{Q(Oe=>({...Oe,[ae.MRP_CTRLER]:!1}))};ze(`/${Re}${ye.DATA.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",we,M,ee)},ut=ee=>{var M,Me,Xe,oe,et,Oe,qe,Ze,he,Nt,Us,es,Hs,Os;const we=fs=>fs.code&&fs.desc?`${fs.code} - ${fs.desc}`:fs.code||"";if(ee.key===((M=ae)==null?void 0:M.MATERIAL_NUM))return a(tg,{param:ee,mandatory:(Xe=(Me=fi)==null?void 0:Me[o])==null?void 0:Xe.includes(ee==null?void 0:ee.key),dropDownData:Ne,allDropDownData:s,selectedValues:C,inputState:$e,handleSelectAll:Jt,handleSelectionChange:Ss,handleMatInputChange:pe,handleScroll:_e,dropdownRef:Fs,errors:Ie,formatOptionLabel:we,handlePopoverOpen:Le,handlePopoverClose:mt,handleMouseEnterPopover:hs,handleMouseLeavePopover:Ds,isPopoverVisible:os,popoverId:Ns,popoverAnchorEl:Qs,popoverRef:Ft,popoverContent:js,isMaterialNum:!0,isLoading:Ae[ae.MATERIAL_NUM],singleSelect:(o===((oe=ve)==null?void 0:oe.LOGISTIC)||(v==null?void 0:v.TemplateName)===((et=ve)==null?void 0:et.LOGISTIC))&&(v==null?void 0:v.RequestType)===((Oe=I)==null?void 0:Oe.CHANGE),hasMoreItems:m.length<rs,totalCount:rs,loadedCount:m.length});if(ee.key===((qe=ae)==null?void 0:qe.PLANT)||ee.key===((Ze=ae)==null?void 0:Ze.SALES_ORG)||ee.key===((he=ae)==null?void 0:he.MRP_CTRLER)||ee.key===((Nt=ae)==null?void 0:Nt.DIVISION)||ee.key===((Us=ae)==null?void 0:Us.WAREHOUSE)||ee.key===((es=ae)==null?void 0:es.DIST_CHNL))return a(Gf,{param:ee,mandatory:(Os=(Hs=fi)==null?void 0:Hs[o])==null?void 0:Os.includes(ee==null?void 0:ee.key),dropDownData:Ne,allDropDownData:s,selectedValues:C,handleSelectAll:Jt,handleSelectionChange:Ss,errors:Ie,formatOptionLabel:we,handlePopoverOpen:Le,handlePopoverClose:mt,handleMouseEnterPopover:hs,handleMouseLeavePopover:Ds,isPopoverVisible:os,popoverId:Ns,popoverAnchorEl:Qs,popoverRef:Ft,popoverContent:js,isMaterialNum:!1,isLoading:Ae[ee.key],isSelectAll:!0})},pt=async ee=>{const we=ee.target.files[0];if(!we)return;const Me=(await uh(we)).map((Xe,oe)=>{const et={id:oe+1};return as.forEach((Oe,qe)=>{et[Oe.field]=Xe[Oe.field]||""}),et});kt(Me),ee.target.value=null};return te($s,{children:[te(on,{open:e,TransitionComponent:sg,keepMounted:!0,onClose:()=>{},maxWidth:yt===1?"md":"sm",fullWidth:!0,children:[te(Tt,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[a($f,{color:"primary",sx:{marginRight:"0.5rem"}}),te(wt,{variant:"h6",component:"div",color:"primary",children:[o," Search Filter(s)"]})]}),te(Tt,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[te(fd,{value:yt,onChange:uo,sx:{borderBottom:1,borderColor:"divider"},children:[a(Fl,{label:"Search Filter"}),a(Fl,{label:"Copy Material"})]}),yt===1&&te(Tt,{sx:{display:"flex",gap:1,marginLeft:"auto",pr:2},children:[a(Xt,{title:"Export Table",children:a(ds,{onClick:no.convertJsonToExcel,children:a(yc,{iconName:"Download"})})}),a(Xt,{title:"Upload Excel",disabled:!$t,children:a(ds,{onClick:Ct,children:a(yc,{iconName:"Upload"})})}),a("input",{type:"file",accept:".xlsx, .xls",ref:ot,style:{display:"none"},onChange:pt})]})]}),te(Uo,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[yt===0&&a($s,{children:n==null?void 0:n.map(ee=>a(Tt,{sx:{marginBottom:"1rem"},children:ut(ee)},ee.key))}),yt===1&&a(Tt,{children:a(Ni,{style:{height:400,width:"100%"},rows:be,columns:as})}),U&&te(wt,{variant:"h6",color:(eo=(qs=De)==null?void 0:qs.error)==null?void 0:eo.dark,children:["* ",P]}),a(Yn,{blurLoading:ue})]}),te(Ho,{sx:{padding:"0.5rem 1.5rem",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[a(wt,{variant:"caption",sx:{color:"text.secondary",fontWeight:"bold"},children:"Note: Please choose other Mandatory fields before selecting Material Number"}),te(Tt,{sx:{display:"flex",gap:1},children:[a(St,{onClick:()=>{var ee,we;if((v==null?void 0:v.RequestType)===((ee=I)==null?void 0:ee.CHANGE)){Pt((we=so)==null?void 0:we.REQUEST_BENCH),t();return}t()},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),(v==null?void 0:v.RequestType)!==((lt=I)==null?void 0:lt.CHANGE_WITH_UPLOAD)&&a(St,{onClick:Qt,variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"}),(v==null?void 0:v.RequestType)===((qt=I)==null?void 0:qt.CHANGE_WITH_UPLOAD)&&a(St,{onClick:()=>{nt(o)&&Se()},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"Download"})]})]})]}),a(Ld,{onDownloadTypeChange:xe,open:S,downloadType:N,handleDownloadTypeChange:ge,onClose:ne}),a(Yn,{blurLoading:f,loaderMessage:p}),V&&a(Vn,{openSnackBar:$,alertMsg:k,alertType:ce,handleSnackBarClose:z})]})},ng=(e,t,n,o,c,s,C,i,h,le)=>{const Ie=po();return{handleObjectChangeFieldRows:(f,X,V,H,ce)=>{var F;const J=e[f].map(p=>(p==null?void 0:p.id)===X?{...p,[V]:H}:p);Ie($n({...e,[f]:J}));const $=(F=t==null?void 0:t[n==null?void 0:n.page])==null?void 0:F[f].map(p=>(p==null?void 0:p.id)===X?{...p,[V]:H}:p);Ie(Gn({...t,[n==null?void 0:n.page]:{...t==null?void 0:t[n==null?void 0:n.page],[f]:$}}));const u=Wl(o,f),k=Wl(Td,u);J==null||J.forEach(p=>{if((p==null?void 0:p.id)===X){const T={ObjectNo:`${p==null?void 0:p.Material}${(k==null?void 0:k.length)>0?`$$${p[k[0]]}`:""}${(k==null?void 0:k.length)>1?`$$${p[k[1]]}`:""}`,ChangedBy:c.emailId,ChangedOn:hh,FieldName:ce??V,PreviousValue:h(p,V,f,k)??"-",SAPValue:h(p,V,f,k)??"-",CurrentValue:H??"",tableName:f};i(T);const S={RequestId:s||(le==null?void 0:le.slice(3)),changeLogId:(p==null?void 0:p.ChangeLogId)??null},g=[...C,T];Object.entries(o).forEach(([m,O])=>{const P=g.filter(q=>q.tableName===m);P.length>0&&(S[O]||(S[O]=[]),P.forEach(q=>{const{tableName:U,...R}=q;S[O].push(R)}))});const N=Object.values(o);let L={RequestId:S.RequestId,changeLogId:S.changeLogId};N.forEach(m=>{const O=pd(S,m),{RequestId:P,changeLogId:q,...U}=O;Object.entries(U).forEach(([R,v])=>{L[R]||(L[R]={}),L[R]={...L[R],...v}})}),Ie(Ed(L))}})}}},xd=()=>{const e=po(),t=rn(),{fetchDisplayDataRows:n}=kf(),{fetchDisplayDataRequestor:o}=yd(),{createFCRows:c}=Od(),s=re(F=>{var p;return(p=F.userManagement)==null?void 0:p.taskData}),C=re(F=>F.paginationData),i=re(F=>F.payload.fcRows),h=re(F=>F.payload.changeFieldRowsDisplay),le=re(F=>F.request.materialRows),Ie=re(F=>F.payload.payloadData),K=re(F=>F.payload.requestorPayload),f=new URLSearchParams(t.search),X=f.get("RequestId"),V=f.get("RequestType"),H=f.get("reqBench"),ce=t.state,{customError:J}=Qo(),$=async(F,p)=>{var T,S;if(F===((T=Lc)==null?void 0:T.DISPLAY)){if(h[C==null?void 0:C.page]){(C==null?void 0:C.totalElements)>((C==null?void 0:C.page)+1)*(C==null?void 0:C.size)?e(pn(((C==null?void 0:C.page)+1)*(C==null?void 0:C.size))):e(pn(C==null?void 0:C.totalElements));return}k()}else if(F===((S=Lc)==null?void 0:S.REQUESTOR)){if(h[C==null?void 0:C.page]){(C==null?void 0:C.totalElements)>((C==null?void 0:C.page)+1)*(C==null?void 0:C.size)?e(pn(((C==null?void 0:C.page)+1)*(C==null?void 0:C.size))):e(pn(C==null?void 0:C.totalElements));return}await o(Ie==null?void 0:Ie.TemplateName,K)}},u=async()=>{C!=null&&C.existingCreatePages.includes(C==null?void 0:C.page)||k()},k=()=>{var L,m,O,P;e(Kr(!0));let F={};const p=X,T=oi(ni.CURRENT_TASK,!0,{}),S=V||(s==null?void 0:s.ATTRIBUTE_2)||(T==null?void 0:T.ATTRIBUTE_2);H?F={massCreationId:ce!=null&&ce.isBifurcated?"":S===I.CREATE||S===I.CREATE_WITH_UPLOAD?p.slice(3):"",massChildCreationId:ce!=null&&ce.isBifurcated&&(S===I.CREATE||S===I.CREATE_WITH_UPLOAD)?p.slice(3):"",massChangeId:ce!=null&&ce.isBifurcated?"":S===I.CHANGE||S===I.CHANGE_WITH_UPLOAD?p.slice(3):"",massExtendId:ce!=null&&ce.isBifurcated?"":S===I.EXTEND||S===I.EXTEND_WITH_UPLOAD?p.slice(3):"",massSchedulingId:ce!=null&&ce.isBifurcated?"":S===I.FINANCE_COSTING?p.slice(3):"",screenName:S===I.FINANCE_COSTING?"":S,dtName:S===I.FINANCE_COSTING?"":(L=Si)==null?void 0:L.MDG_MAT_MATERIAL_FIELD_CONFIG,version:S===I.FINANCE_COSTING?"":"v2",page:C==null?void 0:C.page,size:S===I.FINANCE_COSTING?100:S===I.CHANGE||S===I.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:(s==null?void 0:s.ATTRIBUTE_5)||"",Region:"",massChildSchedulingId:ce!=null&&ce.isBifurcated&&S===I.FINANCE_COSTING?p.slice(3):"",massChildExtendId:ce!=null&&ce.isBifurcated&&(S===I.EXTEND||S===I.EXTEND_WITH_UPLOAD)?p.slice(3):"",massChildChangeId:ce!=null&&ce.isBifurcated&&(S===I.CHANGE||S===I.CHANGE_WITH_UPLOAD)?p.slice(3):""}:F={massCreationId:"",massChangeId:"",massSchedulingId:S===I.FINANCE_COSTING?p.slice(3):"",massExtendId:"",screenName:S===I.FINANCE_COSTING?"":S,dtName:S===I.FINANCE_COSTING?"":(m=Si)==null?void 0:m.MDG_MAT_MATERIAL_FIELD_CONFIG,version:S===I.FINANCE_COSTING?"":"v2",page:C==null?void 0:C.page,size:S===I.FINANCE_COSTING?100:S===I.CHANGE||S===I.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:(s==null?void 0:s.ATTRIBUTE_5)||"",Region:"",massChildCreationId:S===I.CREATE||S===I.CREATE_WITH_UPLOAD?p.slice(3):"",massChildSchedulingId:"",massChildExtendId:S===I.EXTEND||S===I.EXTEND_WITH_UPLOAD?p.slice(3):"",massChildChangeId:S===I.CHANGE||S===I.CHANGE_WITH_UPLOAD?p.slice(3):""};const g=async q=>{var ue,B,Ne,A,Ae,Q,G,Pe;e(Kr(!1));const U=q.body;if(e(dd(q==null?void 0:q.totalElements)),(q==null?void 0:q.totalPages)===1||(q==null?void 0:q.currentPage)+1===(q==null?void 0:q.totalPages)?(e(pn(q==null?void 0:q.totalElements)),e(kl(!0))):e(pn(((q==null?void 0:q.currentPage)+1)*(q==null?void 0:q.pageSize))),(s==null?void 0:s.ATTRIBUTE_2)===((ue=I)==null?void 0:ue.CHANGE)||(s==null?void 0:s.ATTRIBUTE_2)===((B=I)==null?void 0:B.CHANGE_WITH_UPLOAD)||V===((Ne=I)==null?void 0:Ne.CHANGE_WITH_UPLOAD)||V===((A=I)==null?void 0:A.CHANGE)){e(Ul({keyName:"requestHeaderData",data:(Ae=U[0])==null?void 0:Ae.Torequestheaderdata})),n(U);return}if((s==null?void 0:s.ATTRIBUTE_2)===((Q=I)==null?void 0:Q.FINANCE_COSTING)||V===((G=I)==null?void 0:G.FINANCE_COSTING)){const $e=await c(U);e(fh([...i,...$e])),e(xc(C==null?void 0:C.page));return}const R=gh(U,le);e(Th({data:R==null?void 0:R.payload}));const v=Object.keys(R==null?void 0:R.payload).filter($e=>!isNaN(Number($e))),b={};v.forEach($e=>{b[$e]=R==null?void 0:R.payload[$e]}),e(ph((Pe=Object.values(b))==null?void 0:Pe.map($e=>$e.headerData))),e(xc(C==null?void 0:C.page))},N=q=>{J(q)};ze(`/${Re}/${(P=(O=ye)==null?void 0:O.CHG_DISPLAY_REQUESTOR)==null?void 0:P.DISPLAY_DTO}`,"post",g,N,F)};return{getNextDisplayDataForChange:$,getNextDisplayDataForCreate:u}};var Xi={},rg=bn;Object.defineProperty(Xi,"__esModule",{value:!0});var Pi=Xi.default=void 0,lg=rg(An()),ig=wn;Pi=Xi.default=(0,lg.default)((0,ig.jsx)("path",{d:"M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12s4.48 10 10 10 10-4.48 10-10M4 12c0-4.42 3.58-8 8-8s8 3.58 8 8-3.58 8-8 8-8-3.58-8-8m12 0-4 4-1.41-1.41L12.17 13H8v-2h4.17l-1.59-1.59L12 8z"}),"ArrowCircleRightOutlined");const ag=({params:e,field:t,isFieldError:n,isFieldDisable:o,isNewRow:c,keyName:s,handleChangeValue:C,handleRemoveError:i,charCount:h,setCharCount:le,isFocused:Ie,setIsFocused:K})=>{var J;const[f,X]=E.useState(e.row[t.jsonName]||""),V=e.row.id,H=h[s]===(t==null?void 0:t.maxLength),ce=$=>{const u=$.target.value;X(u),C(e.row,V,t.jsonName,(u==null?void 0:u.toUpperCase())||"",t.viewName,t.fieldName,s),le(k=>({...k,[s]:u.length}))};return a(Xt,{title:(J=e.row[t.jsonName])==null?void 0:J.toUpperCase(),arrow:!0,placement:"top",children:a(En,{fullWidth:!0,placeholder:`ENTER ${t.fieldName.toUpperCase()}`,variant:"outlined",size:"small",value:f,disabled:o||!c&&t.visibility===sn.DISPLAY,inputProps:{maxLength:t.maxLength,style:{textTransform:"uppercase"}},InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:n?De.error.dark:void 0},"&.Mui-disabled":{"& input":{WebkitTextFillColor:De.text.primary,color:De.text.primary}}}},onFocus:$=>{$.stopPropagation(),K({...Ie,[s]:!0}),le(u=>({...u,[s]:$.target.value.length})),n&&i(V,t.fieldName)},onKeyDown:$=>$.key===" "&&$.stopPropagation(),onClick:$=>$.stopPropagation(),onChange:ce,onBlur:()=>K({...Ie,[s]:!1}),helperText:Ie[s]&&(H?"Max Length Reached":`${h[s]||0}/${t.maxLength}`),FormHelperTextProps:{sx:{color:H?De.error.dark:De.primary.darkPlus,position:"absolute",bottom:"-20px"}},sx:{"& .MuiInputBase-root":{height:"34px"},"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:H?De.error.dark:""}}}})})},cg=e=>{var rs,ls,$t,is,ot,Ct,as,xs,Wt,uo,no,Le,mt,hs,Ds,zs,Ns,Ss,Jt,Ms,Ys,nt,Qt,ke,dt,Zt;const{customError:t}=Qo(),{getNextDisplayDataForChange:n}=xd(),o=re(z=>z.tabsData.changeFieldsDT),c=re(z=>z.payload.payloadData),s=o==null?void 0:o["Config Data"],C=re(z=>z.payload.tablesList),i=re(z=>z.payload.changeFieldRows),h=re(z=>z.payload.changeFieldRowsDisplay),le=re(z=>z.payload.changeLogData),Ie=re(z=>z.payload.matNoList),K=re(z=>z.payload.newRowIds),f=re(z=>z.AllDropDown.dropDown||{}),X=re(z=>z.payload.dataLoading),V=re(z=>z.payload.errorData),H=re(z=>z.payload.selectedRows),ce=re(z=>{var Se;return(Se=z.request.requestHeader)==null?void 0:Se.requestId}),J=re(z=>z.userManagement.userData),$=re(z=>z.userManagement.taskData),u=re(z=>z.paginationData),k=re(z=>z.payload.templateArray),F=re(z=>z.payload.requestorPayload),[p,T]=E.useState([]),[S,g]=E.useState({}),[N,L]=E.useState({}),[m,O]=E.useState(""),[P,q]=E.useState("success"),[U,R]=E.useState(!1),[v,b]=E.useState(""),ue=re(z=>z.tabsData.dataLoading),[B,Ne]=E.useState({data:{},isVisible:!1}),A=po(),Ae=rn(),Q=new URLSearchParams(Ae.search),G=Q.get("reqBench"),Pe=Q.get("RequestId"),{t:$e}=Qr();let Be=Ae.state;E.useEffect(()=>{i&&(p==null?void 0:p.length)===0&&T(JSON.parse(JSON.stringify(i)))},[i]);const[bs,us]=E.useState(0),[Fs,Ws]=E.useState(10),ws=(z,Se)=>{us(isNaN(Se)?0:Se)},Qs=z=>{const Se=z.target.value;Ws(Se),us(0)},Is=()=>{R(!0)},js=()=>{R(!1)},Zs=()=>{const z=(i==null?void 0:i.length)>0?Object.keys(i[0]):[],Se=z==null?void 0:z.reduce((xe,pe)=>(xe[pe]=pe==="id"?Ls():pe==="slNo"?1:"",xe),{}),ne=[Se,...i].map((xe,pe)=>({...xe,slNo:pe+1})),ge=[Se,...h[u==null?void 0:u.page]||[]].map((xe,pe)=>({...xe,slNo:pe+1}));A(Pc([Se==null?void 0:Se.id,...K])),A($n(ne)),A(Gn({...h,[u==null?void 0:u.page]:ge})),A(_i([Se==null?void 0:Se.id,...H])),A(Yr(!0)),e==null||e.setCompleted([!0,!1])},os=Wl(Eh,c==null?void 0:c.TemplateName),Ht=Wl(Td,os),Ft=(Ht==null?void 0:Ht.length)>1,Pt=(z,Se)=>{const ne=p==null?void 0:p.find(ge=>{const xe=ge.Material===(z==null?void 0:z.Material)&&(ge==null?void 0:ge[Ht[0]])===(z==null?void 0:z[Ht[0]]);return Ft?xe&&(ge==null?void 0:ge[Ht[1]])===(z==null?void 0:z[Ht[1]]):xe});if(ne)return ne[Se]},ns=(z,Se,ne,ge)=>{var pe;const xe=(pe=p==null?void 0:p[ne])==null?void 0:pe.find(Ke=>{let _e=Ke.Material===(z==null?void 0:z.Material);return(ge==null?void 0:ge.length)>0&&(_e=_e&&(Ke==null?void 0:Ke[ge[0]])===(z==null?void 0:z[ge[0]]),(ge==null?void 0:ge.length)>1&&(_e=_e&&(Ke==null?void 0:Ke[ge[1]])===(z==null?void 0:z[ge[1]]))),_e});return xe?xe[Se]:"-"},Ye=z=>{A(Ih(z))},{handleObjectChangeFieldRows:yt}=ng(i,h,u,os,J,ce,k,Ye,ns,e==null?void 0:e.RequestId),Et=(z,Se,ne,ge,xe,pe)=>{var Ke,_e,It,Gt;if(Array.isArray(i)){if(ne==="AltUnit"||ne==="Langu"){const lt=Sh(z,h==null?void 0:h[u==null?void 0:u.page],Ie,ge,c==null?void 0:c.TemplateName);if(lt==="matError"){q("error"),b($e((Ke=tn)==null?void 0:Ke.MATL_ERROR_MSG)),Is();return}else if(lt==="altUnitError"){q("error"),b($e((_e=tn)==null?void 0:_e.ALTUNIT_ERROR_MSG)),Is();return}else if(lt==="languError"){q("error"),b($e((It=tn)==null?void 0:It.LANG_ERROR_MSG)),Is();return}}const ut=i==null?void 0:i.map(lt=>{var qt,ee;return(lt==null?void 0:lt.id)===Se?{...lt,[ne]:ge,...ne==="Material"?{...(c==null?void 0:c.TemplateName)===((qt=ve)==null?void 0:qt.UPD_DESC)?{Langu:""}:{},...(c==null?void 0:c.TemplateName)===((ee=ve)==null?void 0:ee.LOGISTIC)?{AltUnit:""}:{}}:{}}:lt});A($n(ut));const pt=(Gt=h==null?void 0:h[u==null?void 0:u.page])==null?void 0:Gt.map(lt=>{var qt,ee;return(lt==null?void 0:lt.id)===Se?{...lt,[ne]:ge,...ne==="Material"?{...(c==null?void 0:c.TemplateName)===((qt=ve)==null?void 0:qt.UPD_DESC)?{Langu:""}:{},...(c==null?void 0:c.TemplateName)===((ee=ve)==null?void 0:ee.LOGISTIC)?{AltUnit:""}:{}}:{}}:lt});A(Gn({...h,[u==null?void 0:u.page]:pt}));const Ut=Mh(),Kt=lt=>lt!=null&&lt.toString().startsWith("/Date(")&&(lt!=null&&lt.toString().endsWith(")/"))?vh(lt):lt;let Ps={ObjectNo:`${z==null?void 0:z.Material}$$${z==null?void 0:z[Ht[0]]}${Ft?`$$${z==null?void 0:z[Ht[1]]}`:""}`,ChangedBy:J.emailId,ChangedOn:Ut.sapFormat,FieldName:pe??ne,PreviousValue:Pt(z,ne)??"-",SAPValue:Pt(z,ne)??"-",CurrentValue:Kt(ge)??""};Ye(Ps);let qs={RequestId:ce||(e==null?void 0:e.RequestId).slice(3),changeLogId:(z==null?void 0:z.ChangeLogId)??null,[os]:[...k,Ps]};const eo=pd(qs,os);A(Ed(eo))}else typeof i=="object"&&i[xe]&&yt(xe,Se,ne,ge,pe)},_s=(z,Se)=>{const ne={};Object.keys(V).forEach(ge=>{const xe=V[ge];if(xe.id===z){const pe=xe.missingFields.filter(Ke=>Ke!==Se);pe.length>0&&(ne[ge]={...xe,missingFields:pe})}else ne[ge]={...xe}}),A(Oi(ne))},Vt=()=>{var ne,ge,xe,pe;const z=B==null?void 0:B.data,Se=(ne=z==null?void 0:z.row)==null?void 0:ne.id;if(Array.isArray(i)){const _e=i.filter(pt=>(pt==null?void 0:pt.id)!==Se).map((pt,Ut)=>({...pt,slNo:Ut+1}));A($n(_e));const It={...h,[u==null?void 0:u.page]:(xe=(ge=h[u==null?void 0:u.page])==null?void 0:ge.filter(pt=>(pt==null?void 0:pt.id)!==Se))==null?void 0:xe.map((pt,Ut)=>({...pt,slNo:Ut+1}))};A(Gn(It));const Gt=K==null?void 0:K.filter(pt=>pt!==Se);A(Pc(Gt));const ut=i.find(pt=>pt.id===Se);if(ut){const pt=`${ut.Material}$$${ut[Ht[0]]}${Ft?`$$${ut[Ht[1]]}`:""}`,Ut=JSON.parse(JSON.stringify(le));if((pe=Ut[ut.Material])!=null&&pe[os]){const Kt=Ut[ut.Material][os].filter(Ps=>Ps.ObjectNo!==pt&&Ps.ObjectNo!==`${ut.Material}$$`);Kt.length===0?(delete Ut[ut.Material][os],Object.keys(Ut[ut.Material]).length===0&&(delete Ut[ut.Material],delete Ut[""])):Ut[ut.Material][os]=Kt}A(Oh(Ut))}}Ne({...B,isVisible:!1})},be=(z,Se)=>{var pe,Ke,_e,It,Gt,ut,pt,Ut,Kt,Ps,qs,eo,lt,qt,ee,we;const ne=[{headerName:"Sl. No.",field:"slNo",align:"center",flex:(c==null?void 0:c.TemplateName)===((pe=ve)==null?void 0:pe.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Ke=ve)==null?void 0:Ke.MRP)&&z==="Plant Data"?void 0:.1,width:(c==null?void 0:c.TemplateName)===((_e=ve)==null?void 0:_e.LOGISTIC)||(c==null?void 0:c.TemplateName)===((It=ve)==null?void 0:It.MRP)&&z==="Plant Data"?1:void 0},...Se.map(M=>{var Me,Xe,oe,et,Oe,qe,Ze;return{headerName:te("span",{children:[M.fieldName,M.visibility===((Me=sn)==null?void 0:Me.MANDATORY)&&a("span",{style:{color:(oe=(Xe=De)==null?void 0:Xe.error)==null?void 0:oe.dark,marginLeft:4},children:"*"})]}),field:M.jsonName,flex:(c==null?void 0:c.TemplateName)===((et=ve)==null?void 0:et.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Oe=ve)==null?void 0:Oe.MRP)&&(M==null?void 0:M.viewName)==="Plant Data"?void 0:1,width:(c==null?void 0:c.TemplateName)===((qe=ve)==null?void 0:qe.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Ze=ve)==null?void 0:Ze.MRP)&&(M==null?void 0:M.viewName)==="Plant Data"?200:void 0,renderCell:he=>{var fs,Eo,cs,st,ts,ho,fo,Co,mo,Ks,Rt,Ao,gs,bo,wo,Io,Ro,_o,Z,Ge,Fe,Ve,fe,Qe,ht,it,_t,Bt;const Nt=(fs=Object==null?void 0:Object.values(V))==null?void 0:fs.find(We=>{var at;return(We==null?void 0:We.id)===((at=he==null?void 0:he.row)==null?void 0:at.id)}),Us=`${(Eo=he==null?void 0:he.row)==null?void 0:Eo.id}-${M==null?void 0:M.jsonName}`,es=(cs=Nt==null?void 0:Nt.missingFields)==null?void 0:cs.includes(M==null?void 0:M.fieldName),Hs=K==null?void 0:K.includes((st=he==null?void 0:he.row)==null?void 0:st.id),Os=!!(G&&!((ts=Xo)!=null&&ts.includes(Be==null?void 0:Be.reqStatus)));if(M.fieldType===gi.INPUT)return a(ag,{params:he,field:M,isFieldError:es,isFieldDisable:Os,isNewRow:Hs,keyName:Us,handleChangeValue:Et,handleRemoveError:_s,charCount:N,setCharCount:L,isFocused:S,setIsFocused:g});if(M.fieldType===gi.DROPDOWN){const We=K==null?void 0:K.includes((ho=he==null?void 0:he.row)==null?void 0:ho.id),at=(M==null?void 0:M.jsonName)!=="Unittype1"&&(M==null?void 0:M.jsonName)!=="Spproctype"&&(M==null?void 0:M.jsonName)!=="MrpCtrler"?(fo=f==null?void 0:f[M==null?void 0:M.jsonName])==null?void 0:fo.find(Ee=>{var je;return Ee.code===((je=he==null?void 0:he.row)==null?void 0:je[M==null?void 0:M.jsonName])}):(M==null?void 0:M.jsonName)==="Spproctype"||(M==null?void 0:M.jsonName)==="MrpCtrler"?(Ks=(mo=f==null?void 0:f[M==null?void 0:M.jsonName])==null?void 0:mo[(Co=he==null?void 0:he.row)==null?void 0:Co.Plant])==null?void 0:Ks.find(Ee=>{var je;return Ee.code===((je=he==null?void 0:he.row)==null?void 0:je[M==null?void 0:M.jsonName])}):(gs=(Ao=f==null?void 0:f[M==null?void 0:M.jsonName])==null?void 0:Ao[(Rt=he==null?void 0:he.row)==null?void 0:Rt.WhseNo])==null?void 0:gs.find(Ee=>{var je;return Ee.code===((je=he==null?void 0:he.row)==null?void 0:je[M==null?void 0:M.jsonName])});return a(to,{options:(M==null?void 0:M.jsonName)==="Unittype1"?(wo=f==null?void 0:f.Unittype1)==null?void 0:wo[(bo=he==null?void 0:he.row)==null?void 0:bo.WhseNo]:(M==null?void 0:M.jsonName)==="Spproctype"?(Ro=f==null?void 0:f.Spproctype)==null?void 0:Ro[(Io=he==null?void 0:he.row)==null?void 0:Io.Plant]:(M==null?void 0:M.jsonName)==="MrpCtrler"?(Z=f==null?void 0:f.MrpCtrler)==null?void 0:Z[(_o=he==null?void 0:he.row)==null?void 0:_o.Plant]:f!=null&&f[M==null?void 0:M.jsonName]?f==null?void 0:f[M==null?void 0:M.jsonName]:[],value:at||((Ge=he==null?void 0:he.row)!=null&&Ge[M==null?void 0:M.jsonName]?{code:(Fe=he==null?void 0:he.row)==null?void 0:Fe[M==null?void 0:M.jsonName],desc:""}:null),onChange:Ee=>{Et(he.row,he.row.id,M==null?void 0:M.jsonName,Ee==null?void 0:Ee.code,z,M==null?void 0:M.fieldName),es&&_s(he.row.id,M==null?void 0:M.fieldName)},listWidth:150,placeholder:`Select ${M.fieldName}`,disabled:Os?!0:We?!1:(M==null?void 0:M.visibility)===((Ve=sn)==null?void 0:Ve.DISPLAY),isFieldError:es})}else if(M.fieldType===gi.DATE_FIELD){const We=K==null?void 0:K.includes((fe=he==null?void 0:he.row)==null?void 0:fe.id),at=(Qe=he==null?void 0:he.row)!=null&&Qe[M==null?void 0:M.jsonName]?(()=>{var je;const Ee=(je=he==null?void 0:he.row)==null?void 0:je[M==null?void 0:M.jsonName];if(Ee.startsWith("/Date(")&&Ee.endsWith(")/")){const Lt=parseInt(Ee.slice(6,-2));return new Date(Lt)}return typeof Ee=="string"&&Ee.match(/^\d{4}-\d{2}-\d{2}/)?new Date(Ee):Wn(Ee,["YYYY-MM-DD HH:mm:ss.S","DD MMM YYYY HH:mm:ss UTC"]).toDate()})():null;return a(Xt,{title:(ht=he==null?void 0:he.row)==null?void 0:ht[M==null?void 0:M.jsonName],arrow:!0,placement:"top",children:a(Rh,{dateAdapter:_h,children:a(Nh,{disabled:Os?!0:We?!1:(M==null?void 0:M.visibility)===((it=sn)==null?void 0:it.DISPLAY),slotProps:{textField:{size:"small",fullWidth:!0,InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:es?(Bt=(_t=De)==null?void 0:_t.error)==null?void 0:Bt.dark:void 0}}}}},value:at,onChange:Ee=>{if(Ee){const je=`/Date(${Date.parse(Ee)})/`;Et(he.row,he.row.id,M==null?void 0:M.jsonName,je,z,M==null?void 0:M.fieldName)}else Et(he.row,he.row.id,M==null?void 0:M.jsonName,null,z,M==null?void 0:M.fieldName);es&&_s(he.row.id,M==null?void 0:M.fieldName)},onError:Ee=>{Ee&&!es&&t(Vo.DATE_VALIDATION_ERROR,Ee)},maxDate:new Date(9999,11,31)})})})}else return he.value||"-"}}}),{...(((c==null?void 0:c.TemplateName)===((Gt=ve)==null?void 0:Gt.LOGISTIC)||(c==null?void 0:c.TemplateName)===((ut=ve)==null?void 0:ut.UPD_DESC))&&!Pe||((c==null?void 0:c.TemplateName)===((pt=ve)==null?void 0:pt.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Ut=ve)==null?void 0:Ut.UPD_DESC))&&Pe&&(($==null?void 0:$.taskDesc)===((Kt=Mi)==null?void 0:Kt.REQUESTOR)||(Be==null?void 0:Be.reqStatus)===((Ps=Vr)==null?void 0:Ps.DRAFT)))&&{field:"action",headerName:"Action",flex:(c==null?void 0:c.TemplateName)===((qs=ve)==null?void 0:qs.LOGISTIC)||(c==null?void 0:c.TemplateName)===((eo=ve)==null?void 0:eo.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?void 0:1,width:(c==null?void 0:c.TemplateName)===((lt=ve)==null?void 0:lt.LOGISTIC)||(c==null?void 0:c.TemplateName)===((qt=ve)==null?void 0:qt.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?200:void 0,align:"center",headerAlign:"center",renderCell:M=>{var Me;return a(io,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:a(Xt,{title:"Delete Row",children:a(ds,{disabled:!(K!=null&&K.includes((Me=M==null?void 0:M.row)==null?void 0:Me.id)),onClick:()=>{Ne({data:M,isVisible:!0})},color:"error",children:a(Xr,{})})})})}}}],ge=Array.isArray(i)?(h==null?void 0:h[u==null?void 0:u.page])||[]:((ee=h==null?void 0:h[u==null?void 0:u.page])==null?void 0:ee[z])||[],xe=Array.isArray(H)?H:H[z];return te("div",{style:{height:400,width:"100%"},children:[a(Cd,{paginationLoading:X,rows:ge,rowCount:(ge==null?void 0:ge.length)??0,columns:ne,getRowIdValue:"id",rowHeight:70,isLoading:X,tempheight:"calc(100vh - 380px)",page:bs,pageSize:Fs,selectionModel:xe,onPageChange:ws,onPageSizeChange:Qs,onCellEditCommit:Et,checkboxSelection:!(G&&!((we=Xo)!=null&&we.includes(Be==null?void 0:Be.reqStatus))),disableSelectionOnClick:!0,showCustomNavigation:!0,hideFooter:!0}),(B==null?void 0:B.isVisible)&&te(Gi,{isOpen:B==null?void 0:B.isVisible,titleIcon:a(Xr,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:$e("Delete Row")+"!",handleClose:()=>Ne({...B,isVisible:!1}),children:[a(Uo,{sx:{mt:2},children:$e(tn.DELETE_MESSAGE)}),te(Ho,{children:[a(St,{variant:"outlined",size:"small",sx:{...md},onClick:()=>Ne({...B,isVisible:!1}),children:$e(jl.CANCEL)}),a(St,{variant:"contained",size:"small",sx:{...Fi},onClick:Vt,children:$e(jl.DELETE)})]})]})]})},kt=s&&Object.keys(s);if(E.useEffect(()=>{var z,Se,ne;(u==null?void 0:u.page)+1&&((c==null?void 0:c.RequestType)===((z=I)==null?void 0:z.CHANGE)||(c==null?void 0:c.RequestType)===((Se=I)==null?void 0:Se.CHANGE_WITH_UPLOAD))&&(Pe&&(!F||((ne=Object==null?void 0:Object.keys(F))==null?void 0:ne.length)===0)?(n("display"),us(0)):(n("requestor"),us(0)),m==="prev"?A(kl(!1)):m==="next"&&(u==null?void 0:u.currentElements)>=(u==null?void 0:u.totalElements)&&A(kl(!0)))},[u==null?void 0:u.page]),(kt==null?void 0:kt.length)===1){const z=kt[0],Se=s[z];return te(io,{children:[te(io,{direction:"row",justifyContent:"space-between",mb:1.5,children:[((c==null?void 0:c.TemplateName)===((rs=ve)==null?void 0:rs.LOGISTIC)||(c==null?void 0:c.TemplateName)===((ls=ve)==null?void 0:ls.UPD_DESC))&&!Pe||((c==null?void 0:c.TemplateName)===(($t=ve)==null?void 0:$t.LOGISTIC)||(c==null?void 0:c.TemplateName)===((is=ve)==null?void 0:is.UPD_DESC))&&Pe&&(($==null?void 0:$.taskDesc)===((ot=Mi)==null?void 0:ot.REQUESTOR)||(Be==null?void 0:Be.reqStatus)===((Ct=Vr)==null?void 0:Ct.DRAFT))?a(St,{variant:"contained",color:"primary",onClick:Zs,startIcon:a(Ch,{}),sx:{borderRadius:"10px",boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.15)"},children:"Add Row"}):a(Tt,{sx:{width:0,height:0}}),te(Tt,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",padding:"5px",borderRadius:"10px",mt:-1,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[a(Xt,{title:"Previous",placement:"top",arrow:!0,children:a(ds,{disabled:(u==null?void 0:u.page)===0||!1,onClick:()=>{O("prev"),A(zr((u==null?void 0:u.page)-1))},children:a(Dc,{sx:{color:(u==null?void 0:u.page)===0?(xs=(as=De)==null?void 0:as.secondary)==null?void 0:xs.grey:(uo=(Wt=De)==null?void 0:Wt.primary)==null?void 0:uo.main,fontSize:"1.5rem",marginRight:"2px"}})})}),te("span",{style:{marginRight:"2px"},children:[a("strong",{style:{color:(Le=(no=De)==null?void 0:no.primary)==null?void 0:Le.main},children:"Materials :"})," ",te("strong",{children:[(u==null?void 0:u.page)*(u==null?void 0:u.size)+1," -"," ",u==null?void 0:u.currentElements]})," ",a("span",{children:"of"})," ",a("strong",{children:u==null?void 0:u.totalElements})]}),a(Xt,{title:"Next",placement:"top",arrow:!0,children:a(ds,{disabled:(u==null?void 0:u.currentElements)>=(u==null?void 0:u.totalElements)||!1,onClick:()=>{O("next"),A(zr((u==null?void 0:u.page)+1))},children:a(Pi,{sx:{color:(u==null?void 0:u.currentElements)>=(u==null?void 0:u.totalElements)?(hs=(mt=De)==null?void 0:mt.secondary)==null?void 0:hs.grey:(zs=(Ds=De)==null?void 0:Ds.primary)==null?void 0:zs.main,fontSize:"1.5rem"}})})})]})]}),a("div",{children:be(z,Se)}),a(Vn,{openSnackBar:U,alertMsg:v,alertType:P,handleSnackBarClose:js})]})}return te($s,{children:[a(Yn,{blurLoading:ue}),!ue&&a($s,{children:s?te("div",{children:[te(Tt,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",borderRadius:"10px",padding:"5px",width:"fit-content",marginLeft:"auto",mt:-1,mb:2,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[a(Xt,{title:"Previous",placement:"top",arrow:!0,children:a(ds,{disabled:(u==null?void 0:u.page)===0||!1,onClick:()=>{A(zr((u==null?void 0:u.page)-1))},children:a(Dc,{sx:{color:(u==null?void 0:u.page)===0?(Ss=(Ns=De)==null?void 0:Ns.secondary)==null?void 0:Ss.grey:(Ms=(Jt=De)==null?void 0:Jt.primary)==null?void 0:Ms.main,fontSize:"1.5rem",marginRight:"2px"}})})}),te("span",{style:{marginRight:"2px"},children:[a("strong",{style:{color:(nt=(Ys=De)==null?void 0:Ys.primary)==null?void 0:nt.main},children:"Materials :"})," ",te("strong",{children:[(u==null?void 0:u.page)*(u==null?void 0:u.size)+1," -"," ",u==null?void 0:u.currentElements]})," ",a("span",{children:"of"})," ",a("strong",{children:u==null?void 0:u.totalElements})]}),a(Xt,{title:"Next",placement:"top",arrow:!0,children:a(ds,{disabled:(u==null?void 0:u.currentElements)>=(u==null?void 0:u.totalElements)||!1,onClick:()=>{A(zr((u==null?void 0:u.page)+1))},children:a(Pi,{sx:{color:(u==null?void 0:u.currentElements)>=(u==null?void 0:u.totalElements)?(ke=(Qt=De)==null?void 0:Qt.secondary)==null?void 0:ke.grey:(Zt=(dt=De)==null?void 0:dt.primary)==null?void 0:Zt.main,fontSize:"1.5rem"}})})})]}),kt==null?void 0:kt.map(z=>C!=null&&C.includes(z)?te(mh,{sx:{marginBottom:"20px",boxShadow:3},children:[a(Ah,{expandIcon:a(bh,{}),"aria-controls":`${z}-content`,id:`${z}-header`,sx:{backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",padding:"8px 16px","&:hover":{backgroundImage:"linear-gradient(90deg,rgb(242, 242, 255) 0%,rgb(239, 232, 255) 100%)"}},children:a(wt,{variant:"h6",sx:{fontWeight:"bold"},children:z})}),a(wh,{sx:{height:"calc(100vh - 300px)"},children:be(z,s[z])})]},z):null)]}):a(wt,{children:"No data available"})})]})},Dd=e=>{const t=re($=>$.payload.changeFieldRows),n=re($=>$.payload.changeLogData),o=re($=>$.request),c=re($=>$.payload),s=re($=>$.payload.dynamicKeyValues),C=re($=>$.payload.selectedRows),i=re($=>$.userManagement.taskData),h=e||(s==null?void 0:s.templateName),le=($,u)=>{var k,F,p,T,S,g;return n[$]?{RequestId:((T=c==null?void 0:c.payloadData)==null?void 0:T.RequestId)||((S=c==null?void 0:c.changeLogData)==null?void 0:S.RequestId),ChildRequestId:((g=s==null?void 0:s.childRequestHeaderData)==null?void 0:g.ChildRequestId)??null,ChangeLogId:u??null,...n[$]}:{RequestId:((k=c==null?void 0:c.payloadData)==null?void 0:k.RequestId)||((F=c==null?void 0:c.changeLogData)==null?void 0:F.RequestId),ChildRequestId:((p=s==null?void 0:s.childRequestHeaderData)==null?void 0:p.ChildRequestId)??null,ChangeLogId:u??null}},Ie=$=>{var u,k,F,p,T,S,g,N,L,m,O,P,q,U;if(e===((u=ve)==null?void 0:u.LOGISTIC)||(s==null?void 0:s.templateName)===((k=ve)==null?void 0:k.LOGISTIC))return K($);if(e===((F=ve)==null?void 0:F.ITEM_CAT)||(s==null?void 0:s.templateName)===((p=ve)==null?void 0:p.ITEM_CAT))return f($);if(e===((T=ve)==null?void 0:T.MRP)||(s==null?void 0:s.templateName)===((S=ve)==null?void 0:S.MRP))return X($);if(e===((g=ve)==null?void 0:g.UPD_DESC)||(s==null?void 0:s.templateName)===((N=ve)==null?void 0:N.UPD_DESC))return ce($);if(e===((L=ve)==null?void 0:L.WARE_VIEW_2)||(s==null?void 0:s.templateName)===((m=ve)==null?void 0:m.WARE_VIEW_2))return J($);if(e===((O=ve)==null?void 0:O.CHG_STAT)||(s==null?void 0:s.templateName)===((P=ve)==null?void 0:P.CHG_STAT))return V($);if(e===((q=ve)==null?void 0:q.SET_DNU)||(s==null?void 0:s.templateName)===((U=ve)==null?void 0:U.SET_DNU))return H($)},K=$=>{const u=t.reduce((k,F)=>{if((C==null?void 0:C.length)!==0&&!(C!=null&&C.includes(F==null?void 0:F.id)))return k;const p=F==null?void 0:F.Material;return k[p]||(k[p]=[]),k[p].push(F),k},{});if($){const k=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(u).map(p=>{var L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae,Q;const T=u[p],{MaterialId:S,ClientId:g,ChangeLogId:N}=T[(T==null?void 0:T.length)-1];return{MaterialId:S,ChangeLogId:N,Material:p,MatlType:((O=(m=u[p])==null?void 0:m[((L=u[p])==null?void 0:L.length)-1])==null?void 0:O.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Toclientdata:{ClientId:g,Material:p,Function:"UPD"},Touomdata:T.map(G=>{const Pe={...G,Function:"UPD"};return k.forEach($e=>delete Pe[$e]),Pe}),Tochildrequestheaderdata:{ChildRequestId:((P=s==null?void 0:s.childRequestHeaderData)==null?void 0:P.ChildRequestId)||null,MaterialGroupType:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.MaterialGroupType)||null,TaskId:(i==null?void 0:i.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.TotalIntermediateTasks)||null,IntermediateTaskCount:((R=s==null?void 0:s.childRequestHeaderData)==null?void 0:R.IntermediateTaskCount)||null,ReqCreatedBy:((v=s==null?void 0:s.childRequestHeaderData)==null?void 0:v.ReqCreatedBy)||null,ReqCreatedOn:((b=s==null?void 0:s.childRequestHeaderData)==null?void 0:b.ReqCreatedOn)||null,ReqUpdatedOn:((ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:ue.ReqUpdatedOn)||null,RequestType:((B=s==null?void 0:s.childRequestHeaderData)==null?void 0:B.RequestType)||null,RequestPrefix:((Ne=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ne.RequestPrefix)||null,RequestDesc:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.RequestDesc)||null,RequestPriority:((Ae=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ae.RequestPriority)||null,RequestStatus:((Q=s==null?void 0:s.childRequestHeaderData)==null?void 0:Q.RequestStatus)||null,CurrentLevel:(i==null?void 0:i.ATTRIBUTE_3)||"",CurrentLevelName:(i==null?void 0:i.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(i==null?void 0:i.taskDesc)||"",ApproverGroup:(i==null?void 0:i.ATTRIBUTE_5)||""},Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[p])||{},TemplateName:h,changeLogData:le(p,N),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const k=["id","slNo","MatlType"];return Object.keys(u).map(p=>{var T,S,g,N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae;return{Touomdata:u[p].map(Q=>{const G={...Q,Function:"UPD"};return k.forEach(Pe=>delete G[Pe]),G}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedBy,ReqCreatedOn:Ot((g=o==null?void 0:o.requestHeader)==null?void 0:g.reqCreatedOn),ReqUpdatedOn:Ot((N=o==null?void 0:o.requestHeader)==null?void 0:N.reqCreatedOn),RequestType:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestType,RequestPriority:(m=o==null?void 0:o.requestHeader)==null?void 0:m.requestPriority,RequestDesc:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestDesc,RequestStatus:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestStatus,FirstProd:((q=c==null?void 0:c.payloadData)==null?void 0:q.FirstProductionDate)||null,LaunchDate:((U=c==null?void 0:c.payloadData)==null?void 0:U.LaunchDate)||null,LeadingCat:(R=o==null?void 0:o.requestHeader)==null?void 0:R.leadingCat,Division:(v=o==null?void 0:o.requestHeader)==null?void 0:v.division,Region:(b=o==null?void 0:o.requestHeader)==null?void 0:b.region,TemplateName:(ue=o==null?void 0:o.requestHeader)==null?void 0:ue.templateName,FieldName:(B=o==null?void 0:o.requestHeader)==null?void 0:B.fieldName},Tochildrequestheaderdata:{},Toclientdata:{ClientId:null,Function:"UPD"},Material:p,MatlType:((Ae=(A=u[p])==null?void 0:A[((Ne=u[p])==null?void 0:Ne.length)-1])==null?void 0:Ae.MatlType)||"",TemplateName:h,IsFirstCreate:!0,Function:"UPD",changeLogData:le(p),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},f=$=>{const u=t.reduce((k,F)=>{if((C==null?void 0:C.length)!==0&&!(C!=null&&C.includes(F==null?void 0:F.id)))return k;const p=F==null?void 0:F.Material;return k[p]||(k[p]=[]),k[p].push(F),k},{});if($){const k=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(u).map(p=>{var N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae,Q,G;const T=u[p],{MaterialId:S,ClientId:g}=T[0];return{MaterialId:S,Material:p,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,MatlType:((m=(L=u[p])==null?void 0:L[((N=u[p])==null?void 0:N.length)-1])==null?void 0:m.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Tosalesdata:T.map(Pe=>{const $e={...Pe,Function:"UPD"};return k.forEach(Be=>delete $e[Be]),$e}),Tochildrequestheaderdata:{ChildRequestId:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.ChildRequestId)||null,MaterialGroupType:((P=s==null?void 0:s.childRequestHeaderData)==null?void 0:P.MaterialGroupType)||null,TaskId:(i==null?void 0:i.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.TotalIntermediateTasks)||null,IntermediateTaskCount:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.IntermediateTaskCount)||null,ReqCreatedBy:((R=s==null?void 0:s.childRequestHeaderData)==null?void 0:R.ReqCreatedBy)||null,ReqCreatedOn:((v=s==null?void 0:s.childRequestHeaderData)==null?void 0:v.ReqCreatedOn)||null,ReqUpdatedOn:((b=s==null?void 0:s.childRequestHeaderData)==null?void 0:b.ReqUpdatedOn)||null,RequestType:((ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:ue.RequestType)||null,RequestPrefix:((B=s==null?void 0:s.childRequestHeaderData)==null?void 0:B.RequestPrefix)||null,RequestDesc:((Ne=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ne.RequestDesc)||null,RequestPriority:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.RequestPriority)||null,RequestStatus:((Ae=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ae.RequestStatus)||null,CurrentLevel:(i==null?void 0:i.ATTRIBUTE_3)||"",CurrentLevelName:(i==null?void 0:i.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(i==null?void 0:i.taskDesc)||"",ApproverGroup:(i==null?void 0:i.ATTRIBUTE_5)||""},Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[p])||{},TemplateName:h,changeLogData:le(p,(G=(Q=u[p])==null?void 0:Q[0])==null?void 0:G.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const k=["id","slNo","MatlType"];return Object.keys(u).map(p=>{var T,S,g,N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae;return{Tosalesdata:u[p].map(Q=>{const G={...Q,Function:"UPD"};return k.forEach(Pe=>delete G[Pe]),G}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedBy,ReqCreatedOn:Ot((g=o==null?void 0:o.requestHeader)==null?void 0:g.reqCreatedOn),ReqUpdatedOn:Ot((N=o==null?void 0:o.requestHeader)==null?void 0:N.reqCreatedOn),RequestType:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestType,RequestPriority:(m=o==null?void 0:o.requestHeader)==null?void 0:m.requestPriority,RequestDesc:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestDesc,RequestStatus:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestStatus,FirstProd:((q=c==null?void 0:c.payloadData)==null?void 0:q.FirstProductionDate)||null,LaunchDate:((U=c==null?void 0:c.payloadData)==null?void 0:U.LaunchDate)||null,LeadingCat:(R=o==null?void 0:o.requestHeader)==null?void 0:R.leadingCat,Division:(v=o==null?void 0:o.requestHeader)==null?void 0:v.division,Region:(b=o==null?void 0:o.requestHeader)==null?void 0:b.region,TemplateName:(ue=o==null?void 0:o.requestHeader)==null?void 0:ue.templateName,FieldName:(B=o==null?void 0:o.requestHeader)==null?void 0:B.fieldName},Tochildrequestheaderdata:{},Material:p,MatlType:((Ae=(A=u[p])==null?void 0:A[((Ne=u[p])==null?void 0:Ne.length)-1])==null?void 0:Ae.MatlType)||"",TemplateName:h,IsFirstCreate:!0,Function:"UPD",changeLogData:le(p),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},X=$=>{if($){const u={},k=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType"],F=["id","slNo","type","MaterialId","ChangeLogId"];return Object.keys(t).forEach(T=>{t[T].forEach(S=>{const{Material:g,MaterialId:N,ChangeLogId:L}=S;u[g]||(u[g]={Toclientdata:null,Toplantdata:[],MaterialId:N,ChangeLogId:L,MatlType:""});const m={...S};T==="Basic Data"&&!u[g].Toclientdata?(u[g].MatlType=(m==null?void 0:m.MatlType)||"",k.forEach(O=>delete m[O]),u[g].Toclientdata=m):T==="Plant Data"&&(F.forEach(O=>delete m[O]),u[g].Toplantdata.push(m))})}),Object.keys(u).map(T=>{var S,g,N,L,m,O,P,q,U,R,v,b,ue;return{...u[T],Material:T,Function:"UPD",MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[T])||{},TemplateName:h,Tochildrequestheaderdata:{ChildRequestId:((S=s==null?void 0:s.childRequestHeaderData)==null?void 0:S.ChildRequestId)||null,MaterialGroupType:((g=s==null?void 0:s.childRequestHeaderData)==null?void 0:g.MaterialGroupType)||null,TaskId:(i==null?void 0:i.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.TotalIntermediateTasks)||null,IntermediateTaskCount:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.IntermediateTaskCount)||null,ReqCreatedBy:((m=s==null?void 0:s.childRequestHeaderData)==null?void 0:m.ReqCreatedBy)||null,ReqCreatedOn:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.ReqCreatedOn)||null,ReqUpdatedOn:((P=s==null?void 0:s.childRequestHeaderData)==null?void 0:P.ReqUpdatedOn)||null,RequestType:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.RequestType)||null,RequestPrefix:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.RequestPrefix)||null,RequestDesc:((R=s==null?void 0:s.childRequestHeaderData)==null?void 0:R.RequestDesc)||null,RequestPriority:((v=s==null?void 0:s.childRequestHeaderData)==null?void 0:v.RequestPriority)||null,RequestStatus:((b=s==null?void 0:s.childRequestHeaderData)==null?void 0:b.RequestStatus)||null,CurrentLevel:(i==null?void 0:i.ATTRIBUTE_3)||"",CurrentLevelName:(i==null?void 0:i.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(i==null?void 0:i.taskDesc)||"",ApproverGroup:(i==null?void 0:i.ATTRIBUTE_5)||""},changeLogData:le(T,(ue=u[T])==null?void 0:ue.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const u={},k=["id","slNo","type","Plant","MatlType"],F=["id","slNo","type"];return Object.keys(t).forEach(T=>{t[T].forEach(S=>{const{Material:g}=S;u[g]||(u[g]={Toclientdata:null,Toplantdata:[],MatlType:""});const N={...S};T==="Basic Data"&&!u[g].Toclientdata?(u[g].MatlType=(N==null?void 0:N.MatlType)||"",k.forEach(L=>delete N[L]),u[g].Toclientdata={...N,Function:"UPD"}):T==="Plant Data"&&(F.forEach(L=>delete N[L]),u[g].Toplantdata.push({...N,Function:"UPD"}))})}),Object.keys(u).map(T=>{var S,g,N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae;return{...u[T],Torequestheaderdata:{RequestId:(S=o==null?void 0:o.requestHeader)==null?void 0:S.requestId,ReqCreatedBy:(g=o==null?void 0:o.requestHeader)==null?void 0:g.reqCreatedBy,ReqCreatedOn:Ot((N=o==null?void 0:o.requestHeader)==null?void 0:N.reqCreatedOn),ReqUpdatedOn:Ot((L=o==null?void 0:o.requestHeader)==null?void 0:L.reqCreatedOn),RequestType:(m=o==null?void 0:o.requestHeader)==null?void 0:m.requestType,RequestPriority:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestPriority,RequestDesc:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestDesc,RequestStatus:(q=o==null?void 0:o.requestHeader)==null?void 0:q.requestStatus,FirstProd:((U=c==null?void 0:c.payloadData)==null?void 0:U.FirstProductionDate)||null,LaunchDate:((R=c==null?void 0:c.payloadData)==null?void 0:R.LaunchDate)||null,LeadingCat:(v=o==null?void 0:o.requestHeader)==null?void 0:v.leadingCat,Division:(b=o==null?void 0:o.requestHeader)==null?void 0:b.division,Region:(ue=o==null?void 0:o.requestHeader)==null?void 0:ue.region,TemplateName:(B=o==null?void 0:o.requestHeader)==null?void 0:B.templateName,FieldName:(Ne=o==null?void 0:o.requestHeader)==null?void 0:Ne.fieldName},Tochildrequestheaderdata:{},Material:T,TemplateName:(A=o==null?void 0:o.requestHeader)==null?void 0:A.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(Ae=o==null?void 0:o.requestHeader)==null?void 0:Ae.requestId,changeLogData:le(T),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},V=$=>{if($){const u={},k=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType"],F=["id","slNo","type","MaterialId","ChangeLogId"],p=["id","slNo","type","MaterialId","ChangeLogId"];return Object.keys(t).forEach(S=>{t[S].forEach(g=>{const{Material:N,MaterialId:L,ChangeLogId:m}=g;u[N]||(u[N]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],MaterialId:L,ChangeLogId:m,MatlType:""});const O={...g};S==="Basic Data"&&!u[N].Toclientdata?(u[N].MatlType=(O==null?void 0:O.MatlType)||"",k.forEach(P=>delete O[P]),u[N].Toclientdata=O):S==="Plant Data"?(F.forEach(P=>delete O[P]),u[N].Toplantdata.push(O)):S==="Sales Data"&&(p.forEach(P=>delete O[P]),u[N].Tosalesdata.push(O))})}),Object.keys(u).map(S=>{var g,N,L,m,O,P,q,U,R,v,b,ue,B;return{...u[S],Material:S,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[S])||{},TemplateName:h,Tochildrequestheaderdata:{ChildRequestId:((g=s==null?void 0:s.childRequestHeaderData)==null?void 0:g.ChildRequestId)||null,MaterialGroupType:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.MaterialGroupType)||null,TaskId:(i==null?void 0:i.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.TotalIntermediateTasks)||null,IntermediateTaskCount:((m=s==null?void 0:s.childRequestHeaderData)==null?void 0:m.IntermediateTaskCount)||null,ReqCreatedBy:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.ReqCreatedBy)||null,ReqCreatedOn:((P=s==null?void 0:s.childRequestHeaderData)==null?void 0:P.ReqCreatedOn)||null,ReqUpdatedOn:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.ReqUpdatedOn)||null,RequestType:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.RequestType)||null,RequestPrefix:((R=s==null?void 0:s.childRequestHeaderData)==null?void 0:R.RequestPrefix)||null,RequestDesc:((v=s==null?void 0:s.childRequestHeaderData)==null?void 0:v.RequestDesc)||null,RequestPriority:((b=s==null?void 0:s.childRequestHeaderData)==null?void 0:b.RequestPriority)||null,RequestStatus:((ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:ue.RequestStatus)||null,CurrentLevel:(i==null?void 0:i.ATTRIBUTE_3)||"",CurrentLevelName:(i==null?void 0:i.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(i==null?void 0:i.taskDesc)||"",ApproverGroup:(i==null?void 0:i.ATTRIBUTE_5)||""},changeLogData:le(S,(B=u[S])==null?void 0:B.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const u={},k=["id","slNo","type","Plant","MatlType"],F=["id","slNo","type"],p=["id","slNo","type"];return Object.keys(t).forEach(S=>{t[S].forEach(g=>{const{Material:N}=g;u[N]||(u[N]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],MatlType:""});const L={...g};S==="Basic Data"&&!u[N].Toclientdata?(u[N].MatlType=(L==null?void 0:L.MatlType)||"",k.forEach(m=>delete L[m]),u[N].Toclientdata={...L,Function:"UPD"}):S==="Plant Data"?(F.forEach(m=>delete L[m]),u[N].Toplantdata.push({...L,Function:"UPD"})):S==="Sales Data"&&(p.forEach(m=>delete L[m]),u[N].Tosalesdata.push({...L,Function:"UPD"}))})}),Object.keys(u).map(S=>{var g,N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae,Q;return{...u[S],Torequestheaderdata:{RequestId:(g=o==null?void 0:o.requestHeader)==null?void 0:g.requestId,ReqCreatedBy:(N=o==null?void 0:o.requestHeader)==null?void 0:N.reqCreatedBy,ReqCreatedOn:Ot((L=o==null?void 0:o.requestHeader)==null?void 0:L.reqCreatedOn),ReqUpdatedOn:Ot((m=o==null?void 0:o.requestHeader)==null?void 0:m.reqCreatedOn),RequestType:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestType,RequestPriority:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestPriority,RequestDesc:(q=o==null?void 0:o.requestHeader)==null?void 0:q.requestDesc,RequestStatus:(U=o==null?void 0:o.requestHeader)==null?void 0:U.requestStatus,FirstProd:((R=c==null?void 0:c.payloadData)==null?void 0:R.FirstProductionDate)||null,LaunchDate:((v=c==null?void 0:c.payloadData)==null?void 0:v.LaunchDate)||null,LeadingCat:(b=o==null?void 0:o.requestHeader)==null?void 0:b.leadingCat,Division:(ue=o==null?void 0:o.requestHeader)==null?void 0:ue.division,Region:(B=o==null?void 0:o.requestHeader)==null?void 0:B.region,TemplateName:(Ne=o==null?void 0:o.requestHeader)==null?void 0:Ne.templateName,FieldName:(A=o==null?void 0:o.requestHeader)==null?void 0:A.fieldName},Tochildrequestheaderdata:{},Material:S,TemplateName:(Ae=o==null?void 0:o.requestHeader)==null?void 0:Ae.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(Q=o==null?void 0:o.requestHeader)==null?void 0:Q.requestId,changeLogData:le(S),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},H=$=>{if($){const u={},k=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType"],F=["id","slNo","type","MaterialId","ChangeLogId"],p=["id","slNo","type","MaterialId","ChangeLogId"],T=["id","slNo","type","MaterialId","ChangeLogId"];return Object.keys(t).forEach(g=>{t[g].forEach(N=>{const{Material:L,MaterialId:m,ChangeLogId:O}=N;u[L]||(u[L]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],Tomaterialdescription:[],MaterialId:m,ChangeLogId:O,MatlType:""});const P={...N};g==="Basic Data"&&!u[L].Toclientdata?(u[L].MatlType=(P==null?void 0:P.MatlType)||"",k.forEach(q=>delete P[q]),u[L].Toclientdata=P):g==="Plant Data"?(F.forEach(q=>delete P[q]),u[L].Toplantdata.push(P)):g==="Sales Data"?(p.forEach(q=>delete P[q]),u[L].Tosalesdata.push(P)):g==="Description"&&(T.forEach(q=>delete P[q]),u[L].Tomaterialdescription.push(P))})}),Object.keys(u).map(g=>{var N,L,m,O,P,q,U,R,v,b,ue,B,Ne;return{...u[g],Material:g,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[g])||{},TemplateName:h,Tochildrequestheaderdata:{ChildRequestId:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.ChildRequestId)||null,MaterialGroupType:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.MaterialGroupType)||null,TaskId:(i==null?void 0:i.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((m=s==null?void 0:s.childRequestHeaderData)==null?void 0:m.TotalIntermediateTasks)||null,IntermediateTaskCount:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.IntermediateTaskCount)||null,ReqCreatedBy:((P=s==null?void 0:s.childRequestHeaderData)==null?void 0:P.ReqCreatedBy)||null,ReqCreatedOn:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.ReqCreatedOn)||null,ReqUpdatedOn:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.ReqUpdatedOn)||null,RequestType:((R=s==null?void 0:s.childRequestHeaderData)==null?void 0:R.RequestType)||null,RequestPrefix:((v=s==null?void 0:s.childRequestHeaderData)==null?void 0:v.RequestPrefix)||null,RequestDesc:((b=s==null?void 0:s.childRequestHeaderData)==null?void 0:b.RequestDesc)||null,RequestPriority:((ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:ue.RequestPriority)||null,RequestStatus:((B=s==null?void 0:s.childRequestHeaderData)==null?void 0:B.RequestStatus)||null,CurrentLevel:(i==null?void 0:i.ATTRIBUTE_3)||"",CurrentLevelName:(i==null?void 0:i.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(i==null?void 0:i.taskDesc)||"",ApproverGroup:(i==null?void 0:i.ATTRIBUTE_5)||""},changeLogData:le(g,(Ne=u[g])==null?void 0:Ne.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const u={},k=["id","slNo","type","Plant","MatlType"],F=["id","slNo","type"],p=["id","slNo","type"],T=["id","slNo","type"];return Object.keys(t).forEach(g=>{t[g].forEach(N=>{const{Material:L}=N;u[L]||(u[L]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],Tomaterialdescription:[],MatlType:""});const m={...N};g==="Basic Data"&&!u[L].Toclientdata?(u[L].MatlType=(m==null?void 0:m.MatlType)||"",k.forEach(O=>delete m[O]),u[L].Toclientdata={...m,Function:"UPD"}):g==="Plant Data"?(F.forEach(O=>delete m[O]),u[L].Toplantdata.push({...m,Function:"UPD"})):g==="Sales Data"?(p.forEach(O=>delete m[O]),u[L].Tosalesdata.push({...m,Function:"UPD"})):g==="Description"&&(T.forEach(O=>delete m[O]),u[L].Tomaterialdescription.push({...m,Function:"UPD"}))})}),Object.keys(u).map(g=>{var N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae,Q,G;return{...u[g],Torequestheaderdata:{RequestId:(N=o==null?void 0:o.requestHeader)==null?void 0:N.requestId,ReqCreatedBy:(L=o==null?void 0:o.requestHeader)==null?void 0:L.reqCreatedBy,ReqCreatedOn:Ot((m=o==null?void 0:o.requestHeader)==null?void 0:m.reqCreatedOn),ReqUpdatedOn:Ot((O=o==null?void 0:o.requestHeader)==null?void 0:O.reqCreatedOn),RequestType:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestType,RequestPriority:(q=o==null?void 0:o.requestHeader)==null?void 0:q.requestPriority,RequestDesc:(U=o==null?void 0:o.requestHeader)==null?void 0:U.requestDesc,RequestStatus:(R=o==null?void 0:o.requestHeader)==null?void 0:R.requestStatus,FirstProd:((v=c==null?void 0:c.payloadData)==null?void 0:v.FirstProductionDate)||null,LaunchDate:((b=c==null?void 0:c.payloadData)==null?void 0:b.LaunchDate)||null,LeadingCat:(ue=o==null?void 0:o.requestHeader)==null?void 0:ue.leadingCat,Division:(B=o==null?void 0:o.requestHeader)==null?void 0:B.division,Region:(Ne=o==null?void 0:o.requestHeader)==null?void 0:Ne.region,TemplateName:(A=o==null?void 0:o.requestHeader)==null?void 0:A.templateName,FieldName:(Ae=o==null?void 0:o.requestHeader)==null?void 0:Ae.fieldName},Tochildrequestheaderdata:{},Material:g,TemplateName:(Q=o==null?void 0:o.requestHeader)==null?void 0:Q.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(G=o==null?void 0:o.requestHeader)==null?void 0:G.requestId,changeLogData:le(g),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},ce=$=>{const u=t.reduce((k,F)=>{if((C==null?void 0:C.length)!==0&&!(C!=null&&C.includes(F==null?void 0:F.id)))return k;const p=F==null?void 0:F.Material;return k[p]||(k[p]=[]),k[p].push(F),k},{});if($){const k=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(u).map(p=>{var N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae,Q,G;const T=u[p],{MaterialId:S,ClientId:g}=T[(T==null?void 0:T.length)-1];return{MaterialId:S,Material:p,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,MatlType:((m=(L=u[p])==null?void 0:L[((N=u[p])==null?void 0:N.length)-1])==null?void 0:m.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Toclientdata:{ClientId:g,Material:p,Function:"UPD"},Tochildrequestheaderdata:{ChildRequestId:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.ChildRequestId)||null,MaterialGroupType:((P=s==null?void 0:s.childRequestHeaderData)==null?void 0:P.MaterialGroupType)||null,TaskId:(i==null?void 0:i.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.TotalIntermediateTasks)||null,IntermediateTaskCount:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.IntermediateTaskCount)||null,ReqCreatedBy:((R=s==null?void 0:s.childRequestHeaderData)==null?void 0:R.ReqCreatedBy)||null,ReqCreatedOn:((v=s==null?void 0:s.childRequestHeaderData)==null?void 0:v.ReqCreatedOn)||null,ReqUpdatedOn:((b=s==null?void 0:s.childRequestHeaderData)==null?void 0:b.ReqUpdatedOn)||null,RequestType:((ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:ue.RequestType)||null,RequestPrefix:((B=s==null?void 0:s.childRequestHeaderData)==null?void 0:B.RequestPrefix)||null,RequestDesc:((Ne=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ne.RequestDesc)||null,RequestPriority:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.RequestPriority)||null,RequestStatus:((Ae=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ae.RequestStatus)||null,CurrentLevel:(i==null?void 0:i.ATTRIBUTE_3)||"",CurrentLevelName:(i==null?void 0:i.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(i==null?void 0:i.taskDesc)||"",ApproverGroup:(i==null?void 0:i.ATTRIBUTE_5)||""},Tomaterialdescription:T.map(Pe=>{const $e={...Pe,Function:"UPD"};return k.forEach(Be=>delete $e[Be]),$e}),Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[p])||{},TemplateName:h,...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""},changeLogData:le(p,(G=(Q=u[p])==null?void 0:Q[0])==null?void 0:G.ChangeLogId)}})}else{const k=["id","slNo","MatlType"];return Object.keys(u).map(p=>{var T,S,g,N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae;return{Tomaterialdescription:u[p].map(Q=>{const G={...Q,Function:"UPD"};return k.forEach(Pe=>delete G[Pe]),G}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedBy,ReqCreatedOn:Ot((g=o==null?void 0:o.requestHeader)==null?void 0:g.reqCreatedOn),ReqUpdatedOn:Ot((N=o==null?void 0:o.requestHeader)==null?void 0:N.reqCreatedOn),RequestType:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestType,RequestPriority:(m=o==null?void 0:o.requestHeader)==null?void 0:m.requestPriority,RequestDesc:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestDesc,RequestStatus:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestStatus,FirstProd:((q=c==null?void 0:c.payloadData)==null?void 0:q.FirstProductionDate)||null,LaunchDate:((U=c==null?void 0:c.payloadData)==null?void 0:U.LaunchDate)||null,LeadingCat:(R=o==null?void 0:o.requestHeader)==null?void 0:R.leadingCat,Division:(v=o==null?void 0:o.requestHeader)==null?void 0:v.division,Region:(b=o==null?void 0:o.requestHeader)==null?void 0:b.region,TemplateName:(ue=o==null?void 0:o.requestHeader)==null?void 0:ue.templateName,FieldName:(B=o==null?void 0:o.requestHeader)==null?void 0:B.fieldName},Tochildrequestheaderdata:{},Toclientdata:{ClientId:null,Function:"UPD"},Material:p,MatlType:((Ae=(A=u[p])==null?void 0:A[((Ne=u[p])==null?void 0:Ne.length)-1])==null?void 0:Ae.MatlType)||"",TemplateName:h,IsFirstCreate:!0,Function:"UPD",...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""},changeLogData:le(p)}})}},J=$=>{const u=t.reduce((k,F)=>{if((C==null?void 0:C.length)!==0&&!(C!=null&&C.includes(F==null?void 0:F.id)))return k;const p=F==null?void 0:F.Material;return k[p]||(k[p]=[]),k[p].push(F),k},{});if($){const k=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(u).map(p=>{var g,N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae,Q;const T=u[p],{MaterialId:S}=T[0];return{MaterialId:S,Material:p,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,MatlType:((L=(N=u[p])==null?void 0:N[((g=u[p])==null?void 0:g.length)-1])==null?void 0:L.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Towarehousedata:T.map(G=>{const Pe={...G,Function:"UPD"};return k.forEach($e=>delete Pe[$e]),Pe}),Tochildrequestheaderdata:{...s==null?void 0:s.childRequestHeaderData,ChildRequestId:((m=s==null?void 0:s.childRequestHeaderData)==null?void 0:m.ChildRequestId)||null,MaterialGroupType:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.MaterialGroupType)||null,TaskId:(i==null?void 0:i.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((P=s==null?void 0:s.childRequestHeaderData)==null?void 0:P.TotalIntermediateTasks)||null,IntermediateTaskCount:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.IntermediateTaskCount)||null,ReqCreatedBy:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.ReqCreatedBy)||null,ReqCreatedOn:((R=s==null?void 0:s.childRequestHeaderData)==null?void 0:R.ReqCreatedOn)||null,ReqUpdatedOn:((v=s==null?void 0:s.childRequestHeaderData)==null?void 0:v.ReqUpdatedOn)||null,RequestType:((b=s==null?void 0:s.childRequestHeaderData)==null?void 0:b.RequestType)||null,RequestPrefix:((ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:ue.RequestPrefix)||null,RequestDesc:((B=s==null?void 0:s.childRequestHeaderData)==null?void 0:B.RequestDesc)||null,RequestPriority:((Ne=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ne.RequestPriority)||null,RequestStatus:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.RequestStatus)||null,CurrentLevel:(i==null?void 0:i.ATTRIBUTE_3)||"",CurrentLevelName:(i==null?void 0:i.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(i==null?void 0:i.taskDesc)||"",ApproverGroup:(i==null?void 0:i.ATTRIBUTE_5)||""},Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[p])||{},TemplateName:h,changeLogData:le(p,(Q=(Ae=u[p])==null?void 0:Ae[0])==null?void 0:Q.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const k=["id","slNo","MatlType"];return Object.keys(u).map(p=>{var T,S,g,N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae;return{Towarehousedata:u[p].map(Q=>{const G={...Q,Function:"UPD"};return k.forEach(Pe=>delete G[Pe]),G}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedBy,ReqCreatedOn:Ot((g=o==null?void 0:o.requestHeader)==null?void 0:g.reqCreatedOn),ReqUpdatedOn:Ot((N=o==null?void 0:o.requestHeader)==null?void 0:N.reqCreatedOn),RequestType:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestType,RequestPriority:(m=o==null?void 0:o.requestHeader)==null?void 0:m.requestPriority,RequestDesc:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestDesc,RequestStatus:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestStatus,FirstProd:((q=c==null?void 0:c.payloadData)==null?void 0:q.FirstProductionDate)||null,LaunchDate:((U=c==null?void 0:c.payloadData)==null?void 0:U.LaunchDate)||null,LeadingCat:(R=o==null?void 0:o.requestHeader)==null?void 0:R.leadingCat,Division:(v=o==null?void 0:o.requestHeader)==null?void 0:v.division,Region:(b=o==null?void 0:o.requestHeader)==null?void 0:b.region,TemplateName:(ue=o==null?void 0:o.requestHeader)==null?void 0:ue.templateName,FieldName:(B=o==null?void 0:o.requestHeader)==null?void 0:B.fieldName},Tochildrequestheaderdata:{},Material:p,MatlType:((Ae=(A=u[p])==null?void 0:A[((Ne=u[p])==null?void 0:Ne.length)-1])==null?void 0:Ae.MatlType)||"",TemplateName:h,IsFirstCreate:!0,Function:"UPD",...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""},changeLogData:le(p)}})}};return{changePayloadForTemplate:Ie}};var Vi={},dg=bn;Object.defineProperty(Vi,"__esModule",{value:!0});var Pd=Vi.default=void 0,ug=dg(An()),hg=wn;Pd=Vi.default=(0,ug.default)((0,hg.jsx)("path",{d:"m16 5-1.42 1.42-1.59-1.59V16h-1.98V4.83L9.42 6.42 8 5l4-4zm4 5v11c0 1.1-.9 2-2 2H6c-1.11 0-2-.9-2-2V10c0-1.11.89-2 2-2h3v2H6v11h12V10h-3V8h3c1.1 0 2 .89 2 2"}),"IosShareOutlined");const fg=ln(Js)(({theme:e})=>({padding:e.spacing(2),border:"none",backgroundColor:"rgba(179, 236, 243, 0.5)"})),gg=ln(Tt)(({theme:e})=>{var t,n;return{backgroundColor:(n=(t=De)==null?void 0:t.primary)==null?void 0:n.whiteSmoke,padding:e.spacing(1),border:"1px solid #E0E0E0",borderRadius:e.shape.borderRadius,boxShadow:"0px 8px 15px rgba(0, 0, 0, 0.08), 0px 4px 6px rgba(115, 118, 122, 0.5)",minWidth:120,textAlign:"center",fontWeight:"bold",color:e.palette.text.primary}}),Tg=ln(Tt)(({theme:e})=>({display:"flex",justifyContent:"space-between",alignItems:"center",paddingLeft:e.spacing(2),backgroundColor:e.palette.grey[100],borderBottom:`1px solid ${e.palette.divider}`})),pg=ln(Ad)(({theme:e})=>({borderRadius:e.shape.borderRadius,boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"})),Vc=4,Eg=({open:e=!1,onClose:t=()=>{},handleOk:n=()=>{},message:o=""})=>{const c=re(Ie=>Ie.payload.matNoList||[]),s=(c==null?void 0:c.length)||0,C=E.useMemo(()=>{const Ie=[];for(let K=0;K<c.length;K+=Vc)Ie.push(c.slice(K,K+Vc));return Ie},[c]),i=()=>{const Ie=c==null?void 0:c.map((K,f)=>({id:f+1,material:K}));h.convertJsonToExcel(Ie)},h={convertJsonToExcel:Ie=>{let K=[];K.push({header:"Material",key:"material"}),gd({fileName:"Material List",columns:K,rows:Ie})}},le=()=>{t()};return te(on,{open:e,onClose:le,maxWidth:"md",PaperProps:{sx:{borderRadius:2,minWidth:"480px",maxHeight:"80vh"}},children:[te(On,{sx:{display:"flex",alignItems:"center",gap:1,py:2},children:[a(yh,{fontSize:"medium",sx:{color:"0px 4px 6px rgba(115, 118, 122, 0.5)"}}),te(wt,{variant:"h6",fontWeight:"bold",children:["Info: ",o]})]}),te(Uo,{sx:{p:0},children:[te(Tg,{children:[te(wt,{variant:"subtitle2",color:"text.secondary",sx:{marginLeft:"15px"},children:["Total Materials: ",a("strong",{children:s})]}),a(ds,{onClick:i,color:"primary",sx:{marginRight:"10px"},title:"Export Excel",children:a(Pd,{})})]}),a(Tt,{sx:{pt:0,pl:2,pr:2,pb:0},children:a(pg,{component:Wi,children:a(bd,{children:a(wd,{children:C.map((Ie,K)=>a(vi,{sx:{"&:last-child td":{borderBottom:0}},children:a(fg,{children:a(Tt,{sx:{display:"flex",flexWrap:"wrap",gap:2,justifyContent:"center"},children:Ie.map(f=>a(gg,{children:f},f))})})},K))})})})})]}),te(Ho,{sx:{p:2,borderTop:1,borderColor:"divider"},children:[a(St,{onClick:le,variant:"outlined",color:"warning",sx:{minWidth:100,textTransform:"none",fontWeight:"medium"},children:"Close"}),a(St,{onClick:n,variant:"contained",color:"primary",sx:{minWidth:100,textTransform:"none",fontWeight:"medium"},children:"Continue"})]})]})},Cg=({initialReqScreen:e,isReqBench:t,remarks:n="",userInput:o="",selectedLevel:c=""})=>{var K;const s=re(f=>f.payload),C=(K=s==null?void 0:s.payloadData)==null?void 0:K.RequestType,i=re(f=>f.userManagement.taskData),h=re(f=>f.request),le=re(f=>f.changeLog.createChangeLogData);function Ie(f){const X=[];return Object.keys(f).forEach(V=>{var H,ce,J,$,u,k,F,p,T,S,g,N,L,m,O,P,q,U,R,v,b,ue,B,Ne,A,Ae,Q,G,Pe,$e,Be,bs,us,Fs,Ws,ws,Qs,Is,js,Zs,os,Ht,Ft,Pt,ns,Ye,yt,Et,_s,Vt,be,kt,rs,ls,$t,is,ot,Ct,as,xs,Wt,uo,no,Le,mt,hs,Ds,zs,Ns,Ss,Jt,Ms,Ys,nt,Qt,ke,dt,Zt,z,Se,ne,ge,xe,pe,Ke,_e,It,Gt,ut,pt,Ut,Kt,Ps,qs,eo,lt,qt,ee,we,M,Me,Xe,oe,et,Oe,qe,Ze,he,Nt,Us,es,Hs,Os,fs,Eo,cs,st,ts,ho,fo,Co,mo,Ks,Rt,Ao,gs,bo,wo,Io,Ro,_o,Z,Ge,Fe,Ve,fe,Qe,ht,it,_t,Bt,We,at,Ee,je,Lt,zo,In,Rn,an,tr,rl,ll,il,di,al,Zo,vs,Dn,cl,Pn,qn,dl,ul,hl,Ts,fl,gl,Tl,sr,pl,El,Cl,ml,Al,bl,or,wl,Il,nr,Rl,_l,Nl,Sl,Ml,Ol,vl,rr,yl,lr,ir,Ll,xl,ar,cr,dr,ur,hr,fr,gr,Tr,pr,Er,Cr,mr,Ar,br,wr,Ir,Rr,_r,Nr,r,d,w,_,y,D,x,W,Ce,ie,de,Te,He,rt,ft,jt,ss,bt,Xs,No,So,Bo,$o,ro,Go,Fo,Wo,cn,dn,un,hn,ps,xo,lo,go,fn,gn,en,xt,ks,Vs,Ca,ma,Aa,ba,wa,Ia,Ra,_a,Na,Sa,Ma,Oa,va,ya,La,xa,Da,Pa,qa,Ua,Ha,ka,Ba,$a,Ga,Fa,Wa,ja,za,Ya,Ka,Xa,Va,Ja,Qa,Za,ec,tc,sc,oc,nc,rc,lc;if(V.includes("-")||/\d/.test(V)){const l=f[V];if((H=l==null?void 0:l.headerData)!=null&&H.included){const ui=Lh(((ce=l==null?void 0:l.headerData)==null?void 0:ce.orgData)||[]),xu=((($=(J=l==null?void 0:l.headerData)==null?void 0:J.views)==null?void 0:$.filter(me=>!Id.includes(me)))||[]).join(",").trim(),ys=(u=l==null?void 0:l.payloadData)!=null&&u.Sales?Object.values(l.payloadData.Sales):[],ic=new Set,Du=ui.filter(me=>{var ct,Mt,tt;if(!((ct=me.salesOrg)!=null&&ct.code)||!((tt=(Mt=me.dc)==null?void 0:Mt.value)!=null&&tt.code))return!1;const Ue=`${me.salesOrg.code}-${me.dc.value.code}`;return ic.has(Ue)?!1:(ic.add(Ue),!0)}).map((me,Ue)=>{var ct,Mt,tt,Es,se,Cs,Tn,At,gt,Sr,Mr,Or,vr,yr,Lr,xr,Dr,Pr,qr,Ur,Hr,kr,Br;return{SalesId:C===I.EXTEND&&e?null:((ct=ys[Ue])==null?void 0:ct.SalesId)||"",Function:"INS",Material:((Mt=l.headerData)==null?void 0:Mt.materialNumber)||"",SalesOrg:((tt=me.salesOrg)==null?void 0:tt.code)||"",DistrChan:((se=(Es=me.dc)==null?void 0:Es.value)==null?void 0:se.code)||"",DelFlag:!1,MatlStats:((Cs=ys[Ue])==null?void 0:Cs.MatlStats)||"",RebateGrp:((Tn=ys[Ue])==null?void 0:Tn.RebateGrp)||"",CashDisc:((At=ys[Ue])==null?void 0:At.CashDisc)||!0,SalStatus:((gt=ys[Ue])==null?void 0:gt.SalStatus)||"",DelyUnit:"0.000",ValidFrom:(Sr=ys[Ue])!=null&&Sr.ValidFrom?Ot((Mr=ys[Ue])==null?void 0:Mr.ValidFrom):null,DelyUom:((Or=ys[Ue])==null?void 0:Or.DelyUom)||"",DelygPlnt:((vr=ys[Ue])==null?void 0:vr.DelygPlnt)||"",MatPrGrp:((yr=ys[Ue])==null?void 0:yr.MatPrGrp)||"",AcctAssgt:((Lr=ys[Ue])==null?void 0:Lr.AcctAssgt)||"",MatlGrp4:((xr=ys[Ue])==null?void 0:xr.MatlGrp4)||"",MatlGrp2:((Dr=ys[Ue])==null?void 0:Dr.MatlGrp2)||"",MatlGrp5:((Pr=ys[Ue])==null?void 0:Pr.MatlGrp5)||"",BatchMgmt:((qr=ys[Ue])==null?void 0:qr.BatchMgmt)||"",Countryori:((Ur=ys[Ue])==null?void 0:Ur.Countryori)||"",Depcountry:((Hr=ys[Ue])==null?void 0:Hr.Depcountry)||"",SalesUnit:((kr=ys[Ue])==null?void 0:kr.SalesUnit)||"",ItemCat:((Br=ys[Ue])==null?void 0:Br.ItemCat)||""}}),Pu=(k=l==null?void 0:l.payloadData)!=null&&k.Purchasing?Object.entries(l.payloadData.Purchasing):[],qu=(F=l==null?void 0:l.payloadData)!=null&&F.MRP?Object.entries(l.payloadData.MRP):[],Uu=(p=l==null?void 0:l.payloadData)!=null&&p[Y.SALES_PLANT]?Object.entries((T=l.payloadData)==null?void 0:T[Y.SALES_PLANT]):[],Hu=(S=l==null?void 0:l.payloadData)!=null&&S.Accounting?Object.entries(l.payloadData.Accounting):[];let ac=[];if(((m=(L=(N=(g=l==null?void 0:l.payloadData)==null?void 0:g.TaxData)==null?void 0:N.TaxData)==null?void 0:L.TaxDataSet)==null?void 0:m.length)>0){const me={};(U=(q=(P=(O=l==null?void 0:l.payloadData)==null?void 0:O.TaxData)==null?void 0:P.TaxData)==null?void 0:q.TaxDataSet)==null||U.forEach(Ue=>{var tt,Es;const ct=Ue.Country;me[ct]||(me[ct]={ControlId:Ue.ControlId??null,Function:"INS",Material:((tt=l.headerData)==null?void 0:tt.materialNumber)||"",Depcountry:Ue.Country});const Mt=Object.keys(me[ct]).filter(se=>se.startsWith("TaxType")).length+1;Mt<=9&&(me[ct][`TaxType${Mt}`]=Ue.TaxType,me[ct][`Taxclass${Mt}`]=((Es=Ue.SelectedTaxClass)==null?void 0:Es.TaxClass)||"")}),ac=Object.values(me)}const cc=(R=l==null?void 0:l.payloadData)!=null&&R.Costing?Object.entries(l.payloadData.Costing):[],ku=((v=l==null?void 0:l.headerData)==null?void 0:v.orgData)||[],dc=new Set,Bu=ku.filter(me=>{var ct,Mt;if(!((Mt=(ct=me.plant)==null?void 0:ct.value)!=null&&Mt.code))return!1;const Ue=me.plant.value.code;return dc.has(Ue)?!1:(dc.add(Ue),!0)}).map(me=>{var tt,Es,se,Cs,Tn;const Ue=(Es=(tt=me.plant)==null?void 0:tt.value)==null?void 0:Es.code,ct=((se=Hu.find(([At])=>At===Ue))==null?void 0:se[1])||{},Mt=((Cs=cc.find(([At])=>At===Ue))==null?void 0:Cs[1])||{};return{...ct,AccountingId:ct.AccountingId||Mt.AccountingId||null,Function:"INS",Material:((Tn=l.headerData)==null?void 0:Tn.materialNumber)||"",DelFlag:"",PriceCtrl:ct.PriceCtrl||"",MovingPr:ct.MovingPr||Mt.MovingPr||"",StdPrice:ct.StdPrice||Mt.StdPrice||"",PriceUnit:ct.PriceUnit||"",ValClass:ct.ValClass||"",OrigMat:Mt.OrigMat===!0||Mt.OrigMat==="X"||Mt.OrigMat==="TRUE"?"X":"",ValArea:Ue||""}}),$u=(b=l==null?void 0:l.payloadData)!=null&&b.Warehouse?Object.entries(l.payloadData.Warehouse):[],Gu=(Ne=(ue=l.headerData)==null?void 0:ue.views)!=null&&Ne.includes((B=Y)==null?void 0:B.WAREHOUSE)?$u.map(([me,Ue],ct)=>{var Mt;return{WarehouseId:Ue.WarehouseId||"",Function:Ue.Function||"",Material:((Mt=l.headerData)==null?void 0:Mt.materialNumber)||"",DelFlag:Ue.DelFlag||!0,WhseNo:me||"",SpecMvmt:Ue.SpecMvmt||"",LEquip1:Ue.LEquip1||"",LeqUnit1:Ue.LeqUnit1||"",Unittype1:Ue.Unittype1||"",Placement:Ue.Placement||""}}):[],Fu=(A=l==null?void 0:l.payloadData)!=null&&A[Y.WORKSCHEDULING]?Object.entries((Ae=l.payloadData)==null?void 0:Ae[Y.WORKSCHEDULING]):[],Wu=(Q=l==null?void 0:l.payloadData)!=null&&Q[Y.BOM]?Object.entries((G=l.payloadData)==null?void 0:G[Y.BOM]):[],ju=(Pe=l==null?void 0:l.payloadData)!=null&&Pe[Y.SOURCE_LIST]?Object.entries(($e=l.payloadData)==null?void 0:$e[Y.SOURCE_LIST]):[],zu=(((Be=l==null?void 0:l.headerData)==null?void 0:Be.orgData)||[]).filter((me,Ue,ct)=>Ue===(ct==null?void 0:ct.findIndex(Mt=>{var tt,Es,se,Cs;return((Es=(tt=Mt.plant)==null?void 0:tt.value)==null?void 0:Es.code)===((Cs=(se=me==null?void 0:me.plant)==null?void 0:se.value)==null?void 0:Cs.code)}))).map((me,Ue)=>{var Sr,Mr,Or,vr,yr,Lr,xr,Dr,Pr,qr,Ur,Hr,kr,Br,hc,fc,gc,Tc,pc,Ec,Cc,mc,Ac,bc,wc,Ic,Rc,_c,Nc,Sc;const ct=(Mr=(Sr=me.plant)==null?void 0:Sr.value)==null?void 0:Mr.code,Mt=(Or=me.mrpProfile)==null?void 0:Or.code,tt=((vr=Pu.find(([Mo])=>Mo===ct))==null?void 0:vr[1])||{},Es=((yr=cc.find(([Mo])=>Mo===ct))==null?void 0:yr[1])||{},se=((Lr=qu.find(([Mo])=>Mo.startsWith(ct)))==null?void 0:Lr[1])||{},Cs=((xr=Uu.find(([Mo])=>Mo===ct))==null?void 0:xr[1])||{},Tn=((Dr=Fu.find(([Mo])=>Mo===ct))==null?void 0:Dr[1])||{},At=((Pr=Wu.find(([Mo])=>Mo===ct))==null?void 0:Pr[1])||{},gt=((qr=ju.find(([Mo])=>Mo===ct))==null?void 0:qr[1])||{};return{PlantId:C===I.EXTEND&&e?null:((kr=(Hr=(Ur=l.payloadData)==null?void 0:Ur.Purchasing)==null?void 0:Hr[ct])==null?void 0:kr.PlantId)??null,Function:"INS",Material:((Br=l.headerData)==null?void 0:Br.materialNumber)||"",Plant:ct||"",DelFlag:!1,CritPart:!1,PurGroup:(tt==null?void 0:tt.PurGroup)||"",PurStatus:(tt==null?void 0:tt.PurStatus)||"",RoundProf:(se==null?void 0:se.RoundProf)||"",IssueUnit:"",IssueUnitIso:"",Mrpprofile:Mt||"",MrpType:(se==null?void 0:se.MrpType)||"",MrpCtrler:(se==null?void 0:se.MrpCtrler)||"",PlndDelry:(se==null?void 0:se.PlndDelry)||"",GrPrTime:(se==null?void 0:se.GrPrTime)||"",PeriodInd:(se==null?void 0:se.PeriodInd)||"",Lotsizekey:(se==null?void 0:se.Lotsizekey)||"",ProcType:(se==null?void 0:se.ProcType)||"",Consummode:(se==null?void 0:se.Consummode)||"",FwdCons:(se==null?void 0:se.FwdCons)||"",ReorderPt:(se==null?void 0:se.ReorderPt)||"",MaxStock:(se==null?void 0:se.MaxStock)||"",SafetyStk:(se==null?void 0:se.SafetyStk)||"",Minlotsize:(se==null?void 0:se.Minlotsize)||"",PlanStrgp:(se==null?void 0:se.PlanStrgp)||"",BwdCons:(se==null?void 0:se.BwdCons)||"",Maxlotsize:(se==null?void 0:se.Maxlotsize)||"",FixedLot:(se==null?void 0:se.FixedLot)||"",RoundVal:(se==null?void 0:se.RoundVal)||"",GrpReqmts:(se==null?void 0:se.GrpReqmts)||"",MixedMrp:(se==null?void 0:se.MixedMrp)||"",SmKey:(se==null?void 0:se.SmKey)||"",Backflush:(se==null?void 0:se.Backflush)||"",AssyScarp:(se==null?void 0:se.AssyScarp)||"",Replentime:(se==null?void 0:se.Replentime)||"",PlTiFnce:(se==null?void 0:se.PlTiFnce)||"",ReplacePt:"",IndPostToInspStock:(tt==null?void 0:tt.IndPostToInspStock)===!0||(tt==null?void 0:tt.IndPostToInspStock)==="X"||(tt==null?void 0:tt.IndPostToInspStock)==="TRUE"?"X":"",HtsCode:(tt==null?void 0:tt.HtsCode)||"",CtrlKey:"",BatchMgmt:(se==null?void 0:se.BatchMgmt)||!1,DepReqId:(se==null?void 0:se.DepReqId)||"",SaftyTId:(se==null?void 0:se.SaftyTId)||"",Safetytime:(se==null?void 0:se.Safetytime)||"",Matfrgtgrp:(Cs==null?void 0:Cs.Matfrgtgrp)||"",Availcheck:(Cs==null?void 0:Cs.Availcheck)||"",ProfitCtr:(Cs==null?void 0:Cs.ProfitCtr)||"",Loadinggrp:(Cs==null?void 0:Cs.Loadinggrp)||"",MinLotSize:(se==null?void 0:se.MinLotSize)||"",MaxLotSize:(se==null?void 0:se.MaxLotSize)||"",FixLotSize:(se==null?void 0:se.FixLotSize)||"",AssyScrap:(se==null?void 0:se.AssyScrap)||"",IssStLoc:(se==null?void 0:se.IssStLoc)||"",SalesView:((fc=l==null?void 0:l.headerData)==null?void 0:fc.views.includes((hc=Y)==null?void 0:hc.SALES))||!1,PurchView:((Tc=l==null?void 0:l.headerData)==null?void 0:Tc.views.includes((gc=Y)==null?void 0:gc.PURCHASING))||!1,MrpView:((Ec=l==null?void 0:l.headerData)==null?void 0:Ec.views.includes((pc=Y)==null?void 0:pc.MRP))||!1,WorkSchedView:((mc=l==null?void 0:l.headerData)==null?void 0:mc.views.includes((Cc=Y)==null?void 0:Cc.WORK_SCHEDULING_2))||!1,WarehouseView:((bc=l==null?void 0:l.headerData)==null?void 0:bc.views.includes((Ac=Y)==null?void 0:Ac.WAREHOUSE))||!1,AccountView:((Ic=l==null?void 0:l.headerData)==null?void 0:Ic.views.includes((wc=Y)==null?void 0:wc.ACCOUNTING))||!1,CostView:((_c=l==null?void 0:l.headerData)==null?void 0:_c.views.includes((Rc=Y)==null?void 0:Rc.COSTING))||!1,ForecastView:!1,PrtView:!1,StorageView:((Sc=l==null?void 0:l.headerData)==null?void 0:Sc.views.includes((Nc=Y)==null?void 0:Nc.STORAGE))||!1,QualityView:!1,GrProcTime:"",GiProcTime:"",StorageCost:"",LotSizeUom:"",LotSizeUomIso:"",Unlimited:Tn.Unlimited||"",ProdProf:Tn.ProdProf||"",VarianceKey:Es.VarianceKey||"",PoUnit:"",Spproctype:se.Spproctype||"",CommCode:(tt==null?void 0:tt.CommCode)||"",CommCoUn:(tt==null?void 0:tt.CommCoUn)||"",Countryori:tt==null?void 0:tt.Countryori,LotSize:Es.LotSize||"",SlocExprc:se.SlocExprc||"",Inhseprodt:Es.Inhseprodt||"",BomUsage:(At==null?void 0:At.BomUsage)||"",AltBom:(At==null?void 0:At.AltBom)||"",Category:(At==null?void 0:At.Category)||"",Component:(At==null?void 0:At.Component)||"",Quantity:(At==null?void 0:At.Quantity)||"",CompUom:(At==null?void 0:At.CompUom)||"",Bvalidfrom:At!=null&&At.Bvalidfrom?Ot(At==null?void 0:At.Bvalidfrom):Ot(new Date().toISOString()),Bvalidto:At!=null&&At.Bvalidto?Ot(At==null?void 0:At.Bvalidto):Ot(new Date().toISOString()),Supplier:(gt==null?void 0:gt.Supplier)||"",PurchaseOrg:(gt==null?void 0:gt.PurchaseOrg)||"",ProcurementPlant:(gt==null?void 0:gt.ProcurementPlant)||"",SOrderUnit:(gt==null?void 0:gt.SOrderUnit)||"",Agreement:(gt==null?void 0:gt.Agreement)||"",AgreementItem:(gt==null?void 0:gt.AgreementItem)||"",FixedSupplySource:(gt==null?void 0:gt.FixedSupplySource)||"",Blocked:(gt==null?void 0:gt.Blocked)||"",SMrp:(gt==null?void 0:gt.SMrp)||"",Slvalidfrom:gt!=null&&gt.Slvalidfrom?Ot(gt==null?void 0:gt.Slvalidfrom):Ot(new Date().toISOString()),Slvalidto:gt!=null&&gt.Slvalidto?Ot(gt==null?void 0:gt.Slvalidto):Ot(new Date().toISOString())}}),Un=(l==null?void 0:l.additionalData)||[],Hn=(l==null?void 0:l.unitsOfMeasureData)||[],kn=(l==null?void 0:l.eanData)||[],Yu=Un!=null&&Un.length?Un==null?void 0:Un.map(me=>{var Ue;return{MaterialDescriptionId:C===I.EXTEND&&e?null:me.MaterialDescriptionId||null,Function:"INS",Material:((Ue=l.headerData)==null?void 0:Ue.materialNumber)||"",Langu:me.language||"EN",LanguIso:"",MatlDesc:(me==null?void 0:me.materialDescription)||"",DelFlag:!1}}):[{MaterialDescriptionId:null,Function:"INS",Material:((bs=l.headerData)==null?void 0:bs.materialNumber)||"",Langu:"EN",LanguIso:"",MatlDesc:((us=l.headerData)==null?void 0:us.globalMaterialDescription)||"",DelFlag:!1}],Ku=Hn!=null&&Hn.length?Hn==null?void 0:Hn.map(me=>{var Ue;return{UomId:C===I.EXTEND&&e?null:(me==null?void 0:me.UomId)||null,Function:"INS",Material:((Ue=l.headerData)==null?void 0:Ue.materialNumber)||"",AltUnit:(me==null?void 0:me.aUnit)||"",AltUnitIso:"",Numerator:(me==null?void 0:me.yValue)||"1",Denominatr:(me==null?void 0:me.xValue)||"1",EanUpc:(me==null?void 0:me.eanUpc)||"",EanCat:me.eanCategory||"",Length:me.length,NetWeight:me.netWeight||"",Width:me.width,Height:me.height,UnitDim:me.unitsOfDimension||"",UnitDimIso:"",Volume:me.volume||"",Volumeunit:me.volumeUnit||"",VolumeunitIso:"",GrossWt:me.grossWeight||"",UnitOfWt:me.weightUnit||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:me.capacityUsage,EwmCwUomType:"",MaterialLong:null}}):[{UomId:null,Function:"INS",Material:((Fs=l.headerData)==null?void 0:Fs.materialNumber)||"",AltUnit:((Qs=(ws=(Ws=l==null?void 0:l.payloadData)==null?void 0:Ws["Basic Data"])==null?void 0:ws.basic)==null?void 0:Qs.BaseUom)||"",AltUnitIso:"",Numerator:"1",Denominatr:"1",EanUpc:"",EanCat:"",Length:"",Width:"",Height:"",UnitDim:"",UnitDimIso:"",Volume:((Zs=(js=(Is=l==null?void 0:l.payloadData)==null?void 0:Is["Basic Data"])==null?void 0:js.basic)==null?void 0:Zs.Volume)||"",Volumeunit:((Ft=(Ht=(os=l==null?void 0:l.payloadData)==null?void 0:os["Basic Data"])==null?void 0:Ht.basic)==null?void 0:Ft.VolumeUnit)||"",VolumeunitIso:"",GrossWt:"",UnitOfWt:((Ye=(ns=(Pt=l==null?void 0:l.payloadData)==null?void 0:Pt["Basic Data"])==null?void 0:ns.basic)==null?void 0:Ye.UnitOfWt)||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:"",EwmCwUomType:"",MaterialLong:null}],Xu=kn!=null&&kn.length?kn==null?void 0:kn.map(me=>{var Ue;return{EanId:C===I.EXTEND&&e?null:(me==null?void 0:me.EanId)||null,Function:"INS",Material:((Ue=l.headerData)==null?void 0:Ue.materialNumber)||"",Unit:(me==null?void 0:me.altunit)||"",EanUpc:(me==null?void 0:me.eanUpc)||"",EanCat:(me==null?void 0:me.eanCategory)||"",MainEan:me.MainEan||!1,Au:me.au||!1}}):null,uc=new Set,Vu=(yt=l==null?void 0:l.payloadData)!=null&&yt.Tostroragelocationdata?(Et=l==null?void 0:l.payloadData)==null?void 0:Et.Tostroragelocationdata:(be=(_s=l.headerData)==null?void 0:_s.views)!=null&&be.includes((Vt=Y)==null?void 0:Vt.STORAGE)?ui.filter(me=>{var ct,Mt,tt,Es;if(!((Mt=(ct=me==null?void 0:me.plant)==null?void 0:ct.value)!=null&&Mt.code)||!((Es=(tt=me==null?void 0:me.sloc)==null?void 0:tt.value)!=null&&Es.code))return!1;const Ue=`${me.plant.value.code}-${me.sloc.value.code}`;return uc.has(Ue)?!1:(uc.add(Ue),!0)}).map(me=>{var Ue,ct,Mt,tt,Es;return{Function:"INS",Material:((Ue=l==null?void 0:l.headerData)==null?void 0:Ue.materialNumber)||"",Plant:((Mt=(ct=me==null?void 0:me.plant)==null?void 0:ct.value)==null?void 0:Mt.code)||"",StgeLoc:((Es=(tt=me==null?void 0:me.sloc)==null?void 0:tt.value)==null?void 0:Es.code)||""}}):[],Ju={ChildRequestId:((kt=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:kt.ChildRequestId)||null,MaterialGroupType:((rs=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:rs.MaterialGroupType)||null,TaskId:(i==null?void 0:i.taskId)||null,Comments:n||o,TotalIntermediateTasks:((ls=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:ls.TotalIntermediateTasks)||null,IntermediateTaskCount:(($t=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:$t.IntermediateTaskCount)||null,ReqCreatedBy:((is=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:is.ReqCreatedBy)||null,ReqCreatedOn:((ot=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:ot.ReqCreatedOn)||null,ReqUpdatedOn:((Ct=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:Ct.ReqUpdatedOn)||null,RequestType:((as=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:as.RequestType)||null,RequestPrefix:((xs=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:xs.RequestPrefix)||null,RequestDesc:((Wt=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:Wt.RequestDesc)||null,RequestPriority:((uo=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:uo.RequestPriority)||null,RequestStatus:((no=l==null?void 0:l.Tochildrequestheaderdata)==null?void 0:no.RequestStatus)||null,CurrentLevel:(i==null?void 0:i.ATTRIBUTE_3)||"",CurrentLevelName:(i==null?void 0:i.ATTRIBUTE_4)||"",ParticularLevel:c,TaskName:(i==null?void 0:i.taskDesc)||"",ApproverGroup:(i==null?void 0:i.ATTRIBUTE_5)||""},Qu={MaterialId:C===I.EXTEND&&e||C===I.CREATE&&e?null:(i!=null&&i.taskId||t)&&!V.includes("-")?Number(V):null,Flag:"",Function:"INS",Material:((Le=l==null?void 0:l.headerData)==null?void 0:Le.materialNumber)||"",MatlType:((hs=(mt=l==null?void 0:l.headerData)==null?void 0:mt.materialType)==null?void 0:hs.code)||((Ds=l==null?void 0:l.headerData)==null?void 0:Ds.materialType)||"",IndSector:((Ns=(zs=l==null?void 0:l.headerData)==null?void 0:zs.industrySector)==null?void 0:Ns.code)||((Ss=l==null?void 0:l.headerData)==null?void 0:Ss.industrySector)||"",Comments:n||o,ViewNames:xu,Description:((Jt=l==null?void 0:l.headerData)==null?void 0:Jt.globalMaterialDescription)||"",Bom:((Ms=l==null?void 0:l.headerData)==null?void 0:Ms.Bom)||"",SourceList:((Ys=l==null?void 0:l.headerData)==null?void 0:Ys.sourceList)||"",Pir:((nt=l==null?void 0:l.headerData)==null?void 0:nt.PIR)||"",Uom:(ke=(Qt=l==null?void 0:l.headerData)==null?void 0:Qt.Uom)!=null&&ke.code?l.headerData.Uom.code:((dt=l==null?void 0:l.headerData)==null?void 0:dt.Uom)||"",Category:(z=(Zt=l==null?void 0:l.headerData)==null?void 0:Zt.Category)!=null&&z.code?l.headerData.Category.code:((Se=l==null?void 0:l.headerData)==null?void 0:Se.Category)||"",Relation:(ge=(ne=l==null?void 0:l.headerData)==null?void 0:ne.Relation)!=null&&ge.code?l.headerData.Relation.code:((xe=l==null?void 0:l.headerData)==null?void 0:xe.Relation)||"",Usage:((pe=l==null?void 0:l.headerData)==null?void 0:pe.Usage)||"",CreationDate:i!=null&&i.requestId||t||(Ke=f==null?void 0:f.payloadData)!=null&&Ke.RequestId?Ot((_e=f==null?void 0:f.payloadData)==null?void 0:_e.ReqCreatedOn):`/Date(${Date.now()}+0000)/`,EditId:null,ExtendId:null,MassCreationId:C===I.CREATE||C===I.CREATE_WITH_UPLOAD?i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(It=f==null?void 0:f.payloadData)!=null&&It.RequestId?(Gt=f==null?void 0:f.payloadData)==null?void 0:Gt.RequestId:"":null,MassEditId:((ut=l==null?void 0:l.payloadData)==null?void 0:ut.MassEditId)||"",MassExtendId:C===I.EXTEND||C===I.EXTEND_WITH_UPLOAD?i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(pt=f==null?void 0:f.payloadData)!=null&&pt.RequestId?(Ut=f==null?void 0:f.payloadData)==null?void 0:Ut.RequestId:(Kt=h==null?void 0:h.requestHeader)!=null&&Kt.requestId?(Ps=h==null?void 0:h.requestHeader)==null?void 0:Ps.requestId:requestId.slice(3):null,TaskId:(i==null?void 0:i.taskId)||null,TaskName:(i==null?void 0:i.taskDesc)||null,TotalIntermediateTasks:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(qs=f==null?void 0:f.payloadData)!=null&&qs.RequestId?(eo=f==null?void 0:f.payloadData)==null?void 0:eo.TotalIntermediateTasks:"",IntermediateTaskCount:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(lt=f==null?void 0:f.payloadData)!=null&&lt.RequestId?(qt=f==null?void 0:f.payloadData)==null?void 0:qt.IntermediateTaskCount:"",BasicView:((we=l==null?void 0:l.headerData)==null?void 0:we.views.includes((ee=Y)==null?void 0:ee.BASIC_DATA))||!1,SalesView:((Me=l==null?void 0:l.headerData)==null?void 0:Me.views.includes((M=Y)==null?void 0:M.SALES))||!1,MrpView:((oe=l==null?void 0:l.headerData)==null?void 0:oe.views.includes((Xe=Y)==null?void 0:Xe.MRP))||!1,PurchaseView:((Oe=l==null?void 0:l.headerData)==null?void 0:Oe.views.includes((et=Y)==null?void 0:et.PURCHASING))||!1,AccountView:((Ze=l==null?void 0:l.headerData)==null?void 0:Ze.views.includes((qe=Y)==null?void 0:qe.ACCOUNTING))||!1,CostView:((Nt=l==null?void 0:l.headerData)==null?void 0:Nt.views.includes((he=Y)==null?void 0:he.COSTING))||!1,WorkSchedView:((es=l==null?void 0:l.headerData)==null?void 0:es.views.includes((Us=Y)==null?void 0:Us.WORK_SCHEDULING_2))||!1,WarehouseView:((Os=l==null?void 0:l.headerData)==null?void 0:Os.views.includes((Hs=Y)==null?void 0:Hs.WAREHOUSE))||!1,ForecastView:!1,PrtView:!1,StorageView:((Eo=l==null?void 0:l.headerData)==null?void 0:Eo.views.includes((fs=Y)==null?void 0:fs.STORAGE))||!1,QualityView:!1,IsFirstChangeLogCommit:!1,IsMarkedForDeletion:!1,IsFirstCreate:!(i!=null&&i.taskId),creationTime:i!=null&&i.createdOn?Ot(i==null?void 0:i.createdOn):null,dueDate:i!=null&&i.criticalDeadline?Ot(i==null?void 0:i.criticalDeadline):null,ManufacturerId:(l==null?void 0:l.ManufacturerID)||"",OrgData:ui||[],Toclientdata:{ClientId:((cs=l==null?void 0:l.headerData)==null?void 0:cs.clientId)||null,Function:"INS",Material:((st=l==null?void 0:l.headerData)==null?void 0:st.materialNumber)||"",DelFlag:!0,MatlGroup:((fo=(ho=(ts=l==null?void 0:l.payloadData)==null?void 0:ts["Basic Data"])==null?void 0:ho.basic)==null?void 0:fo.MatlGroup)||"",Extmatlgrp:((Ks=(mo=(Co=l==null?void 0:l.payloadData)==null?void 0:Co["Basic Data"])==null?void 0:mo.basic)==null?void 0:Ks.Extmatlgrp)||"",OldMatNo:((gs=(Ao=(Rt=l==null?void 0:l.payloadData)==null?void 0:Rt["Basic Data"])==null?void 0:Ao.basic)==null?void 0:gs.OldMatNo)||"",BaseUom:((Io=(wo=(bo=l==null?void 0:l.payloadData)==null?void 0:bo["Basic Data"])==null?void 0:wo.basic)==null?void 0:Io.BaseUom)||"",Document:(Z=(_o=(Ro=l==null?void 0:l.payloadData)==null?void 0:Ro["Basic Data"])==null?void 0:_o.basic)==null?void 0:Z.Document,DocType:(Ve=(Fe=(Ge=l==null?void 0:l.payloadData)==null?void 0:Ge["Basic Data"])==null?void 0:Fe.basic)==null?void 0:Ve.DocType,DocVers:(ht=(Qe=(fe=l==null?void 0:l.payloadData)==null?void 0:fe["Basic Data"])==null?void 0:Qe.basic)==null?void 0:ht.DocVers,DocFormat:(Bt=(_t=(it=l==null?void 0:l.payloadData)==null?void 0:it["Basic Data"])==null?void 0:_t.basic)==null?void 0:Bt.DocFormat,DocChgNo:(Ee=(at=(We=l==null?void 0:l.payloadData)==null?void 0:We["Basic Data"])==null?void 0:at.basic)==null?void 0:Ee.DocChgNo,PageNo:(zo=(Lt=(je=l==null?void 0:l.payloadData)==null?void 0:je["Basic Data"])==null?void 0:Lt.basic)==null?void 0:zo.PageNo,NoSheets:(In=l==null?void 0:l.payloadData)==null?void 0:In.NoSheets,ProdMemo:(Rn=l==null?void 0:l.payloadData)==null?void 0:Rn.ProdMemo,Pageformat:(an=l==null?void 0:l.payloadData)==null?void 0:an.DocFormat,SizeDim:(tr=l==null?void 0:l.payloadData)==null?void 0:tr.SizeDim,BaseUomIso:"",BasicMatl:((il=(ll=(rl=l==null?void 0:l.payloadData)==null?void 0:rl["Basic Data"])==null?void 0:ll.basic)==null?void 0:il.BasicMatl)||"",StdDescr:(di=l==null?void 0:l.payloadData)==null?void 0:di.StdDescr,DsnOffice:((vs=(Zo=(al=l==null?void 0:l.payloadData)==null?void 0:al["Basic Data"])==null?void 0:Zo.basic)==null?void 0:vs.DsnOffice)||"",PurValkey:((Pn=(cl=(Dn=l==null?void 0:l.payloadData)==null?void 0:Dn["Purchasing-General"])==null?void 0:cl["Purchasing-General"])==null?void 0:Pn.PurValkey)||"",NetWeight:(ul=(dl=(qn=l==null?void 0:l.payloadData)==null?void 0:qn["Basic Data"])==null?void 0:dl.basic)==null?void 0:ul.NetWeight,UnitOfWt:((fl=(Ts=(hl=l==null?void 0:l.payloadData)==null?void 0:hl["Basic Data"])==null?void 0:Ts.basic)==null?void 0:fl.UnitOfWt)||"",TransGrp:(sr=(Tl=(gl=l==null?void 0:l.payloadData)==null?void 0:gl["Sales-General"])==null?void 0:Tl["Sales-General"])==null?void 0:sr.TransGrp,XSalStatus:(Cl=(El=(pl=l==null?void 0:l.payloadData)==null?void 0:pl["Sales-General"])==null?void 0:El["Sales-General"])==null?void 0:Cl.XSalStatus,Svalidfrom:(bl=(Al=(ml=l==null?void 0:l.payloadData)==null?void 0:ml["Sales-General"])==null?void 0:Al["Sales-General"])!=null&&bl.Svalidfrom?Ot((Il=(wl=(or=l==null?void 0:l.payloadData)==null?void 0:or["Sales-General"])==null?void 0:wl["Sales-General"])==null?void 0:Il.Svalidfrom):null,Division:(nr=f==null?void 0:f.payloadData)!=null&&nr.Division?(Rl=f==null?void 0:f.payloadData)==null?void 0:Rl.Division:((Sl=(Nl=(_l=l==null?void 0:l.payloadData)==null?void 0:_l["Basic Data"])==null?void 0:Nl.basic)==null?void 0:Sl.Division)||"",ProdHier:((vl=(Ol=(Ml=l==null?void 0:l.payloadData)==null?void 0:Ml["Basic Data"])==null?void 0:Ol.basic)==null?void 0:vl.ProdHier)||"",CadId:(lr=(yl=(rr=l==null?void 0:l.payloadData)==null?void 0:rr["Basic Data"])==null?void 0:yl.basic)==null?void 0:lr.CadId,VarOrdUn:(xl=(Ll=(ir=l==null?void 0:l.payloadData)==null?void 0:ir["Purchasing-General"])==null?void 0:Ll["Purchasing-General"])==null?void 0:xl.VarOrdUn,UnitOfWtIso:"",MatGrpSm:"",Authoritygroup:"",QmProcmnt:"",BatchMgmt:(dr=(cr=(ar=l==null?void 0:l.payloadData)==null?void 0:ar["Sales-General"])==null?void 0:cr["Sales-General"])==null?void 0:dr.BatchMgmt,SalStatus:"",Catprofile:"",Minremlife:"",ShelfLife:"",StorPct:"",Hazmatprof:((fr=(hr=(ur=l==null?void 0:l.payloadData)==null?void 0:ur["Basic Data"])==null?void 0:hr.basic)==null?void 0:fr.Hazmatprof)||"",HighVisc:(pr=(Tr=(gr=l==null?void 0:l.payloadData)==null?void 0:gr["Basic Data"])==null?void 0:Tr.basic)==null?void 0:pr.HighVisc,AppdBRec:"",Pvalidfrom:(mr=(Cr=(Er=l==null?void 0:l.payloadData)==null?void 0:Er["Basic Data"])==null?void 0:Cr.basic)!=null&&mr.Pvalidfrom?Ot((wr=(br=(Ar=l==null?void 0:l.payloadData)==null?void 0:Ar["Basic Data"])==null?void 0:br.basic)==null?void 0:wr.Pvalidfrom):null,EnvtRlvt:"",ProdAlloc:(_r=(Rr=(Ir=l==null?void 0:l.payloadData)==null?void 0:Ir["Basic Data"])==null?void 0:Rr.basic)==null?void 0:_r.ProdAlloc,PeriodIndExpirationDate:"",ParEff:!0,Matcmpllvl:"",GItemCat:((d=(r=(Nr=l==null?void 0:l.payloadData)==null?void 0:Nr["Basic Data"])==null?void 0:r.basic)==null?void 0:d.GItemCat)||"",CSalStatus:((y=(_=(w=l==null?void 0:l.payloadData)==null?void 0:w["Basic Data"])==null?void 0:_.basic)==null?void 0:y.CSalStatus)||"",IntlPoPrice:((W=(x=(D=l==null?void 0:l.payloadData)==null?void 0:D["Basic Data"])==null?void 0:x.basic)==null?void 0:W.IntlPoPrice)||"",PryVendor:((de=(ie=(Ce=l==null?void 0:l.payloadData)==null?void 0:Ce["Basic Data"])==null?void 0:ie.basic)==null?void 0:de.PryVendor)||"",PlanningArea:((rt=(He=(Te=l==null?void 0:l.payloadData)==null?void 0:Te["Basic Data"])==null?void 0:He.basic)==null?void 0:rt.PlanningArea)||"",PlanningFactor:((ss=(jt=(ft=l==null?void 0:l.payloadData)==null?void 0:ft["Basic Data"])==null?void 0:jt.basic)==null?void 0:ss.PlanningFactor)||"",ReturnMatNumber:((No=(Xs=(bt=l==null?void 0:l.payloadData)==null?void 0:bt["Basic Data"])==null?void 0:Xs.basic)==null?void 0:No.ReturnMatNumber)||"",ParentMatNumber:(($o=(Bo=(So=l==null?void 0:l.payloadData)==null?void 0:So["Basic Data"])==null?void 0:Bo.basic)==null?void 0:$o.ParentMatNumber)||"",DiversionControlFlag:((Fo=(Go=(ro=l==null?void 0:l.payloadData)==null?void 0:ro["Basic Data"])==null?void 0:Go.basic)==null?void 0:Fo.DiversionControlFlag)||"",MatGroupPackagingMat:((dn=(cn=(Wo=l==null?void 0:l.payloadData)==null?void 0:Wo["Basic Data"])==null?void 0:cn.basic)==null?void 0:dn.MatGroupPackagingMat)||""},Toplantdata:zu,Tosalesdata:(un=l==null?void 0:l.headerData)!=null&&un.views.includes("Sales")?Du:[],Tomaterialdescription:Yu,Touomdata:Ku,Toeandata:Xu,Tostroragelocationdata:Vu,Tomaterialerrordata:(l==null?void 0:l.Tomaterialerrordata)||{},Toaccountingdata:(ps=l==null?void 0:l.headerData)!=null&&ps.views.includes((hn=Y)==null?void 0:hn.ACCOUNTING)?Bu:[],Tocontroldata:(xo=l==null?void 0:l.headerData)!=null&&xo.views.includes("Sales")?ac:[],Torequestheaderdata:{RequestId:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(lo=f==null?void 0:f.payloadData)!=null&&lo.RequestId?(go=f==null?void 0:f.payloadData)==null?void 0:go.RequestId:(fn=h==null?void 0:h.requestHeader)!=null&&fn.requestId?(gn=h==null?void 0:h.requestHeader)==null?void 0:gn.requestId:requestId.slice(3),ReqCreatedBy:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(en=f==null?void 0:f.payloadData)!=null&&en.RequestId?(xt=f==null?void 0:f.payloadData)==null?void 0:xt.ReqCreatedBy:(ks=h==null?void 0:h.requestHeader)==null?void 0:ks.reqCreatedBy,ReqCreatedOn:Ot(i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Vs=f==null?void 0:f.payloadData)!=null&&Vs.RequestId?(Ca=f==null?void 0:f.payloadData)==null?void 0:Ca.ReqCreatedOn:(ma=h==null?void 0:h.requestHeader)==null?void 0:ma.reqCreatedOn),ReqUpdatedOn:Ot(i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Aa=f==null?void 0:f.payloadData)!=null&&Aa.RequestId?(ba=f==null?void 0:f.payloadData)==null?void 0:ba.ReqUpdatedOn:(wa=h==null?void 0:h.requestHeader)==null?void 0:wa.reqCreatedOn),RequestType:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Ia=f==null?void 0:f.payloadData)!=null&&Ia.RequestId?(Ra=f==null?void 0:f.payloadData)==null?void 0:Ra.RequestType:(_a=h==null?void 0:h.requestHeader)==null?void 0:_a.requestType,Division:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Na=f==null?void 0:f.payloadData)!=null&&Na.RequestId?(Sa=f==null?void 0:f.payloadData)==null?void 0:Sa.Division:(Ma=h==null?void 0:h.requestHeader)==null?void 0:Ma.Division,RequestPriority:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Oa=f==null?void 0:f.payloadData)!=null&&Oa.RequestId?(va=f==null?void 0:f.payloadData)==null?void 0:va.RequestPriority:(ya=h==null?void 0:h.requestHeader)==null?void 0:ya.requestPriority,RequestDesc:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(La=f==null?void 0:f.payloadData)!=null&&La.RequestId?(xa=f==null?void 0:f.payloadData)==null?void 0:xa.RequestDesc:(Da=h==null?void 0:h.requestHeader)==null?void 0:Da.requestDesc,RequestStatus:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Pa=f==null?void 0:f.payloadData)!=null&&Pa.RequestId?(qa=f==null?void 0:f.payloadData)==null?void 0:qa.RequestStatus:(Ua=h==null?void 0:h.requestHeader)==null?void 0:Ua.requestStatus,FirstProd:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Ha=f==null?void 0:f.payloadData)!=null&&Ha.RequestId?(ka=f==null?void 0:f.payloadData)==null?void 0:ka.FirstProd:((Ba=s==null?void 0:s.payloadData)==null?void 0:Ba.FirstProductionDate)||null,LaunchDate:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||($a=f==null?void 0:f.payloadData)!=null&&$a.RequestId?(Ga=f==null?void 0:f.payloadData)==null?void 0:Ga.LaunchDate:((Fa=s==null?void 0:s.payloadData)==null?void 0:Fa.LaunchDate)||null,LeadingCat:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Wa=f==null?void 0:f.payloadData)!=null&&Wa.RequestId?(ja=f==null?void 0:f.payloadData)==null?void 0:ja.LeadingCat:(za=h==null?void 0:h.requestHeader)==null?void 0:za.leadingCat,Region:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Ya=f==null?void 0:f.payloadData)!=null&&Ya.RequestId?(Ka=f==null?void 0:f.payloadData)==null?void 0:Ka.Region:(Xa=h==null?void 0:h.requestHeader)==null?void 0:Xa.region,IsBifurcated:!0},Tochildrequestheaderdata:i!=null&&i.requestId||i!=null&&i.ATTRIBUTE_1||t||(Va=f==null?void 0:f.payloadData)!=null&&Va.RequestId?Ju:{},Towarehousedata:Gu,changeLogData:le&&Object.keys(le).length>0&&(le[(Ja=l.headerData)==null?void 0:Ja.materialNumber]||le[(Qa=l.headerData)==null?void 0:Qa.id])?{RequestId:(le==null?void 0:le.RequestId)||((Za=l==null?void 0:l.changeLogData)==null?void 0:Za.RequestId),ChangeLogId:((ec=l==null?void 0:l.changeLogData)==null?void 0:ec.ChangeLogId)??null,ChildRequestId:((le==null?void 0:le.ChildRequestId)||((tc=l==null?void 0:l.changeLogData)==null?void 0:tc.ChildRequestId))??null,...le[(sc=l.headerData)==null?void 0:sc.materialNumber],...le[(oc=l.headerData)==null?void 0:oc.id]}:{RequestId:(nc=l==null?void 0:l.changeLogData)==null?void 0:nc.RequestId,ChangeLogId:((rc=l==null?void 0:l.changeLogData)==null?void 0:rc.ChangeLogId)??null,ChildRequestId:((lc=l==null?void 0:l.changeLogData)==null?void 0:lc.ChildRequestId)??null}};X.push(Qu)}}}),X}return{createPayloadFromReduxState:Ie}},Jc={handleSubmitForApproval:6,handleSendBack:1,handleReject:3,handleValidate:5,handleSAPSyndication:8,handleValidate1:4,handleSubmitForReview:7,handleCorrection:2},Qc={Approve:6,"Send Back":3,"Save As Draft":1,Reject:3,Validate:4,Forward:6,"SAP Syndication":8,Submit:7,Correction:2},Hl={REQUEST_HEADER:0,REQUEST_DETAILS:1,ATTACHMENT_AND_COMMENTS:2,PREVIEW:3},Oo={HANDLE_SEND_BACK:"handleSendBack",HANDLE_VALIDATE1:"handleValidate1",HANDLE_SUBMIT_FOR_APPROVAL:"handleSubmitForApproval",HANDLE_VALIDATE:"handleValidate",HANDLE_SAP_SYNDICATION:"handleSAPSyndication",HANDLE_SUBMIT_FOR_REVIEW:"handleSubmitForReview",HANDLE_CORRECTION:"handleCorrection",HANDLE_SUBMIT:"handleSubmit",HANDLE_DRAFT:"handleDraft"},mg=E.forwardRef((e,t)=>{var S;const[n,o]=E.useState([]),c=re(g=>g.payload),s=((S=c==null?void 0:c.payloadData)==null?void 0:S.TemplateName)||"",[C,i]=E.useState(!1),[h,le]=E.useState(!1),{changePayloadForTemplate:Ie}=Dd(s),K=re(g=>g.commonFilter.RequestBench),{customError:f}=Qo(),X=Jr();E.useImperativeHandle(t,()=>({handlePriorityDialogClickOpen:ce}));const V=re(g=>g.paginationData),H=[{field:"id",headerName:"",editable:!1,flex:1,hide:!0},{field:"reqId",headerName:"Request ID",editable:!1,flex:2.5},{field:"module",headerName:"Module",editable:!1,flex:2},{field:"reqType",headerName:"Request Type",editable:!1,flex:1.5},{field:"scheduledBy",headerName:"Scheduled By",editable:!1,flex:4},{field:"scheduledOn",headerName:"Scheduled On",editable:!1,flex:1},{field:"totalObjects",headerName:"Total Objects",editable:!1,flex:1,renderHeader:()=>a(Xt,{title:"Objects count scheduled for SAP syndication",arrow:!0,children:a("span",{children:"Request ID"})})},{field:"pendingObjects",headerName:"Pending Objects",editable:!1,flex:1,renderHeader:()=>a(Xt,{title:"Objects count pending for SAP syndicated.",arrow:!0,children:a("span",{children:"Request ID"})})},{field:"objectSuccessCount",headerName:"Success Objects",editable:!1,flex:1,renderHeader:()=>a(Xt,{title:"Objects count syndicated in SAP",arrow:!0,children:a("span",{children:"Request ID"})})},{field:"objectFailureCount",headerName:"Failure Objects",editable:!1,flex:1,renderHeader:()=>a(Xt,{title:"Objects count failed during syndication in SAP",arrow:!0,children:a("span",{children:"Request ID"})})},{field:"retryCount",headerName:"Retry Count",editable:!1,flex:1,renderHeader:()=>a(Xt,{title:"Number of times request retriggered.(Max- 3 count, after that it wouldnt be picked & set as status- Retry Count Exceeded)",arrow:!0,children:a("span",{children:"Request ID"})})},{field:"schedulerStatus",headerName:"Scheduler Status",editable:!1,flex:1},{field:"action",headerName:"Action",editable:!1,flex:1}],ce=()=>{J()},J=()=>{var O;const g=Wn().format("YYYY-MM-DDTHH:mm:ss.SSSZ"),N={modules:"Material,Cost Center,Profit Center,CC-PC Combo,General Ledger",requestTypes:"Create with Upload,Change with Upload,Extend with Upload",statuses:"Scheduler - Failed,Scheduler - Pending,Scheduler - Partially Completed",toDate:Wn(K==null?void 0:K.createdOn[1]).utc().format("YYYY-MM-DDTHH:mm:ss")??"",fromDate:Wn(K==null?void 0:K.createdOn[0]).utc().format("YYYY-MM-DDTHH:mm:ss")??""},L=P=>{var U,R,v,b;if(P.statusCode===200){let ue=(P==null?void 0:P.data)||[];le(!0);let B=ue==null?void 0:ue.map((G,Pe)=>({id:Pe,schedulerId:G==null?void 0:G.SchedulerId,reqId:G==null?void 0:G.RequestId,reqType:G==null?void 0:G.RequestType,priority:G==null?void 0:G.Priority,scheduledBy:G==null?void 0:G.ReqScheduledBy,scheduledOn:G==null?void 0:G.ReqScheduledOn,totalObjects:G==null?void 0:G.ObjectCount,pendingObjects:G==null?void 0:G.PendingObjectsCount,retryCount:G==null?void 0:G.RetryCount,module:G==null?void 0:G.Module,objectSuccessCount:G==null?void 0:G.ObjectsSuccessCount,objectFailureCount:G==null?void 0:G.ObjectsFailureCount,updatedOn:G==null?void 0:G.ReqUpdatedOn,schedulerStatus:G==null?void 0:G.SchedulerStatus}));const Ne=B==null?void 0:B.filter(G=>G.module==="Material"&&G.reqType==="Create with Upload"),A=(Ne==null?void 0:Ne.length)>0?Math.max(...Ne.map(G=>G.priority)):0;var q={id:B.length+1,schedulerId:"",reqId:((U=e.taskData)==null?void 0:U.requestId)||((R=e.taskData)==null?void 0:R.ATTRIBUTE_1)||"",reqType:((v=e==null?void 0:e.taskData)==null?void 0:v.ATTRIBUTE_2)??"Create with Upload",priority:A+1,scheduledBy:((b=e.userData)==null?void 0:b.emailId)??"",scheduledOn:g??"",totalObjects:(V==null?void 0:V.totalElements)||0,pendingObjects:(V==null?void 0:V.totalElements)||0,retryCount:0,module:"Material",objectSuccessCount:0,objectFailureCount:0,updatedOn:g??"",schedulerStatus:"Scheduler - Pending"};const Q=[...B,q].sort((G,Pe)=>G.module!==Pe.module?G.module.localeCompare(Pe.module):G.reqType!==Pe.reqType?G.reqType.localeCompare(Pe.reqType):G.priority-Pe.priority);o(Q)}else e.setDialogTitle("Error"),e.setSuccessMsg(!1),e.setMessageDialogMessage("Failed Fetching Scheduled Requests. Try Once more."),e.setMessageDialogSeverity("danger"),e.handleMessageDialogClickOpen()},m=P=>{f(P)};ze(`/${xh}${(O=ye.DATA)==null?void 0:O.FETCH_SCHEDULERS_IN_REQ_BENCH}`,"post",L,m,N)},$=()=>{i(!1),le(!1)},u=g=>{o(g)},k=g=>{var q;const N=e.requestType===I.CREATE_WITH_UPLOAD?`/${Re}${ye.MASS_ACTION.CREATE_MAT_APPROVED}`:e.requestType===I.EXTEND_WITH_UPLOAD?`/${Re}${ye.MASS_ACTION.EXTEND_MAT_APPROVED}`:`/${Re}${ye.MASS_ACTION.CHANGE_MAT_APPROVED}`;$(),e.setBlurLoading(!0);let L=(e==null?void 0:e.requestType)===((q=I)==null?void 0:q.CHANGE_WITH_UPLOAD)?Ie(!0):e.createPayloadFromReduxState(c);const m=L==null?void 0:L.map(U=>({...U,Tochildrequestheaderdata:{...U==null?void 0:U.Tochildrequestheaderdata,IsScheduled:!0}}));ze(N,"post",U=>{e.setBlurLoading(!1),U.statusCode===200||U.statusCode===201?(e.setMessageDialogSeverity("success"),e.setSuccessMsg(!0),p()):(e.setDialogTitle("Error"),e.setSuccessMsg(!1),e.setTextInput(!1),e.setMessageDialogMessage((U==null?void 0:U.message)??"Failed Submitting Request"),e.setMessageDialogSeverity("danger"),e.setBlurLoading(!1),e.handleMessageDialogClickOpen())},U=>{console.log("error")},m)};var F=n==null?void 0:n.map((g,N)=>({SchedulerId:(g==null?void 0:g.schedulerId)??"",RequestId:g==null?void 0:g.reqId,RequestType:g==null?void 0:g.reqType,Module:g==null?void 0:g.module,Priority:g==null?void 0:g.priority,ObjectCount:g==null?void 0:g.totalObjects,ObjectsSuccessCount:g==null?void 0:g.objectSuccessCount,ObjectsFailureCount:g==null?void 0:g.objectFailureCount,PendingObjectsCount:g==null?void 0:g.pendingObjects,ReqScheduledBy:g==null?void 0:g.scheduledBy,ReqScheduledOn:g==null?void 0:g.scheduledOn,ReqUpdatedOn:g==null?void 0:g.updatedOn,SchedulerStatus:g==null?void 0:g.schedulerStatus,RetryCount:g==null?void 0:g.retryCount}));const p=()=>{var L,m;const g=O=>{O.statusCode===200?(e.setMessageDialogSeverity("success"),e.setMessageDialogMessage("Request has been submitted and scheduled for Syndication."),e.setBlurLoading(!1),e.setSuccessMsg(!0),e.handleSnackBarOpen(),setTimeout(()=>{X("/masterDataCockpit/materialMaster/materialSingle")},3e3)):(O==null?void 0:O.statusCode)===400?(e.setDialogTitle("Info"),e.setSuccessMsg(!1),e.setMessageDialogMessage(O==null?void 0:O.message),e.setTextInput(!1),e.setMessageDialogSeverity("info"),e.setMessageDialogOK(!1),e.setBlurLoading(!1),e.handleErrorDialogClickOpen(),setTimeout(()=>{X("/requestBench")},3e3)):(e.setDialogTitle("Error"),e.setSuccessMsg(!1),e.setMessageDialogMessage("Failed Scheduling Request, You will be redirected to Request Bench for Scheduling it."),e.setTextInput(!1),e.setMessageDialogSeverity("danger"),e.setMessageDialogOK(!1),e.setBlurLoading(!1),e.handleErrorDialogClickOpen(),setTimeout(()=>{X("/requestBench")},3e3))},N=O=>{console.log(O)};ze(`/${Re}${(m=(L=ye)==null?void 0:L.DATA)==null?void 0:m.UPDATE_PRIORITY}`,"post",g,N,F)},T=ln(({className:g,...N})=>a(Xt,{...N,classes:{popper:g}}))({[`& .${hd.tooltip}`]:{maxWidth:"none"}});return te(on,{open:h,onClose:$,fullWidth:!0,maxWidth:"xl",PaperProps:{sx:{borderRadius:3,boxShadow:8,backgroundColor:"#ffffff"}},children:[te(Tt,{sx:{px:3,py:2,borderBottom:"1px solid #e0e0e0",display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"#F4F6FA",borderTopLeftRadius:12,borderTopRightRadius:12},children:[a(wt,{variant:"h6",fontWeight:600,color:"text.primary",children:"List of Requests Scheduled"}),a(T,{arrow:!0,title:a(wt,{fontSize:12,children:"Here you can prioritize your requests"}),children:a(ds,{size:"small",children:a(Dh,{fontSize:"small"})})})]}),a(Uo,{sx:{px:3,py:2},children:a(Tt,{sx:{border:"1px solid #e0e0e0",borderRadius:2,overflow:"hidden"},children:a(Ph,{columns:H,row:n,onRowUpdate:u,selectionType:"SAPScheduler",showDragIcon:!0})})}),te(Ho,{sx:{px:3,py:2,backgroundColor:"#FAFAFA",borderTop:"1px solid #e0e0e0",borderBottomLeftRadius:12,borderBottomRightRadius:12},children:[a(St,{onClick:$,variant:"outlined",color:"primary",sx:{textTransform:"capitalize",minWidth:100},children:"Cancel"}),a(St,{onClick:()=>k(e.currentButtonState),variant:"contained",color:"primary",sx:{textTransform:"capitalize",minWidth:100},children:"Submit"})]})]})}),Ji=e=>{var Nt,Us,es,Hs,Os,fs,Eo,cs,st,ts,ho,fo,Co,mo,Ks,Rt,Ao,gs,bo,wo,Io,Ro,_o;const t=re(Z=>Z.payload),n=re(Z=>Z.payload.dynamicKeyValues),o=re(Z=>Z.payload.changeFieldRows),c=re(Z=>Z.payload.selectedRows),s=(Nt=t==null?void 0:t.payloadData)==null?void 0:Nt.RequestType,[C,i]=E.useState(!1),[h,le]=E.useState("success"),[Ie,K]=E.useState(!1),[f,X]=E.useState(""),[V,H]=E.useState(""),[ce,J]=E.useState(!1),[$,u]=E.useState(""),[k,F]=E.useState(!1),[p,T]=E.useState(""),[S,g]=E.useState(""),[N,L]=E.useState(!1),[m,O]=E.useState([]),[P,q]=E.useState([]),[U,R]=E.useState(!1),[v,b]=E.useState(!1),{t:ue}=Qr(),[B,Ne]=E.useState(!1),[A,Ae]=E.useState(!1),[Q,G]=E.useState(""),[Pe,$e]=E.useState(!1),[Be,bs]=E.useState(!1),[us,Fs]=E.useState(""),[Ws,ws]=E.useState(""),[Qs,Is]=E.useState(!1),[js,Zs]=E.useState({}),[os,Ht]=E.useState(""),[Ft,Pt]=E.useState("");let ns=re(Z=>Z.userManagement.userData),Ye=re(Z=>Z.userManagement.taskData);const yt=Jr(),Et=po(),_s=rn(),Vt=_s.state,be=re(Z=>Z.payload.payloadData),kt=re(Z=>Z.payload.requestorPayload),rs=re(Z=>Z.tabsData.changeFieldsDT),ls=(be==null?void 0:be.TemplateName)||"",{changePayloadForTemplate:$t}=Dd(ls),is=new URLSearchParams(_s.search.split("?")[1]),ot=is.get("RequestId"),Ct=is.get("reqBench"),as=!(Ye!=null&&Ye.taskId)&&!Ct,[xs,Wt]=E.useState(!1),{customError:uo}=Qo(),{createFCPayload:no}=Od(),{createPayloadFromReduxState:Le}=Cg({initialReqScreen:as,isReqBench:Ct,remarks:p,userInput:Q,selectedLevel:S}),[mt,hs]=E.useState(!1),[Ds,zs]=E.useState(!1),[Ns,Ss]=E.useState(!1),[Jt,Ms]=E.useState(""),Ys=((Us=t==null?void 0:t.payloadData)==null?void 0:Us.RequestType)===I.CREATE_WITH_UPLOAD||((es=t==null?void 0:t.payloadData)==null?void 0:es.RequestType)===I.EXTEND_WITH_UPLOAD||((Hs=t==null?void 0:t.payloadData)==null?void 0:Hs.RequestType)===I.CHANGE_WITH_UPLOAD,{showSnackbar:nt}=qh(),Qt=re(Z=>Z.request.tabValue),ke=200,dt=E.useRef(),Zt=()=>{K(!0)},z=()=>{K(!1)},Se=()=>{b(!0)},ne=()=>{b(!1)},ge=()=>{var Z;Ft===Bs.SAVE?(ne(),It()):Ft===((Z=Bs)==null?void 0:Z.VALIDATE)&&(ne(),qs())},xe=()=>{F(!0)},pe=()=>{T(""),F(!1)},Ke=(Z,Ge)=>{const Fe=Z.target.value;if(hs(Fe.length>=ke),Fe.length>0&&Fe[0]===" ")T(Fe.trimStart()),Et(Ul({keyName:"Comments",data:Fe.trimStart()}));else{let Ve=Fe;T(Ve),Et(Ul({keyName:"Comments",data:Ve}))}},_e=Z=>{g(Z.target.value),Et(Ul({keyName:"Level",data:Z.target.value}))},It=()=>{var fe,Qe,ht,it,_t,Bt,We,at,Ee;pe(),J(!0);var Z;((fe=t==null?void 0:t.payloadData)==null?void 0:fe.RequestType)===I.CREATE||((Qe=t==null?void 0:t.payloadData)==null?void 0:Qe.RequestType)===I.CREATE_WITH_UPLOAD?Ft===Bs.SAVE?Z=`/${Re}/massAction/createMaterialSaveAsDraft`:Z=(ns==null?void 0:ns.role)==="Approver"?`/${Re}/massAction/createBasicMaterialsApproved`:`/${Re}/massAction/createMaterialSubmitForReview`:((ht=t==null?void 0:t.payloadData)==null?void 0:ht.RequestType)===I.EXTEND_WITH_UPLOAD?Ft===Bs.SAVE?Z=`/${Re}${ye.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`:Z=`/${Re}${ye.MASS_ACTION.EXTEND_MATERIAL_DIRECT_APPROVED}`:(s===I.CHANGE||s===I.CHANGE_WITH_UPLOAD)&&(Ft===Bs.SAVE?Z=`/${Re}/massAction/changeMaterialSaveAsDraft`:Z=(ns==null?void 0:ns.role)==="Approver"?`/${Re}/massAction/changeBasicMaterialsApproved`:`/${Re}/massAction/changeMaterialSubmitForReview`);const Ge=je=>{if(je.statusCode>=zt.STATUS_200&&je.statusCode<zt.STATUS_300){J(!1);let Lt;(ns==null?void 0:ns.role)==="Approver"?Lt=`Material Syndicated successfully in SAP with Material ID : ${je==null?void 0:je.body.join(", ")}`:Ft===Bs.SAVE?Lt=je==null?void 0:je.message:Lt=`Request Submitted for Approval with Request ID ${je==null?void 0:je.body}`,nt(Lt,"success"),Zt(),yt("/masterDataCockpit/materialMaster/materialSingle")}else J(!1),nt(je==null?void 0:je.message,"error");Pt("")},Fe=je=>{nt(je==null?void 0:je.message,"error"),J(!1),Pt("")};var Ve;Ve=((it=t==null?void 0:t.payloadData)==null?void 0:it.RequestType)===I.CREATE||((_t=t==null?void 0:t.payloadData)==null?void 0:_t.RequestType)===I.CREATE_WITH_UPLOAD||((Bt=t==null?void 0:t.payloadData)==null?void 0:Bt.RequestType)===I.EXTEND_WITH_UPLOAD?Le(t):((We=t==null?void 0:t.payloadData)==null?void 0:We.RequestType)===I.CHANGE?$t(!!Ct):((at=t==null?void 0:t.payloadData)==null?void 0:at.RequestType)===I.CHANGE_WITH_UPLOAD?$t(!0):((Ee=t==null?void 0:t.payloadData)==null?void 0:Ee.RequestType)===I.CHANGE?Le(t):[],ze(Z,"post",Ge,Fe,Ve)},Gt=async Z=>{var Ge,Fe,Ve,fe,Qe;if(((Z==null?void 0:Z.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate"||(Z==null?void 0:Z.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate1")&&(((Ge=t==null?void 0:t.payloadData)==null?void 0:Ge.RequestType)===I.CREATE||((Fe=t==null?void 0:t.payloadData)==null?void 0:Fe.RequestType)===I.CREATE_WITH_UPLOAD))try{const ht=await e.validateMaterials();Wt(ht)}catch(ht){uo(ht);return}H(""),bs("success"),Zs(Z),ws(Z.MDG_MAT_DYN_BTN_COMMENT_BOX_NAME),Ae(Z.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Ve=sn)==null?void 0:Ve.MANDATORY)),$e(Z.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((fe=sn)==null?void 0:fe.MANDATORY)||Z.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"),Ht(Z.MDG_MAT_DYN_BTN_ACTION_TYPE),Z.MDG_MAT_DYN_BTN_BUTTON_NAME===Bs.SEND_BACK||Z.MDG_MAT_DYN_BTN_BUTTON_NAME===Bs.CORRECTION?zs(!0):zs(!1),Z.MDG_MAT_DYN_BTN_BUTTON_NAME===Bs.SAP_SYNDICATE?Ss(!0):Ss(!1),Z.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Qe=sn)==null?void 0:Qe.MANDATORY)||Z.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"?qe():qt(Z.MDG_MAT_DYN_BTN_ACTION_TYPE,Z)},ut=()=>{pe(),J(!0);const Z=s===I.CREATE||s===I.CREATE_WITH_UPLOAD?`/${Re}/massAction/createMaterialApprovalSubmit`:s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?`/${Re}/massAction/extendMaterialApprovalSubmit`:`/${Re}/massAction/changeMaterialApprovalSubmit`,Ge=fe=>{fe.statusCode>=200&&fe.statusCode<300?(J(!1),nt(`Request Submitted for Approval with Request ID ${fe==null?void 0:fe.body}`,"success"),yt("/masterDataCockpit/materialMaster/materialSingle")):(J(!1),nt(fe==null?void 0:fe.message,"error"))},Fe=()=>{J(!1),nt("Failed Submitting Request.","error")};var Ve;Ve=s===I.CREATE||s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD||s===I.CREATE_WITH_UPLOAD?Le(t):$t(!0),ze(Z,"post",Ge,Fe,Ve)},pt=Z=>{var ht;pe(),J(!0);var Ge=s===I.CREATE||s===I.CREATE_WITH_UPLOAD?`/${Re}/massAction/createMaterialApproved`:s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?`/${Re}/massAction/extendMaterialApproved`:Ye.ATTRIBUTE_2===I.FINANCE_COSTING?`/${Re}/${ye.MASS_ACTION.FINANCE_COSTING_APPROVED}`:`/${Re}/massAction/changeMaterialApproved`;const Fe=it=>{it.statusCode>=200&&it.statusCode<300?(J(!1),nt(Z==null?void 0:Z.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG,"success"),yt("/masterDataCockpit/materialMaster/materialSingle")):(J(!1),nt(Z==null?void 0:Z.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"))},Ve=it=>{nt((it==null?void 0:it.message)||"Failed Submitting Request.","error"),J(!1)};var fe;const Qe={requestId:(ht=t==null?void 0:t.payloadData)==null?void 0:ht.RequestId,taskId:(Ye==null?void 0:Ye.taskId)||"",taskName:(Ye==null?void 0:Ye.taskDesc)||"",comments:p||Q,creationDate:Ye!=null&&Ye.createdOn?Ot(Ye==null?void 0:Ye.createdOn):null,dueDate:Ye!=null&&Ye.criticalDeadline?Ot(Ye==null?void 0:Ye.criticalDeadline):null};fe=s===I.CREATE||s===I.CREATE_WITH_UPLOAD||s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?Le(t):$t(!0),ze(Ge,"post",Fe,Ve,Ye.ATTRIBUTE_2===I.FINANCE_COSTING?Qe:fe)},Ut=()=>{pe(),J(!0);const Z=s===I.CREATE||s===I.CREATE_WITH_UPLOAD?`/${Re}${ye.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?`/${Re}${ye.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${Re}${ye.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,Ge=s===I.CREATE||s===I.CREATE_WITH_UPLOAD||s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?Le(t):$t(!0);ze(Z,"post",fe=>{(fe==null?void 0:fe.statusCode)===zt.STATUS_200?(nt(fe.message,"success"),Et(Ti({data:{}})),yt(so.MY_TASK)):nt(fe.error,"error"),J(!1)},fe=>{nt(fe.error,"error"),J(!1)},Ge)},Kt=()=>{pe(),J(!0);const Z=s===I.CREATE||s===I.CREATE_WITH_UPLOAD?`/${Re}${ye.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?`/${Re}${ye.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${Re}${ye.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,Ge=s===I.CREATE||s===I.CREATE_WITH_UPLOAD||s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?Le(t):$t(!0);ze(Z,"post",fe=>{(fe==null?void 0:fe.statusCode)===zt.STATUS_200?(nt(fe.message,"success"),Et(Ti({data:{}})),yt(so.MY_TASK)):nt(fe.error,"error"),J(!1)},fe=>{nt(fe.error,"error"),J(!1)},Ge)},Ps=()=>{pe(),J(!0);const Z=s===I.CREATE||s===I.CREATE_WITH_UPLOAD?`/${Re}${ye.MASS_ACTION.CREATE_MATERIAL_REJECTION}`:s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?`/${Re}${ye.MASS_ACTION.EXTEND_MATERIAL_REJECTION}`:`/${Re}${ye.MASS_ACTION.CHANGE_MATERIAL_REJECTION}`,Ge=s===I.CREATE||s===I.CREATE_WITH_UPLOAD||s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?Le(t):$t(!0);ze(Z,"post",fe=>{(fe==null?void 0:fe.statusCode)===zt.STATUS_200?(nt(fe.message,"success"),Et(Ti({data:{}})),yt(so.MY_TASK)):nt(fe.error,"error"),J(!1)},fe=>{nt(fe.error,"error"),J(!1)},Ge)},qs=Z=>{J(!0);const Ge=(Ye==null?void 0:Ye.ATTRIBUTE_2)===I.FINANCE_COSTING?`/${Re}${ye.MASS_ACTION.VALIDATE_FINANCE_COSTING}?requestId=${ot==null?void 0:ot.slice(3)}`:`/${Re}${ye.MASS_ACTION.VALIDATE_MATERIAL}`,Fe=(Ye==null?void 0:Ye.ATTRIBUTE_2)===I.FINANCE_COSTING?no():s===I.CREATE||s===I.CREATE_WITH_UPLOAD||s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?Le(t):s===I.CHANGE||s===I.CHANGE_WITH_UPLOAD?Ct&&ot?$t(!0):!Ct&&!ot?$t(!1):!Ct&&ot?$t(!0):[]:[];ze(Ge,"post",Qe=>{if(J(!1),(Qe==null?void 0:Qe.statusCode)===zt.STATUS_200){if(nt((Z==null?void 0:Z.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG)||(Qe==null?void 0:Qe.message),"success"),(as||Ct)&&(s===I.CHANGE||s===I.CHANGE_WITH_UPLOAD)||s===I.CREATE_WITH_UPLOAD||s===I.EXTEND_WITH_UPLOAD||s===I.EXTEND){yt(so.REQUEST_BENCH);return}yt(so.MY_TASK)}else nt((Z==null?void 0:Z.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG)||"Validation failed.","error")},()=>{nt(Z==null?void 0:Z.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"),J(!1)},Fe)},eo=()=>{pe(),J(!0);const Z=s===I.CREATE||s===I.CREATE_WITH_UPLOAD?`/${Re}/massAction/createMaterialApprovalSubmit`:s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?`/${Re}/massAction/extendMaterialSubmitForReview`:`/${Re}/massAction/changeMaterialApprovalSubmit`,Ge=fe=>{fe.statusCode===zt.STATUS_200?(J(!1),nt(`Request Submitted for Approval with Request ID ${fe==null?void 0:fe.body}`,"success"),Zt(),yt("/masterDataCockpit/materialMaster/materialSingle")):nt(fe==null?void 0:fe.message,"error")},Fe=fe=>{nt((fe==null?void 0:fe.error)||"Failed Submitting Request.","error"),J(!1)};var Ve;Ve=s===I.CREATE||s===I.CREATE_WITH_UPLOAD||s===I.EXTEND||s===I.EXTEND_WITH_UPLOAD?Le(t):$t(!0),ze(Z,"post",Ge,Fe,Ve),i(!0),Zt()},lt=()=>{pe(),J(!0);const Z=`/${Re}${ye.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`,Ge=fe=>{fe.statusCode===zt.STATUS_200?(J(!1),nt(fe==null?void 0:fe.message,"success"),yt(so.REQUEST_BENCH)):(J(!1),nt(fe==null?void 0:fe.message,"error"))},Fe=fe=>{nt(fe==null?void 0:fe.error,"error"),J(!1)};let Ve;Ve=Le(t),ze(Z,"post",Ge,Fe,Ve)},qt=(Z,Ge)=>{switch(Z){case"handleSubmitForApproval":ut();break;case"handleSubmitForReview":eo();break;case"handleSendBack":Ut();break;case"handleCorrection":Kt();break;case"handleReject":Ps();break;case"Validate":ee(Ge);break;case"handleValidate":ee(Ge);break;case"handleSAPSyndication":pt(Ge);break;case"handleDraft":lt();break;case"handleSubmit":eo();break;default:console.log("Unknown action type")}},ee=Z=>{var Ge,Fe;Pt((Ge=Bs)==null?void 0:Ge.VALIDATE),X(ue((Fe=tn)==null?void 0:Fe.VALIDATE_MSG)),s===I.CREATE||s===I.EXTEND||s===I.CREATE_WITH_UPLOAD||s===I.EXTEND_WITH_UPLOAD||(Ye==null?void 0:Ye.ATTRIBUTE_2)===I.FINANCE_COSTING?qs(Z):we(Z)},we=Z=>{Array.isArray(o)?oe(Z):typeof o=="object"&&Xe(Z)},M=()=>{const Z=rs==null?void 0:rs["Config Data"],Ge={};return Object.entries(Z).forEach(([Fe,Ve])=>{const fe=Ve.filter(Qe=>{var ht;return Qe.visibility===((ht=sn)==null?void 0:ht.MANDATORY)}).map(Qe=>({jsonName:Qe.jsonName,fieldName:Qe.fieldName}));if(!(fe!=null&&fe.some(Qe=>Qe.jsonName==="Material"))){const Qe=Ve.find(ht=>ht.jsonName==="Material");Qe&&fe.push({jsonName:Qe.jsonName,fieldName:Qe.fieldName})}(fe==null?void 0:fe.length)>0&&(Ge[Fe]=fe)}),Ge},Me=(Z,Ge)=>{var Fe,Ve;if(Array.isArray(o)){const fe=ls===((Fe=ve)==null?void 0:Fe.LOGISTIC)?[...Ge,{jsonName:"AltUnit",fieldName:"Alternative Unit of Measure"}]:ls===((Ve=ve)==null?void 0:Ve.UPD_DESC)?[...Ge,{jsonName:"Langu",fieldName:"Language"}]:Ge,Qe={};return Z==null||Z.forEach((ht,it)=>{var Bt;const _t=(Bt=fe==null?void 0:fe.filter(We=>!ht[We==null?void 0:We.jsonName]||ht[We==null?void 0:We.jsonName]===""))==null?void 0:Bt.map(We=>We==null?void 0:We.fieldName);(_t==null?void 0:_t.length)>0&&(Qe[it]={id:ht.id,slNo:ht.slNo,missingFields:_t})}),Qe}else if(typeof o=="object"){let fe={},Qe=0;return Object.keys(Z).forEach(ht=>{Z[ht].forEach(it=>{var Bt;const _t=(Bt=Ge[ht])==null?void 0:Bt.filter(We=>!it[We.jsonName]||it[We.jsonName]==="").map(We=>We.fieldName);_t.length>0&&(fe[Qe]={id:it.id,slNo:it.slNo,type:it.type,missingFields:_t},Qe++)})}),fe}},Xe=Z=>{var fe,Qe,ht,it;const Ge=Object.fromEntries(Object.entries(o).map(([_t,Bt])=>[_t,Bt.filter(We=>{var at;return(at=c==null?void 0:c[_t])==null?void 0:at.includes(We.id)})])),Fe=M(),Ve=Me(Ge,Fe);if(Et(Oi(Ve)),Object.keys(Ve).length>0){const _t=Object.keys(Ve).map(at=>{var Ee,je,Lt;return{"Table Name":(Ee=Ve[at])==null?void 0:Ee.type,"Sl. No":(je=Ve[at])==null?void 0:je.slNo,"Missing Fields":(Lt=Ve[at].missingFields)==null?void 0:Lt.join(", ")}});R(!0),bs("danger"),ws("Please Fill All the Mandatory Fields : ");const Bt=(fe=Object.keys(_t[0]))==null?void 0:fe.map(at=>({field:at,headerName:(at==null?void 0:at.charAt(0).toUpperCase())+(at==null?void 0:at.slice(1)),flex:at==="Sl. No"?.5:at==="Missing Fields"?3:1.5,align:"center",headerAlign:"center"}));q(Bt);const We=_t==null?void 0:_t.map(at=>({...at,id:Ls()}));O(We),L(!0),qe(),Et(Yr(!0))}else{if(s===((Qe=I)==null?void 0:Qe.CHANGE)||s===((ht=I)==null?void 0:ht.CHANGE_WITH_UPLOAD)){if(!ot||ot&&kt&&((it=Object==null?void 0:Object.keys(kt))!=null&&it.length)){Se();return}qs(Z);return}le("success"),H("Data Validated Successfully"),Zt(),Et(Yr(!1)),e==null||e.setCompleted([!0,!0])}},oe=Z=>{var Ve,fe,Qe,ht;const Ge=o==null?void 0:o.filter(it=>c==null?void 0:c.includes(it.id)),Fe=Me(Ge,rs==null?void 0:rs["Mandatory Fields"]);if(Et(Oi(Fe)),Object.keys(Fe).length>0){const it=Object.keys(Fe).map(We=>{var at,Ee;return{"Sl. No":(at=Fe[We])==null?void 0:at.slNo,"Missing Fields":(Ee=Fe[We].missingFields)==null?void 0:Ee.join(", ")}});R(!0),bs("danger"),ws("Please Fill All the Mandatory Fields : ");const _t=(Ve=Object.keys(it[0]))==null?void 0:Ve.map(We=>({field:We,headerName:(We==null?void 0:We.charAt(0).toUpperCase())+(We==null?void 0:We.slice(1)),flex:We==="Sl. No"?.5:We==="Missing Fields"?3:1,align:"center",headerAlign:"center"}));q(_t);const Bt=it==null?void 0:it.map(We=>({...We,id:Ls()}));O(Bt),L(!0),qe(),Et(Yr(!0))}else{if(s===((fe=I)==null?void 0:fe.CHANGE)||s===((Qe=I)==null?void 0:Qe.CHANGE_WITH_UPLOAD)){if(!ot||ot&&kt&&((ht=Object==null?void 0:Object.keys(kt))!=null&&ht.length)){Se();return}qs(Z);return}le("success"),H("Data Validated Successfully"),Zt(),Et(Yr(!1)),e==null||e.setCompleted([!0,!0])}},et=()=>{var Z;if(A&&!Q){Ne(!0);return}else Jt==="scheduleSyndication"?dt!=null&&dt.current&&((Z=dt==null?void 0:dt.current)==null||Z.handlePriorityDialogClickOpen()):qt(os,js);Oe()},Oe=()=>{Is(!1),G(""),Ne(!1),Ae(!1)},qe=()=>{Is(!0)};function Ze(){xe(),Pt("")}const he=()=>{Ze()};return te(io,{children:[a(Wi,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:te(Uh,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:1,width:"100%"},children:[(!ot||Ct&&(Vt==null?void 0:Vt.reqStatus)===((Os=Vr)==null?void 0:Os.DRAFT))&&(be==null?void 0:be.RequestType)===I.CHANGE&&(be==null?void 0:be.TemplateName)===((fs=ve)==null?void 0:fs.SET_DNU)&&a(Tt,{sx:{flex:2,marginLeft:"90px"},children:te("span",{children:[a("strong",{children:"Note"}),": All default values for ",a("strong",{children:"Set To DNU"})," template will be fetched after ",a("strong",{children:"Validation"}),"."]})}),te(Tt,{sx:{display:"flex",gap:1},children:[s!==I.EXTEND_WITH_UPLOAD&&s!==I.EXTEND&&a($s,{children:!ot||Ct&&((Eo=Xo)!=null&&Eo.includes(Vt==null?void 0:Vt.reqStatus))?te($s,{children:[a(St,{variant:"contained",color:"primary",onClick:()=>{var Z,Ge;if(Pt(Bs.SAVE),X(ue((Z=tn)==null?void 0:Z.SAVE_AS_DRAFT_MSG)),(be==null?void 0:be.RequestType)===I.CHANGE&&(!ot||ot&&kt&&((Ge=Object==null?void 0:Object.keys(kt))!=null&&Ge.length))){Se();return}xe()},children:ue("Save As Draft")}),((cs=t==null?void 0:t.payloadData)==null?void 0:cs.RequestType)===I.CREATE&&Qt===Hl.REQUEST_DETAILS&&a(Xt,{title:Rd.VALIDATE_MANDATORY,children:a(St,{variant:"contained",color:"primary",onClick:e==null?void 0:e.validateMaterials,children:ue("Validate")})}),Qt===Hl.REQUEST_DETAILS&&((be==null?void 0:be.RequestType)===I.CHANGE||(be==null?void 0:be.RequestType)===I.CHANGE_WITH_UPLOAD||(be==null?void 0:be.RequestType)===I.CREATE_WITH_UPLOAD||(be==null?void 0:be.RequestType)===I.EXTEND_WITH_UPLOAD)?a($s,{children:a(St,{variant:"contained",color:"primary",onClick:ee,children:ue("Validate")})}):a($s,{children:Qt===Hl.PREVIEW&&a(St,{variant:"contained",color:"primary",onClick:he,disabled:(be==null?void 0:be.RequestType)===((st=I)==null?void 0:st.CHANGE)||(be==null?void 0:be.RequestType)===((ts=I)==null?void 0:ts.CHANGE_WITH_UPLOAD)||(be==null?void 0:be.RequestType)===I.CREATE_WITH_UPLOAD||(be==null?void 0:be.RequestType)===I.EXTEND_WITH_UPLOAD?(be==null?void 0:be.RequestStatus)!==((ho=Vr)==null?void 0:ho.VALIDATED_REQUESTOR):e==null?void 0:e.submitForApprovalDisabled,children:ue("Submit")})})]}):null}),((be==null?void 0:be.RequestType)===I.EXTEND||(be==null?void 0:be.RequestType)===I.EXTEND_WITH_UPLOAD&&(!ot||Ct&&((fo=Xo)==null?void 0:fo.includes(Vt==null?void 0:Vt.reqStatus))||!Ct&&ot)||!Ct&&ot)&&((Co=e==null?void 0:e.filteredButtons)==null?void 0:Co.map((Z,Ge)=>{var _t,Bt,We,at,Ee,je,Lt,zo,In,Rn,an;const{MDG_MAT_DYN_BTN_BUTTON_NAME:Fe,MDG_MAT_DYN_BTN_BUTTON_STATUS:Ve}=Z,fe=Fe==="SAP Syndication"||Fe==="Submit",Qe=Fe==="Forward"||Fe==="Submit",ht=((_t=n==null?void 0:n.requestHeaderData)==null?void 0:_t.RequestStatus)==="Validated-MDM"||(be==null?void 0:be.RequestStatus)==="Validated-MDM"||(be==null?void 0:be.RequestStatus)==="Validated-Requestor"||((Bt=n==null?void 0:n.childRequestHeaderData)==null?void 0:Bt.RequestStatus)==="Validated-MDM"||((We=n==null?void 0:n.childRequestHeaderData)==null?void 0:We.RequestStatus)==="Validated-Requestor"||((at=e==null?void 0:e.childRequestHeaderData)==null?void 0:at.RequestStatus)==="Validated-MDM"||((Ee=e==null?void 0:e.childRequestHeaderData)==null?void 0:Ee.RequestStatus)==="Validated-Requestor";let it=Ve==="DISABLED";return fe&&ht&&(it=!1),(Qe&&((be==null?void 0:be.RequestType)===((je=I)==null?void 0:je.CREATE)||(be==null?void 0:be.RequestType)===((Lt=I)==null?void 0:Lt.CREATE_WITH_UPLOAD))&&!(e!=null&&e.submitForApprovalDisabled)||((be==null?void 0:be.RequestType)===((zo=I)==null?void 0:zo.CHANGE)||(be==null?void 0:be.RequestType)===((In=I)==null?void 0:In.CHANGE_WITH_UPLOAD)||(be==null?void 0:be.RequestType)===((Rn=I)==null?void 0:Rn.EXTEND)||(be==null?void 0:be.RequestType)===((an=I)==null?void 0:an.EXTEND_WITH_UPLOAD))&&Qe&&ht)&&(it=!1),a(St,{variant:"contained",size:"small",sx:{...Fi,mr:1},disabled:it||ce,onClick:()=>Gt(Z),children:Z.MDG_MAT_DYN_BTN_BUTTON_NAME},Ge)}))]})]})}),a(_d,{dialogState:Qs,openReusableDialog:qe,closeReusableDialog:Oe,dialogTitle:Ws,dialogMessage:us,handleDialogConfirm:et,dialogOkText:"OK",dialogSeverity:Be,showCancelButton:!0,showInputText:Pe,inputText:Q,blurLoading:ce,setInputText:G,mandatoryTextInput:A,remarksError:B,isTable:N,tableColumns:P,tableRows:m,isShowWFLevel:(e==null?void 0:e.showWfLevels)&&Ds,isSyndicationBtn:Ns,selectedLevel:S,handleLevelChange:_e,workFlowLevels:e.workFlowLevels,setSyndicationType:Ms,syndicationType:Jt,isMassSyndication:Ys}),a(mg,{ref:dt,dialogTitle:Ws,setDialogTitle:ws,messageDialogMessage:V,setMessageDialogMessage:H,messageDialogSeverity:Be,setMessageDialogSeverity:bs,handleMessageDialogClickOpen:qe,blurLoading:ce,setBlurLoading:J,handleMessageDialogClose:Oe,createPayloadFromReduxState:Le,successMsg:C,setSuccessMsg:i,setTextInput:$e,inputText:Pe,handleSnackBarOpen:Zt,taskData:Ye,userData:ns,currentButtonState:js,requestType:s}),a(Eg,{open:v,onClose:ne,handleOk:ge,message:f}),V&&a(Vn,{openSnackBar:Ie,alertMsg:V,alertType:h,handleSnackBarClose:z}),a(Yn,{blurLoading:ce,loaderMessage:$}),te(on,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:k,onClose:pe,maxWidth:"xl",children:[te(On,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[a(wt,{variant:"h6",children:Ft===Bs.SAVE?"Save As Draft":"Remarks"}),a(ds,{sx:{width:"max-content"},onClick:pe,children:a(Nd,{})})]}),a(Uo,{sx:{padding:".5rem 1rem"},children:Ft!==Bs.SAVE?a(io,{sx:{marginTop:"16px"},children:a(Tt,{sx:{minWidth:400},children:te($i,{sx:{height:"auto"},fullWidth:!0,children:[a(En,{sx:{backgroundColor:"#F5F5F5","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:mt?(Ks=(mo=De)==null?void 0:mo.error)==null?void 0:Ks.dark:"rgba(0, 0, 0, 0.23)"},"&:hover fieldset":{borderColor:mt?(Ao=(Rt=De)==null?void 0:Rt.error)==null?void 0:Ao.dark:"rgba(0, 0, 0, 0.23)"},"&.Mui-focused fieldset":{borderColor:mt?(bo=(gs=De)==null?void 0:gs.error)==null?void 0:bo.dark:(Io=(wo=De)==null?void 0:wo.primary)==null?void 0:Io.dark}}},value:p,onChange:Ke,inputProps:{maxLength:ke},multiline:!0,placeholder:"Enter Remarks"}),a(Hh,{sx:{textAlign:"right",color:mt?(_o=(Ro=De)==null?void 0:Ro.error)==null?void 0:_o.dark:"rgba(0, 0, 0, 0.6)",marginTop:"4px"},children:`${(p==null?void 0:p.length)||0}/${ke}`})]})})}):a(Tt,{sx:{margin:"15px"},children:a(wt,{sx:{fontWeight:"200"},children:tn.DRAFT_MESSAGE})})}),te(Ho,{sx:{display:"flex",justifyContent:"end"},children:[a(St,{sx:{width:"max-content",textTransform:"capitalize"},onClick:pe,children:ue("Cancel")}),a(St,{className:"button_primary--normal",type:"save",disabled:ce,onClick:It,variant:"contained",children:Ft===Bs.SAVE?"Yes":"Submit"})]})]})]})},Ag=()=>{const{customError:e}=Qo(),[t,n]=E.useState([]),[o,c]=E.useState(!1),s=re(X=>X.userManagement.taskData),[C,i]=E.useState([]),h=re(X=>X.applicationConfig),le=po();let Ie={handleSubmitForApproval:6,handleSendBack:1,handleReject:3,handleValidate:5,handleSAPSyndication:8,handleIdGenerator:4,handleSubmitForReview:7,handleCorrection:2};const K=oi(ni.CURRENT_TASK,!0,{});return E.useEffect(()=>{const X=(s==null?void 0:s.taskDesc)||(K==null?void 0:K.taskDesc),V=t==null?void 0:t.filter(ce=>ce.MDG_MAT_DYN_BTN_TASK_NAME===X),H=V==null?void 0:V.sort((ce,J)=>{const $=Ie[ce.MDG_MAT_DYN_BTN_ACTION_TYPE],u=Ie[J.MDG_MAT_DYN_BTN_ACTION_TYPE];return $-u});i(H),le(kh(H)),(H.find(ce=>ce.MDG_MAT_DYN_BTN_BUTTON_NAME===Bs.SEND_BACK)||H.find(ce=>ce.MDG_MAT_DYN_BTN_BUTTON_NAME===Bs.CORRECTION))&&c(!0)},[t]),{getButtonsDisplay:()=>{let X={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":(s==null?void 0:s.ATTRIBUTE_2)||(K==null?void 0:K.ATTRIBUTE_2)}],systemFilters:null,systemOrders:null,filterString:null};const V=ce=>{var J,$;if(ce.statusCode===200){let u=($=(J=ce==null?void 0:ce.data)==null?void 0:J.result[0])==null?void 0:$.MDG_MAT_DYN_BUTTON_CONFIG;n(u)}},H=ce=>{e(ce)};h.environment==="localhost"?ze(`/${qo}${ye.INVOKE_RULES.LOCAL}`,"post",V,H,X):ze(`/${qo}${ye.INVOKE_RULES.PROD}`,"post",V,H,X)},showWfLevels:o}},qd=()=>{re(c=>c.payload.payloadData);const e=re(c=>c.applicationConfig);re(c=>{var s;return(s=c.userManagement)==null?void 0:s.taskData}),po();const t=rn();return new URLSearchParams(t.search).get("RequestType"),{getDynamicWorkflowDT:(c,s,C="NA",i="GROUP-2",h=1)=>new Promise((le,Ie)=>{let K={decisionTableId:null,decisionTableName:"MDG_MAT_DYNAMIC_WF_DT",version:"v4",conditions:[{"MDG_CONDITIONS.MDG_MAT_REQUEST_TYPE":c||"","MDG_CONDITIONS.MDG_MAT_REGION":s||"","MDG_CONDITIONS.MDG_MAT_TEMPLATE":C||"NA","MDG_CONDITIONS.MDG_MAT_BIFURCATION_GROUP":i||""}]};const f=V=>{var H,ce;if(V.statusCode===zt.STATUS_200){let J=((ce=(H=V==null?void 0:V.data)==null?void 0:H.result[0])==null?void 0:ce.MDG_MAT_DYNAMIC_WF_DT)||[],$=[];J==null||J.forEach(u=>{u.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(h)&&u.MDG_MAT_SENDBACK_ALLOWED.split(",").map(F=>parseInt(F)).forEach(F=>$.push(F))}),$=[...new Set($)],le($)}else Ie(new Error("Failed to fetch workflow levels"))},X=V=>{Ie(V)};e.environment==="localhost"?ze(`/${qo}${ye.INVOKE_RULES.LOCAL}`,"post",f,X,K):ze(`/${qo}${ye.INVOKE_RULES.PROD}`,"post",f,X,K)})}},bg=({requestType:e,initialPayload:t,dynamicData:n,taskData:o,singlePayloadData:c})=>{const[s,C]=E.useState([]),[i,h]=E.useState(!1),{getDynamicWorkflowDT:le}=qd(),{customError:Ie}=Qo();return E.useEffect(()=>{const K=async()=>{var f,X,V;try{h(!0);const H=(t==null?void 0:t.RequestType)===I.CHANGE||(t==null?void 0:t.RequestType)===I.CHANGE_WITH_UPLOAD?await le(t==null?void 0:t.RequestType,t==null?void 0:t.Region,t==null?void 0:t.TemplateName,(f=n==null?void 0:n.childRequestHeaderData)==null?void 0:f.MaterialGroupType,o==null?void 0:o.ATTRIBUTE_3):await le(t==null?void 0:t.RequestType,t==null?void 0:t.Region,"",(V=(X=c==null?void 0:c[n])==null?void 0:X.Tochildrequestheaderdata)==null?void 0:V.MaterialGroupType,o==null?void 0:o.ATTRIBUTE_3);C(H)}catch(H){Ie(H)}finally{h(!1)}};t!=null&&t.RequestType&&(t!=null&&t.Region)&&n&&(o!=null&&o.ATTRIBUTE_3)&&K()},[t==null?void 0:t.Region,n,o==null?void 0:o.ATTRIBUTE_3]),{wfLevels:s,loading:i}},wg=e=>{const[t,n]=E.useState(!0),o=re(g=>g.AllDropDown.dropDown),c=re(g=>g.payload),s=re(g=>g.payload.payloadData),C=s==null?void 0:s.RequestType,[i,h]=E.useState(!1),[le,Ie]=E.useState(!1),K=re(g=>g.userManagement.taskData),f=re(g=>g.payload.filteredButtons),X=rn(),V=new URLSearchParams(X.search),H=V.get("RequestType"),ce=V.get("RequestId"),J=re(g=>g.payload.changeFieldRows),$=re(g=>g.payload.dynamicKeyValues),u=Jr(),{getButtonsDisplay:k,showWfLevels:F}=Ag(),{wfLevels:p}=bg({initialPayloadRequestType:C,initialPayload:s,dynamicData:$,taskData:K,singlePayloadData:c}),T=ji(f,[Oo.HANDLE_SUBMIT_FOR_APPROVAL,Oo.HANDLE_SAP_SYNDICATION,Oo.HANDLE_SUBMIT_FOR_REVIEW]);E.useEffect(()=>{(K!=null&&K.ATTRIBUTE_1||H)&&k()},[K]),E.useEffect(()=>{((J==null?void 0:J.length)!==0&&(J==null?void 0:J.length)!==void 0||!S())&&(Ie(!0),h(!0))},[J]);const S=()=>{var g;return(g=Object==null?void 0:Object.values(J))==null?void 0:g.every(N=>(Array==null?void 0:Array.isArray(N))&&(N==null?void 0:N.length)===0)};return E.useEffect(()=>{e.downloadClicked&&n(!0)},[e.downloadClicked]),te("div",{children:[((s==null?void 0:s.TemplateName)&&(J&&(J==null?void 0:J.length)===0||S())||e.downloadClicked)&&a(og,{open:t,onClose:()=>{var g;n(!1),e==null||e.setDownloadClicked(!1),ce||u((g=so)==null?void 0:g.REQUEST_BENCH)},parameters:ql[s==null?void 0:s.TemplateName],templateName:s==null?void 0:s.TemplateName,setShowTable:h,allDropDownData:o,setDownloadClicked:e==null?void 0:e.setDownloadClicked}),(i||le)&&!(e!=null&&e.downloadClicked)&&te($s,{children:[a(cg,{setCompleted:e==null?void 0:e.setCompleted,RequestId:ce}),a(Ji,{filteredButtons:T,setCompleted:e==null?void 0:e.setCompleted,showWfLevels:F,workFlowLevels:p})]}),a(zi,{})]})},QE=({setIsSecondTabEnabled:e,setIsAttachmentTabEnabled:t,requestStatus:n,downloadClicked:o,setDownloadClicked:c})=>{var Ys,nt,Qt,ke,dt,Zt,z,Se;const[s,C]=E.useState({}),[i,h]=E.useState(!1),[le,Ie]=E.useState(!1),[K,f]=E.useState("success"),[X,V]=E.useState(!1),[H,ce]=E.useState([]);E.useState(!1);const[J,$]=E.useState(),[u,k]=E.useState({}),[F,p]=E.useState(!1),[T,S]=E.useState("systemGenerated"),[g,N]=E.useState(""),[L,m]=E.useState(""),[O,P]=E.useState([]),[q,U]=E.useState(!1),R=po(),v=Jr(),b=re(ne=>ne.payload.payloadData),ue=re(ne=>ne.tabsData.requestHeaderData),B=re(ne=>ne.tabsData.changeFieldsDT);let Ne=re(ne=>ne.userManagement.roles);const A=re(ne=>ne.payload.payloadData),Ae=re(ne=>ne.userManagement.userData),Q=re(ne=>{var ge,xe;return(xe=(ge=ne.userManagement)==null?void 0:ge.entitiesAndActivities)==null?void 0:xe.Material}),G=re(ne=>ne.request.requestHeader),Pe=re(ne=>ne.request.salesOrgDTData),$e=rn(),Be=new URLSearchParams($e.search),bs=Be.get("reqBench"),us=Be.get("RequestId"),{t:Fs}=Qr(),{getRequestHeaderTemplate:Ws}=Xf(),{getChangeTemplate:ws}=Bf(),{fetchOrgData:Qs}=vd(),{getDtCall:Is}=Bh(),{customError:js}=Qo(),os=[{code:"Create",desc:"Create New Material in Application"},{code:"Change",desc:"Modify Existing Material in Application"},{code:"Extend",desc:"Extend Existing Material in Application"},{code:"Create with Upload",desc:"Create New Material with Excel Upload"},{code:"Change with Upload",desc:"Modify Existing Material with Excel Upload"},{code:"Extend with Upload",desc:"Extend Existing Material with Excel Upload"}].filter(ne=>Q==null?void 0:Q.includes(ne.code)),Ht=[{code:"Oncology",desc:""},{code:"Anesthesia/Pain Management",desc:""},{code:"Cardiovascular",desc:""}],Ft=[{code:(Ys=ve)==null?void 0:Ys.LOGISTIC,desc:""},{code:(nt=ve)==null?void 0:nt.MRP,desc:""},{code:(Qt=ve)==null?void 0:Qt.WARE_VIEW_2,desc:""},{code:(ke=ve)==null?void 0:ke.ITEM_CAT,desc:""},{code:(dt=ve)==null?void 0:dt.SET_DNU,desc:""},{code:(Zt=ve)==null?void 0:Zt.UPD_DESC,desc:""},{code:(z=ve)==null?void 0:z.CHG_STAT,desc:""}],Pt=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];R(jo({keyName:(Se=$h)==null?void 0:Se.REQUEST_TYPE,data:os})),R(jo({keyName:"LeadingCat",data:Ht})),R(jo({keyName:"RequestPriority",data:Pt})),R(jo({keyName:"TemplateName",data:Ft})),!us&&!bs&&(R(qc({keyName:"ReqCreatedBy",data:Ae==null?void 0:Ae.user_id})),R(qc({keyName:"RequestStatus",data:"DRAFT"})));const ns="Basic Data",[Ye,yt]=E.useState([ns]),[Et,_s]=E.useState(""),[Vt,be]=E.useState(""),[kt,rs]=E.useState(!0);E.useEffect(()=>{R(Gh(Ye))},[R,Ye]);const ls=()=>{var ge,xe;let ne=!0;return A&&((ge=ue[Object.keys(ue)])!=null&&ge.length)?(xe=ue[Object.keys(ue)[0]])==null||xe.forEach(pe=>{var Ke;!A[pe.jsonName]&&pe.visibility===((Ke=sn)==null?void 0:Ke.MANDATORY)&&(ne=!1)}):ne=!1,ne};E.useEffect(()=>{A!=null&&A.MatlType&&$t(A),ls()},[A]);const $t=ne=>{var pe;const ge=Ke=>{_s(Ke.body[0].MaintStatus.split("")),be(Ke.body[0].MaterialType)},xe=Ke=>{console.log(Ke)};ze(`/${Re}/data/getViewForMaterialType?materialType=${(pe=ne==null?void 0:ne.MatlType)==null?void 0:pe.code}`,"get",ge,xe)},is=()=>{V(!0)},ot=()=>{V(!1)},Ct=()=>{var ne;c(!1),p(!1),S("systemGenerated"),us||v((ne=so)==null?void 0:ne.REQUEST_BENCH)},as=ne=>{var ge;S((ge=ne==null?void 0:ne.target)==null?void 0:ge.value)},xs=()=>{T==="systemGenerated"&&(Wt(),Ct()),T==="mailGenerated"&&(uo(),Ct())},Wt=()=>{N("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),m(!0);let ne={region:b==null?void 0:b.Region,scenario:b==null?void 0:b.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:G!=null&&G.requestId?G==null?void 0:G.requestId:b!=null&&b.RequestId?b==null?void 0:b.RequestId:""};const ge=Ke=>{if((Ke==null?void 0:Ke.size)==0){m(!1),N(""),Ie(!0),$("No data found for the selected criteria."),f("danger"),is();return}const _e=URL.createObjectURL(Ke),It=document.createElement("a");It.href=_e,It.setAttribute("download",`${(b==null?void 0:b.RequestType)===I.EXTEND_WITH_UPLOAD?"Mass_Extend.xlsx":"Mass_Create.xlsx"}`),document.body.appendChild(It),It.click(),document.body.removeChild(It),URL.revokeObjectURL(_e),m(!1),N(""),Ie(!0),$(`${b!=null&&b.TemplateName?`${b.TemplateName}_Mass Change`:(b==null?void 0:b.RequestType)===I.EXTEND_WITH_UPLOAD?"Mass_Extend":"Mass_Create"}.xlsx has been downloaded successfully.`),f("success"),is(),setTimeout(()=>{v("/requestBench")},2600)},xe=()=>{m(!1)},pe=`/${Re}${(b==null?void 0:b.RequestType)===I.EXTEND_WITH_UPLOAD?ye.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:ye.EXCEL.DOWNLOAD_EXCEL}`;ze(pe,"postandgetblob",ge,xe,ne)},uo=()=>{m(!0);let ne={region:b==null?void 0:b.Region,scenario:b==null?void 0:b.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:G!=null&&G.requestId?G==null?void 0:G.requestId:b!=null&&b.RequestId?b==null?void 0:b.RequestId:""};const ge=()=>{var Ke;m(!1),N(""),Ie(!0),$((Ke=zh)==null?void 0:Ke.DOWNLOAD_MAIL_INITIATED),f("success"),is(),setTimeout(()=>{var _e;v((_e=so)==null?void 0:_e.REQUEST_BENCH)},2600)},xe=()=>{var Ke;m(!1),Ie(!0),$((Ke=Vo)==null?void 0:Ke.ERR_DOWNLOADING_EXCEL),f("danger"),is(),setTimeout(()=>{var _e;v((_e=so)==null?void 0:_e.REQUEST_BENCH)},2600)},pe=`/${Re}${(b==null?void 0:b.RequestType)===I.EXTEND_WITH_UPLOAD?ye.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:ye.EXCEL.DOWNLOAD_EXCEL_MAIL}`;ze(pe,"post",ge,xe,ne)},no=()=>h(!1),Le=ne=>{if(H.includes("Distribution Channel")){const ge=pe=>P(pe==null?void 0:pe.body),xe=pe=>console.error(pe);ze(`/${Re}/data/getDistrChan?salesOrg=${ne.code}`,"get",ge,xe)}},mt={orgData:["Plant","Sales Organization","Distribution Channel"].map(ne=>({info:u[ne]||{code:"",desc:""},desc:ne})),selectedViews:{selectedSections:Ye}},hs=(ne,ge)=>{k(xe=>({...xe,[ne]:ge})),ne==="Sales Organization"&&Le(ge)},Ds=`/Date(${Date.now()})/`,zs=()=>{var _e;let ne=Fh(A==null?void 0:A.Region,Ne);R(Wh({...Ae,role:ne})),U(!1);const ge=new Date(A==null?void 0:A.ReqCreatedOn).getTime(),xe={RequestId:G!=null&&G.requestId?G==null?void 0:G.requestId:"",Region:(A==null?void 0:A.Region)||"",MatlType:(A==null?void 0:A.MatlType)||"",ReqCreatedBy:(Ae==null?void 0:Ae.user_id)||"",ReqCreatedOn:ge?`/Date(${ge})/`:Ds,ReqUpdatedOn:ge?`/Date(${ge})/`:Ds,RequestType:(A==null?void 0:A.RequestType)||"",RequestDesc:(A==null?void 0:A.RequestDesc)||"",Division:(A==null?void 0:A.Division)||"",RequestStatus:"DRAFT",RequestPriority:(A==null?void 0:A.RequestPriority)||"",LeadingCat:(A==null?void 0:A.LeadingCat)||"",FieldName:((_e=A==null?void 0:A.FieldName)==null?void 0:_e.join("$^$"))||"",TemplateName:(A==null?void 0:A.TemplateName)||""},pe=It=>{var Gt,ut,pt;if(Ie(!0),$(`Request Header Created Successfully with request ID ${Yh(A==null?void 0:A.RequestType,(Gt=It==null?void 0:It.body)==null?void 0:Gt.requestId)}`),f("success"),is(),R(Kh(It.body)),t(!0),rs(!1),R(Li({})),R(Xh({})),(b==null?void 0:b.RequestType)===I.CREATE_WITH_UPLOAD||(b==null?void 0:b.RequestType)===I.EXTEND_WITH_UPLOAD){p(!0);return}if((b==null?void 0:b.RequestType)===((ut=I)==null?void 0:ut.CHANGE_WITH_UPLOAD)){U(!0);return}if((b==null?void 0:b.RequestType)===((pt=I)==null?void 0:pt.CHANGE)){const Ut=Vh(B==null?void 0:B["Config Data"],b==null?void 0:b.FieldName,["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);R(Jh({...B,"Config Data":Ut}));const Kt=Qh(B==null?void 0:B[b==null?void 0:b.TemplateName],b==null?void 0:b.FieldName);R(Zh([...Kt]))}setTimeout(()=>{R(Uc(1)),e(!0)},2500)},Ke=()=>{Ie(!0),f("error"),$("Error occured while saving Request Header"),is()};ze(`/${Re}/alter/createRequestHeader`,"post",pe,Ke,xe)};E.useEffect(()=>{var ne;if(o){if((b==null?void 0:b.RequestType)===I.CREATE_WITH_UPLOAD||(b==null?void 0:b.RequestType)===I.EXTEND_WITH_UPLOAD){p(!0);return}if((b==null?void 0:b.RequestType)===((ne=I)==null?void 0:ne.CHANGE_WITH_UPLOAD)){U(!0);return}}},[o]);function Ns(ne){return ne.every(ge=>ge.info.code&&ge.info.desc)}const Ss=()=>{if(!Ns(mt.orgData))Ie(!0),f("error"),$("Please choose all mandatory fields"),is();else{const ge={label:"Attachments & Comments",value:"attachments&comments"},pe=[{label:"General Information",value:"generalInformation"},...Ye,ge];mt.selectedViews=pe,R(jh(mt)),R(Uc(1)),e(!0)}};E.useEffect(()=>{Ws()},[b==null?void 0:b.RequestType]);const Jt=(ne="")=>{var Ke,_e,It,Gt;const ge={materialNo:ne??"",top:500,skip:0,salesOrg:((_e=(Ke=Pe==null?void 0:Pe.uniqueSalesOrgList)==null?void 0:Ke.map(ut=>ut.code))==null?void 0:_e.join("$^$"))||""},xe=ut=>{(ut==null?void 0:ut.statusCode)===zt.STATUS_200&&(R(jo({keyName:zl.RETURN_MAT_NUMBER,data:ut==null?void 0:ut.body})),R(jo({keyName:zl.PARENT_MAT_NUMBER,data:ut==null?void 0:ut.body})))},pe=ut=>{js(ut)};ze(`/${Re}${(Gt=(It=ye)==null?void 0:It.DATA)==null?void 0:Gt.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",xe,pe,ge)};E.useEffect(()=>{Pe!=null&&Pe.uniqueSalesOrgList&&Jt()},[]);const Ms=ne=>{let ge={decisionTableId:null,decisionTableName:Si.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":ne||""}]};Is(ge)};return E.useEffect(()=>{b!=null&&b.Region&&(Qs(),Ms(b==null?void 0:b.Region))},[b==null?void 0:b.Region]),E.useEffect(()=>{b!=null&&b.TemplateName&&(((b==null?void 0:b.TemplateName)===ve.MRP||(b==null?void 0:b.TemplateName)===ve.WARE_VIEW_2)&&R(yi({keyName:"FieldName",data:void 0})),ws())},[b==null?void 0:b.TemplateName]),a("div",{children:te(io,{spacing:2,children:[Object.entries(ue).map(([ne,ge])=>te(Rs,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Sd},children:[a(wt,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:Fs(ne)}),a(Tt,{children:a(Rs,{container:!0,spacing:1,children:ge.filter(xe=>xe.visibility!=="Hidden").sort((xe,pe)=>xe.sequenceNo-pe.sequenceNo).map(xe=>a(Hf,{isHeader:!0,field:xe,dropDownData:s,disabled:us||(G==null?void 0:G.requestId),requestHeader:!0},xe.id))})}),!us&&!(G!=null&&G.requestId)&&a(Tt,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:a(St,{variant:"contained",color:"primary",disabled:!ls(),onClick:zs,children:Fs("Save Request Header")})}),a(zi,{})]},ne)),te(on,{open:i,onClose:no,children:[a(On,{sx:{backgroundColor:"#EAE9FF"},children:"Select Org Data"}),a(Uo,{children:a(Rs,{container:!0,columnSpacing:1,children:H.map((ne,ge)=>te(E.Fragment,{children:[a(Rs,{item:!0,md:4,children:te(wt,{children:[ne,a("span",{style:{color:"red"},children:"*"})]})}),a(Rs,{item:!0,md:8,children:a(Gl,{options:ne==="Distribution Channel"?O:s[ne]||[],size:"small",getOptionLabel:xe=>`${xe.code} - ${xe.desc}`,renderOption:(xe,pe)=>a("li",{...xe,children:a(wt,{children:`${pe.code} - ${pe.desc}`})}),onChange:(xe,pe)=>hs(ne,pe),renderInput:xe=>a(En,{...xe,placeholder:`Select ${ne}`})})})]},ge))})}),te(Ho,{children:[a(St,{onClick:no,variant:"outlined",children:Fs("Cancel")}),a(St,{variant:"contained",onClick:()=>{Ss()},children:Fs("Proceed")})]})]}),q&&a(wg,{downloadClicked:o,setDownloadClicked:c}),a(Ld,{onDownloadTypeChange:xs,open:F,downloadType:T,handleDownloadTypeChange:as,onClose:Ct}),a(Yn,{blurLoading:L,loaderMessage:g}),le&&a(Vn,{openSnackBar:X,alertMsg:J,alertType:K,handleSnackBarClose:ot})]})})};var Qi={},Ig=bn;Object.defineProperty(Qi,"__esModule",{value:!0});var Ud=Qi.default=void 0,Rg=Ig(An()),_g=wn;Ud=Qi.default=(0,Rg.default)((0,_g.jsx)("path",{d:"M22 5.18 10.59 16.6l-4.24-4.24 1.41-1.41 2.83 2.83 10-10zm-2.21 5.04c.13.57.21 1.17.21 1.78 0 4.42-3.58 8-8 8s-8-3.58-8-8 3.58-8 8-8c1.58 0 3.04.46 4.28 1.25l1.44-1.44C16.1 2.67 14.13 2 12 2 6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10c0-1.19-.22-2.33-.6-3.39z"}),"TaskAlt");const jr={plant:[Y.ACCOUNTING,Y.COSTING,Y.MRP,Y.SALES,Y.PURCHASING,Y.FORCASTING,Y.WAREHOUSE_MANAGEMENT,Y.WORK_SCHEDULING,Y.STORAGE_LOCATION_STOCKS,Y.PRODUCTION,Y.PLANT_STOCKS],salesOrg:[Y.SALES],distributionChannel:[Y.SALES],storageLocation:[Y.MRP,Y.STORAGE,Y.STORAGE_LOCATION_STOCKS],mrpProfile:[Y.MRP],warehouse:[Y.WAREHOUSE],storage:[Y.STORAGE]},Ng=(e,t,n,o,c)=>({checkValidation:(C,i,h,le,Ie)=>{var $,u,k,F,p,T,S,g,N,L;const K=($=e==null?void 0:e[C])==null?void 0:$.payloadData,f=(u=e==null?void 0:e[C])==null?void 0:u.headerData;(k=e==null?void 0:e[C])==null||k.ManufacturerID;const X=(F=e==null?void 0:e.payloadData)==null?void 0:F.Region;if(!(f!=null&&f.materialNumber))return{missingFields:["Material number"],isValid:!1};if(!(f!=null&&f.globalMaterialDescription)||!K)return{missingFields:["Material Description"],isValid:!1};const V=ef(K[Y.BASIC_DATA]);V.Material=f==null?void 0:f.materialNumber,V.MatlDesc=f==null?void 0:f.globalMaterialDescription;const H=t==null?void 0:t.find(m=>{var O;return(m==null?void 0:m[X])&&(m==null?void 0:m[X][(O=f==null?void 0:f.materialType)==null?void 0:O.code])}),ce=H&&H[X]&&((T=H[X][(p=f==null?void 0:f.materialType)==null?void 0:p.code])==null?void 0:T.mandatoryFields),J=ce==null?void 0:ce[Y.BASIC_DATA];if((J==null?void 0:J.length)>0){for(const m of J)if(!V[m==null?void 0:m.jsonName])return{missingFields:tf(J,V),viewType:Y.BASIC_DATA,isValid:!1,plant:[Y.BASIC_DATA]}}if(n.includes(Y.PURCHASING)){const m=ce==null?void 0:ce[Y.PURCHASING];if(m)if(K[Y.PURCHASING]){const{validCount:O}=pi(i,Y.PURCHASING),{totalCount:P,allValid:q}=Ei(K[Y.PURCHASING],m);if(P===O)if(q){if(!Ci(K[Y.PURCHASING],m)){const U=Nn(i),R=Dl(U,K[Y.PURCHASING],m);return{missingFields:Do(m,K[Y.PURCHASING]),viewType:Y.PURCHASING,isValid:!1,plant:R==null?void 0:R.missingFields}}}else{const U=Nn(i),R=Hc(U,K[Y.PURCHASING]);return{missingFields:Do(m,K[Y.PURCHASING]),viewType:Y.PURCHASING,isValid:!1,plant:R}}else{const U=Nn(i),R=Dl(U,K[Y.PURCHASING],m);return{missingFields:Do(m,K[Y.PURCHASING]),viewType:Y.PURCHASING,isValid:!1,plant:R==null?void 0:R.missingFields}}}else{const O=Nn(i);return{missingFields:Do(m,K[Y.PURCHASING]),viewType:Y.PURCHASING,isValid:!1,plant:O}}}if(n.includes(Y.MRP)){const m=ce==null?void 0:ce[Y.MRP];if(m){const O=sf(i);if(K[Y.MRP]){const{validCount:P}=pi(i,Y.MRP),{totalCount:q,allValid:U}=Ei(K[Y.MRP],m);if(q===P)if(U){if(!Ci(K[Y.MRP],m)){const R=Nn(i),v=Dl(R,K[Y.MRP],m),b=Do(m,K[Y.MRP]),ue=mi(v==null?void 0:v.missingFields,(N=O==null?void 0:O[Y.MRP])==null?void 0:N.displayCombinations);return{missingFields:b,viewType:Y.MRP,isValid:!1,plant:ue}}}else{const R=Nn(i),v=Hc(R,K[Y.MRP]),b=Do(m,K[Y.MRP]),ue=mi(v,(g=O==null?void 0:O[Y.MRP])==null?void 0:g.displayCombinations);return{missingFields:b,viewType:Y.MRP,isValid:!1,plant:ue}}else{const R=Nn(i),v=Dl(R,K[Y.MRP],m),b=Do(m,K[Y.MRP]),ue=mi(v==null?void 0:v.missingFields,(L=O==null?void 0:O[Y.MRP])==null?void 0:L.displayCombinations);return{missingFields:b,viewType:Y.MRP,isValid:!1,plant:ue}}}else return{missingFields:Do(m,K[Y.MRP]),viewType:Y.MRP,isValid:!1,plant:(S=O==null?void 0:O[Y.MRP])==null?void 0:S.displayCombinations}}}if(n.includes(Y.SALES)){const m=ce==null?void 0:ce[Y.SALES];if(m)if(K[Y.SALES]){const{validCount:O}=pi(i,Y.SALES),{totalCount:P,allValid:q}=Ei(K[Y.SALES],m);if(P===O)if(q){if(!Ci(K[Y.SALES],m))return{missingFields:Do(m,K[Y.SALES]),viewType:Y.SALES,isValid:!1}}else return{missingFields:Do(m,K[Y.SALES]),viewType:Y.SALES,isValid:!1};else return{missingFields:Do(m,K[Y.SALES]),viewType:Y.SALES,isValid:!1}}else return{missingFields:Do(m,K[Y.SALES]),viewType:Y.SALES,isValid:!1}}return{missingFields:null,isValid:!0}}}),Sg=()=>{const e=po(),{fetchDataAndDispatch:t}=Yi(),n=re(c=>c.payload.valuationClassData||{});return{fetchValuationClassData:c=>{if(!c)return;c in n?e(jo({keyName:zl.VAL_CLASS,data:n[c]})):t(`/${Re}${ye.DATA.GET_VALUATION_CLASS}?matlType=${c}`,zl.VAL_CLASS)}}};var Zi={},Mg=bn;Object.defineProperty(Zi,"__esModule",{value:!0});var Hd=Zi.default=void 0,Og=Mg(An()),vg=wn;Hd=Zi.default=(0,Og.default)((0,vg.jsx)("path",{d:"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12zm-1 4 6 6v10c0 1.1-.9 2-2 2H7.99C6.89 23 6 22.1 6 21l.01-14c0-1.1.89-2 1.99-2zm-1 7h5.5L14 6.5z"}),"FileCopy");const yg=({open:e,onClose:t,title:n,lengthOfOrgRow:o,selectedMaterialPayload:c,materialID:s,orgRows:C})=>{var f,X;const[i,h]=E.useState({}),le=po(),Ie=()=>{const V=[];return C&&C.length>0&&(C==null||C.forEach((H,ce)=>{var J,$,u,k,F,p,T,S,g,N,L,m,O,P;if(ce!==(o==null?void 0:o.copyFor)){const q=($=(J=H.plant)==null?void 0:J.value)==null?void 0:$.code,U=((k=(u=H.plant)==null?void 0:u.value)==null?void 0:k.desc)||q,R=(F=H.salesOrg)==null?void 0:F.code,v=((p=H.salesOrg)==null?void 0:p.desc)||R,b=(S=(T=H.dc)==null?void 0:T.value)==null?void 0:S.code,ue=((N=(g=H.dc)==null?void 0:g.value)==null?void 0:N.desc)||b,B=(m=(L=H.warehouse)==null?void 0:L.value)==null?void 0:m.code,Ne=((P=(O=H.warehouse)==null?void 0:O.value)==null?void 0:P.desc)||B;if(q){let A=`Plant: ${U||"N/A"}`;R&&(A+=` | SalesOrg: ${v||"N/A"}`),b&&(A+=` | DC: ${ue||"N/A"}`),B&&(A+=` | Warehouse: ${Ne||"N/A"}`);let Ae=q;R&&(Ae+=`-${R}`),b&&(Ae+=`-${b}`),B&&(Ae+=`-${B}`),V==null||V.push({code:Ae,desc:A,index:ce,plant:q,salesOrg:R,dc:b,warehouse:B})}}})),V},K=()=>{var k,F,p,T,S,g,N,L;if(!i.code)return;const V=C[o.copyFor],H=(F=(k=V==null?void 0:V.plant)==null?void 0:k.value)==null?void 0:F.code,ce=(p=V==null?void 0:V.salesOrg)==null?void 0:p.code,J=(S=(T=V==null?void 0:V.dc)==null?void 0:T.value)==null?void 0:S.code,$=(N=(g=V==null?void 0:V.warehouse)==null?void 0:g.value)==null?void 0:N.code;if(!H)return;const u=JSON.parse(JSON.stringify(c));(L=Object.keys(u))==null||L.forEach(m=>{const O=u[m];if(!(m===Y.BASIC_DATA||m===Y.SALES_GENERAL||m===Y.PURCHASING_GENERAL||m===Y.TAX_DATA)&&typeof O=="object"){const P=Object.keys(O);if(m===Y.WAREHOUSE){const q=P==null?void 0:P.find(R=>R.includes(i.warehouse)),U=P==null?void 0:P.find(R=>R.includes($));if(q&&U&&U!==q){const R=JSON.parse(JSON.stringify(O[q]));delete R.WarehouseId,u[m][U]={...JSON.parse(JSON.stringify(u[m][U]||{})),...R}}}else if(m===Y.SALES){const q=`${i.salesOrg}-${i.dc}`,U=`${ce}-${J}`,R=P==null?void 0:P.find(b=>b===q),v=P==null?void 0:P.find(b=>b===U);if(R&&v&&v!==R){const b=JSON.parse(JSON.stringify(O[R]));delete b.SalesId,u[m][v]={...JSON.parse(JSON.stringify(u[m][v]||{})),...b}}}else{const q=P==null?void 0:P.find(R=>R.includes(i.plant)),U=P==null?void 0:P.find(R=>R.includes(H));if(q&&U&&U!==q){const R=JSON.parse(JSON.stringify(O[q]));R&&(delete R.SalesId,delete R.PlantId,delete R.StorageLocationId,delete R.AccountingId,U&&(u[m][U]={...JSON.parse(JSON.stringify(u[m][U]||{})),...R}))}}}}),le(of({materialID:s,data:u})),t()};return te(Gi,{isOpen:e,titleIcon:a(xi,{size:"small",sx:{color:(X=(f=De)==null?void 0:f.primary)==null?void 0:X.dark,fontSize:"20px"}}),Title:n,handleClose:()=>t(),children:[te(Uo,{sx:{mt:2},children:[a(wt,{sx:{mb:2},children:Md.COPY_ORG_DATA_VALUES}),a(to,{options:Ie(),placeholder:"SELECT SOURCE ORGANIZATION",onChange:V=>h(V),value:i})]}),a(Ho,{children:a(St,{variant:"contained",size:"small",onClick:()=>K(),children:"Ok"})})]})},Lg=()=>{const{fetchDataAndDispatch:e}=Yi();return{fetchTabSpecificData:(n,o)=>{if(o===Y.SALES&&n&&n.includes("-")){const[c,s]=n.split("-");c&&e(`/${Re}${ye.DATA.GET_DELIVARING_PLANT_BASED_ON_SALES_ORG_AND_DISTCHNL}`,"DelygPlnt","post",{salesOrg:c,distChnl:s},!0)}else if(o===Y.PLANT&&n){const c=n;e(`/${Re}${ye.DATA.GET_SPPROC_TYPE}`,"Spproctype","post",{plant:c},!0),e(`/${Re}${ye.DATA.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"MrpCtrler","post",{plant:c},!0),e(`/${Re}${ye.DATA.GET_PROD_STORAGE_LOCATION_BASED_ON_PLANT}`,"IssStLoc","post",{plant:c},!0),e(`/${Re}${ye.DATA.GET_PROCUREMENT_STORAGE_LOCATION_BASED_ON_PLANT}`,"SlocExprc","post",{plant:c},!0),e(`/${Re}${ye.DATA.GET_SCHEDULING_MARGIN_KEY_BASED_ON_PLANT}`,"SmKey","post",{plant:c},!0),e(`/${Re}${ye.DATA.GET_PROFIT_CENTER_BASED_ON_PLANT}`,"ProfitCtr","post",{plant:c},!0),e(`/${Re}${ye.DATA.GET_PRODUCTION_SCHEDULING_PROFILE_BASED_ON_PLANT}`,"ProdProf","post",{plant:c},!0)}else o===Y.WAREHOUSE&&n&&e(`/${Re}${ye.DATA.GET_PLACEMENT}?wareHouseNo=${n}`,"Placement","get",{plant:n},!0)}}},xg=({doAjax:e,customError:t,fetchDataAndDispatch:n,destination_MaterialMgmt:o})=>({getContryBasedOnPlant:s=>{const C=h=>{var le;if((h==null?void 0:h.statusCode)===zt.STATUS_200){const Ie=(le=h==null?void 0:h.body[0])==null?void 0:le.code;Ie&&(n(`/${o}${ye.DATA.GET_COMMODITY_CODE_BASED_ON_COUNTRY}?country=${Ie}`,"CommCode","get",{plant:s},!0),n(`/${o}${ye.DATA.GET_HTS_CODE}?country=${Ie}`,"HtsCode","get",{plant:s},!0))}},i=h=>{t(h)};e(`/${o}${ye.DATA.GET_COUNTRY_BASED_ON_PLANT}`,"post",C,i,{plant:s})}});var ea={},Dg=bn;Object.defineProperty(ea,"__esModule",{value:!0});var qi=ea.default=void 0,Pg=Dg(An()),qg=wn;qi=ea.default=(0,Pg.default)((0,qg.jsx)("path",{d:"M3 5v4h2V5h4V3H5c-1.1 0-2 .9-2 2m2 10H3v4c0 1.1.9 2 2 2h4v-2H5zm14 4h-4v2h4c1.1 0 2-.9 2-2v-4h-2zm0-16h-4v2h4v4h2V5c0-1.1-.9-2-2-2"}),"CropFree");var ta={},Ug=bn;Object.defineProperty(ta,"__esModule",{value:!0});var Ui=ta.default=void 0,Hg=Ug(An()),kg=wn;Ui=ta.default=(0,Hg.default)((0,kg.jsx)("path",{d:"M22 3.41 16.71 8.7 20 12h-8V4l3.29 3.29L20.59 2zM3.41 22l5.29-5.29L12 20v-8H4l3.29 3.29L2 20.59z"}),"CloseFullscreen");/*!
* sweetalert2 v11.22.2
* Released under the MIT License.
*/function kd(e,t,n){if(typeof e=="function"?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}function Bg(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Zc(e,t){return e.get(kd(e,t))}function $g(e,t,n){Bg(e,t),t.set(e,n)}function Gg(e,t,n){return e.set(kd(e,t),n),n}const Fg=100,Je={},Wg=()=>{Je.previousActiveElement instanceof HTMLElement?(Je.previousActiveElement.focus(),Je.previousActiveElement=null):document.body&&document.body.focus()},jg=e=>new Promise(t=>{if(!e)return t();const n=window.scrollX,o=window.scrollY;Je.restoreFocusTimeout=setTimeout(()=>{Wg(),t()},Fg),window.scrollTo(n,o)}),Bd="swal2-",zg=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"],j=zg.reduce((e,t)=>(e[t]=Bd+t,e),{}),Yg=["success","warning","info","question","error"],Yl=Yg.reduce((e,t)=>(e[t]=Bd+t,e),{}),$d="SweetAlert2:",sa=e=>e.charAt(0).toUpperCase()+e.slice(1),ao=e=>{console.warn(`${$d} ${typeof e=="object"?e.join(" "):e}`)},Ln=e=>{console.error(`${$d} ${e}`)},ed=[],Kg=e=>{ed.includes(e)||(ed.push(e),ao(e))},Gd=(e,t=null)=>{Kg(`"${e}" is deprecated and will be removed in the next major release.${t?` Use "${t}" instead.`:""}`)},ri=e=>typeof e=="function"?e():e,oa=e=>e&&typeof e.toPromise=="function",Zr=e=>oa(e)?e.toPromise():Promise.resolve(e),na=e=>e&&Promise.resolve(e)===e,co=()=>document.body.querySelector(`.${j.container}`),el=e=>{const t=co();return t?t.querySelector(e):null},yo=e=>el(`.${e}`),vt=()=>yo(j.popup),Jn=()=>yo(j.icon),Xg=()=>yo(j["icon-content"]),Fd=()=>yo(j.title),ra=()=>yo(j["html-container"]),Wd=()=>yo(j.image),la=()=>yo(j["progress-steps"]),li=()=>yo(j["validation-message"]),Jo=()=>el(`.${j.actions} .${j.confirm}`),Qn=()=>el(`.${j.actions} .${j.cancel}`),xn=()=>el(`.${j.actions} .${j.deny}`),Vg=()=>yo(j["input-label"]),Zn=()=>el(`.${j.loader}`),tl=()=>yo(j.actions),jd=()=>yo(j.footer),ii=()=>yo(j["timer-progress-bar"]),ia=()=>yo(j.close),Jg=`
  a[href],
  area[href],
  input:not([disabled]),
  select:not([disabled]),
  textarea:not([disabled]),
  button:not([disabled]),
  iframe,
  object,
  embed,
  [tabindex="0"],
  [contenteditable],
  audio[controls],
  video[controls],
  summary
`,aa=()=>{const e=vt();if(!e)return[];const t=e.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),n=Array.from(t).sort((s,C)=>{const i=parseInt(s.getAttribute("tabindex")||"0"),h=parseInt(C.getAttribute("tabindex")||"0");return i>h?1:i<h?-1:0}),o=e.querySelectorAll(Jg),c=Array.from(o).filter(s=>s.getAttribute("tabindex")!=="-1");return[...new Set(n.concat(c))].filter(s=>To(s))},ca=()=>nn(document.body,j.shown)&&!nn(document.body,j["toast-shown"])&&!nn(document.body,j["no-backdrop"]),ai=()=>{const e=vt();return e?nn(e,j.toast):!1},Qg=()=>{const e=vt();return e?e.hasAttribute("data-loading"):!1},Lo=(e,t)=>{if(e.textContent="",t){const o=new DOMParser().parseFromString(t,"text/html"),c=o.querySelector("head");c&&Array.from(c.childNodes).forEach(C=>{e.appendChild(C)});const s=o.querySelector("body");s&&Array.from(s.childNodes).forEach(C=>{C instanceof HTMLVideoElement||C instanceof HTMLAudioElement?e.appendChild(C.cloneNode(!0)):e.appendChild(C)})}},nn=(e,t)=>{if(!t)return!1;const n=t.split(/\s+/);for(let o=0;o<n.length;o++)if(!e.classList.contains(n[o]))return!1;return!0},Zg=(e,t)=>{Array.from(e.classList).forEach(n=>{!Object.values(j).includes(n)&&!Object.values(Yl).includes(n)&&!Object.values(t.showClass||{}).includes(n)&&e.classList.remove(n)})},vo=(e,t,n)=>{if(Zg(e,t),!t.customClass)return;const o=t.customClass[n];if(o){if(typeof o!="string"&&!o.forEach){ao(`Invalid type of customClass.${n}! Expected string or iterable object, got "${typeof o}"`);return}Dt(e,o)}},ci=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${j.popup} > .${j[t]}`);case"checkbox":return e.querySelector(`.${j.popup} > .${j.checkbox} input`);case"radio":return e.querySelector(`.${j.popup} > .${j.radio} input:checked`)||e.querySelector(`.${j.popup} > .${j.radio} input:first-child`);case"range":return e.querySelector(`.${j.popup} > .${j.range} input`);default:return e.querySelector(`.${j.popup} > .${j.input}`)}},zd=e=>{if(e.focus(),e.type!=="file"){const t=e.value;e.value="",e.value=t}},Yd=(e,t,n)=>{!e||!t||(typeof t=="string"&&(t=t.split(/\s+/).filter(Boolean)),t.forEach(o=>{Array.isArray(e)?e.forEach(c=>{n?c.classList.add(o):c.classList.remove(o)}):n?e.classList.add(o):e.classList.remove(o)}))},Dt=(e,t)=>{Yd(e,t,!0)},ko=(e,t)=>{Yd(e,t,!1)},Cn=(e,t)=>{const n=Array.from(e.children);for(let o=0;o<n.length;o++){const c=n[o];if(c instanceof HTMLElement&&nn(c,t))return c}},vn=(e,t,n)=>{n===`${parseInt(n)}`&&(n=parseInt(n)),n||parseInt(n)===0?e.style.setProperty(t,typeof n=="number"?`${n}px`:n):e.style.removeProperty(t)},Gs=(e,t="flex")=>{e&&(e.style.display=t)},oo=e=>{e&&(e.style.display="none")},da=(e,t="block")=>{e&&new MutationObserver(()=>{sl(e,e.innerHTML,t)}).observe(e,{childList:!0,subtree:!0})},td=(e,t,n,o)=>{const c=e.querySelector(t);c&&c.style.setProperty(n,o)},sl=(e,t,n="flex")=>{t?Gs(e,n):oo(e)},To=e=>!!(e&&(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),eT=()=>!To(Jo())&&!To(xn())&&!To(Qn()),Hi=e=>e.scrollHeight>e.clientHeight,tT=(e,t)=>{let n=e;for(;n&&n!==t;){if(Hi(n))return!0;n=n.parentElement}return!1},Kd=e=>{const t=window.getComputedStyle(e),n=parseFloat(t.getPropertyValue("animation-duration")||"0"),o=parseFloat(t.getPropertyValue("transition-duration")||"0");return n>0||o>0},ua=(e,t=!1)=>{const n=ii();n&&To(n)&&(t&&(n.style.transition="none",n.style.width="100%"),setTimeout(()=>{n.style.transition=`width ${e/1e3}s linear`,n.style.width="0%"},10))},sT=()=>{const e=ii();if(!e)return;const t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const n=parseInt(window.getComputedStyle(e).width),o=t/n*100;e.style.width=`${o}%`},oT=()=>typeof window>"u"||typeof document>"u",nT=`
 <div aria-labelledby="${j.title}" aria-describedby="${j["html-container"]}" class="${j.popup}" tabindex="-1">
   <button type="button" class="${j.close}"></button>
   <ul class="${j["progress-steps"]}"></ul>
   <div class="${j.icon}"></div>
   <img class="${j.image}" />
   <h2 class="${j.title}" id="${j.title}"></h2>
   <div class="${j["html-container"]}" id="${j["html-container"]}"></div>
   <input class="${j.input}" id="${j.input}" />
   <input type="file" class="${j.file}" />
   <div class="${j.range}">
     <input type="range" />
     <output></output>
   </div>
   <select class="${j.select}" id="${j.select}"></select>
   <div class="${j.radio}"></div>
   <label class="${j.checkbox}">
     <input type="checkbox" id="${j.checkbox}" />
     <span class="${j.label}"></span>
   </label>
   <textarea class="${j.textarea}" id="${j.textarea}"></textarea>
   <div class="${j["validation-message"]}" id="${j["validation-message"]}"></div>
   <div class="${j.actions}">
     <div class="${j.loader}"></div>
     <button type="button" class="${j.confirm}"></button>
     <button type="button" class="${j.deny}"></button>
     <button type="button" class="${j.cancel}"></button>
   </div>
   <div class="${j.footer}"></div>
   <div class="${j["timer-progress-bar-container"]}">
     <div class="${j["timer-progress-bar"]}"></div>
   </div>
 </div>
`.replace(/(^|\n)\s*/g,""),rT=()=>{const e=co();return e?(e.remove(),ko([document.documentElement,document.body],[j["no-backdrop"],j["toast-shown"],j["has-column"]]),!0):!1},Sn=()=>{Je.currentInstance.resetValidationMessage()},lT=()=>{const e=vt(),t=Cn(e,j.input),n=Cn(e,j.file),o=e.querySelector(`.${j.range} input`),c=e.querySelector(`.${j.range} output`),s=Cn(e,j.select),C=e.querySelector(`.${j.checkbox} input`),i=Cn(e,j.textarea);t.oninput=Sn,n.onchange=Sn,s.onchange=Sn,C.onchange=Sn,i.oninput=Sn,o.oninput=()=>{Sn(),c.value=o.value},o.onchange=()=>{Sn(),c.value=o.value}},iT=e=>typeof e=="string"?document.querySelector(e):e,aT=e=>{const t=vt();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")},cT=e=>{window.getComputedStyle(e).direction==="rtl"&&Dt(co(),j.rtl)},dT=e=>{const t=rT();if(oT()){Ln("SweetAlert2 requires document to initialize");return}const n=document.createElement("div");n.className=j.container,t&&Dt(n,j["no-transition"]),Lo(n,nT),n.dataset.swal2Theme=e.theme;const o=iT(e.target);o.appendChild(n),e.topLayer&&(n.setAttribute("popover",""),n.showPopover()),aT(e),cT(o),lT()},ha=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):typeof e=="object"?uT(e,t):e&&Lo(t,e)},uT=(e,t)=>{e.jquery?hT(t,e):Lo(t,e.toString())},hT=(e,t)=>{if(e.textContent="",0 in t)for(let n=0;n in t;n++)e.appendChild(t[n].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},fT=(e,t)=>{const n=tl(),o=Zn();!n||!o||(!t.showConfirmButton&&!t.showDenyButton&&!t.showCancelButton?oo(n):Gs(n),vo(n,t,"actions"),gT(n,o,t),Lo(o,t.loaderHtml||""),vo(o,t,"loader"))};function gT(e,t,n){const o=Jo(),c=xn(),s=Qn();!o||!c||!s||(Ii(o,"confirm",n),Ii(c,"deny",n),Ii(s,"cancel",n),TT(o,c,s,n),n.reverseButtons&&(n.toast?(e.insertBefore(s,o),e.insertBefore(c,o)):(e.insertBefore(s,t),e.insertBefore(c,t),e.insertBefore(o,t))))}function TT(e,t,n,o){if(!o.buttonsStyling){ko([e,t,n],j.styled);return}Dt([e,t,n],j.styled),o.confirmButtonColor&&e.style.setProperty("--swal2-confirm-button-background-color",o.confirmButtonColor),o.denyButtonColor&&t.style.setProperty("--swal2-deny-button-background-color",o.denyButtonColor),o.cancelButtonColor&&n.style.setProperty("--swal2-cancel-button-background-color",o.cancelButtonColor),wi(e),wi(t),wi(n)}function wi(e){const t=window.getComputedStyle(e);if(t.getPropertyValue("--swal2-action-button-focus-box-shadow"))return;const n=t.backgroundColor.replace(/rgba?\((\d+), (\d+), (\d+).*/,"rgba($1, $2, $3, 0.5)");e.style.setProperty("--swal2-action-button-focus-box-shadow",t.getPropertyValue("--swal2-outline").replace(/ rgba\(.*/,` ${n}`))}function Ii(e,t,n){const o=sa(t);sl(e,n[`show${o}Button`],"inline-block"),Lo(e,n[`${t}ButtonText`]||""),e.setAttribute("aria-label",n[`${t}ButtonAriaLabel`]||""),e.className=j[t],vo(e,n,`${t}Button`)}const pT=(e,t)=>{const n=ia();n&&(Lo(n,t.closeButtonHtml||""),vo(n,t,"closeButton"),sl(n,t.showCloseButton),n.setAttribute("aria-label",t.closeButtonAriaLabel||""))},ET=(e,t)=>{const n=co();n&&(CT(n,t.backdrop),mT(n,t.position),AT(n,t.grow),vo(n,t,"container"))};function CT(e,t){typeof t=="string"?e.style.background=t:t||Dt([document.documentElement,document.body],j["no-backdrop"])}function mT(e,t){t&&(t in j?Dt(e,j[t]):(ao('The "position" parameter is not valid, defaulting to "center"'),Dt(e,j.center)))}function AT(e,t){t&&Dt(e,j[`grow-${t}`])}var Yt={innerParams:new WeakMap,domCache:new WeakMap};const bT=["input","file","range","select","radio","checkbox","textarea"],wT=(e,t)=>{const n=vt();if(!n)return;const o=Yt.innerParams.get(e),c=!o||t.input!==o.input;bT.forEach(s=>{const C=Cn(n,j[s]);C&&(_T(s,t.inputAttributes),C.className=j[s],c&&oo(C))}),t.input&&(c&&IT(t),NT(t))},IT=e=>{if(!e.input)return;if(!ms[e.input]){Ln(`Unexpected type of input! Expected ${Object.keys(ms).join(" | ")}, got "${e.input}"`);return}const t=Xd(e.input);if(!t)return;const n=ms[e.input](t,e);Gs(t),e.inputAutoFocus&&setTimeout(()=>{zd(n)})},RT=e=>{for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;["id","type","value","style"].includes(n)||e.removeAttribute(n)}},_T=(e,t)=>{const n=vt();if(!n)return;const o=ci(n,e);if(o){RT(o);for(const c in t)o.setAttribute(c,t[c])}},NT=e=>{if(!e.input)return;const t=Xd(e.input);t&&vo(t,e,"input")},fa=(e,t)=>{!e.placeholder&&t.inputPlaceholder&&(e.placeholder=t.inputPlaceholder)},ol=(e,t,n)=>{if(n.inputLabel){const o=document.createElement("label"),c=j["input-label"];o.setAttribute("for",e.id),o.className=c,typeof n.customClass=="object"&&Dt(o,n.customClass.inputLabel),o.innerText=n.inputLabel,t.insertAdjacentElement("beforebegin",o)}},Xd=e=>{const t=vt();if(t)return Cn(t,j[e]||j.input)},Kl=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:na(t)||ao(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},ms={};ms.text=ms.email=ms.password=ms.number=ms.tel=ms.url=ms.search=ms.date=ms["datetime-local"]=ms.time=ms.week=ms.month=(e,t)=>(Kl(e,t.inputValue),ol(e,e,t),fa(e,t),e.type=t.input,e);ms.file=(e,t)=>(ol(e,e,t),fa(e,t),e);ms.range=(e,t)=>{const n=e.querySelector("input"),o=e.querySelector("output");return Kl(n,t.inputValue),n.type=t.input,Kl(o,t.inputValue),ol(n,e,t),e};ms.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const n=document.createElement("option");Lo(n,t.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,e.appendChild(n)}return ol(e,e,t),e};ms.radio=e=>(e.textContent="",e);ms.checkbox=(e,t)=>{const n=ci(vt(),"checkbox");n.value="1",n.checked=!!t.inputValue;const o=e.querySelector("span");return Lo(o,t.inputPlaceholder||t.inputLabel),n};ms.textarea=(e,t)=>{Kl(e,t.inputValue),fa(e,t),ol(e,e,t);const n=o=>parseInt(window.getComputedStyle(o).marginLeft)+parseInt(window.getComputedStyle(o).marginRight);return setTimeout(()=>{if("MutationObserver"in window){const o=parseInt(window.getComputedStyle(vt()).width),c=()=>{if(!document.body.contains(e))return;const s=e.offsetWidth+n(e);s>o?vt().style.width=`${s}px`:vn(vt(),"width",t.width)};new MutationObserver(c).observe(e,{attributes:!0,attributeFilter:["style"]})}}),e};const ST=(e,t)=>{const n=ra();n&&(da(n),vo(n,t,"htmlContainer"),t.html?(ha(t.html,n),Gs(n,"block")):t.text?(n.textContent=t.text,Gs(n,"block")):oo(n),wT(e,t))},MT=(e,t)=>{const n=jd();n&&(da(n),sl(n,t.footer,"block"),t.footer&&ha(t.footer,n),vo(n,t,"footer"))},OT=(e,t)=>{const n=Yt.innerParams.get(e),o=Jn();if(!o)return;if(n&&t.icon===n.icon){od(o,t),sd(o,t);return}if(!t.icon&&!t.iconHtml){oo(o);return}if(t.icon&&Object.keys(Yl).indexOf(t.icon)===-1){Ln(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${t.icon}"`),oo(o);return}Gs(o),od(o,t),sd(o,t),Dt(o,t.showClass&&t.showClass.icon),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",Vd)},sd=(e,t)=>{for(const[n,o]of Object.entries(Yl))t.icon!==n&&ko(e,o);Dt(e,t.icon&&Yl[t.icon]),LT(e,t),Vd(),vo(e,t,"icon")},Vd=()=>{const e=vt();if(!e)return;const t=window.getComputedStyle(e).getPropertyValue("background-color"),n=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let o=0;o<n.length;o++)n[o].style.backgroundColor=t},vT=e=>`
  ${e.animation?'<div class="swal2-success-circular-line-left"></div>':""}
  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
  <div class="swal2-success-ring"></div>
  ${e.animation?'<div class="swal2-success-fix"></div>':""}
  ${e.animation?'<div class="swal2-success-circular-line-right"></div>':""}
`,yT=`
  <span class="swal2-x-mark">
    <span class="swal2-x-mark-line-left"></span>
    <span class="swal2-x-mark-line-right"></span>
  </span>
`,od=(e,t)=>{if(!t.icon&&!t.iconHtml)return;let n=e.innerHTML,o="";t.iconHtml?o=nd(t.iconHtml):t.icon==="success"?(o=vT(t),n=n.replace(/ style=".*?"/g,"")):t.icon==="error"?o=yT:t.icon&&(o=nd({question:"?",warning:"!",info:"i"}[t.icon])),n.trim()!==o.trim()&&Lo(e,o)},LT=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const n of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])td(e,n,"background-color",t.iconColor);td(e,".swal2-success-ring","border-color",t.iconColor)}},nd=e=>`<div class="${j["icon-content"]}">${e}</div>`,xT=(e,t)=>{const n=Wd();if(n){if(!t.imageUrl){oo(n);return}Gs(n,""),n.setAttribute("src",t.imageUrl),n.setAttribute("alt",t.imageAlt||""),vn(n,"width",t.imageWidth),vn(n,"height",t.imageHeight),n.className=j.image,vo(n,t,"image")}};let ga=!1,Jd=0,Qd=0,Zd=0,eu=0;const DT=e=>{e.addEventListener("mousedown",Xl),document.body.addEventListener("mousemove",Vl),e.addEventListener("mouseup",Jl),e.addEventListener("touchstart",Xl),document.body.addEventListener("touchmove",Vl),e.addEventListener("touchend",Jl)},PT=e=>{e.removeEventListener("mousedown",Xl),document.body.removeEventListener("mousemove",Vl),e.removeEventListener("mouseup",Jl),e.removeEventListener("touchstart",Xl),document.body.removeEventListener("touchmove",Vl),e.removeEventListener("touchend",Jl)},Xl=e=>{const t=vt();if(e.target===t||Jn().contains(e.target)){ga=!0;const n=tu(e);Jd=n.clientX,Qd=n.clientY,Zd=parseInt(t.style.insetInlineStart)||0,eu=parseInt(t.style.insetBlockStart)||0,Dt(t,"swal2-dragging")}},Vl=e=>{const t=vt();if(ga){let{clientX:n,clientY:o}=tu(e);t.style.insetInlineStart=`${Zd+(n-Jd)}px`,t.style.insetBlockStart=`${eu+(o-Qd)}px`}},Jl=()=>{const e=vt();ga=!1,ko(e,"swal2-dragging")},tu=e=>{let t=0,n=0;return e.type.startsWith("mouse")?(t=e.clientX,n=e.clientY):e.type.startsWith("touch")&&(t=e.touches[0].clientX,n=e.touches[0].clientY),{clientX:t,clientY:n}},qT=(e,t)=>{const n=co(),o=vt();if(!(!n||!o)){if(t.toast){vn(n,"width",t.width),o.style.width="100%";const c=Zn();c&&o.insertBefore(c,Jn())}else vn(o,"width",t.width);vn(o,"padding",t.padding),t.color&&(o.style.color=t.color),t.background&&(o.style.background=t.background),oo(li()),UT(o,t),t.draggable&&!t.toast?(Dt(o,j.draggable),DT(o)):(ko(o,j.draggable),PT(o))}},UT=(e,t)=>{const n=t.showClass||{};e.className=`${j.popup} ${To(e)?n.popup:""}`,t.toast?(Dt([document.documentElement,document.body],j["toast-shown"]),Dt(e,j.toast)):Dt(e,j.modal),vo(e,t,"popup"),typeof t.customClass=="string"&&Dt(e,t.customClass),t.icon&&Dt(e,j[`icon-${t.icon}`])},HT=(e,t)=>{const n=la();if(!n)return;const{progressSteps:o,currentProgressStep:c}=t;if(!o||o.length===0||c===void 0){oo(n);return}Gs(n),n.textContent="",c>=o.length&&ao("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),o.forEach((s,C)=>{const i=kT(s);if(n.appendChild(i),C===c&&Dt(i,j["active-progress-step"]),C!==o.length-1){const h=BT(t);n.appendChild(h)}})},kT=e=>{const t=document.createElement("li");return Dt(t,j["progress-step"]),Lo(t,e),t},BT=e=>{const t=document.createElement("li");return Dt(t,j["progress-step-line"]),e.progressStepsDistance&&vn(t,"width",e.progressStepsDistance),t},$T=(e,t)=>{const n=Fd();n&&(da(n),sl(n,t.title||t.titleText,"block"),t.title&&ha(t.title,n),t.titleText&&(n.innerText=t.titleText),vo(n,t,"title"))},su=(e,t)=>{qT(e,t),ET(e,t),HT(e,t),OT(e,t),xT(e,t),$T(e,t),pT(e,t),ST(e,t),fT(e,t),MT(e,t);const n=vt();typeof t.didRender=="function"&&n&&t.didRender(n),Je.eventEmitter.emit("didRender",n)},GT=()=>To(vt()),ou=()=>{var e;return(e=Jo())===null||e===void 0?void 0:e.click()},FT=()=>{var e;return(e=xn())===null||e===void 0?void 0:e.click()},WT=()=>{var e;return(e=Qn())===null||e===void 0?void 0:e.click()},er=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),nu=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},jT=(e,t,n)=>{nu(e),t.toast||(e.keydownHandler=o=>YT(t,o,n),e.keydownTarget=t.keydownListenerCapture?window:vt(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0)},ki=(e,t)=>{var n;const o=aa();if(o.length){e=e+t,e===-2&&(e=o.length-1),e===o.length?e=0:e===-1&&(e=o.length-1),o[e].focus();return}(n=vt())===null||n===void 0||n.focus()},ru=["ArrowRight","ArrowDown"],zT=["ArrowLeft","ArrowUp"],YT=(e,t,n)=>{e&&(t.isComposing||t.keyCode===229||(e.stopKeydownPropagation&&t.stopPropagation(),t.key==="Enter"?KT(t,e):t.key==="Tab"?XT(t):[...ru,...zT].includes(t.key)?VT(t.key):t.key==="Escape"&&JT(t,e,n)))},KT=(e,t)=>{if(!ri(t.allowEnterKey))return;const n=ci(vt(),t.input);if(e.target&&n&&e.target instanceof HTMLElement&&e.target.outerHTML===n.outerHTML){if(["textarea","file"].includes(t.input))return;ou(),e.preventDefault()}},XT=e=>{const t=e.target,n=aa();let o=-1;for(let c=0;c<n.length;c++)if(t===n[c]){o=c;break}e.shiftKey?ki(o,-1):ki(o,1),e.stopPropagation(),e.preventDefault()},VT=e=>{const t=tl(),n=Jo(),o=xn(),c=Qn();if(!t||!n||!o||!c)return;const s=[n,o,c];if(document.activeElement instanceof HTMLElement&&!s.includes(document.activeElement))return;const C=ru.includes(e)?"nextElementSibling":"previousElementSibling";let i=document.activeElement;if(i){for(let h=0;h<t.children.length;h++){if(i=i[C],!i)return;if(i instanceof HTMLButtonElement&&To(i))break}i instanceof HTMLButtonElement&&i.focus()}},JT=(e,t,n)=>{e.preventDefault(),ri(t.allowEscapeKey)&&n(er.esc)};var Kn={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const QT=()=>{const e=co();Array.from(document.body.children).forEach(n=>{n.contains(e)||(n.hasAttribute("aria-hidden")&&n.setAttribute("data-previous-aria-hidden",n.getAttribute("aria-hidden")||""),n.setAttribute("aria-hidden","true"))})},lu=()=>{Array.from(document.body.children).forEach(t=>{t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")||""),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})},iu=typeof window<"u"&&!!window.GestureEvent,ZT=()=>{if(iu&&!nn(document.body,j.iosfix)){const e=document.body.scrollTop;document.body.style.top=`${e*-1}px`,Dt(document.body,j.iosfix),ep()}},ep=()=>{const e=co();if(!e)return;let t;e.ontouchstart=n=>{t=tp(n)},e.ontouchmove=n=>{t&&(n.preventDefault(),n.stopPropagation())}},tp=e=>{const t=e.target,n=co(),o=ra();return!n||!o||sp(e)||op(e)?!1:t===n||!Hi(n)&&t instanceof HTMLElement&&!tT(t,o)&&t.tagName!=="INPUT"&&t.tagName!=="TEXTAREA"&&!(Hi(o)&&o.contains(t))},sp=e=>e.touches&&e.touches.length&&e.touches[0].touchType==="stylus",op=e=>e.touches&&e.touches.length>1,np=()=>{if(nn(document.body,j.iosfix)){const e=parseInt(document.body.style.top,10);ko(document.body,j.iosfix),document.body.style.top="",document.body.scrollTop=e*-1}},rp=()=>{const e=document.createElement("div");e.className=j["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t};let jn=null;const lp=e=>{jn===null&&(document.body.scrollHeight>window.innerHeight||e==="scroll")&&(jn=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${jn+rp()}px`)},ip=()=>{jn!==null&&(document.body.style.paddingRight=`${jn}px`,jn=null)};function au(e,t,n,o){ai()?rd(e,o):(jg(n).then(()=>rd(e,o)),nu(Je)),iu?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),ca()&&(ip(),np(),lu()),ap()}function ap(){ko([document.documentElement,document.body],[j.shown,j["height-auto"],j["no-backdrop"],j["toast-shown"]])}function mn(e){e=dp(e);const t=Kn.swalPromiseResolve.get(this),n=cp(this);this.isAwaitingPromise?e.isDismissed||(nl(this),t(e)):n&&t(e)}const cp=e=>{const t=vt();if(!t)return!1;const n=Yt.innerParams.get(e);if(!n||nn(t,n.hideClass.popup))return!1;ko(t,n.showClass.popup),Dt(t,n.hideClass.popup);const o=co();return ko(o,n.showClass.backdrop),Dt(o,n.hideClass.backdrop),up(e,t,n),!0};function cu(e){const t=Kn.swalPromiseReject.get(this);nl(this),t&&t(e)}const nl=e=>{e.isAwaitingPromise&&(delete e.isAwaitingPromise,Yt.innerParams.get(e)||e._destroy())},dp=e=>typeof e>"u"?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),up=(e,t,n)=>{var o;const c=co(),s=Kd(t);typeof n.willClose=="function"&&n.willClose(t),(o=Je.eventEmitter)===null||o===void 0||o.emit("willClose",t),s?hp(e,t,c,n.returnFocus,n.didClose):au(e,c,n.returnFocus,n.didClose)},hp=(e,t,n,o,c)=>{Je.swalCloseEventFinishedCallback=au.bind(null,e,n,o,c);const s=function(C){if(C.target===t){var i;(i=Je.swalCloseEventFinishedCallback)===null||i===void 0||i.call(Je),delete Je.swalCloseEventFinishedCallback,t.removeEventListener("animationend",s),t.removeEventListener("transitionend",s)}};t.addEventListener("animationend",s),t.addEventListener("transitionend",s)},rd=(e,t)=>{setTimeout(()=>{var n;typeof t=="function"&&t.bind(e.params)(),(n=Je.eventEmitter)===null||n===void 0||n.emit("didClose"),e._destroy&&e._destroy()})},Xn=e=>{let t=vt();if(t||new si,t=vt(),!t)return;const n=Zn();ai()?oo(Jn()):fp(t,e),Gs(n),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},fp=(e,t)=>{const n=tl(),o=Zn();!n||!o||(!t&&To(Jo())&&(t=Jo()),Gs(n),t&&(oo(t),o.setAttribute("data-button-to-replace",t.className),n.insertBefore(o,t)),Dt([e,n],j.loading))},gp=(e,t)=>{t.input==="select"||t.input==="radio"?mp(e,t):["text","email","number","tel","textarea"].some(n=>n===t.input)&&(oa(t.inputValue)||na(t.inputValue))&&(Xn(Jo()),Ap(e,t))},Tp=(e,t)=>{const n=e.getInput();if(!n)return null;switch(t.input){case"checkbox":return pp(n);case"radio":return Ep(n);case"file":return Cp(n);default:return t.inputAutoTrim?n.value.trim():n.value}},pp=e=>e.checked?1:0,Ep=e=>e.checked?e.value:null,Cp=e=>e.files&&e.files.length?e.getAttribute("multiple")!==null?e.files:e.files[0]:null,mp=(e,t)=>{const n=vt();if(!n)return;const o=c=>{t.input==="select"?bp(n,Ql(c),t):t.input==="radio"&&wp(n,Ql(c),t)};oa(t.inputOptions)||na(t.inputOptions)?(Xn(Jo()),Zr(t.inputOptions).then(c=>{e.hideLoading(),o(c)})):typeof t.inputOptions=="object"?o(t.inputOptions):Ln(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof t.inputOptions}`)},Ap=(e,t)=>{const n=e.getInput();n&&(oo(n),Zr(t.inputValue).then(o=>{n.value=t.input==="number"?`${parseFloat(o)||0}`:`${o}`,Gs(n),n.focus(),e.hideLoading()}).catch(o=>{Ln(`Error in inputValue promise: ${o}`),n.value="",Gs(n),n.focus(),e.hideLoading()}))};function bp(e,t,n){const o=Cn(e,j.select);if(!o)return;const c=(s,C,i)=>{const h=document.createElement("option");h.value=i,Lo(h,C),h.selected=du(i,n.inputValue),s.appendChild(h)};t.forEach(s=>{const C=s[0],i=s[1];if(Array.isArray(i)){const h=document.createElement("optgroup");h.label=C,h.disabled=!1,o.appendChild(h),i.forEach(le=>c(h,le[1],le[0]))}else c(o,i,C)}),o.focus()}function wp(e,t,n){const o=Cn(e,j.radio);if(!o)return;t.forEach(s=>{const C=s[0],i=s[1],h=document.createElement("input"),le=document.createElement("label");h.type="radio",h.name=j.radio,h.value=C,du(C,n.inputValue)&&(h.checked=!0);const Ie=document.createElement("span");Lo(Ie,i),Ie.className=j.label,le.appendChild(h),le.appendChild(Ie),o.appendChild(le)});const c=o.querySelectorAll("input");c.length&&c[0].focus()}const Ql=e=>{const t=[];return e instanceof Map?e.forEach((n,o)=>{let c=n;typeof c=="object"&&(c=Ql(c)),t.push([o,c])}):Object.keys(e).forEach(n=>{let o=e[n];typeof o=="object"&&(o=Ql(o)),t.push([n,o])}),t},du=(e,t)=>!!t&&t.toString()===e.toString(),Ip=e=>{const t=Yt.innerParams.get(e);e.disableButtons(),t.input?uu(e,"confirm"):pa(e,!0)},Rp=e=>{const t=Yt.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?uu(e,"deny"):Ta(e,!1)},_p=(e,t)=>{e.disableButtons(),t(er.cancel)},uu=(e,t)=>{const n=Yt.innerParams.get(e);if(!n.input){Ln(`The "input" parameter is needed to be set when using returnInputValueOn${sa(t)}`);return}const o=e.getInput(),c=Tp(e,n);n.inputValidator?Np(e,c,t):o&&!o.checkValidity()?(e.enableButtons(),e.showValidationMessage(n.validationMessage||o.validationMessage)):t==="deny"?Ta(e,c):pa(e,c)},Np=(e,t,n)=>{const o=Yt.innerParams.get(e);e.disableInput(),Promise.resolve().then(()=>Zr(o.inputValidator(t,o.validationMessage))).then(s=>{e.enableButtons(),e.enableInput(),s?e.showValidationMessage(s):n==="deny"?Ta(e,t):pa(e,t)})},Ta=(e,t)=>{const n=Yt.innerParams.get(e||void 0);n.showLoaderOnDeny&&Xn(xn()),n.preDeny?(e.isAwaitingPromise=!0,Promise.resolve().then(()=>Zr(n.preDeny(t,n.validationMessage))).then(c=>{c===!1?(e.hideLoading(),nl(e)):e.close({isDenied:!0,value:typeof c>"u"?t:c})}).catch(c=>hu(e||void 0,c))):e.close({isDenied:!0,value:t})},ld=(e,t)=>{e.close({isConfirmed:!0,value:t})},hu=(e,t)=>{e.rejectPromise(t)},pa=(e,t)=>{const n=Yt.innerParams.get(e||void 0);n.showLoaderOnConfirm&&Xn(),n.preConfirm?(e.resetValidationMessage(),e.isAwaitingPromise=!0,Promise.resolve().then(()=>Zr(n.preConfirm(t,n.validationMessage))).then(c=>{To(li())||c===!1?(e.hideLoading(),nl(e)):ld(e,typeof c>"u"?t:c)}).catch(c=>hu(e||void 0,c))):ld(e,t)};function Zl(){const e=Yt.innerParams.get(this);if(!e)return;const t=Yt.domCache.get(this);oo(t.loader),ai()?e.icon&&Gs(Jn()):Sp(t),ko([t.popup,t.actions],j.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const Sp=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?Gs(t[0],"inline-block"):eT()&&oo(e.actions)};function fu(){const e=Yt.innerParams.get(this),t=Yt.domCache.get(this);return t?ci(t.popup,e.input):null}function gu(e,t,n){const o=Yt.domCache.get(e);t.forEach(c=>{o[c].disabled=n})}function Tu(e,t){const n=vt();if(!(!n||!e))if(e.type==="radio"){const o=n.querySelectorAll(`[name="${j.radio}"]`);for(let c=0;c<o.length;c++)o[c].disabled=t}else e.disabled=t}function pu(){gu(this,["confirmButton","denyButton","cancelButton"],!1)}function Eu(){gu(this,["confirmButton","denyButton","cancelButton"],!0)}function Cu(){Tu(this.getInput(),!1)}function mu(){Tu(this.getInput(),!0)}function Au(e){const t=Yt.domCache.get(this),n=Yt.innerParams.get(this);Lo(t.validationMessage,e),t.validationMessage.className=j["validation-message"],n.customClass&&n.customClass.validationMessage&&Dt(t.validationMessage,n.customClass.validationMessage),Gs(t.validationMessage);const o=this.getInput();o&&(o.setAttribute("aria-invalid","true"),o.setAttribute("aria-describedby",j["validation-message"]),zd(o),Dt(o,j.inputerror))}function bu(){const e=Yt.domCache.get(this);e.validationMessage&&oo(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),ko(t,j.inputerror))}const zn={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0,topLayer:!1},Mp=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],Op={allowEnterKey:void 0},vp=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],wu=e=>Object.prototype.hasOwnProperty.call(zn,e),Iu=e=>Mp.indexOf(e)!==-1,Ru=e=>Op[e],yp=e=>{wu(e)||ao(`Unknown parameter "${e}"`)},Lp=e=>{vp.includes(e)&&ao(`The parameter "${e}" is incompatible with toasts`)},xp=e=>{const t=Ru(e);t&&Gd(e,t)},_u=e=>{e.backdrop===!1&&e.allowOutsideClick&&ao('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),e.theme&&!["light","dark","auto","minimal","borderless","embed-iframe","bulma","bulma-light","bulma-dark"].includes(e.theme)&&ao(`Invalid theme "${e.theme}"`);for(const t in e)yp(t),e.toast&&Lp(t),xp(t)};function Nu(e){const t=co(),n=vt(),o=Yt.innerParams.get(this);if(!n||nn(n,o.hideClass.popup)){ao("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");return}const c=Dp(e),s=Object.assign({},o,c);_u(s),t.dataset.swal2Theme=s.theme,su(this,s),Yt.innerParams.set(this,s),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const Dp=e=>{const t={};return Object.keys(e).forEach(n=>{Iu(n)?t[n]=e[n]:ao(`Invalid parameter to update: ${n}`)}),t};function Su(){const e=Yt.domCache.get(this),t=Yt.innerParams.get(this);if(!t){Mu(this);return}e.popup&&Je.swalCloseEventFinishedCallback&&(Je.swalCloseEventFinishedCallback(),delete Je.swalCloseEventFinishedCallback),typeof t.didDestroy=="function"&&t.didDestroy(),Je.eventEmitter.emit("didDestroy"),Pp(this)}const Pp=e=>{Mu(e),delete e.params,delete Je.keydownHandler,delete Je.keydownTarget,delete Je.currentInstance},Mu=e=>{e.isAwaitingPromise?(Ri(Yt,e),e.isAwaitingPromise=!0):(Ri(Kn,e),Ri(Yt,e),delete e.isAwaitingPromise,delete e.disableButtons,delete e.enableButtons,delete e.getInput,delete e.disableInput,delete e.enableInput,delete e.hideLoading,delete e.disableLoading,delete e.showValidationMessage,delete e.resetValidationMessage,delete e.close,delete e.closePopup,delete e.closeModal,delete e.closeToast,delete e.rejectPromise,delete e.update,delete e._destroy)},Ri=(e,t)=>{for(const n in e)e[n].delete(t)};var qp=Object.freeze({__proto__:null,_destroy:Su,close:mn,closeModal:mn,closePopup:mn,closeToast:mn,disableButtons:Eu,disableInput:mu,disableLoading:Zl,enableButtons:pu,enableInput:Cu,getInput:fu,handleAwaitingPromise:nl,hideLoading:Zl,rejectPromise:cu,resetValidationMessage:bu,showValidationMessage:Au,update:Nu});const Up=(e,t,n)=>{e.toast?Hp(e,t,n):(Bp(t),$p(t),Gp(e,t,n))},Hp=(e,t,n)=>{t.popup.onclick=()=>{e&&(kp(e)||e.timer||e.input)||n(er.close)}},kp=e=>!!(e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton);let ei=!1;const Bp=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=()=>{},t.target===e.container&&(ei=!0)}}},$p=e=>{e.container.onmousedown=t=>{t.target===e.container&&t.preventDefault(),e.popup.onmouseup=function(n){e.popup.onmouseup=()=>{},(n.target===e.popup||n.target instanceof HTMLElement&&e.popup.contains(n.target))&&(ei=!0)}}},Gp=(e,t,n)=>{t.container.onclick=o=>{if(ei){ei=!1;return}o.target===t.container&&ri(e.allowOutsideClick)&&n(er.backdrop)}},Fp=e=>typeof e=="object"&&e.jquery,id=e=>e instanceof Element||Fp(e),Wp=e=>{const t={};return typeof e[0]=="object"&&!id(e[0])?Object.assign(t,e[0]):["title","html","icon"].forEach((n,o)=>{const c=e[o];typeof c=="string"||id(c)?t[n]=c:c!==void 0&&Ln(`Unexpected type of ${n}! Expected "string" or "Element", got ${typeof c}`)}),t};function jp(...e){return new this(...e)}function zp(e){class t extends this{_main(o,c){return super._main(o,Object.assign({},e,c))}}return t}const Yp=()=>Je.timeout&&Je.timeout.getTimerLeft(),Ou=()=>{if(Je.timeout)return sT(),Je.timeout.stop()},vu=()=>{if(Je.timeout){const e=Je.timeout.start();return ua(e),e}},Kp=()=>{const e=Je.timeout;return e&&(e.running?Ou():vu())},Xp=e=>{if(Je.timeout){const t=Je.timeout.increase(e);return ua(t,!0),t}},Vp=()=>!!(Je.timeout&&Je.timeout.isRunning());let ad=!1;const Bi={};function Jp(e="data-swal-template"){Bi[e]=this,ad||(document.body.addEventListener("click",Qp),ad=!0)}const Qp=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const n in Bi){const o=t.getAttribute(n);if(o){Bi[n].fire({template:o});return}}};class Zp{constructor(){this.events={}}_getHandlersByEventName(t){return typeof this.events[t]>"u"&&(this.events[t]=[]),this.events[t]}on(t,n){const o=this._getHandlersByEventName(t);o.includes(n)||o.push(n)}once(t,n){const o=(...c)=>{this.removeListener(t,o),n.apply(this,c)};this.on(t,o)}emit(t,...n){this._getHandlersByEventName(t).forEach(o=>{try{o.apply(this,n)}catch(c){console.error(c)}})}removeListener(t,n){const o=this._getHandlersByEventName(t),c=o.indexOf(n);c>-1&&o.splice(c,1)}removeAllListeners(t){this.events[t]!==void 0&&(this.events[t].length=0)}reset(){this.events={}}}Je.eventEmitter=new Zp;const eE=(e,t)=>{Je.eventEmitter.on(e,t)},tE=(e,t)=>{Je.eventEmitter.once(e,t)},sE=(e,t)=>{if(!e){Je.eventEmitter.reset();return}t?Je.eventEmitter.removeListener(e,t):Je.eventEmitter.removeAllListeners(e)};var oE=Object.freeze({__proto__:null,argsToParams:Wp,bindClickHandler:Jp,clickCancel:WT,clickConfirm:ou,clickDeny:FT,enableLoading:Xn,fire:jp,getActions:tl,getCancelButton:Qn,getCloseButton:ia,getConfirmButton:Jo,getContainer:co,getDenyButton:xn,getFocusableElements:aa,getFooter:jd,getHtmlContainer:ra,getIcon:Jn,getIconContent:Xg,getImage:Wd,getInputLabel:Vg,getLoader:Zn,getPopup:vt,getProgressSteps:la,getTimerLeft:Yp,getTimerProgressBar:ii,getTitle:Fd,getValidationMessage:li,increaseTimer:Xp,isDeprecatedParameter:Ru,isLoading:Qg,isTimerRunning:Vp,isUpdatableParameter:Iu,isValidParameter:wu,isVisible:GT,mixin:zp,off:sE,on:eE,once:tE,resumeTimer:vu,showLoading:Xn,stopTimer:Ou,toggleTimer:Kp});class nE{constructor(t,n){this.callback=t,this.remaining=n,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date().getTime()-this.started.getTime()),this.remaining}increase(t){const n=this.running;return n&&this.stop(),this.remaining+=t,n&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const yu=["swal-title","swal-html","swal-footer"],rE=e=>{const t=typeof e.template=="string"?document.querySelector(e.template):e.template;if(!t)return{};const n=t.content;return fE(n),Object.assign(lE(n),iE(n),aE(n),cE(n),dE(n),uE(n),hE(n,yu))},lE=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach(o=>{yn(o,["name","value"]);const c=o.getAttribute("name"),s=o.getAttribute("value");!c||!s||(typeof zn[c]=="boolean"?t[c]=s!=="false":typeof zn[c]=="object"?t[c]=JSON.parse(s):t[c]=s)}),t},iE=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach(o=>{const c=o.getAttribute("name"),s=o.getAttribute("value");!c||!s||(t[c]=new Function(`return ${s}`)())}),t},aE=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach(o=>{yn(o,["type","color","aria-label"]);const c=o.getAttribute("type");!c||!["confirm","cancel","deny"].includes(c)||(t[`${c}ButtonText`]=o.innerHTML,t[`show${sa(c)}Button`]=!0,o.hasAttribute("color")&&(t[`${c}ButtonColor`]=o.getAttribute("color")),o.hasAttribute("aria-label")&&(t[`${c}ButtonAriaLabel`]=o.getAttribute("aria-label")))}),t},cE=e=>{const t={},n=e.querySelector("swal-image");return n&&(yn(n,["src","width","height","alt"]),n.hasAttribute("src")&&(t.imageUrl=n.getAttribute("src")||void 0),n.hasAttribute("width")&&(t.imageWidth=n.getAttribute("width")||void 0),n.hasAttribute("height")&&(t.imageHeight=n.getAttribute("height")||void 0),n.hasAttribute("alt")&&(t.imageAlt=n.getAttribute("alt")||void 0)),t},dE=e=>{const t={},n=e.querySelector("swal-icon");return n&&(yn(n,["type","color"]),n.hasAttribute("type")&&(t.icon=n.getAttribute("type")),n.hasAttribute("color")&&(t.iconColor=n.getAttribute("color")),t.iconHtml=n.innerHTML),t},uE=e=>{const t={},n=e.querySelector("swal-input");n&&(yn(n,["type","label","placeholder","value"]),t.input=n.getAttribute("type")||"text",n.hasAttribute("label")&&(t.inputLabel=n.getAttribute("label")),n.hasAttribute("placeholder")&&(t.inputPlaceholder=n.getAttribute("placeholder")),n.hasAttribute("value")&&(t.inputValue=n.getAttribute("value")));const o=Array.from(e.querySelectorAll("swal-input-option"));return o.length&&(t.inputOptions={},o.forEach(c=>{yn(c,["value"]);const s=c.getAttribute("value");if(!s)return;const C=c.innerHTML;t.inputOptions[s]=C})),t},hE=(e,t)=>{const n={};for(const o in t){const c=t[o],s=e.querySelector(c);s&&(yn(s,[]),n[c.replace(/^swal-/,"")]=s.innerHTML.trim())}return n},fE=e=>{const t=yu.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach(n=>{const o=n.tagName.toLowerCase();t.includes(o)||ao(`Unrecognized element <${o}>`)})},yn=(e,t)=>{Array.from(e.attributes).forEach(n=>{t.indexOf(n.name)===-1&&ao([`Unrecognized attribute "${n.name}" on <${e.tagName.toLowerCase()}>.`,`${t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."}`])})},Lu=10,gE=e=>{const t=co(),n=vt();typeof e.willOpen=="function"&&e.willOpen(n),Je.eventEmitter.emit("willOpen",n);const c=window.getComputedStyle(document.body).overflowY;EE(t,n,e),setTimeout(()=>{TE(t,n)},Lu),ca()&&(pE(t,e.scrollbarPadding,c),QT()),!ai()&&!Je.previousActiveElement&&(Je.previousActiveElement=document.activeElement),typeof e.didOpen=="function"&&setTimeout(()=>e.didOpen(n)),Je.eventEmitter.emit("didOpen",n),ko(t,j["no-transition"])},ti=e=>{const t=vt();if(e.target!==t)return;const n=co();t.removeEventListener("animationend",ti),t.removeEventListener("transitionend",ti),n.style.overflowY="auto"},TE=(e,t)=>{Kd(t)?(e.style.overflowY="hidden",t.addEventListener("animationend",ti),t.addEventListener("transitionend",ti)):e.style.overflowY="auto"},pE=(e,t,n)=>{ZT(),t&&n!=="hidden"&&lp(n),setTimeout(()=>{e.scrollTop=0})},EE=(e,t,n)=>{Dt(e,n.showClass.backdrop),n.animation?(t.style.setProperty("opacity","0","important"),Gs(t,"grid"),setTimeout(()=>{Dt(t,n.showClass.popup),t.style.removeProperty("opacity")},Lu)):Gs(t,"grid"),Dt([document.documentElement,document.body],j.shown),n.heightAuto&&n.backdrop&&!n.toast&&Dt([document.documentElement,document.body],j["height-auto"])};var cd={email:(e,t)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function CE(e){e.inputValidator||(e.input==="email"&&(e.inputValidator=cd.email),e.input==="url"&&(e.inputValidator=cd.url))}function mE(e){(!e.target||typeof e.target=="string"&&!document.querySelector(e.target)||typeof e.target!="string"&&!e.target.appendChild)&&(ao('Target parameter is not valid, defaulting to "body"'),e.target="body")}function AE(e){CE(e),e.showLoaderOnConfirm&&!e.preConfirm&&ao(`showLoaderOnConfirm is set to true, but preConfirm is not defined.
showLoaderOnConfirm should be used together with preConfirm, see usage example:
https://sweetalert2.github.io/#ajax-request`),mE(e),typeof e.title=="string"&&(e.title=e.title.split(`
`).join("<br />")),dT(e)}let Ko;var Pl=new WeakMap;class As{constructor(...t){if($g(this,Pl,void 0),typeof window>"u")return;Ko=this;const n=Object.freeze(this.constructor.argsToParams(t));this.params=n,this.isAwaitingPromise=!1,Gg(Pl,this,this._main(Ko.params))}_main(t,n={}){if(_u(Object.assign({},n,t)),Je.currentInstance){const s=Kn.swalPromiseResolve.get(Je.currentInstance),{isAwaitingPromise:C}=Je.currentInstance;Je.currentInstance._destroy(),C||s({isDismissed:!0}),ca()&&lu()}Je.currentInstance=Ko;const o=wE(t,n);AE(o),Object.freeze(o),Je.timeout&&(Je.timeout.stop(),delete Je.timeout),clearTimeout(Je.restoreFocusTimeout);const c=IE(Ko);return su(Ko,o),Yt.innerParams.set(Ko,o),bE(Ko,c,o)}then(t){return Zc(Pl,this).then(t)}finally(t){return Zc(Pl,this).finally(t)}}const bE=(e,t,n)=>new Promise((o,c)=>{const s=C=>{e.close({isDismissed:!0,dismiss:C})};Kn.swalPromiseResolve.set(e,o),Kn.swalPromiseReject.set(e,c),t.confirmButton.onclick=()=>{Ip(e)},t.denyButton.onclick=()=>{Rp(e)},t.cancelButton.onclick=()=>{_p(e,s)},t.closeButton.onclick=()=>{s(er.close)},Up(n,t,s),jT(Je,n,s),gp(e,n),gE(n),RE(Je,n,s),_E(t,n),setTimeout(()=>{t.container.scrollTop=0})}),wE=(e,t)=>{const n=rE(e),o=Object.assign({},zn,t,n,e);return o.showClass=Object.assign({},zn.showClass,o.showClass),o.hideClass=Object.assign({},zn.hideClass,o.hideClass),o.animation===!1&&(o.showClass={backdrop:"swal2-noanimation"},o.hideClass={}),o},IE=e=>{const t={popup:vt(),container:co(),actions:tl(),confirmButton:Jo(),denyButton:xn(),cancelButton:Qn(),loader:Zn(),closeButton:ia(),validationMessage:li(),progressSteps:la()};return Yt.domCache.set(e,t),t},RE=(e,t,n)=>{const o=ii();oo(o),t.timer&&(e.timeout=new nE(()=>{n("timer"),delete e.timeout},t.timer),t.timerProgressBar&&(Gs(o),vo(o,t,"timerProgressBar"),setTimeout(()=>{e.timeout&&e.timeout.running&&ua(t.timer)})))},_E=(e,t)=>{if(!t.toast){if(!ri(t.allowEnterKey)){Gd("allowEnterKey"),ME();return}NE(e)||SE(e,t)||ki(-1,1)}},NE=e=>{const t=Array.from(e.popup.querySelectorAll("[autofocus]"));for(const n of t)if(n instanceof HTMLElement&&To(n))return n.focus(),!0;return!1},SE=(e,t)=>t.focusDeny&&To(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&To(e.cancelButton)?(e.cancelButton.focus(),!0):t.focusConfirm&&To(e.confirmButton)?(e.confirmButton.focus(),!0):!1,ME=()=>{document.activeElement instanceof HTMLElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur()};if(typeof window<"u"&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/(1e3*60*60*24)>3&&setTimeout(()=>{document.body.style.pointerEvents="none";const n=document.createElement("audio");n.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",n.loop=!0,document.body.appendChild(n),setTimeout(()=>{n.play().catch(()=>{})},2500)},500):localStorage.setItem("swal-initiation",`${e}`)}As.prototype.disableButtons=Eu;As.prototype.enableButtons=pu;As.prototype.getInput=fu;As.prototype.disableInput=mu;As.prototype.enableInput=Cu;As.prototype.hideLoading=Zl;As.prototype.disableLoading=Zl;As.prototype.showValidationMessage=Au;As.prototype.resetValidationMessage=bu;As.prototype.close=mn;As.prototype.closePopup=mn;As.prototype.closeModal=mn;As.prototype.closeToast=mn;As.prototype.rejectPromise=cu;As.prototype.update=Nu;As.prototype._destroy=Su;Object.assign(As,oE);Object.keys(qp).forEach(e=>{As[e]=function(...t){return Ko&&Ko[e]?Ko[e](...t):null}});As.DismissReason=er;As.version="11.22.2";const si=As;si.default=si;typeof document<"u"&&function(e,t){var n=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(n),n.styleSheet)n.styleSheet.disabled||(n.styleSheet.cssText=t);else try{n.innerHTML=t}catch{n.innerText=t}}(document,':root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}');const OE=(e,t,n,o)=>{const[c,s]=E.useState([]),[C,i]=E.useState([]),[h,le]=E.useState(!1),Ie=new URLSearchParams(location.search),K=re(V=>V.payload.payloadData),f=re(V=>{var H,ce;return(ce=(H=V==null?void 0:V.userManagement)==null?void 0:H.taskData)==null?void 0:ce.ATTRIBUTE_2}),X=Ie.get("RequestType");return E.useEffect(()=>{let V={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":X||f||(K==null?void 0:K.RequestType)}],systemFilters:null,systemOrders:null,filterString:null};const H=$=>{var u,k;$.statusCode===200&&s((k=(u=$==null?void 0:$.data)==null?void 0:u.result[0])==null?void 0:k.MDG_MAT_DYN_BUTTON_CONFIG)},ce=$=>{console.error($)},J=t.environment==="localhost"?`/${n}/rest/v1/invoke-rules`:`/${n}/v1/invoke-rules`;ze(J,"post",H,ce,V)},[e]),E.useEffect(()=>{const V=oi(ni.CURRENT_TASK,!0,{}),H=(V==null?void 0:V.taskDesc)||(e==null?void 0:e.taskDesc),J=c.filter($=>$.MDG_MAT_DYN_BTN_TASK_NAME===H).sort(($,u)=>{const k=Jc[$.MDG_MAT_DYN_BTN_ACTION_TYPE]??999,F=Jc[u.MDG_MAT_DYN_BTN_ACTION_TYPE]??999;return k-F});i(J),(J.find($=>$.MDG_MAT_DYN_BTN_BUTTON_NAME===o.SEND_BACK)||J.find($=>$.MDG_MAT_DYN_BTN_BUTTON_NAME===o.CORRECTION))&&le(!0)},[c]),{filteredButtons:C,showWfLevels:h}},vE=jf(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),ZE=e=>{var ar,cr,dr,ur,hr,fr,gr,Tr,pr,Er,Cr,mr,Ar,br,wr,Ir,Rr,_r,Nr;const t=vE(),{customError:n}=Qo(),o=po(),{getDynamicWorkflowDT:c}=qd(),{fetchMaterialFieldConfig:s}=vd(),{getNextDisplayDataForCreate:C}=xd(),{fetchValuationClassData:i}=Sg(),h=re(r=>r.payload.payloadData),le=h==null?void 0:h.RequestType,Ie=re(r=>r.request.salesOrgDTData),K=re(r=>r.applicationConfig),f=re(r=>r.paginationData),X=re(r=>r.payload),V=re(r=>r.request.requestHeader),H=re(r=>r.request.materialRows),ce=re(r=>r.payload.payloadData),J=re(r=>{var d;return((d=r.AllDropDown)==null?void 0:d.dropDown)||{}}),$=re(r=>r.tabsData.allTabsData);re(r=>r.userManagement.userData);let u=re(r=>r.userManagement.roles),k=re(r=>r.userManagement.taskData);const F=re(r=>r.tabsData.allMaterialFieldConfigDT),p=rn(),T=new URLSearchParams(p.search),S=T.get("reqBench"),g=T.get("RequestId"),[N,L]=E.useState(!1),[m,O]=E.useState(!1),[P,q]=E.useState(0),[U,R]=E.useState(null),[v,b]=E.useState(null),ue="Basic Data",[B,Ne]=E.useState([ue]),[A,Ae]=E.useState({data:{},isVisible:!1}),[Q,G]=E.useState(H||[]),Pe=re(r=>r.selectedSections.selectedSections),[$e,Be]=E.useState(!!(Q!=null&&Q.length)),[bs,us]=E.useState(!1),[Fs,Ws]=E.useState(!1),[ws,Qs]=E.useState(""),{fetchTabSpecificData:Is}=Lg(),[js,Zs]=E.useState([]),[os,Ht]=E.useState(0),[Ft,Pt]=E.useState(null),[ns,Ye]=E.useState(!1),[yt,Et]=E.useState(!0),[_s,Vt]=E.useState(Q.length+1),[be,kt]=E.useState(0),[rs,ls]=E.useState(H.length>0),[$t,is]=E.useState({}),[ot,Ct]=E.useState({}),[as,xs]=E.useState(0),[Wt,uo]=E.useState([]),[no,Le]=E.useState({}),[mt,hs]=E.useState([]),[Ds,zs]=E.useState(!1),[Ns,Ss]=E.useState(""),[Jt,Ms]=E.useState("Basic Data"),[Ys,nt]=E.useState(!1);let Qt={id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null};const[ke,dt]=E.useState([Qt]),[Zt,z]=E.useState(!1),[Se,ne]=E.useState(null),[ge,xe]=E.useState("yes"),[pe,Ke]=E.useState([]),[_e,It]=E.useState(null),Gt=(ar=X==null?void 0:X[_e])==null?void 0:ar.headerData,[ut,pt]=E.useState("success"),[Ut,Kt]=E.useState(!1),[Ps,qs]=E.useState([]),[eo,lt]=E.useState(""),[qt,ee]=E.useState(""),[we,M]=E.useState(""),Me=re(r=>r.tabsData.matViews),{checkValidation:Xe}=Ng(X,F,B),{t:oe}=Qr(),et=[{region:"US",temp:"MIDDLE EAST HUB"},{region:"US",temp:"SOUTHERN HUB"},{region:"EUR",temp:"NORTH HUB"},{region:"EUR",temp:"CENTRAL HUB"},{region:"EUR",temp:"WEST HUB"}],[Oe,qe]=E.useState(null),[Ze,he]=E.useState(""),[Nt,Us]=E.useState(""),es=E.useRef(ke),[Hs,Os]=E.useState(!1),fs=(cr=X==null?void 0:X[_e])==null?void 0:cr.payloadData,{fetchDataAndDispatch:Eo}=Yi(),cs=["Sales Org","Plant","Distribution Channel","Storage Location","Warehouse"],[st,ts]=E.useState({}),[ho,fo]=E.useState(!1),[Co,mo]=E.useState(0),[Ks,Rt]=E.useState({"Material No":!1}),{getContryBasedOnPlant:Ao}=xg({doAjax:ze,customError:n,fetchDataAndDispatch:Eo,destination_MaterialMgmt:Re}),gs=["BOM","Source List","PIR"],[bo,wo]=E.useState([]),{filteredButtons:Io,showWfLevels:Ro}=OE(k,K,qo,Bs),_o=ji(Io,[Oo.HANDLE_SUBMIT_FOR_APPROVAL,Oo.HANDLE_SAP_SYNDICATION,Oo.HANDLE_SUBMIT_FOR_REVIEW]);E.useEffect(()=>{var r,d,w,_,y,D,x,W,Ce,ie,de,Te,He,rt;if(G(H),ls((H==null?void 0:H.length)>0),(H==null?void 0:H.length)>0&&g){It((r=H==null?void 0:H[0])==null?void 0:r.id),M((d=H==null?void 0:H[0])==null?void 0:d.materialNumber),Zo((_=(w=H==null?void 0:H[0])==null?void 0:w.materialType)==null?void 0:_.code),kt(0),Ms(Y.BASIC_DATA),Ne((D=(y=H==null?void 0:H[0])==null?void 0:y.views)!=null&&D.length?(x=H==null?void 0:H[0])==null?void 0:x.views:[ue]);const ft=nf(X),jt=rf(ft);let ss=JSON.parse(JSON.stringify(jt));o(lf(ss)),o(yi({keyName:"selectedMaterialID",data:(W=H==null?void 0:H[0])==null?void 0:W.id})),(de=(ie=X==null?void 0:X[(Ce=H==null?void 0:H[0])==null?void 0:Ce.id])==null?void 0:ie.Tochildrequestheaderdata)!=null&&de.ChildRequestId&&o(yi({keyName:"childRequestId",data:(rt=(He=X==null?void 0:X[(Te=H==null?void 0:H[0])==null?void 0:Te.id])==null?void 0:He.Tochildrequestheaderdata)==null?void 0:rt.ChildRequestId}))}},[H]),E.useEffect(()=>{var r,d,w;(r=H==null?void 0:H[0])!=null&&r.materialType&&(tr((d=H==null?void 0:H[0])==null?void 0:d.materialType),Dn({row:H[0]}),Mn(H)&&(Et(!1),Be(!1))),H!=null&&H.length&&xs((w=H==null?void 0:H.at(-1))==null?void 0:w.lineNumber),o(jo({keyName:"VarOrdUn",data:af}))},[]),E.useEffect(()=>{const r=async()=>{var d,w;try{const _=await c(le,h==null?void 0:h.Region,"",(w=(d=X[_e])==null?void 0:d.Tochildrequestheaderdata)==null?void 0:w.MaterialGroupType,k==null?void 0:k.ATTRIBUTE_3);wo(_)}catch(_){n(_)}};le&&(h!=null&&h.Region)&&_e&&(k!=null&&k.ATTRIBUTE_3)&&r()},[le,h==null?void 0:h.Region,_e,k==null?void 0:k.ATTRIBUTE_3]),E.useEffect(()=>{ge==="no"&&(ts({}),ne(null),Pt(null))},[ge]),E.useEffect(()=>{var r,d,w,_,y,D;_e&&($!=null&&$[Y.BASIC_DATA])&&(r=X[_e])!=null&&r.headerData.refMaterialData&&!((_=(w=(d=X[_e])==null?void 0:d.payloadData)==null?void 0:w["Basic Data"])!=null&&_.basic)&&Z((D=(y=X[_e])==null?void 0:y.headerData)==null?void 0:D.refMaterialData)},[_e,$]),E.useEffect(()=>{(Q==null?void 0:Q.length)===0&&Be(!1)},[Q]),E.useEffect(()=>{Se&&(Ge(Se==null?void 0:Se.code,"extended"),ts(r=>({...r,[ae.SALES_ORG]:null})))},[Se]),E.useEffect(()=>{var r,d,w,_,y,D,x,W;if(_e&&((d=(r=X[_e])==null?void 0:r.headerData)!=null&&d.materialType)){let Ce=(w=X[_e])==null?void 0:w.headerData;if(Me&&Me[(_=Ce==null?void 0:Ce.materialType)==null?void 0:_.code]&&((y=Ce==null?void 0:Ce.views)==null?void 0:y.length)<2){const ie=(h==null?void 0:h.Region)==="EUR"?((x=Me[(D=Ce==null?void 0:Ce.materialType)==null?void 0:D.code])==null?void 0:x.filter(de=>de!==Y.WAREHOUSE))||[]:Me[(W=Ce==null?void 0:Ce.materialType)==null?void 0:W.code]||[];hs(ie),Ne(ie),vs({id:_e,field:"views",value:ie})}}},[Me,_e,(ur=(dr=X[_e])==null?void 0:dr.headerData)==null?void 0:ur.materialType]),E.useEffect(()=>{st[ae.SALES_ORG]&&(Qe(),ts(r=>({...r,[ae.DIST_CHNL]:null,[ae.PLANT]:null})))},[st[ae.SALES_ORG]]);const Z=r=>{var w,_,y,D;(y=Object.keys((_=(w=r==null?void 0:r.copyPayload)==null?void 0:w.payloadData["Basic Data"])==null?void 0:_.basic))==null||y.forEach(x=>{var Ce,ie,de;let W=x==="Division"?h==null?void 0:h.Division:cf(x,(de=(ie=(Ce=r==null?void 0:r.copyPayload)==null?void 0:Ce.payloadData["Basic Data"])==null?void 0:ie.basic)==null?void 0:de[x],$["Basic Data"]);o(kc({materialID:_e,viewID:"Basic Data",itemID:"basic",keyName:x,data:W}))});let d=(D=r==null?void 0:r.copyPayload)==null?void 0:D.unitsOfMeasureData;if(d!=null&&d.length){let x=[];d==null||d.forEach(W=>{x.push({...W,id:(W==null?void 0:W.id)||x.length+1})}),o(Bc({materialID:_e,data:x}))}},Ge=(r,d)=>{const w=y=>{Rt(D=>({...D,"Sales Org":!1})),(y==null?void 0:y.statusCode)===zt.STATUS_200&&Ct(d==="notExtended"?D=>({...D,"Sales Org":y.body}):D=>({...D,"Sales Org":(y==null?void 0:y.body.length)>0?y.body:[]}))},_=()=>{Rt(y=>({...y,"Sales Org":!1}))};Rt(y=>({...y,"Sales Org":!0})),ze(`/${Re}/data/${d==="notExtended"?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${r}&region=${h==null?void 0:h.Region}`,"get",w,_)},Fe=(r,d,w)=>{Rt(x=>({...x,Plant:!0}));const _=x=>{Rt(W=>({...W,Plant:!1})),(x==null?void 0:x.statusCode)===zt.STATUS_200&&Ct(d==="notExtended"?W=>({...W,Plant:x.body}):W=>({...W,Plant:(x==null?void 0:x.body.length)>0?x.body:[]}))},y=()=>{Rt(x=>({...x,Plant:!1}))},D=w?`&salesOrg=${w.code}`:"";ze(`/${Re}/data/${d==="notExtended"?"getPlantNotExtended":"getPlantExtended"}?materialNo=${r}&region=${h==null?void 0:h.Region}${D}`,"get",_,y)},Ve=(r,d,w)=>{Rt(x=>({...x,Warehouse:!0}));const _=x=>{Rt(W=>({...W,Warehouse:!1})),(x==null?void 0:x.statusCode)===zt.STATUS_200&&Ct(d==="notExtended"?W=>({...W,Warehouse:x.body}):W=>({...W,Warehouse:(x==null?void 0:x.body.length)>0?x.body:[]}))},y=()=>{Rt(x=>({...x,Warehouse:!1}))},D=w?`&plant=${w.code}`:"";ze(`/${Re}/data/${d==="notExtended"?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${r}&region=${h==null?void 0:h.Region}${D}`,"get",_,y)},fe=(r,d,w)=>{Rt(D=>({...D,"Storage Location":!0}));const _=D=>{Rt(x=>({...x,"Storage Location":!1})),(D==null?void 0:D.statusCode)===zt.STATUS_200&&Ct(x=>{var W;return{...x,[(W=ae)==null?void 0:W.STORAGE_LOC]:D.body||[]}})},y=D=>{n(D),Rt(x=>({...x,"Storage Location":!1}))};ze(`/${Re}/data/getStorageLocationExtended?plant=${d==null?void 0:d.code}&materialNo=${r}&region=${h==null?void 0:h.Region}&salesOrg=${w==null?void 0:w.code}`,"get",_,y)},Qe=()=>{var w;Rt(_=>({..._,"Distribution Channel":!0}));const r=_=>{Rt(y=>({...y,"Distribution Channel":!1})),(_==null?void 0:_.statusCode)===zt.STATUS_200&&Ct(y=>{var D;return{...y,[(D=ae)==null?void 0:D.DIST_CHNL]:_.body&&(_==null?void 0:_.body)}})},d=_=>{n(_),Rt(y=>({...y,"Distribution Channel":!1}))};ze(`/${Re}/data/getDistributionChannelExtended?materialNo=${Se==null?void 0:Se.code}&salesOrg=${(w=st[ae.SALES_ORG])==null?void 0:w.code}`,"get",r,d)};E.useEffect(()=>{["Mrp Profile"].forEach(El),(H==null?void 0:H.length)===0&&(le===I.CREATE||le===I.CREATE_WITH_UPLOAD)&&z(!0),Ee()},[]),E.useEffect(()=>{var d,w,_,y,D,x,W,Ce,ie,de,Te,He,rt,ft,jt,ss,bt,Xs,No,So,Bo,$o;es.current=ke,ke.some(ro=>{var Go,Fo,Wo;return((Go=ro==null?void 0:ro.salesOrg)==null?void 0:Go.code)&&!((Wo=(Fo=ro==null?void 0:ro.dc)==null?void 0:Fo.value)!=null&&Wo.code)})?Os(!1):(w=(d=ke[0])==null?void 0:d.salesOrg)!=null&&w.code&&((D=(y=(_=ke[0])==null?void 0:_.dc)==null?void 0:y.value)!=null&&D.code)&&((Ce=(W=(x=ke[0])==null?void 0:x.plant)==null?void 0:W.value)!=null&&Ce.code)&&((Te=(de=(ie=ke[0])==null?void 0:ie.sloc)==null?void 0:de.value)!=null&&Te.code||!((jt=(rt=(He=X[_e])==null?void 0:He.headerData)==null?void 0:rt.views)!=null&&jt.includes((ft=Y)==null?void 0:ft.STORAGE)))&&((Xs=(bt=(ss=ke[0])==null?void 0:ss.warehouse)==null?void 0:bt.value)!=null&&Xs.code||(h==null?void 0:h.Region)==="EUR"||!(($o=(So=(No=X[_e])==null?void 0:No.headerData)==null?void 0:So.views)!=null&&$o.includes((Bo=Y)==null?void 0:Bo.WAREHOUSE)))?Os(!0):Os(!1)},[ke]),E.useEffect(()=>{Be(!0),Et(!0)},[(hr=X[_e])==null?void 0:hr.headerData,(fr=X[_e])==null?void 0:fr.payloadData]),E.useEffect(()=>{var d;let r=(d=X[_e])==null?void 0:d.headerData;(r!=null&&r.Bom||r!=null&&r.sourceList||r!=null&&r.PIR)&&ht(r)},[(Tr=(gr=X[_e])==null?void 0:gr.headerData)==null?void 0:Tr.Bom,(Er=(pr=X[_e])==null?void 0:pr.headerData)==null?void 0:Er.sourceList,(mr=(Cr=X[_e])==null?void 0:Cr.headerData)==null?void 0:mr.PIR]);const ht=r=>{var y,D;const d={Bom:"BOM",sourceList:"Source List",PIR:"PIR"},w=((D=(y=Object.keys(d))==null?void 0:y.filter(x=>r==null?void 0:r[x]))==null?void 0:D.map(x=>d[x]))||[],_=B==null?void 0:B.filter(x=>!(gs!=null&&gs.includes(x)));Ne([..._,...w]),vs({id:_e,field:$r.VIEWS,value:[..._,...w]})},it=(r,d="",w)=>new Promise((_,y)=>{var de;const D=[{materialNo:r,requestNo:d||(V==null?void 0:V.requestId)}],x=Te=>{var He;(He=Te==null?void 0:Te.body)!=null&&He.length?(Kt(!0),lt(`Duplicate material number ${Te.body[0].split("$^$")[0]} (${Te.body[0].split("$^$")[1]})`),pt("error"),_(!0)):_(!1)},W=Te=>{n(Te),_(!1)};let Ce=0;Object.keys(X).forEach((Te,He)=>{var rt,ft;(Te.includes("-")||/\d/.test(Te))&&((ft=(rt=X[Te])==null?void 0:rt.headerData)==null?void 0:ft.materialNumber)===r&&Ce++});let ie=0;Object.keys(X).forEach(Te=>{var He,rt;(Te.includes("-")||/\d/.test(Te))&&((rt=(He=X[Te])==null?void 0:He.headerData)==null?void 0:rt.globalMaterialDescription)===w&&ie++}),Ce>1?(Kt(!0),lt(`${Vo.DUPLICATE_MATERIAL}${r}`),pt("error"),_(!0)):ie>1?(Kt(!0),lt(`${Vo.DUPLICATE_MATERIAL_DESCRIPTION}${w}`),pt("error"),_(!0)):ze(`/${Re}${(de=ye.MASS_ACTION)==null?void 0:de.MAT_NO_DUPLICATE_CHECK}`,"post",x,W,D)}),_t=async()=>{let r=[...Q],d=!0;return he(!0),Us(Rd.VALIDATING_MATS),new Promise(async(w,_)=>{for(let D=0;D<(Q==null?void 0:Q.length);D++){const x=Q[D],{missingFields:W,viewType:Ce,isValid:ie,plant:de=[]}=Xe(x.id,(x==null?void 0:x.orgData)||[],!1,!1,!1);if(qs(de),ie){let Te=!1;ie&&(!g||x!=null&&x.isMatNoChanged)&&(Te=await it(x.materialNumber,g,x==null?void 0:x.globalMaterialDescription)),Te&&(d=!1),r=r==null?void 0:r.map(He=>He.id===x.id?{...He,validated:!Te}:He),o(Po(r))}else{if(d=!1,r=r.map(Te=>Te.id===x.id?{...Te,validated:!1}:Te),o(Po(r)),(W==null?void 0:W.length)>0){if(pt("error"),typeof W=="object"&&!Array.isArray(W)){const Te=Object.entries(W).map(([He,rt])=>`Combination ${He}: ${rt.join(", ")}`);lt(`Line No ${x.lineNumber} : Please fill all the Mandatory fields in ${Ce||""}: ${Te.join(" | ")}`)}else lt(`Line No ${x.lineNumber} : Please fill all the Mandatory fields in ${Ce||""}: ${W.join(", ")}`);Kt(!0)}break}}d?w(!0):_(),he(!1);const y=Mn(r);Be(!y),Et(!y)})},Bt=r=>{var d,w;if(r){let _=JSON.parse(JSON.stringify(((w=(d=X==null?void 0:X[_e])==null?void 0:d.headerData)==null?void 0:w.calledMrpCodes)||[]))||[];r.forEach((D,x)=>{var W,Ce,ie,de,Te,He,rt,ft,jt;(W=D==null?void 0:D.mrpProfile)!=null&&W.code&&!((Te=(ie=(Ce=X==null?void 0:X[_e])==null?void 0:Ce.headerData)==null?void 0:ie.calledMrpCodes)!=null&&Te.includes((de=D==null?void 0:D.mrpProfile)==null?void 0:de.code))&&(We((rt=(He=D==null?void 0:D.plant)==null?void 0:He.value)==null?void 0:rt.code,(ft=D==null?void 0:D.mrpProfile)==null?void 0:ft.code),_.push((jt=D==null?void 0:D.mrpProfile)==null?void 0:jt.code))}),o(Gr({materialID:_e,keyName:"calledMrpCodes",data:_}));const y=Q==null?void 0:Q.map(D=>D.id===_e?{...D,calledMrpCodes:_}:D);o(Po(y))}},We=(r,d,w)=>{var x;const _={mrpProfile:d},y=W=>{W.body[0]&&Object.keys(W==null?void 0:W.body[0]).filter(ie=>W==null?void 0:W.body[0][ie]).forEach(ie=>{at(r,ie,W==null?void 0:W.body[0][ie],Y.MRP)})},D=W=>{n(W)};ze(`/${Re}${(x=ye.MASS_ACTION)==null?void 0:x.MRP_DEFAULT_VALUES}`,"post",y,D,_)},at=(r,d,w,_)=>{o(kc({materialID:_e||"",keyName:d||"",data:w??null,viewID:_,itemID:r}))},Ee=()=>{let r={decisionTableId:null,decisionTableName:"MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(h==null?void 0:h.Region)||Yo.US}],systemFilters:null,systemOrders:null,filterString:null};const d=_=>{var y,D;if(_.statusCode===zt.STATUS_200){let x=(D=(y=_==null?void 0:_.data)==null?void 0:y.result[0])==null?void 0:D.MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING;ee(x);let W=[];x==null||x.map(ie=>{let de={};de.code=ie.MDG_MAT_SALES_ORG,de.desc=ie.MDG_MAT_SALES_ORG_DESC,W.push(de)});const Ce=Object.values(W.reduce((ie,de)=>{const Te=`${de.code}-${de.desc}`;return ie[Te]=de,ie},{}));Ct(ie=>({...ie,"Sales Organization":Ce==null?void 0:Ce.sort((de,Te)=>de.code-Te.code)}))}},w=_=>{n(_)};K.environment==="localhost"?ze(`/${qo}${ye.INVOKE_RULES.LOCAL}`,"post",d,w,r):ze(`/${qo}${ye.INVOKE_RULES.PROD}`,"post",d,w,r)},je=(r,d,w,_)=>{var D;let y=JSON.parse(JSON.stringify(es.current));if(r==="Sales Organization"){let x=(D=qt==null?void 0:qt.filter(W=>W.MDG_MAT_SALES_ORG===(d==null?void 0:d.code)))==null?void 0:D.map(W=>{let Ce={};return Ce.code=W.MDG_MAT_PLANT,Ce.desc=W.MDG_MAT_PLANT_DESC,Ce});x=x==null?void 0:x.filter((W,Ce,ie)=>Ce===ie.findIndex(de=>de.code===W.code)),y[w].plant.options=x==null?void 0:x.sort((W,Ce)=>W.code-Ce.code),y[w].plant.value=null,y[w].dc.value=null,y[w].sloc.value=null,y[w].salesOrg=d,y[w].warehouse.value=null}else if(r==="plant"){let x=qt==null?void 0:qt.filter(ie=>{var de;return ie.MDG_MAT_SALES_ORG===((de=_==null?void 0:_.salesOrg)==null?void 0:de.code)&&ie.MDG_MAT_PLANT===(d==null?void 0:d.code)}),W=x==null?void 0:x.map(ie=>{let de={};if(ie.MDG_MAT_STORAGE_LOCATION)return de.code=ie.MDG_MAT_STORAGE_LOCATION,de.desc=ie.MDG_MAT_STORE_LOC_DESC,de}).filter(Boolean),Ce=x==null?void 0:x.map(ie=>ie.MDG_MAT_WAREHOUSE?{code:ie.MDG_MAT_WAREHOUSE,desc:ie.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);y[w].plant.value=d,y[w].sloc.value=null,y[w].warehouse.value=null,y[w].sloc.options=W==null?void 0:W.filter((ie,de,Te)=>de===Te.findIndex(He=>He.code===ie.code)),y[w].warehouse.options=Ce==null?void 0:Ce.filter((ie,de,Te)=>de===Te.findIndex(He=>He.code===ie.code))}dt(y)};E.useEffect(()=>{Mn(H)&&(H!=null&&H.length)||S||g?(e.setCompleted([!0,!0]),e==null||e.setIsAttachmentTabEnabled(!0)):(e.setCompleted([!0,!1]),e==null||e.setIsAttachmentTabEnabled(!1))},[H]);const Lt=as+10,zo=()=>{var w,_;const r=Ls(),d={id:r,included:!0,lineNumber:Lt,industrySector:(w=$c)==null?void 0:w.DEFAULT_IND_SECTOR,materialType:((_=ce==null?void 0:ce.MatlType)==null?void 0:_.code)??"",materialNumber:(Se==null?void 0:Se.code)||"",globalMaterialDescription:"",Bom:!1,sourceList:!1,PIR:!1,views:"",orgData:"",validated:Ai.default,withReference:ge};o(bi({materialID:r,data:d})),o(Po([...Q,d])),Vt(_s+1),xs(Lt),ls(!0),Be(!0),Et(!0),Ne([ue]),It(r),Zo("")},In=()=>{z(!1),(h==null?void 0:h.RequestType)==="Create"?zo():(h==null?void 0:h.RequestType)==="Change"&&us(!0)},Rn=()=>{Pn()},an=(r="",d=!0)=>{var D,x;const w={materialNo:r??"",salesOrg:((D=Ie==null?void 0:Ie.uniqueSalesOrgList)==null?void 0:D.map(W=>W.code).join("$^$"))||"",top:500,skip:d?0:os,matlType:((x=st==null?void 0:st["Material Type"])==null?void 0:x.code)??""};Rt(W=>({...W,"Material No":!0}));const _=W=>{(W==null?void 0:W.statusCode)===zt.STATUS_200&&(W!=null&&W.body)&&Zs(d?W==null?void 0:W.body:Ce=>[...Ce,...W==null?void 0:W.body]),Ye(!1),Rt(Ce=>({...Ce,"Material No":!1}))},y=()=>{Ye(!1),Rt(W=>({...W,"Material No":!1}))};Ye(!0),ze(`/${Re}/data/getSearchParamsMaterialNo`,"post",_,y,w)},tr=r=>{const d=_=>{(_==null?void 0:_.statusCode)===zt.STATUS_200&&uo(_==null?void 0:_.body)},w=_=>{console.error(_,"while fetching the validation data of material number")};ze(`/${Re}/data/getNumberRangeForMaterialType?materialType=${r==null?void 0:r.code}`,"get",d,w)};E.useEffect(()=>{var r;(r=st==null?void 0:st["Material Type"])!=null&&r.code&&(an(),ne(null),Pt(null))},[(Ar=st==null?void 0:st["Material Type"])==null?void 0:Ar.code]);const rl=((br=Wt==null?void 0:Wt[0])==null?void 0:br.External)==="X",ll=((wr=Wt==null?void 0:Wt[1])==null?void 0:wr.External)==="X",il=Wt==null?void 0:Wt.some(r=>r.ExtNAwock==="X"),al=(r=>{const d=new Set;let w=null;r==null||r.forEach(y=>{y.External==="X"&&y.ExtNAwock==="X"?(d.add(`External Number Range: Allowed (${y.FromNumber}-${y.ToNumber})`),d.add("Ext W/O Check: Allowed")):y.External!=="X"&&y.ExtNAwock==="X"?(d.add("Internal Number Range: Allowed"),d.add("Ext W/O Check: Allowed")):y.External==="X"&&y.ExtNAwock!=="X"?(d.add(`External Number Range: Allowed (${y.FromNumber}-${y.ToNumber})`),w="Ext W/O Check: Not Allowed"):y.External!=="X"&&y.ExtNAwock!=="X"&&(d.add("Internal Number Range: Allowed"),w="Ext W/O Check: Not Allowed")});const _=Array.from(d);return w&&_.push(w),_.length===0?a(wt,{children:oe("Please select Material type")}):_.map((y,D)=>a("div",{children:a(wt,{children:y})},D))})(Wt);function Zo(r){var _;const d=(h==null?void 0:h.Region)||Yo.US;if(!F.some(y=>y[d]&&y[d][r])&&r)s(r,d);else if(!r)o(Li({}));else{const y=F==null?void 0:F.find(D=>(D==null?void 0:D[d])&&(D==null?void 0:D[d][r]));y&&o(Li((_=y[d][r])==null?void 0:_.allfields))}r&&i(r)}const vs=r=>{const{id:d,field:w,value:_}=r;let y=Q.map(D=>D.id===d?{...D,[w]:_}:D);Le({...no,[w]:_}),w===$r.MATERIALTYPE&&(tr(_),Ne([ue]),df([Qt]),o(Gr({materialID:d,keyName:"views",data:[ue]})),o(Gr({materialID:d,keyName:"orgData",data:""})),y=y.map(D=>D.id===d?{...D,orgData:"",Bom:!1,sourceList:!1,PIR:!1}:D),Zo(_==null?void 0:_.code)),w===$r.INCLUDED&&(Mn(y)?(Be(!1),Et(!1)):(Be(!0),Et(!0))),w===$r.VIEWS&&(Be(!0),Et(!0)),G(y),o(Gr({materialID:d,keyName:w,data:_})),o(Po(y))},Dn=r=>{var d,w,_,y,D,x,W,Ce,ie,de,Te,He,rt;It(r.row.id),M(r.row.materialNumber),Zo((w=(d=r==null?void 0:r.row)==null?void 0:d.materialType)==null?void 0:w.code),hs((h==null?void 0:h.Region)==="EUR"?((D=Me[(y=(_=r==null?void 0:r.row)==null?void 0:_.materialType)==null?void 0:y.code])==null?void 0:D.filter(ft=>ft!==Y.WAREHOUSE))||[]:Me[(W=(x=r==null?void 0:r.row)==null?void 0:x.materialType)==null?void 0:W.code]||[]),Ne((ie=(Ce=r==null?void 0:r.row)==null?void 0:Ce.views)!=null&&ie.length?(de=r.row)==null?void 0:de.views:[ue]),dt((He=(Te=r==null?void 0:r.row)==null?void 0:Te.orgData)!=null&&He.length?(rt=r.row)==null?void 0:rt.orgData:[Qt]),kt(0),Ms("Basic Data")},cl=()=>{Ws(!0)},Pn=()=>{Ws(!1)},qn=(r,d)=>{d==="backdropClick"||d==="escapeKeyDown"||nt(!1)},dl=()=>Ne(mt||[ue]),ul=()=>{if(z(!1),ge==="yes")if(pe!=null&&pe.length){let r=[...Q];pe==null||pe.forEach(d=>{var x,W;const w=Ls();let _=JSON.parse(JSON.stringify(d));_!=null&&_.refMaterialData&&delete _.refMaterialData;let y=JSON.parse(JSON.stringify((x=X==null?void 0:X[d.id])==null?void 0:x.payloadData));_.id=w,_.lineNumber=Lt,_.globalMaterialDescription="",_.materialNumber="",_.validated=Ai.default,o(bi({materialID:w,data:_,payloadData:y})),r.push(_),G(r),o(Po(r)),Vt(_s+1),xs(Lt),ls(!0),Be(!0),Et(!0);let D=(W=X==null?void 0:X[d.id])==null?void 0:W.unitsOfMeasureData;if(D!=null&&D.length){let Ce=[];D==null||D.forEach(ie=>{var de,Te,He;Ce.push({...ie,eanUpc:"",eanCategory:"",length:"",width:"",height:"",volume:"",grossWeight:"",netWeight:"",eanCategory:(h==null?void 0:h.Region)===((de=Yo)==null?void 0:de.US)?ie==null?void 0:ie.EanCat:"",eanUpc:(ie==null?void 0:ie.EanCat)==="MB"&&(h==null?void 0:h.Region)===((Te=Yo)==null?void 0:Te.US)||(h==null?void 0:h.Region)===((He=Yo)==null?void 0:He.EUR)?"":ie==null?void 0:ie.EanUpc,id:(ie==null?void 0:ie.id)||Ce.length+1})}),o(Bc({materialID:w,data:Ce}))}}),Ke([])}else Se&&hl();else In()},hl=()=>{var _,y,D,x,W,Ce,ie;he(!0);let r={material:Se==null?void 0:Se.code,wareHouseNumber:(_=st==null?void 0:st.Warehouse)==null?void 0:_.code,storageLocation:(y=st==null?void 0:st["Storage Location"])==null?void 0:y.code,salesOrg:(D=st==null?void 0:st["Sales Org"])==null?void 0:D.code,distributionChannel:(x=st==null?void 0:st["Distribution Channel"])==null?void 0:x.code,valArea:(W=st==null?void 0:st.Plant)==null?void 0:W.code,plant:(Ce=st==null?void 0:st.Plant)==null?void 0:Ce.code};const d=de=>{var Te,He,rt,ft,jt,ss,bt,Xs,No,So,Bo,$o,ro,Go,Fo,Wo,cn,dn,un,hn,ps,xo,lo,go,fn;if(he(!1),ts({}),de!=null&&de.body[0]){Wc(de==null?void 0:de.body,h);let gn=[...Q];const en=Ls();let xt={};xt.id=en,xt.included=!0,xt.lineNumber=Lt,xt.globalMaterialDescription="",xt.materialType={code:((Te=de.body[0])==null?void 0:Te.MatlType)||"",desc:((rt=(He=J==null?void 0:J.MatlType)==null?void 0:He.find(ks=>{var Vs;return ks.code===((Vs=de.body[0])==null?void 0:Vs.MatlType)}))==null?void 0:rt.desc)||""},xt.industrySector={code:((ft=de.body[0])==null?void 0:ft.IndSector)||"",desc:((ss=(jt=J==null?void 0:J.IndSector)==null?void 0:jt.find(ks=>{var Vs;return ks.code===((Vs=de.body[0])==null?void 0:Vs.IndSector)}))==null?void 0:ss.desc)||""},xt.materialNumber="",xt.Bom=((bt=de.body[0])==null?void 0:bt.Bom)||"",xt.Category=(Xs=de.body[0])!=null&&Xs.Category?{code:((No=de.body[0])==null?void 0:No.Category)||"",desc:((Bo=(So=mf)==null?void 0:So.find(ks=>{var Vs;return ks.code===((Vs=de.body[0])==null?void 0:Vs.Category)}))==null?void 0:Bo.desc)||""}:"",xt.Uom=($o=de.body[0])!=null&&$o.Uom?{code:((ro=de.body[0])==null?void 0:ro.Uom)||"",desc:((Fo=(Go=J==null?void 0:J.BaseUom)==null?void 0:Go.find(ks=>{var Vs;return ks.code===((Vs=de.body[0])==null?void 0:Vs.Uom)}))==null?void 0:Fo.desc)||""}:"",xt.Relation=(Wo=de.body[0])!=null&&Wo.Relation?{code:((cn=de.body[0])==null?void 0:cn.Relation)||"",desc:((un=(dn=Af)==null?void 0:dn.find(ks=>{var Vs;return ks.code===((Vs=de.body[0])==null?void 0:Vs.Relation)}))==null?void 0:un.desc)||""}:"",xt.Usage=((hn=de.body[0])==null?void 0:hn.Usage)||"",xt.views=(xo=(((ps=de.body[0])==null?void 0:ps.Views)||"").split(",").map(ks=>ks.trim()))==null?void 0:xo.filter(ks=>!Id.includes(ks)),(h==null?void 0:h.Region)===((lo=Yo)==null?void 0:lo.EUR)&&(xt.views=((go=xt==null?void 0:xt.views)==null?void 0:go.filter(ks=>ks!==Y.WAREHOUSE))||[]),xt.validated=Ai.default,xt.withReference=ge,xt.refMaterialData=Wc(de.body,h),o(bi({materialID:en,data:xt,payloadData:{}})),gn.push(xt),G(gn),o(Po(gn)),Ne(xt==null?void 0:xt.views),Vt(_s+1),xs(Lt),ls(!0),Be(!0),Et(!0),Zo((fn=de.body[0])==null?void 0:fn.MatlType),It(en)}else he(!1),Kt(!0),lt(Vo.NO_MATERIAL_FOUND),pt("warning"),z(!0)},w=de=>{n(de),he(!1),z(!0)};ts({}),ne(null),Pt(null),ze(`/${Re}${(ie=ye.DATA)==null?void 0:ie.GET_COPY_MATERIAL}`,"post",d,w,r)},Ts=!Xo.includes(e==null?void 0:e.requestStatus)||g&&!S,fl=r=>({hasFertRole:r.includes("CA-MDG-MRKTNG-FERT-EUR"),hasSalesRole:r.includes("CA-MDG-MRKTNG-SALES-EUR")}),gl=(r,d,w)=>{var D;const{hasFertRole:_,hasSalesRole:y}=fl(u);if(_&&!y&&(h==null?void 0:h.Region)===Yo.EUR)return(r==null?void 0:r.code)!=="FERT";if(!_&&y&&(h==null?void 0:h.Region)===Yo.EUR)return(r==null?void 0:r.code)==="FERT";if(_&&y&&(h==null?void 0:h.Region)===Yo.EUR){const x=d[0];if(w===(x==null?void 0:x.id))return!1;const W=(D=x==null?void 0:x.materialType)==null?void 0:D.code;if(W==="FERT")return(r==null?void 0:r.code)!=="FERT";if(W)return(r==null?void 0:r.code)==="FERT"}return!1},Tl=(r,d)=>{var w;si.fire({title:oe("Are you sure?"),text:oe("Changing the material type will reset all the field values entered!"),icon:"warning",showCancelButton:!0,confirmButtonColor:(w=De.primary)==null?void 0:w.main,cancelButtonColor:De.error.red,confirmButtonText:oe("Yes, do it!"),cancelButtonText:oe("Cancel"),reverseButtons:!0}).then(_=>{_.isConfirmed&&(o(Tf({materialId:d})),vs({id:d,field:"materialType",value:r}))})},sr=[{field:"included",headerName:oe("Included"),flex:.5,align:"center",headerAlign:"center",renderCell:r=>a(Bn,{checked:r.row.included,disabled:Ts,onChange:d=>vs({id:r.row.id,field:"included",value:d.target.checked})})},{field:"lineNumber",headerName:oe("Line Number"),flex:.5,editable:!0,align:"center",headerAlign:"center"},{field:"industrySector",headerName:oe("Industry Sector"),flex:.7,align:"center",headerAlign:"center",...le===I.CREATE||le===I.CREATE_WITH_UPLOAD?{renderCell:r=>{var d;return a(to,{options:(J==null?void 0:J.IndSector)||[],value:r.row.industrySector||((d=$c)==null?void 0:d.DEFAULT_IND_SECTOR),onChange:w=>vs({id:r.row.id,field:"industrySector",value:w}),placeholder:oe("Select Industry Sector"),disabled:Ts,minWidth:"90%",listWidth:235})}}:{editable:!1,renderCell:r=>{var d,w;return((w=(d=X==null?void 0:X[r.row.id])==null?void 0:d.headerData)==null?void 0:w.industrySector)||""}}},{field:"materialType",headerName:oe("Material Type"),flex:.7,align:"center",headerAlign:"center",...le===I.CREATE||le===I.CREATE_WITH_UPLOAD?{renderCell:r=>a(to,{options:Gc||[],value:r.row.materialType,onChange:d=>{r.row.materialType?Tl(d,r.row.id):vs({id:r.row.id,field:"materialType",value:d})},placeholder:oe("Select Material Type"),disabled:Ts,minWidth:"90%",listWidth:235,isOptionDisabled:d=>gl(d,Q,r.row.id)})}:{editable:!1,renderCell:r=>{var d,w;return((w=(d=X==null?void 0:X[r.row.id])==null?void 0:d.headerData)==null?void 0:w.materialType)||""}}},{field:"materialNumber",headerName:oe("Material Number"),flex:.7,editable:!1,align:"center",headerAlign:"center",renderHeader:()=>te("span",{children:[oe("Material Number"),a("span",{style:{color:"red"},children:"*"})]}),renderCell:r=>{var D;const[d,w]=E.useState({[(D=r==null?void 0:r.row)==null?void 0:D.id]:r.row.materialNumber}),_=r.row.id,y=x=>{const W=x.target.value.toUpperCase(),Ce=(h==null?void 0:h.Region)==="US"?W.replace(/[^A-Z0-9-]/g,"").replace(/-{2,}/g,"-"):W.replace(/[^A-Z0-9]/g,"");w(de=>({...de,[_]:Ce})),vs({id:r.row.id,field:"materialNumber",value:Ce});const ie=Q.map(de=>de.id===r.row.id?{...de,isMatNoChanged:!0,materialNumber:Ce}:de);o(Po(ie))};return a(Xt,{title:al,arrow:!0,children:(h==null?void 0:h.RequestType)===I.CREATE||(h==null?void 0:h.RequestType)===I.CREATE_WITH_UPLOAD?a(En,{fullWidth:!0,placeholder:oe("ENTER MATERIAL NUMBER"),variant:"outlined",size:"small",name:"material number",value:d[_]||"",onChange:x=>{y(x)},sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:De.black.dark,color:De.black.dark}}},disabled:Ts}):r.row.materialNumber})}},{field:"globalMaterialDescription",flex:.7,headerName:oe("Material Description"),renderHeader:()=>te("span",{children:[oe("Material Description"),a("span",{style:{color:"red"},children:"*"})]}),renderCell:r=>{var x,W;const[d,w]=E.useState({[(x=r==null?void 0:r.row)==null?void 0:x.id]:r.row.globalMaterialDescription}),_=r.row.id,y=Ce=>{const de=Ce.target.value.toUpperCase().replace(/[^A-Z0-9-]/g,"").slice(0,40);w(Te=>({...Te,[_]:de})),vs({id:r.row.id,field:"globalMaterialDescription",value:de})},D=(((W=d[_])==null?void 0:W.length)||0)===40;return a(Tt,{sx:{display:"flex",alignItems:"center",width:"100%"},children:a(Xt,{title:d[_]||"",arrow:!0,placement:"top",children:a(En,{fullWidth:!0,variant:"outlined",size:"small",placeholder:oe("ENTER MATERIAL DESCRIPTION"),value:d[_]||"",onChange:y,inputProps:{maxLength:40},sx:{flexGrow:1,"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:D?De.error.dark:void 0},"&:hover fieldset":{borderColor:D?De.error.dark:void 0},"&.Mui-focused fieldset":{borderColor:D?De.error.dark:void 0}}}})})})},align:"center",headerAlign:"center",editable:!1},{field:"Bom",headerName:oe("BOM"),flex:.3,align:"center",headerAlign:"center",renderCell:r=>{var d;return a(Bn,{checked:((d=r.row)==null?void 0:d.Bom)||!1,disabled:Ts,onChange:w=>vs({id:r.row.id,field:"Bom",value:w.target.checked})})}},{field:"sourceList",headerName:oe("Source List"),flex:.3,align:"center",headerAlign:"center",renderCell:r=>{var d;return a(Bn,{checked:((d=r.row)==null?void 0:d.sourceList)||!1,disabled:Ts,onChange:w=>vs({id:r.row.id,field:"sourceList",value:w.target.checked})})}},{field:"PIR",headerName:oe("PIR"),flex:.3,align:"center",headerAlign:"center",renderCell:r=>{var d;return a(Bn,{checked:((d=r.row)==null?void 0:d.PIR)||!1,disabled:Ts,onChange:w=>vs({id:r.row.id,field:"PIR",value:w.target.checked})})}},{...le===I.CREATE||le===I.CREATE_WITH_UPLOAD?{field:"views",headerName:"",flex:.6,align:"center",headerAlign:"center",renderCell:r=>{var d,w,_;return te($s,{children:[a(St,{variant:"contained",size:"small",disabled:!((d=r==null?void 0:r.row)!=null&&d.materialType),onClick:()=>{var y,D,x;zs(!0),Ss(r.row.id),Ne((D=(y=r==null?void 0:r.row)==null?void 0:y.views)!=null&&D.length?(x=r.row)==null?void 0:x.views:[ue])},children:oe("Views")}),a(St,{variant:"contained",disabled:!(((_=(w=r==null?void 0:r.row)==null?void 0:w.views)==null?void 0:_.length)>1),size:"small",sx:{marginLeft:"4px"},onClick:()=>{var y,D,x;nt(!0),Ss(r.row.id),dt((D=(y=r==null?void 0:r.row)==null?void 0:y.orgData)!=null&&D.length?(x=r.row)==null?void 0:x.orgData:[Qt])},children:oe("ORG Data")})]})}}:{}},{field:"action",headerName:oe("Action"),flex:.5,align:"center",headerAlign:"center",renderCell:r=>{let d=uf(r==null?void 0:r.row);const w=async _=>{var Te,He,rt;_.stopPropagation();const{missingFields:y,viewType:D,isValid:x,plant:W=[]}=Xe(r.row.id,((Te=r==null?void 0:r.row)==null?void 0:Te.orgData)||[],rl,ll,il);if(qs(W),y){if(pt("error"),typeof y=="object"&&!Array.isArray(y)){const ft=Object.entries(y).map(([jt,ss])=>`Combination ${jt}: ${ss.join(", ")}`);lt(`${oe("Line No")} ${r.row.lineNumber} : ${oe("Please fill all the Mandatory fields in")} ${D||""}: ${ft.join(" | ")}`)}else lt(`${oe("Line No")} ${r.row.lineNumber} : ${oe("Please fill all the Mandatory fields in")} ${D||""}: ${y.join(", ")}`);Kt(!0)}let Ce=!1;x&&(!g||(He=r.row)!=null&&He.isMatNoChanged)&&(Ce=await it(r.row.materialNumber,g,(rt=r==null?void 0:r.row)==null?void 0:rt.globalMaterialDescription)),d=x&&!Ce?"success":"error";const ie=Q.map(ft=>ft.id===r.row.id?{...ft,validated:x&&!Ce}:ft);o(Po(ie));const de=Mn(ie);Be(!de),Et(!de)};return te(io,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:[a(Xt,{title:d==="success"?"Validated Successfully":oe(d==="error"?"Validation Failed":"Click to Validate"),children:a(ds,{onClick:w,color:d==="success"?"success":d==="error"?"error":"default",children:d==="error"?a(hf,{}):a(Ud,{})})}),!Ts&&a(Xt,{title:oe("Delete Row"),children:a(ds,{onClick:()=>{Ae({...A,data:r,isVisible:!0})},color:"error",children:a(Xr,{})})})]})}}],pl=(r,d)=>{var w,_;kt(d),Ms(((w=r==null?void 0:r.target)==null?void 0:w.id)==="AdditionalKey"?"Additional Data":(_=B==null?void 0:B.filter(y=>y!==Y.STORAGE))==null?void 0:_[d])},El=r=>{const d={"Sales Org":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},w=y=>{const D=Fc(y.body);Ct(x=>({...x,[r]:D}))},_=y=>console.error(y);ze(`/${Re}/data${d[r]}`,"get",w,_)},Cl=r=>{pf(r,B,fs,_e,ke,o,Wr)},ml=r=>{Ef(r,B,fs,_e,ke,o,Wr,Y)},Al=(r,d,w)=>(_,y)=>{var Te,He,rt;let D={},x="",W="";w==="Purchasing"||w==="Costing"?(D={materialNo:d==null?void 0:d.Material,plant:d==null?void 0:d.Plant},W=d==null?void 0:d.Plant,x=`/${Re}/data/displayLimitedPlantData`):w==="Accounting"?(D={materialNo:d==null?void 0:d.Material,valArea:d==null?void 0:d.ValArea},W=d==null?void 0:d.ValArea,x=`/${Re}/data/displayLimitedAccountingData`):w==="Sales"&&(D={materialNo:d==null?void 0:d.Material,salesOrg:d==null?void 0:d.SalesOrg,distChnl:d==null?void 0:d.DistrChan},W=`${d==null?void 0:d.SalesOrg}-${d==null?void 0:d.DistrChan}`,x=`/${Re}/data/displayLimitedSalesData`);const Ce=ft=>{var jt,ss,bt;w==="Purchasing"||w==="Costing"?o(Wr({materialID:_e,viewID:w,itemID:d==null?void 0:d.Plant,data:(jt=ft==null?void 0:ft.body)==null?void 0:jt.SpecificPlantDataViewDto[0]})):w==="Accounting"?o(Wr({materialID:_e,viewID:w,itemID:d==null?void 0:d.ValArea,data:(ss=ft==null?void 0:ft.body)==null?void 0:ss.SpecificAccountingDataViewDto[0]})):w==="Sales"&&o(Wr({materialID:_e,viewID:w,itemID:`${d==null?void 0:d.SalesOrg}-${d==null?void 0:d.DistrChan}`,data:(bt=ft==null?void 0:ft.body)==null?void 0:bt.SpecificSalesDataViewDto[0]}))},ie=()=>{};!((rt=(He=(Te=X==null?void 0:X[_e])==null?void 0:Te.payloadData)==null?void 0:He[w])!=null&&rt[W])&&ze(x,"post",Ce,ie,D),b(y?r:null)},bl=()=>$&&Jt&&($[Jt]||Jt==="Additional Data"||gs!=null&&gs.includes(Jt))?Jt==="Additional Data"?[a(Wf,{disableCheck:S&&!Xo.includes(e==null?void 0:e.requestStatus),materialID:_e,selectedMaterialNumber:we})]:[a(Ff,{disabled:S&&!Xo.includes(e==null?void 0:e.requestStatus),selectedMaterialNumber:we,materialID:_e,basicData:$t,setBasicData:is,dropDownData:ot,basicDataTabDetails:$[Jt],allTabsData:$,activeViewTab:Jt,selectedViews:B,handleAccordionClick:Al,missingValidationPlant:Ps,isDisplay:g||S})]:a($s,{}),or=r=>{var _,y;const d=((y=(_=r==null?void 0:r.target)==null?void 0:_.value)==null?void 0:y.toUpperCase())||"";Pt(null),Ht(0),U&&clearTimeout(U);const w=setTimeout(()=>{an(d,!0)},500);R(w)},wl=(r,d)=>{const w=Se==null?void 0:Se.code,_=ge==="yes"?"extended":"notExtended";ts(y=>({...y,[r]:d})),r==="Sales Org"&&d?Fe(w,_,d):r==="Plant"&&d&&(Ve(w,_,d),fe(w,d,st["Sales Org"]),ts(y=>({...y,"Storage Location":null,Warehouse:null})))},Il=(r,d,w)=>{r==="Sales Organization"&&(d?(dt(_=>_.map((y,D)=>D===w?{...y,salesOrg:d}:y)),nr(d,w).then(_=>{je(r,d,w)})):je(r,d,w))},nr=(r,d,w="",_="")=>new Promise((y,D)=>{Rt(ie=>({...ie,"Distribution Channel":{...ie["Distribution Channel"],[d]:!0}}));let x={salesOrg:r==null?void 0:r.code};const W=ie=>{Rt(Te=>({...Te,"Distribution Channel":{...Te["Distribution Channel"],[d]:!1}}));let de=JSON.parse(JSON.stringify(w||es.current));if(de[d].dc.options=Fc(ie.body),dt(de),es.current=de,_){o(Gr({materialID:_==null?void 0:_.id,keyName:"orgData",data:de}));let Te=(Q==null?void 0:Q.length)||[JSON.parse(JSON.stringify(_))],He=Te.findIndex(rt=>rt.id===(_==null?void 0:_.id));Te[He].orgData=de,o(Po(Te)),y({org:de,material:Te[He]})}else y(""),Rt(Te=>({...Te,"Distribution Channel":{...Te["Distribution Channel"],[d]:!1}}))},Ce=ie=>{console.error(ie),Rt(de=>({...de,"Distribution Channel":{...de["Distribution Channel"],[d]:!1}}))};ze(`/${Re}/data/getDistrChan`,"post",W,Ce,x)}),Rl=(r,d)=>{let w=JSON.parse(JSON.stringify(ke));w[d].dc.value=r,dt(w)},_l=r=>{let d=JSON.parse(JSON.stringify(ke));d.splice(r,1),dt(d)},Nl=(r,d)=>{let w=JSON.parse(JSON.stringify(ke));w[d].sloc.value=r,dt(w)},Sl=(r,d)=>{let w=JSON.parse(JSON.stringify(ke));w[d].mrpProfile=r,dt(w)},Ml=(r,d)=>{let w=JSON.parse(JSON.stringify(ke));w[d].warehouse.value=r,dt(w)},Ol=()=>{let r=JSON.parse(JSON.stringify(ke));r.push(Qt),dt(r)},vl=r=>{if(!(r!=null&&r.temp)||(r==null?void 0:r.temp)===(Oe==null?void 0:Oe.temp))return;he(!0);let d={decisionTableId:null,decisionTableName:"MDG_MAT_ORGDATA_TEMPLATE_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(h==null?void 0:h.Region)||Yo.US,"MDG_CONDITIONS.MDG_MAT_TEMPLATE":r.temp||""}],systemFilters:null,systemOrders:null,filterString:null};const w=y=>{var D,x;if(y.statusCode===zt.STATUS_200){he(!1);let W=(x=(D=y==null?void 0:y.data)==null?void 0:D.result[0])==null?void 0:x.MDG_MAT_ORGDATA_TEMPLATE_CONFIG,Ce=[];W==null||W.forEach((ie,de)=>{var ss;let Te=JSON.parse(JSON.stringify(Qt));Te.salesOrg={},Te.salesOrg.code=ie.MDG_MAT_SALES_ORG,Te.salesOrg.desc=ie.MDG_MAT_SALES_ORG_DESC,Te.plant.value={},Te.plant.value.code=ie.MDG_MAT_PLANT,Te.plant.value.desc=ie.MDG_MAT_PLANT_DESC;let He=(ss=qt==null?void 0:qt.filter(bt=>bt.MDG_MAT_SALES_ORG===ie.MDG_MAT_SALES_ORG))==null?void 0:ss.map(bt=>({code:bt.MDG_MAT_PLANT,desc:bt.MDG_MAT_PLANT_DESC}));He=He==null?void 0:He.filter((bt,Xs,No)=>Xs===No.findIndex(So=>So.code===bt.code)),Te.plant.options=He==null?void 0:He.sort((bt,Xs)=>bt.code-Xs.code);let rt=qt==null?void 0:qt.filter(bt=>bt.MDG_MAT_SALES_ORG===ie.MDG_MAT_SALES_ORG&&bt.MDG_MAT_PLANT===ie.MDG_MAT_PLANT),ft=rt==null?void 0:rt.map(bt=>({code:bt.MDG_MAT_STORAGE_LOCATION,desc:bt.MDG_MAT_STORE_LOC_DESC})),jt=rt==null?void 0:rt.map(bt=>bt.MDG_MAT_WAREHOUSE?{code:bt.MDG_MAT_WAREHOUSE,desc:bt.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);ie.MDG_MAT_STORAGE_LOCATION&&(Te.sloc.value={},Te.sloc.value.code=ie.MDG_MAT_STORAGE_LOCATION,Te.sloc.value.desc=ie.MDG_MAT_STORE_LOC_DESC),Te.sloc.options=ft,ie.MDG_MAT_WAREHOUSE&&(Te.warehouse.value={},Te.warehouse.value.code=ie.MDG_MAT_WAREHOUSE||"",Te.warehouse.value.desc=ie.MDG_MAT_WAREHOUSE_DESC||""),Te.warehouse.options=jt,Ce.push(Te)}),es.current=Ce,dt(Ce),rr(Ce,0)}},_=y=>{n(y),he(!1)};K.environment==="localhost"?ze(`/${qo}${ye.INVOKE_RULES.LOCAL}`,"post",w,_,d):ze(`/${qo}${ye.INVOKE_RULES.PROD}`,"post",w,_,d)},rr=async(r,d)=>{d<(r==null?void 0:r.length)&&(await nr(r[d].salesOrg,d),d++,rr(r,d))},yl=()=>{const r=A==null?void 0:A.data;G(Q==null?void 0:Q.filter(d=>{var w;return d.id!==((w=r==null?void 0:r.row)==null?void 0:w.id)})),o(Cf(r==null?void 0:r.row.id)),Zo(""),o(Po(Q==null?void 0:Q.filter(d=>{var w;return d.id!==((w=r==null?void 0:r.row)==null?void 0:w.id)}))),Q!=null&&Q.length?Q.filter(d=>{var w,_;return((w=d.params)==null?void 0:w.id)!==((_=r==null?void 0:r.row)==null?void 0:_.id)}).every(d=>d.validated)&&Be(!1):Be(!1),Ae({...A,isVisible:!1})};E.useEffect(()=>{var w,_;const r=B==null?void 0:B.includes((w=Y)==null?void 0:w.SALES),d=B==null?void 0:B.includes((_=Y)==null?void 0:_.SALES_PLANT);r&&!d&&Ne(y=>{var W,Ce;const D=[...y],x=D.indexOf((W=Y)==null?void 0:W.SALES);return D.splice(x+1,0,(Ce=Y)==null?void 0:Ce.SALES_PLANT),D})},[B]);const lr=r=>{!r||!Array.isArray(r)||r.forEach(d=>{var w,_,y,D,x,W,Ce,ie,de,Te,He,rt,ft,jt,ss,bt;if((_=(w=d.plant)==null?void 0:w.value)!=null&&_.code){if(Is((D=(y=d.plant)==null?void 0:y.value)==null?void 0:D.code,Y.PLANT),(x=d.salesOrg)!=null&&x.code||(Ce=(W=d.dc)==null?void 0:W.value)!=null&&Ce.code){const Xs=`${((ie=d.salesOrg)==null?void 0:ie.code)||""}-${((Te=(de=d.dc)==null?void 0:de.value)==null?void 0:Te.code)||""}`;Is(Xs,Y.SALES)}(rt=(He=d.warehouse)==null?void 0:He.value)!=null&&rt.code&&Is((jt=(ft=d.warehouse)==null?void 0:ft.value)==null?void 0:jt.code,Y.WAREHOUSE),Ao((bt=(ss=d.plant)==null?void 0:ss.value)==null?void 0:bt.code)}})};E.useEffect(()=>{if(g){const r=Gt==null?void 0:Gt.orgData;(r==null?void 0:r.length)>0&&r.some(d=>{var w,_,y,D,x;return((_=(w=d.plant)==null?void 0:w.value)==null?void 0:_.code)&&(((y=d.salesOrg)==null?void 0:y.code)||((x=(D=d.dc)==null?void 0:D.value)==null?void 0:x.code))})&&lr(r)}},[Gt==null?void 0:Gt.orgData]);const ir=r=>{o(zr(r)),q(r)};E.useEffect(()=>{var r,d;(f==null?void 0:f.page)!==0&&(le===((r=I)==null?void 0:r.CREATE_WITH_UPLOAD)||le===((d=I)==null?void 0:d.CREATE))&&C(),q((f==null?void 0:f.page)||0)},[f==null?void 0:f.page]);const Ll=()=>{L(!N),m&&O(!1)},xl=()=>{O(!m),N&&L(!1)};return te("div",{children:[a("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:te(Tt,{sx:{position:N?"fixed":"relative",top:N?0:"auto",left:N?0:"auto",right:N?0:"auto",bottom:N?0:"auto",width:N?"100vw":"100%",height:N?"100vh":"auto",zIndex:N?1004:void 0,backgroundColor:N?"white":"transparent",padding:N?"20px":"0",display:"flex",flexDirection:"column",boxShadow:N?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[te(Tt,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[a(wt,{variant:"h6",children:oe("Material Data")}),te(Tt,{sx:{display:"flex",alignItems:"center",gap:1},children:[te(St,{variant:"contained",color:"primary",size:"small",onClick:()=>{le===I.CREATE&&(z(!0),Ke([]),ne(null),ts({}),Zs([]))},disabled:$e||Ts,children:["+ ",oe("Add")]}),a(Xt,{title:oe(N?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:a(ds,{onClick:Ll,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:N?a(Ui,{}):a(qi,{})})})]})]}),g&&Q&&(Q==null?void 0:Q.length)>0?a("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:a("div",{style:{height:"100%"},children:a(Ni,{rows:Q,columns:sr,pageSize:50,autoHeight:!1,page:P,rowCount:(f==null?void 0:f.totalElements)||0,rowsPerPageOptions:[50],onRowClick:Dn,onCellEditCommit:vs,onPageChange:r=>ir(r),pagination:!0,disableSelectionOnClick:!0,getRowClassName:r=>r.id===_e?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:N?"calc(100vh - 150px)":`${Math.min(Q.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})}):a($s,{children:a("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:a("div",{style:{height:"100%"},children:a(Ni,{autoHeight:!1,rows:Q,columns:sr,pageSize:50,page:P,rowsPerPageOptions:[50],onRowClick:Dn,onCellEditCommit:vs,onPageChange:r=>ir(r),disableSelectionOnClick:!0,getRowClassName:r=>r.id===_e?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:N?"calc(100vh - 150px)":`${Math.min(Q.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})})]})}),le===I.CREATE||le===I.CREATE_WITH_UPLOAD||k!=null&&k.ATTRIBUTE_1?_e&&rs&&(Q==null?void 0:Q.length)>0&&(Pe==null?void 0:Pe.length)>0&&$&&((Ir=Object.getOwnPropertyNames($))==null?void 0:Ir.length)>0&&te(Tt,{sx:{position:m?"fixed":"relative",top:m?0:"auto",left:m?0:"auto",right:m?0:"auto",bottom:m?0:"auto",width:m?"100vw":"100%",height:m?"100vh":"auto",zIndex:m?1004:void 0,backgroundColor:m?"white":"transparent",padding:m?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:m?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[te(Tt,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[a(wt,{variant:"h6",children:oe("View Details")}),a(Xt,{title:oe(m?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:a(ds,{onClick:xl,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:m?a(Ui,{}):a(qi,{})})})]}),te(Tt,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[te(fd,{value:be,onChange:pl,className:t.customTabs,"aria-label":"material tabs",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:De.background.container,borderBottom:`1px solid ${De.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:De.black.graphite,"&.Mui-selected":{color:De.primary.main,fontWeight:700},"&:hover":{color:De.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:De.primary.main,height:"3px"}},children:[B&&ke.length>0&&(B==null?void 0:B.length)>0?(Rr=B==null?void 0:B.filter(r=>r!==Y.STORAGE))==null?void 0:Rr.map((r,d)=>a(Fl,{label:oe(r)},d)):a($s,{}),a(Fl,{label:oe("Additional Data"),id:"AdditionalKey"},"Additional data")]}),a(Tt,{sx:{padding:2,marginTop:2,flexGrow:1,overflow:"auto",height:m?"calc(100vh - 180px)":"auto"},children:(Q==null?void 0:Q.length)>0&&bl()}),(!Ts||g&&!S||S&&Xo.includes(e==null?void 0:e.requestStatus))&&a(Tt,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:a(Ji,{activeTab:be,submitForApprovalDisabled:!Mn(H),filteredButtons:_o,validateMaterials:_t,workFlowLevels:bo,showWfLevels:Ro,childRequestHeaderData:(_r=X==null?void 0:X[_e])==null?void 0:_r.Tochildrequestheaderdata})})]})]}):a($s,{}),a(_d,{dialogState:Fs,openReusableDialog:cl,closeReusableDialog:Pn,dialogTitle:"Warning",dialogMessage:ws,showCancelButton:!1,handleOk:Rn,handleDialogConfirm:Pn,dialogOkText:"OK",dialogSeverity:"danger"}),Ds&&a(on,{fullWidth:!0,maxWidth:!1,open:!0,onClose:qn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:te(Tt,{sx:{width:"600px !important"},children:[te(On,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[a(xi,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),a("span",{children:oe("Select Views")})]}),a(Uo,{sx:{paddingBottom:".5rem"},children:te(Tt,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[a(Gl,{size:"small",multiple:!0,fullWidth:!0,options:mt||[],disabled:Ts,disableCloseOnSelect:!0,value:(Nr=B==null?void 0:B.filter(r=>r!=="Sales-Plant"))==null?void 0:Nr.filter(r=>!(gs!=null&&gs.includes(r))),onChange:(r,d)=>{k!=null&&k.requestId||(Ne([ue,...d.filter(w=>w!==ue)]),vs({id:Ns,field:$r.VIEWS,value:d}))},getOptionDisabled:r=>r===ue,renderOption:(r,d,{selected:w})=>te("li",{...r,children:[a(Bn,{checked:w,sx:{marginRight:1}}),d]}),renderTags:(r,d)=>r.map((w,_)=>{const{key:y,...D}=d({index:_});return a(Fn,{label:w,...D,disabled:w===ue||Ts},y)}),renderInput:r=>a(En,{...r,label:oe("Select Views")})}),a(St,{variant:"contained",size:"small",onClick:()=>dl(),disabled:Ts,children:oe("Select all")})]})}),a(Ho,{children:a(St,{onClick:()=>{zs(!1),vs({id:Ns,field:"views",value:B})},variant:"contained",children:oe("Ok")})})]})}),Ys&&te(on,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:qn,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[te(On,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[a(xi,{fontSize:"small"}),a("span",{children:oe("Select Org Data")}),a(Tt,{sx:{position:"absolute",right:"7%",width:"15%"},children:a(Gl,{options:et.filter(r=>r.region===(h==null?void 0:h.Region)),value:Oe,size:"small",disabled:Ts,isOptionEqualToValue:(r,d)=>r.region===d.region,onChange:(r,d)=>{qe(d),vl(d)},getOptionLabel:r=>r==null?void 0:r.temp,renderInput:r=>a(En,{...r,label:oe("Select Template"),sx:{minWidth:165}}),sx:{"& .MuiAutocomplete-popper":{minWidth:250}}})}),a(ds,{onClick:qn,sx:{position:"absolute",right:15},children:a(Nd,{})})]}),a(Uo,{sx:{padding:0},children:a(Ad,{component:Wi,children:te(bd,{children:[a(ff,{children:te(vi,{children:[a(Js,{align:"center",children:oe("S NO.")}),a(Js,{align:"center",children:oe("Sales Org")}),a(Js,{align:"center",children:oe("Distribution Channel")}),a(Js,{align:"center",children:oe("Plant")}),a(Js,{align:"center",children:oe("Storage Location")}),(h==null?void 0:h.Region)!=="EUR"&&a(Js,{align:"center",children:oe("Warehouse")}),a(Js,{align:"center",children:oe("MRP Profile")}),ke.length>1&&a(Js,{align:"center",children:oe("Action")})]})}),a(wd,{children:ke.map((r,d)=>{var w,_,y,D,x,W,Ce,ie,de,Te,He,rt,ft,jt,ss,bt,Xs,No,So,Bo,$o,ro,Go,Fo,Wo,cn,dn,un,hn;return te(vi,{sx:{padding:"12px",opacity:Ts?.5:1,pointerEvents:Ts?"none":"auto"},children:[a(Js,{children:a(wt,{variant:"body2",children:d+1})}),a(Js,{children:a(to,{options:ot["Sales Organization"],value:r.salesOrg,onChange:ps=>Il("Sales Organization",ps,d),placeholder:oe("Select Sales Org"),minWidth:165,listWidth:215,title:((w=r==null?void 0:r.salesOrg)==null?void 0:w.code)+` - ${(_=r==null?void 0:r.salesOrg)==null?void 0:_.desc}`})}),a(Js,{children:a(to,{options:((y=r.dc)==null?void 0:y.options)||[],isLoading:((D=Ks["Distribution Channel"])==null?void 0:D[d])||!1,value:(x=r.dc)==null?void 0:x.value,onChange:ps=>Rl(ps,d),placeholder:oe("Select DC"),disabled:!Fr(jr.distributionChannel,B),minWidth:165,listWidth:215,title:((Ce=(W=r==null?void 0:r.dc)==null?void 0:W.value)==null?void 0:Ce.code)+` - ${(de=(ie=r==null?void 0:r.dc)==null?void 0:ie.value)==null?void 0:de.desc}`})}),a(Js,{children:a(to,{options:((Te=r.plant)==null?void 0:Te.options)||[],value:(He=r.plant)==null?void 0:He.value,onChange:ps=>je("plant",ps,d,r),placeholder:oe("Select Plant"),disabled:!Fr(jr.plant,B),minWidth:165,listWidth:215,title:((ft=(rt=r==null?void 0:r.plant)==null?void 0:rt.value)==null?void 0:ft.code)+` - ${(ss=(jt=r==null?void 0:r.plant)==null?void 0:jt.value)==null?void 0:ss.desc}`})}),a(Js,{children:a(to,{options:(bt=r==null?void 0:r.sloc)==null?void 0:bt.options,value:(Xs=r==null?void 0:r.sloc)==null?void 0:Xs.value,onChange:ps=>Nl(ps,d),placeholder:oe("Select Sloc"),disabled:!Fr(jr.storage,B),minWidth:165,listWidth:215,title:((So=(No=r==null?void 0:r.sloc)==null?void 0:No.value)==null?void 0:So.code)+` - ${($o=(Bo=r==null?void 0:r.sloc)==null?void 0:Bo.value)==null?void 0:$o.desc}`})}),(h==null?void 0:h.Region)!=="EUR"&&a(Js,{children:a(to,{options:((ro=r==null?void 0:r.warehouse)==null?void 0:ro.options)||[],value:(Go=r==null?void 0:r.warehouse)==null?void 0:Go.value,onChange:ps=>Ml(ps,d),disabled:!Fr(jr.warehouse,B),placeholder:oe("Select Warehouse"),minWidth:165,listWidth:215,title:((Wo=(Fo=r==null?void 0:r.warehouse)==null?void 0:Fo.value)==null?void 0:Wo.code)+` - ${(dn=(cn=r==null?void 0:r.warehouse)==null?void 0:cn.value)==null?void 0:dn.desc}`})}),a(Js,{children:a(to,{options:ot["Mrp Profile"]||[],value:r.mrpProfile,onChange:ps=>Sl(ps,d),placeholder:oe("Select MRP Profile"),disabled:!Fr(jr.mrpProfile,B),isOptionDisabled:ps=>{var go,fn;if(d===0)return!1;const xo=(fn=(go=ke[d].plant)==null?void 0:go.value)==null?void 0:fn.code;if(!xo)return!1;const lo=ke.slice(0,d).find(gn=>{var en,xt;return((xt=(en=gn.plant)==null?void 0:en.value)==null?void 0:xt.code)===xo});return lo&&lo.mrpProfile?ps.code!==lo.mrpProfile.code:!1},minWidth:165,listWidth:215,title:((un=r==null?void 0:r.mrpProfile)==null?void 0:un.code)+` - ${(hn=r==null?void 0:r.mrpProfile)==null?void 0:hn.desc}`})}),ke.length>1&&te(Js,{align:"right",children:[a(ds,{size:"small",color:"primary",onClick:()=>{fo(!0),mo({orgRowLength:ke.length,copyFor:d});const ps=ke.filter(xo=>{var lo,go;return(go=(lo=xo.plant)==null?void 0:lo.value)==null?void 0:go.code}).map(xo=>{var lo,go;return(go=(lo=xo.plant)==null?void 0:lo.value)==null?void 0:go.code});ge==="yes"&&ml(ps)},style:{display:d===0?"none":"inline-flex"},children:a(Hd,{})}),a(ds,{size:"small",color:"error",onClick:()=>_l(d),children:a(Xr,{})})]})]},d)})})]})})}),te(Ho,{sx:{justifyContent:"flex-end",gap:.5},children:[te(St,{onClick:Ol,variant:"contained",disabled:!Hs||Ts,children:["+ ",oe("Add")]}),a(Xt,{title:Hs?"":oe("Please fill all the fields of first row at least"),arrow:!0,children:a("span",{children:a(St,{onClick:()=>{var r,d;if(nt(!1),(d=(r=ke[0].plant)==null?void 0:r.value)!=null&&d.code){lr(ke),vs({id:Ns,field:"orgData",value:ke}),Bt(ke);const w=Q==null?void 0:Q.map(_=>_.id===_e?{..._,orgData:ke}:_);if(o(Po(w)),ge==="no"){const _=ke.filter(y=>{var D,x;return(x=(D=y.plant)==null?void 0:D.value)==null?void 0:x.code}).map(y=>{var D,x;return(x=(D=y.plant)==null?void 0:D.value)==null?void 0:x.code});_.length>0&&Cl(_)}qe(null)}},variant:"contained",disabled:!Hs||Ts,tooltip:Hs?"":oe("Please fill all the fields of first row at least"),children:oe("Ok")})})})]})]}),Zt&&te(on,{fullWidth:!0,open:!0,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[a(On,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",display:"flex"},children:a(wt,{variant:"h6",children:oe("Add New Material")})}),te(Uo,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[te($i,{component:"fieldset",sx:{paddingBottom:"2%"},children:[a(gf,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:oe("Do You Want To Continue")}),te(ud,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:ge,onChange:r=>xe(r.target.value),children:[a(Bl,{value:"yes",control:a($l,{}),label:oe("With Reference")}),a(Bl,{value:"no",control:a($l,{}),label:oe("Without Reference")})]})]}),te(Rs,{container:!0,spacing:2,children:[a(Rs,{item:!0,xs:12,children:te(Rs,{container:!0,spacing:2,children:[a(Rs,{item:!0,xs:3,children:a(to,{options:Gc||[],value:st[ae.MATERIAL_TYPE]||"",onChange:r=>{ts(d=>({...d,[ae.MATERIAL_TYPE]:r}))},placeholder:oe("Select Material Type"),minWidth:180,listWidth:266,disabled:(pe==null?void 0:pe.length)||ge==="no",getOptionLabel:r=>r!=null&&r.desc?`${r.code} - ${r.desc}`:(r==null?void 0:r.code)||"",renderOption:(r,d)=>te("li",{...r,children:[a("strong",{children:d==null?void 0:d.code}),d!=null&&d.desc?` - ${d==null?void 0:d.desc}`:""]})})}),a(Rs,{item:!0,xs:3,children:a(to,{options:js,value:Ft||Se,onChange:r=>{ne(r),Pt(r),r||or(r)},minWidth:180,listWidth:266,placeholder:oe("Select Material"),disabled:(pe==null?void 0:pe.length)||ge==="no",getOptionLabel:r=>r!=null&&r.desc?`${r.code} - ${r.desc}`:(r==null?void 0:r.code)||"",renderOption:(r,d)=>te("li",{...r,children:[a("strong",{children:d==null?void 0:d.code}),d!=null&&d.desc?` - ${d==null?void 0:d.desc}`:""]}),handleInputChange:or,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},isLoading:Ks["Material No"]})}),cs==null?void 0:cs.slice(0,2).map(r=>a(Rs,{item:!0,xs:3,children:a(to,{options:(ot==null?void 0:ot[r])||[],value:st[r]||"",onChange:d=>{wl(r,d)},placeholder:oe(`Select ${r}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(pe==null?void 0:pe.length)||ge==="no",isLoading:Ks[r]})},r))]})}),a(Rs,{item:!0,xs:12,children:te(Rs,{container:!0,spacing:2,alignItems:"center",children:[a(Rs,{item:!0,xs:3,children:a(to,{options:(ot==null?void 0:ot[cs[2]])||[],value:st[cs[2]]||"",onChange:r=>{ts(d=>({...d,[cs[2]]:r}))},placeholder:oe(`Select ${cs[2]}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(pe==null?void 0:pe.length)||ge==="no",isLoading:Ks["Distribution Channel"]===!0})}),cs==null?void 0:cs.slice(3).map(r=>a(Rs,{item:!0,xs:3,children:a(to,{options:(ot==null?void 0:ot[r])||[],value:st[r]||"",onChange:d=>{ts(w=>({...w,[r]:d}))},placeholder:oe(`Select ${r}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(pe==null?void 0:pe.length)||ge==="no",isLoading:Ks[r]})},r)),(H==null?void 0:H.length)>0&&te($s,{children:[a(Rs,{item:!0,xs:1,sx:{textAlign:"center"},children:a(wt,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),a(Rs,{item:!0,xs:3,children:a(to,{options:H.map(r=>({...r,code:r.lineNumber,desc:""})),value:pe[0],onChange:r=>{Ke(r?[r]:[]),ts({}),ne(null),Pt(null)},minWidth:180,listWidth:266,placeholder:oe("Select Material Line Number"),disabled:(Se==null?void 0:Se.code)||ge==="no",getOptionLabel:r=>r!=null&&r.desc?`${r.code} - ${r.desc}`:(r==null?void 0:r.code)||"",renderOption:(r,d)=>te("li",{...r,children:[a("strong",{children:d==null?void 0:d.code}),d!=null&&d.desc?` - ${d==null?void 0:d.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})})]})]})})]})]}),te(Ho,{sx:{display:"flex",justifyContent:"end"},children:[a(St,{sx:{width:"max-content",textTransform:"capitalize"},onClick:()=>z(!1),variant:"outlined",children:oe("Cancel")}),a(St,{className:"button_primary--normal",type:"save",disabled:!(pe!=null&&pe.length||Se!=null&&Se.code)&&ge==="yes",onClick:ul,variant:"contained",children:oe("Proceed")})]})]}),(A==null?void 0:A.isVisible)&&te(Gi,{isOpen:A==null?void 0:A.isVisible,titleIcon:a(Xr,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:oe("Delete Row")+"!",handleClose:()=>Ae({...A,isVisible:!1}),children:[a(Uo,{sx:{mt:2},children:oe(tn.DELETE_MESSAGE)}),te(Ho,{children:[a(St,{variant:"outlined",size:"small",sx:{...md},onClick:()=>Ae({...A,isVisible:!1}),children:oe(jl.CANCEL)}),a(St,{variant:"contained",size:"small",sx:{...Fi},onClick:yl,children:oe(jl.DELETE)})]})]}),ho&&a(yg,{open:ho,onClose:()=>fo(!1),title:Md.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:fs,lengthOfOrgRow:Co,materialID:_e,orgRows:ke}),eo&&a(Vn,{openSnackBar:Ut,alertMsg:eo,alertType:ut,handleSnackBarClose:()=>Kt(!1)}),a(Yn,{blurLoading:Ze,loaderMessage:Nt}),a(zi,{})]})},yE=(e,t,n,o)=>{const[c,s]=E.useState([]),[C,i]=E.useState([]),h=re(J=>J.payload.payloadData),Ie=new URLSearchParams(location.search).get("RequestType"),K=re(J=>{var $,u;return(u=($=J==null?void 0:J.userManagement)==null?void 0:$.taskData)==null?void 0:u.ATTRIBUTE_2}),[f,X]=E.useState(!1),{customError:V}=Qo(),H=(h==null?void 0:h.RequestType)||K||Ie;E.useEffect(()=>{H&&ce()},[e,H]);const ce=()=>{const J={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":H}],systemFilters:null,systemOrders:null,filterString:null},$=F=>{var p,T;if(F.statusCode===zt.STATUS_200){const S=oi(ni.CURRENT_TASK,!0,{}),g=(e==null?void 0:e.taskDesc)||(S==null?void 0:S.taskDesc),L=(((T=(p=F==null?void 0:F.data)==null?void 0:p.result[0])==null?void 0:T.MDG_MAT_DYN_BUTTON_CONFIG)||[]).filter(O=>(O==null?void 0:O.MDG_MAT_DYN_BTN_TASK_NAME)===(g??Mi.INITIATOR));s(L),(L.find(O=>O.MDG_MAT_DYN_BTN_BUTTON_NAME===o.SEND_BACK)||L.find(O=>O.MDG_MAT_DYN_BTN_BUTTON_NAME===o.CORRECTION))&&X(!0)}},u=F=>{V("Dynamic Button Fetch Error:",F)},k=t.environment==="localhost"?`/${n}${ye.INVOKE_RULES.LOCAL}`:`/${n}${ye.INVOKE_RULES.PROD}`;ze(k,"post",$,u,J)};return E.useEffect(()=>{const J=[...c].sort(($,u)=>{const k=Qc[$.MDG_MAT_DYN_BTN_BUTTON_NAME]??999,F=Qc[u.MDG_MAT_DYN_BTN_BUTTON_NAME]??999;return k-F});i(J)},[c]),{extendFilteredButtons:C,showWfLevels:f}},LE=e=>{var o,c;const t=((c=(o=e==null?void 0:e.split("."))==null?void 0:o.pop())==null?void 0:c.toLowerCase())||"",n={fontSize:"small",sx:{mr:1}};switch(t){case"xlsx":case"xls":case"csv":return a(Yf,{sx:{color:De.icon.excel}});case"pdf":return a(qf,{...n,sx:{color:De.icon.pdf}});case"doc":case"docx":return a(zc,{...n,sx:{color:De.icon.doc}});case"ppt":case"pptx":return a(zc,{...n,sx:{color:De.icon.ppt}});default:return a(Pf,{...n,sx:{color:De.icon.file}})}},eC=({attachmentsData:e=[],requestIdHeader:t="",pcNumber:n="",disabled:o=!1,requestStatus:c})=>{const[s,C]=E.useState({}),i=re(A=>A.appSettings),h=re(A=>A.payload.dynamicKeyValues),[le,Ie]=E.useState([]),[K,f]=E.useState([]),X=po(),{t:V}=Qr(),H=re(A=>A.userManagement.taskData),ce=re(A=>A.applicationConfig),J=re(A=>A.request.materialRows),[$,u]=E.useState(!1),k=re(A=>A.payload.payloadData),[F,p]=E.useState(""),T=rn(),g=new URLSearchParams(T.search).get("reqBench"),N=T==null?void 0:T.state,{extendFilteredButtons:L}=yE(H,ce,qo,Bs);let m;const O=[Oo.HANDLE_SEND_BACK,Oo.HANDLE_VALIDATE,Oo.HANDLE_CORRECTION,Oo.HANDLE_SUBMIT_FOR_APPROVAL,Oo.HANDLE_SAP_SYNDICATION,Oo.HANDLE_SUBMIT_FOR_REVIEW,Oo.HANDLE_SUBMIT];(k==null?void 0:k.RequestType)===I.EXTEND||(k==null?void 0:k.RequestType)===I.EXTEND_WITH_UPLOAD?m=ji(L,O):m=[];const P=re(A=>A.payload.payloadData),q=re(A=>{var Ae,Q;return(Q=(Ae=A==null?void 0:A.payload)==null?void 0:Ae.dynamicKeyValues)==null?void 0:Q.childRequestHeaderData}),U=()=>{u(!0)},R=()=>{u(!1)},v=!Xo.includes(c)||t&&!g,b={primary:De.blue.main,light:De.text.offWhite,accent:De.primary.grey,text:De.text.charcoal,secondaryText:De.text.greyBlue,background:De.background.paper},ue=[{field:"id",headerName:"Document ID",flex:1.2,hideable:!1,hidden:!0},{field:"attachmentType",headerName:V("Attachment Type"),flex:1.5,renderCell:A=>{var Ae;return a(Fn,{label:A.value,size:"small",sx:{backgroundColor:(Ae=De)==null?void 0:Ae.reportTile.lightBlue,color:De.primary.lightPlus,fontWeight:"medium"}})}},{field:"docName",headerName:V("Document Name"),flex:2,renderCell:A=>te(io,{direction:"row",spacing:1,alignItems:"center",children:[LE(A.value),a(wt,{variant:"body2",children:A.value})]})},{field:"uploadedOn",headerName:V("Uploaded On"),flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:V("Uploaded By"),sortable:!1,flex:1},{field:"view",headerName:V("View"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",renderCell:A=>{var Ae,Q;return a(ds,{size:"small",sx:{color:De.icon.matView,"&:hover":{backgroundColor:"rgba(2, 136, 209, 0.1)"}},children:a(bf,{index:A.row.id,name:((Ae=A==null?void 0:A.row)==null?void 0:Ae.docName)||((Q=A==null?void 0:A.row)==null?void 0:Q.fileName),documentViewUrl:A.row.documentViewUrl})})}},{field:"action",headerName:V("Action"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:A=>{var Ae,Q,G,Pe,$e;return te(io,{direction:"row",spacing:0,children:[a(ds,{size:"small",sx:{color:(Ae=De)==null?void 0:Ae.icon.matDownload,"&:hover":{backgroundColor:"rgba(46, 125, 50, 0.1)"}},children:a(wf,{index:A.row.id,name:((Q=A==null?void 0:A.row)==null?void 0:Q.docName)||((G=A==null?void 0:A.row)==null?void 0:G.fileName)})}),a(ds,{size:"small",sx:{color:(Pe=De)==null?void 0:Pe.icon.delete,"&:hover":{backgroundColor:"rgba(211, 47, 47, 0.1)"}},children:a(If,{index:A.row.id,name:A.row.docName||(($e=A==null?void 0:A.row)==null?void 0:$e.fileName),setSnackbar:u,setopenSnackbar:u,setMessageDialogMessage:p,handleSnackbarOpen:U,setDownloadLoader:C,DownloadLoader:s})})]})}}];E.useEffect(()=>{B()},[$]),E.useEffect(()=>{var Ae,Q;if(g&&(N==null?void 0:N.reqStatus)===((Ae=Vr)==null?void 0:Ae.SYNDICATED_IN_SAP_DIRECT)){let G=[];var A={id:N.requestId,comment:((Q=h==null?void 0:h.otherPayloadData)==null?void 0:Q.Comments)||"",user:N.createdBy,createdAt:N.changedOnAct,taskName:"Direct Syndication Task",role:"Requestor"};G.push(A),f(G);return}Ne()},[]);const B=()=>{let A=t,Ae=Pe=>{var $e=[];Pe.documentDetailDtoList.forEach(Be=>{var bs={id:Be.documentId,docType:Be.fileType,docName:Be.fileName,uploadedOn:Wn(Be.docCreationDate).format(i.dateFormat),uploadedBy:Be.createdBy,attachmentType:Be.attachmentType,documentViewUrl:Be.documentViewUrl};X(Uf(Be==null?void 0:Be.attachmentType)),$e.push(bs)}),Ie($e)};const G=(q==null?void 0:q.ChildRequestId)||(P==null?void 0:P.childRequestId)?`/${Yc}/${ye.TASK_ACTION_DETAIL.GET_CHILD_DOCS}/${A}`:`/${Yc}/${ye.TASK_ACTION_DETAIL.GET_DOCS}/${A}`;ze(G,"get",Ae)},Ne=()=>{let A=t,Ae=G=>{var Pe=[];G.body.forEach($e=>{var Be={id:$e.requestId,comment:$e.comment,user:$e.createdByUser,createdAt:$e.updatedAt,taskName:$e.taskName,role:$e.createdByRole};Pe.push(Be)}),f(Pe)},Q=G=>{console.log(G)};ze(`/${Re}/${ye.TASK_ACTION_DETAIL.TASKDETAILS_FOR_REQUESTID}?requestId=${A}`,"get",Ae,Q)};return te("div",{children:[a(Rs,{container:!0,spacing:2,sx:{padding:"10px",margin:0},children:te(Rs,{item:!0,md:12,sx:{backgroundColor:De.primary.white,maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:2,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Sd},children:[a(Rs,{container:!0,sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",alignItems:"center"},children:te(Tt,{sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",alignItems:"space-between",width:"100%"},children:[a(wt,{variant:"h6",children:a("strong",{children:V("Attachments")})}),!o&&(e==null?void 0:e.map((A,Ae)=>a(Rf,{title:"Material",useMetaData:!1,artifactId:`${A.MDG_ATTACHMENTS_NAME}_${n}`,artifactName:"MaterialMaster",attachmentType:A.MDG_ATTACHMENTS_NAME,requestId:t,getAttachments:B})))]})}),le.length>0?a(Cd,{width:"100%",rows:le,columns:ue,hideFooter:!1,getRowIdValue:"id",autoHeight:!0,disableSelectionOnClick:!0,stopPropagation_Column:"action"}):te(io,{alignItems:"center",spacing:2,sx:{py:4},children:[a(zf,{sx:{fontSize:40,color:b.accent,transform:"rotate(90deg)"}}),a(wt,{variant:"body2",color:b.secondaryText,children:V("No Attachments Found")})]}),a("br",{}),te(Tt,{sx:{maxWidth:"100%",bgcolor:b.background,borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[a(io,{direction:"row",alignItems:"center",spacing:1,mb:3,children:a(wt,{variant:"h6",fontWeight:"bold",color:b.text,children:V("Comments")})}),K.length>0?a(Kf,{position:"right",sx:{p:0,m:0,"& .MuiTimelineItem-root:before":{flex:0,padding:0}},children:K.map((A,Ae)=>te(_f,{sx:{},children:[te(Nf,{children:[a(Sf,{sx:{bgcolor:b.light,boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:a(jc,{size:18,sx:{color:"blue"}})}),a(Mf,{sx:{width:"2.2px"}})]}),a(Of,{sx:{py:"12px",px:2},children:a(vf,{variant:"outlined",sx:{borderRadius:"12px",borderColor:b.accent,background:"linear-gradient(135deg, #FFFFFF 0%, #F9FAFB 100%)",transition:"all 0.3s ease"},children:te(yf,{sx:{p:2},children:[te(io,{direction:"row",justifyContent:"space-between",alignItems:"center",children:[te(io,{direction:"row",spacing:1.5,alignItems:"center",children:[a(Lf,{sx:{bgcolor:"#e3f2fd",color:b.primary,boxShadow:"none",width:32,height:32,fontSize:"14px",transition:"all 0.3s ease","&:hover":{transform:"rotate(5deg)"}},children:A.user.charAt(0).toUpperCase()}),a(wt,{fontWeight:"bold",color:b.text,children:A.user}),a(Fn,{label:A.taskName,size:"small",sx:{backgroundColor:De.reportTile.lightBlue,color:De.primary.lightPlus,fontSize:"12px",borderRadius:"16px"}})]}),te(io,{direction:"row",alignItems:"center",spacing:1,children:[a(wt,{variant:"body2",color:b.secondaryText,sx:{fontSize:"12px"},children:Wn(A.createdAt).format("MM/DD/YYYY, h:mm A")}),a(ds,{size:"small",sx:{color:b.secondaryText},children:a(xf,{fontSize:"small"})})]})]}),a(Df,{sx:{my:2,borderColor:b.accent}}),a(wt,{variant:"body2",color:b.text,sx:{my:1,fontSize:"14px",lineHeight:"1.6"},children:A.comment||"-"})]})})})]},A.id))}):te(io,{alignItems:"center",spacing:2,sx:{py:4},children:[a(jc,{sx:{fontSize:40,color:b.accent}}),a(wt,{variant:"body2",color:b.secondaryText,children:V("No Remarks found")})]})]}),a("br",{})]})}),(!v||t&&!g||g&&Xo.includes(c))&&a(Tt,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:a(Ji,{activeTab:Hl.ATTACHMENT_AND_COMMENTS,submitForApprovalDisabled:!Mn(J),filteredButtons:m})}),$&&a(Vn,{openSnackBar:$,alertMsg:F,handleSnackBarClose:R})]})};var Ea={},xE=bn;Object.defineProperty(Ea,"__esModule",{value:!0});var DE=Ea.default=void 0,PE=xE(An()),qE=wn;DE=Ea.default=(0,PE.default)((0,qE.jsx)("path",{d:"M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 9c2.7 0 5.8 1.29 6 2v1H6v-.99c.2-.72 3.3-2.01 6-2.01m0-11C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4m0 9c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4"}),"PermIdentityOutlined");export{eC as A,Ji as B,tg as H,yg as O,QE as R,Hl as T,Sg as a,qd as b,Lg as c,yE as d,Hd as e,Oo as f,xg as g,Cg as h,Dd as i,OE as j,Ag as k,Xf as l,ZE as m,DE as n,wg as o,xd as u};
