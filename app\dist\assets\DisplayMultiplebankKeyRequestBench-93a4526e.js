import{b as Se,l as je,r as s,i as p,u as Ae,ig as Me,j as a,$ as r,hp as Be,a as n,i7 as _e,T as g,ia as we,B,g_ as oe,id as Fe,ie as Ee,ic as Te,h_ as Ne,gZ as qe,m7 as De,hE as ie,a1 as f,gW as _,P as le,iv as w,w as Le,hL as Pe,hn as Ve,W as We,hv as ze,i8 as ke,gU as ae,i9 as Ie,ib as Oe,hP as $e}from"./index-fdfa25a0.js";import"./dayjs.min-774e293a.js";const Ze=()=>{var $,H,K,G,U,Z,J,Q,R,X,Y,ee,te;const T=Se(),b=je();s.useState({});const[N,q]=s.useState(0);s.useState([]);const[y,re]=s.useState(!1),[He,se]=s.useState(!0);s.useState([]),s.useState([]);const[de,Ke]=s.useState([]),[h,S]=s.useState(0);s.useState(),p(e=>e.tabsData);const[ce,D]=s.useState(!1),[L,pe]=s.useState([]),[he,P]=s.useState(!1),j=Ae(),x=j.state.tabsData;console.log(j.state,"state");const l=j.state.rowData,o=j.state.requestbenchRowData;let d=p(e=>{var i;return(i=e==null?void 0:e.initialData)==null?void 0:i.IWMMyTask});console.log(d,"taskData_in_mass"),console.log(o,"========massBKRowData==========="),console.log("selectedrowdata",l,x),p(e=>e.payload);let V=p(e=>e.edit.payload),F=p(e=>e.bankKey.requiredFields);console.log(F,"required_field_for_data");const ue=p(e=>e.profitCenter.profitCenterCompCodes);let t=p(e=>e.userManagement.taskData),ge=p(e=>e.profitCenter.MultipleProfitCenterData),E=p(e=>e.userManagement.userData);console.log(ge,"profitCenterMultiple"),console.log(t,"task_in_mass========================="),console.log(t==null?void 0:t.processDesc,"task?.processDesc"),console.log(t==null?void 0:t.subject,"task?.subject");let m="",C="",v="";(t==null?void 0:t.processDesc)==="Mass Change"?(m=t!=null&&t.subject?($=t==null?void 0:t.subject)==null?void 0:$.slice(3):o==null?void 0:o.requestId.slice(3),C=(H=d==null?void 0:d.body)!=null&&H.controllingArea?"":l.controllingArea,v=(K=t==null?void 0:t.body)!=null&&K.profitCenter?"":l.profitCenter):(t==null?void 0:t.processDesc)==="Mass Create"?(m=t!=null&&t.subject?(G=t==null?void 0:t.subject)==null?void 0:G.slice(3):o==null?void 0:o.requestId.slice(3),C=(U=d==null?void 0:d.body)!=null&&U.controllingArea?"":l.controllingArea,v=(Z=t==null?void 0:t.body)!=null&&Z.profitCenter?"":l.profitCenter):(o==null?void 0:o.requestType)==="Mass Create"?(m=t!=null&&t.subject?(J=t==null?void 0:t.subject)==null?void 0:J.slice(3):o==null?void 0:o.requestId.slice(3),C=(Q=d==null?void 0:d.body)!=null&&Q.controllingArea?"":l.controllingArea,v=(R=t==null?void 0:t.body)!=null&&R.profitCenter?"":l.profitCenter):(o==null?void 0:o.requestType)==="Mass Change"&&(m=t!=null&&t.subject?(X=t==null?void 0:t.subject)==null?void 0:X.slice(3):o==null?void 0:o.requestId.slice(3),C=(Y=d==null?void 0:d.body)!=null&&Y.controllingArea?"":l.controllingArea,v=(ee=t==null?void 0:t.body)!=null&&ee.profitCenter?"":l.profitCenter);const fe=(e,i)=>{setActiveTab(i)},W=()=>{const e=O();y?e?(S(i=>i-1),b(w())):k():(S(i=>i-1),b(w()))},z=()=>{const e=O();y?e?(S(i=>i+1),b(w())):k():(S(i=>i+1),b(w()))},k=()=>{P(!0)},A=Object.entries(x==null?void 0:x.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]),M=Object.entries(x.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),I={};M.map(e=>{e.forEach((i,c)=>{i.forEach((u,ve)=>{ve!==0&&u.forEach(ne=>{I[ne.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=ne.value})})})});const be=()=>{re(!0),se(!1)},xe=e=>{D(e)},me=()=>{D(!0)},ye=e=>{console.log("compcode",e);const i=u=>{console.log("value",u),b($e({keyName:"Region",data:u.body}))},c=u=>{console.log(u,"error in dojax")};Le(`/${Pe}/data/getRegionBasedOnCountry?country=${e}`,"get",i,c)};s.useEffect(()=>{b(Me(I))},[]),s.useEffect(()=>{ye(l.bankCountry)},[]),console.log(V,F,"requiredFieldTabWise");const O=()=>Ve(V,F,pe),Ce=()=>{P(!1)};return console.log(m,C,v,"================"),console.log("tabcontents",x),a("div",{children:[a(r,{container:!0,style:{...Be,backgroundColor:"#FAFCFF"},children:[L.length!=0&&n(_e,{openSnackBar:he,alertMsg:"Please fill the following Field: "+L.join(", "),handleSnackBarClose:Ce}),a(r,{sx:{width:"inherit"},children:[a(r,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[n(r,{style:{display:"flex",justifyContent:"flex-end"},children:n(We,{color:"primary","aria-label":"upload picture",component:"label",sx:ze,children:n(ke,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{T(-1)}})})}),a(r,{md:10,children:[n(g,{variant:"h3",children:a("strong",{children:["Multiple Bank  Key : ",l.bankKey," "]})}),n(g,{variant:"body2",color:"#777",children:"This view displays details of uploaded Bank Key"})]}),n(r,{md:1,sx:{display:"flex",justifyContent:"flex-end",marginRight:"4px"},children:n(f,{variant:"outlined",size:"small",sx:ae,onClick:me,title:"Chnage Log",children:n(Ie,{sx:{padding:"2px"},fontSize:"small"})})}),ce&&n(we,{open:!0,closeModal:xe,requestId:m,requestType:"Mass",pageName:"bankKey",controllingArea:l.bankCountry,centerName:l.bankKey}),y?"":(E==null?void 0:E.role)==="Finance"?n(r,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:n(r,{item:!0,children:a(f,{variant:"outlined",size:"small",sx:ae,onClick:be,children:["Change",n(Oe,{sx:{padding:"2px"},fontSize:"small"})]})})}):""]}),n(r,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:a(B,{width:"70%",sx:{marginLeft:"40px"},children:[n(r,{item:!0,sx:{paddingTop:"2px !important"},children:a(oe,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(g,{variant:"body2",color:"#777",children:"Bank Key"})}),a(g,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",l.bankKey]})]})}),n(r,{item:!0,sx:{paddingTop:"2px !important"},children:a(oe,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(g,{variant:"body2",color:"#777",children:"Bank Country"})}),a(g,{variant:"body2",fontWeight:"bold",children:[": ",l.bankCountry]})]})})]})}),a(r,{container:!0,style:{padding:"16px"},children:[n(Te,{activeStep:h,onChange:fe,variant:"scrollable",sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:A.map((e,i)=>n(Fe,{children:n(Ee,{sx:{fontWeight:"700"},children:e})},e))}),n(r,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:M&&((te=M[h])==null?void 0:te.map((e,i)=>h===2?n(CompCodesProfitCenter,{compCodesTabDetails:ue,displayCompCode:de}):n(B,{sx:{width:"100%"},children:a(r,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Ne},children:[n(r,{container:!0,children:n(g,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),n(B,{children:n(B,{sx:{width:"100%"},children:n(qe,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(r,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(c=>n(De,{activeTabIndex:h,fieldGroup:e[0],selectedRowData:l.bankKey,pcTabs:A,label:c.fieldName,value:c.value,length:c.maxLength,visibility:c.visibility,onSave:u=>handleFieldSave(c.fieldName,u),isEditMode:y,type:c.fieldType,field:c}))})})})})]})},i)))},M)]})]})]}),y?n(le,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ie,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:N,onChange:e=>{q(e)},children:[n(f,{size:"small",variant:"contained",onClick:()=>{T(-1)},children:"Save"}),n(f,{variant:"contained",size:"small",sx:{..._,mr:1},onClick:W,disabled:h===0,children:"Back"}),n(f,{variant:"contained",size:"small",sx:{..._,mr:1},onClick:z,disabled:h===A.length-1,children:"Next"})]})}):n(le,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ie,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:N,onChange:e=>{q(e)},children:[n(f,{variant:"contained",size:"small",sx:{..._,mr:1},onClick:W,disabled:h===0,children:"Back"}),n(f,{variant:"contained",size:"small",sx:{..._,mr:1},onClick:z,disabled:h===A.length-1,children:"Next"})]})})]})};export{Ze as default};
