import{r as A,aj as ye,ah as wo,ai as Po,a as d,$ as k,j as C,P as Bt,B as P,jr as $,g_ as Y,K as Mo,c0 as Do,hd as zt,gK as T,T as V,k6 as Vo,k7 as Lo,k8 as Fo,k9 as ko,a6 as xe,F as rt,fF as Bo,V as Ut,a7 as ze,iB as Oo,ka as Ro,cf as No,a1 as be,i7 as jo,hi as Io,kb as _o,h as Tt,U as Me,kc as $o,kd as Fs,ke as Uo,u as Wo,i as ci,hZ as zo,jZ as Ko,b as Yo,jP as Go,fV as ui,h1 as hi,gQ as di,hC as De,fX as fi,hl as Ho,hp as Xo,hq as Zo,W as Mt,hv as Qo,i8 as Jo,kf as qo,jz as ta,H as pi,w as ce,G as Ve,hg as tt,kg as ea,ij as na,ik as ia,hI as mi,J as sa,hK as gi,hL as yi,io as xi,iu as bi,iD as ra,E as oa}from"./index-fdfa25a0.js";import{d as aa}from"./CommentOutlined-075abc93.js";const ks=A.createContext({});function la(t){const e=A.useRef(null);return e.current===null&&(e.current=t()),e.current}const dn=typeof window<"u",ca=dn?A.useLayoutEffect:A.useEffect,fn=A.createContext(null);function pn(t,e){t.indexOf(e)===-1&&t.push(e)}function mn(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const ct=(t,e,n)=>n>e?e:n<t?t:n;let gn=()=>{};const ut={},Bs=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function Os(t){return typeof t=="object"&&t!==null}const Rs=t=>/^0[^.\s]+$/u.test(t);function yn(t){let e;return()=>(e===void 0&&(e=t()),e)}const it=t=>t,ua=(t,e)=>n=>e(t(n)),ie=(...t)=>t.reduce(ua),Zt=(t,e,n)=>{const i=e-t;return i===0?1:(n-t)/i};class xn{constructor(){this.subscriptions=[]}add(e){return pn(this.subscriptions,e),()=>mn(this.subscriptions,e)}notify(e,n,i){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](e,n,i);else for(let o=0;o<s;o++){const r=this.subscriptions[o];r&&r(e,n,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const ot=t=>t*1e3,at=t=>t/1e3;function Ns(t,e){return e?t*(1e3/e):0}const js=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,ha=1e-7,da=12;function fa(t,e,n,i,s){let o,r,a=0;do r=e+(n-e)/2,o=js(r,i,s)-t,o>0?n=r:e=r;while(Math.abs(o)>ha&&++a<da);return r}function se(t,e,n,i){if(t===e&&n===i)return it;const s=o=>fa(o,0,1,t,n);return o=>o===0||o===1?o:js(s(o),e,i)}const Is=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,_s=t=>e=>1-t(1-e),$s=se(.33,1.53,.69,.99),bn=_s($s),Us=Is(bn),Ws=t=>(t*=2)<1?.5*bn(t):.5*(2-Math.pow(2,-10*(t-1))),vn=t=>1-Math.sin(Math.acos(t)),zs=_s(vn),Ks=Is(vn),pa=se(.42,0,1,1),ma=se(0,0,.58,1),Ys=se(.42,0,.58,1),ga=t=>Array.isArray(t)&&typeof t[0]!="number",Gs=t=>Array.isArray(t)&&typeof t[0]=="number",ya={linear:it,easeIn:pa,easeInOut:Ys,easeOut:ma,circIn:vn,circInOut:Ks,circOut:zs,backIn:bn,backInOut:Us,backOut:$s,anticipate:Ws},xa=t=>typeof t=="string",vi=t=>{if(Gs(t)){gn(t.length===4);const[e,n,i,s]=t;return se(e,n,i,s)}else if(xa(t))return ya[t];return t},ue=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Ti={value:null,addProjectionMetrics:null};function ba(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function c(h){r.has(h)&&(u.schedule(h),t()),l++,h(a)}const u={schedule:(h,p=!1,g=!1)=>{const b=g&&s?n:i;return p&&r.add(h),b.has(h)||b.add(h),h},cancel:h=>{i.delete(h),r.delete(h)},process:h=>{if(a=h,s){o=!0;return}s=!0,[n,i]=[i,n],n.forEach(c),e&&Ti.value&&Ti.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,u.process(h))}};return u}const va=40;function Hs(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=ue.reduce((v,D)=>(v[D]=ba(o,e?D:void 0),v),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:u,update:h,preRender:p,render:g,postRender:x}=r,b=()=>{const v=ut.useManualTiming?s.timestamp:performance.now();n=!1,ut.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(v-s.timestamp,va),1)),s.timestamp=v,s.isProcessing=!0,a.process(s),l.process(s),c.process(s),u.process(s),h.process(s),p.process(s),g.process(s),x.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(b))},y=()=>{n=!0,i=!0,s.isProcessing||t(b)};return{schedule:ue.reduce((v,D)=>{const w=r[D];return v[D]=(F,I=!1,L=!1)=>(n||y(),w.schedule(F,I,L)),v},{}),cancel:v=>{for(let D=0;D<ue.length;D++)r[ue[D]].cancel(v)},state:s,steps:r}}const{schedule:N,cancel:pt,state:K,steps:Le}=Hs(typeof requestAnimationFrame<"u"?requestAnimationFrame:it,!0);let fe;function Ta(){fe=void 0}const Q={now:()=>(fe===void 0&&Q.set(K.isProcessing||ut.useManualTiming?K.timestamp:performance.now()),fe),set:t=>{fe=t,queueMicrotask(Ta)}},Xs=t=>e=>typeof e=="string"&&e.startsWith(t),Tn=Xs("--"),Sa=Xs("var(--"),Sn=t=>Sa(t)?Ca.test(t.split("/*")[0].trim()):!1,Ca=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Nt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Qt={...Nt,transform:t=>ct(0,1,t)},he={...Nt,default:1},Kt=t=>Math.round(t*1e5)/1e5,Cn=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Aa(t){return t==null}const Ea=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,An=(t,e)=>n=>!!(typeof n=="string"&&Ea.test(n)&&n.startsWith(t)||e&&!Aa(n)&&Object.prototype.hasOwnProperty.call(n,e)),Zs=(t,e,n)=>i=>{if(typeof i!="string")return i;const[s,o,r,a]=i.match(Cn);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},wa=t=>ct(0,255,t),Fe={...Nt,transform:t=>Math.round(wa(t))},St={test:An("rgb","red"),parse:Zs("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+Fe.transform(t)+", "+Fe.transform(e)+", "+Fe.transform(n)+", "+Kt(Qt.transform(i))+")"};function Pa(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}}const Ke={test:An("#"),parse:Pa,transform:St.transform},re=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),ft=re("deg"),lt=re("%"),M=re("px"),Ma=re("vh"),Da=re("vw"),Si=(()=>({...lt,parse:t=>lt.parse(t)/100,transform:t=>lt.transform(t*100)}))(),Dt={test:An("hsl","hue"),parse:Zs("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+lt.transform(Kt(e))+", "+lt.transform(Kt(n))+", "+Kt(Qt.transform(i))+")"},U={test:t=>St.test(t)||Ke.test(t)||Dt.test(t),parse:t=>St.test(t)?St.parse(t):Dt.test(t)?Dt.parse(t):Ke.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?St.transform(t):Dt.transform(t),getAnimatableNone:t=>{const e=U.parse(t);return e.alpha=0,U.transform(e)}},Va=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function La(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(Cn))==null?void 0:e.length)||0)+(((n=t.match(Va))==null?void 0:n.length)||0)>0}const Qs="number",Js="color",Fa="var",ka="var(",Ci="${}",Ba=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Jt(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const a=e.replace(Ba,l=>(U.test(l)?(i.color.push(o),s.push(Js),n.push(U.parse(l))):l.startsWith(ka)?(i.var.push(o),s.push(Fa),n.push(l)):(i.number.push(o),s.push(Qs),n.push(parseFloat(l))),++o,Ci)).split(Ci);return{values:n,split:a,indexes:i,types:s}}function qs(t){return Jt(t).values}function tr(t){const{split:e,types:n}=Jt(t),i=e.length;return s=>{let o="";for(let r=0;r<i;r++)if(o+=e[r],s[r]!==void 0){const a=n[r];a===Qs?o+=Kt(s[r]):a===Js?o+=U.transform(s[r]):o+=s[r]}return o}}const Oa=t=>typeof t=="number"?0:U.test(t)?U.getAnimatableNone(t):t;function Ra(t){const e=qs(t);return tr(t)(e.map(Oa))}const mt={test:La,parse:qs,createTransformer:tr,getAnimatableNone:Ra};function ke(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Na({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,e/=100,n/=100;let s=0,o=0,r=0;if(!e)s=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;s=ke(l,a,t+1/3),o=ke(l,a,t),r=ke(l,a,t-1/3)}return{red:Math.round(s*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:i}}function ve(t,e){return n=>n>0?e:t}const R=(t,e,n)=>t+(e-t)*n,Be=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},ja=[Ke,St,Dt],Ia=t=>ja.find(e=>e.test(t));function Ai(t){const e=Ia(t);if(!e)return!1;let n=e.parse(t);return e===Dt&&(n=Na(n)),n}const Ei=(t,e)=>{const n=Ai(t),i=Ai(e);if(!n||!i)return ve(t,e);const s={...n};return o=>(s.red=Be(n.red,i.red,o),s.green=Be(n.green,i.green,o),s.blue=Be(n.blue,i.blue,o),s.alpha=R(n.alpha,i.alpha,o),St.transform(s))},Ye=new Set(["none","hidden"]);function _a(t,e){return Ye.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function $a(t,e){return n=>R(t,e,n)}function En(t){return typeof t=="number"?$a:typeof t=="string"?Sn(t)?ve:U.test(t)?Ei:za:Array.isArray(t)?er:typeof t=="object"?U.test(t)?Ei:Ua:ve}function er(t,e){const n=[...t],i=n.length,s=t.map((o,r)=>En(o)(o,e[r]));return o=>{for(let r=0;r<i;r++)n[r]=s[r](o);return n}}function Ua(t,e){const n={...t,...e},i={};for(const s in n)t[s]!==void 0&&e[s]!==void 0&&(i[s]=En(t[s])(t[s],e[s]));return s=>{for(const o in i)n[o]=i[o](s);return n}}function Wa(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}const za=(t,e)=>{const n=mt.createTransformer(e),i=Jt(t),s=Jt(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?Ye.has(t)&&!s.values.length||Ye.has(e)&&!i.values.length?_a(t,e):ie(er(Wa(i,s),s.values),n):ve(t,e)};function nr(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?R(t,e,n):En(t)(t,e)}const Ka=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>N.update(e,n),stop:()=>pt(e),now:()=>K.isProcessing?K.timestamp:Q.now()}},ir=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let o=0;o<s;o++)i+=Math.round(t(o/(s-1))*1e4)/1e4+", ";return`linear(${i.substring(0,i.length-2)})`},Te=2e4;function wn(t){let e=0;const n=50;let i=t.next(e);for(;!i.done&&e<Te;)e+=n,i=t.next(e);return e>=Te?1/0:e}function Ya(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(wn(i),Te);return{type:"keyframes",ease:o=>i.next(s*o).value/e,duration:at(s)}}const Ga=5;function sr(t,e,n){const i=Math.max(e-Ga,0);return Ns(n-t(i),e-i)}const j={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Oe=.001;function Ha({duration:t=j.duration,bounce:e=j.bounce,velocity:n=j.velocity,mass:i=j.mass}){let s,o,r=1-e;r=ct(j.minDamping,j.maxDamping,r),t=ct(j.minDuration,j.maxDuration,at(t)),r<1?(s=c=>{const u=c*r,h=u*t,p=u-n,g=Ge(c,r),x=Math.exp(-h);return Oe-p/g*x},o=c=>{const h=c*r*t,p=h*n+n,g=Math.pow(r,2)*Math.pow(c,2)*t,x=Math.exp(-h),b=Ge(Math.pow(c,2),r);return(-s(c)+Oe>0?-1:1)*((p-g)*x)/b}):(s=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-Oe+u*h},o=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=Za(s,o,a);if(t=ot(t),isNaN(l))return{stiffness:j.stiffness,damping:j.damping,duration:t};{const c=Math.pow(l,2)*i;return{stiffness:c,damping:r*2*Math.sqrt(i*c),duration:t}}}const Xa=12;function Za(t,e,n){let i=n;for(let s=1;s<Xa;s++)i=i-t(i)/e(i);return i}function Ge(t,e){return t*Math.sqrt(1-e*e)}const Qa=["duration","bounce"],Ja=["stiffness","damping","mass"];function wi(t,e){return e.some(n=>t[n]!==void 0)}function qa(t){let e={velocity:j.velocity,stiffness:j.stiffness,damping:j.damping,mass:j.mass,isResolvedFromDuration:!1,...t};if(!wi(t,Ja)&&wi(t,Qa))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(n*1.2),s=i*i,o=2*ct(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:j.mass,stiffness:s,damping:o}}else{const n=Ha(t);e={...e,...n,mass:j.mass},e.isResolvedFromDuration=!0}return e}function Se(t=j.visualDuration,e=j.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:h,velocity:p,isResolvedFromDuration:g}=qa({...n,velocity:-at(n.velocity||0)}),x=p||0,b=c/(2*Math.sqrt(l*u)),y=r-o,m=at(Math.sqrt(l/u)),S=Math.abs(y)<5;i||(i=S?j.restSpeed.granular:j.restSpeed.default),s||(s=S?j.restDelta.granular:j.restDelta.default);let v;if(b<1){const w=Ge(m,b);v=F=>{const I=Math.exp(-b*m*F);return r-I*((x+b*m*y)/w*Math.sin(w*F)+y*Math.cos(w*F))}}else if(b===1)v=w=>r-Math.exp(-m*w)*(y+(x+m*y)*w);else{const w=m*Math.sqrt(b*b-1);v=F=>{const I=Math.exp(-b*m*F),L=Math.min(w*F,300);return r-I*((x+b*m*y)*Math.sinh(L)+w*y*Math.cosh(L))/w}}const D={calculatedDuration:g&&h||null,next:w=>{const F=v(w);if(g)a.done=w>=h;else{let I=w===0?x:0;b<1&&(I=w===0?ot(x):sr(v,w,F));const L=Math.abs(I)<=i,W=Math.abs(r-F)<=s;a.done=L&&W}return a.value=a.done?r:F,a},toString:()=>{const w=Math.min(wn(D),Te),F=ir(I=>D.next(w*I).value,w,30);return w+"ms "+F},toTransition:()=>{}};return D}Se.applyToOptions=t=>{const e=Ya(t,100,Se);return t.ease=e.ease,t.duration=ot(e.duration),t.type="keyframes",t};function He({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],p={done:!1,value:h},g=L=>a!==void 0&&L<a||l!==void 0&&L>l,x=L=>a===void 0?l:l===void 0||Math.abs(a-L)<Math.abs(l-L)?a:l;let b=n*e;const y=h+b,m=r===void 0?y:r(y);m!==y&&(b=m-h);const S=L=>-b*Math.exp(-L/i),v=L=>m+S(L),D=L=>{const W=S(L),X=v(L);p.done=Math.abs(W)<=c,p.value=p.done?m:X};let w,F;const I=L=>{g(p.value)&&(w=L,F=Se({keyframes:[p.value,x(p.value)],velocity:sr(v,L,p.value),damping:s,stiffness:o,restDelta:c,restSpeed:u}))};return I(0),{calculatedDuration:null,next:L=>{let W=!1;return!F&&w===void 0&&(W=!0,D(L),I(L)),w!==void 0&&L>=w?F.next(L-w):(!W&&D(L),p)}}}function tl(t,e,n){const i=[],s=n||ut.mix||nr,o=t.length-1;for(let r=0;r<o;r++){let a=s(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||it:e;a=ie(l,a)}i.push(a)}return i}function el(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(gn(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=tl(e,i,s),l=a.length,c=u=>{if(r&&u<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(u<t[h+1]);h++);const p=Zt(t[h],t[h+1],u);return a[h](p)};return n?u=>c(ct(t[0],t[o-1],u)):c}function nl(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=Zt(0,e,i);t.push(R(n,1,s))}}function il(t){const e=[0];return nl(e,t.length-1),e}function sl(t,e){return t.map(n=>n*e)}function rl(t,e){return t.map(()=>e||Ys).splice(0,t.length-1)}function Yt({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=ga(i)?i.map(vi):vi(i),o={done:!1,value:e[0]},r=sl(n&&n.length===e.length?n:il(e),t),a=el(r,e,{ease:Array.isArray(s)?s:rl(e,s)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const ol=t=>t!==null;function Pn(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(ol),a=s<0||e&&n!=="loop"&&e%2===1?0:o.length-1;return!a||i===void 0?o[a]:i}const al={decay:He,inertia:He,tween:Yt,keyframes:Yt,spring:Se};function rr(t){typeof t.type=="string"&&(t.type=al[t.type])}class Mn{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const ll=t=>t/100;class Dn extends Mn{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var i,s;const{motionValue:n}=this.options;n&&n.updatedAt!==Q.now()&&this.tick(Q.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(s=(i=this.options).onStop)==null||s.call(i))},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;rr(e);const{type:n=Yt,repeat:i=0,repeatDelay:s=0,repeatType:o,velocity:r=0}=e;let{keyframes:a}=e;const l=n||Yt;l!==Yt&&typeof a[0]!="number"&&(this.mixKeyframes=ie(ll,nr(a[0],a[1])),a=[0,100]);const c=l({...e,keyframes:a});o==="mirror"&&(this.mirroredGenerator=l({...e,keyframes:[...a].reverse(),velocity:-r})),c.calculatedDuration===null&&(c.calculatedDuration=wn(c));const{calculatedDuration:u}=c;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=c}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:i,totalDuration:s,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return i.next(0);const{delay:c=0,keyframes:u,repeat:h,repeatType:p,repeatDelay:g,type:x,onUpdate:b,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-s/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const m=this.currentTime-c*(this.playbackSpeed>=0?1:-1),S=this.playbackSpeed>=0?m<0:m>s;this.currentTime=Math.max(m,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=s);let v=this.currentTime,D=i;if(h){const L=Math.min(this.currentTime,s)/a;let W=Math.floor(L),X=L%1;!X&&L>=1&&(X=1),X===1&&W--,W=Math.min(W,h+1),!!(W%2)&&(p==="reverse"?(X=1-X,g&&(X-=g/a)):p==="mirror"&&(D=r)),v=ct(0,1,X)*a}const w=S?{done:!1,value:u[0]}:D.next(v);o&&(w.value=o(w.value));let{done:F}=w;!S&&l!==null&&(F=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const I=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&F);return I&&x!==He&&(w.value=Pn(u,this.options,y,this.speed)),b&&b(w.value),I&&this.finish(),w}then(e,n){return this.finished.then(e,n)}get duration(){return at(this.calculatedDuration)}get time(){return at(this.currentTime)}set time(e){var n;e=ot(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(Q.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=at(this.currentTime))}play(){var s,o;if(this.isStopped)return;const{driver:e=Ka,startTime:n}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),(o=(s=this.options).onPlay)==null||o.call(s);const i=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=i):this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime||(this.startTime=n??i),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Q.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(e=this.options).onComplete)==null||n.call(e)}cancel(){var e,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(e=this.options).onCancel)==null||n.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),e.observe(this)}}function cl(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Ct=t=>t*180/Math.PI,Xe=t=>{const e=Ct(Math.atan2(t[1],t[0]));return Ze(e)},ul={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Xe,rotateZ:Xe,skewX:t=>Ct(Math.atan(t[1])),skewY:t=>Ct(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ze=t=>(t=t%360,t<0&&(t+=360),t),Pi=Xe,Mi=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Di=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),hl={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Mi,scaleY:Di,scale:t=>(Mi(t)+Di(t))/2,rotateX:t=>Ze(Ct(Math.atan2(t[6],t[5]))),rotateY:t=>Ze(Ct(Math.atan2(-t[2],t[0]))),rotateZ:Pi,rotate:Pi,skewX:t=>Ct(Math.atan(t[4])),skewY:t=>Ct(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Qe(t){return t.includes("scale")?1:0}function Je(t,e){if(!t||t==="none")return Qe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=hl,s=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ul,s=a}if(!s)return Qe(e);const o=i[e],r=s[1].split(",").map(fl);return typeof o=="function"?o(r):r[o]}const dl=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Je(n,e)};function fl(t){return parseFloat(t.trim())}const jt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],It=(()=>new Set(jt))(),Vi=t=>t===Nt||t===M,pl=new Set(["x","y","z"]),ml=jt.filter(t=>!pl.has(t));function gl(t){const e=[];return ml.forEach(n=>{const i=t.getValue(n);i!==void 0&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}const At={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Je(e,"x"),y:(t,{transform:e})=>Je(e,"y")};At.translateX=At.x;At.translateY=At.y;const Et=new Set;let qe=!1,tn=!1,en=!1;function or(){if(tn){const t=Array.from(Et).filter(i=>i.needsMeasurement),e=new Set(t.map(i=>i.element)),n=new Map;e.forEach(i=>{const s=gl(i);s.length&&(n.set(i,s),i.render())}),t.forEach(i=>i.measureInitialState()),e.forEach(i=>{i.render();const s=n.get(i);s&&s.forEach(([o,r])=>{var a;(a=i.getValue(o))==null||a.set(r)})}),t.forEach(i=>i.measureEndState()),t.forEach(i=>{i.suspendedScrollY!==void 0&&window.scrollTo(0,i.suspendedScrollY)})}tn=!1,qe=!1,Et.forEach(t=>t.complete(en)),Et.clear()}function ar(){Et.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tn=!0)})}function yl(){en=!0,ar(),or(),en=!1}class Vn{constructor(e,n,i,s,o,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=i,this.motionValue=s,this.element=o,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(Et.add(this),qe||(qe=!0,N.read(ar),N.resolveKeyframes(or))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:i,motionValue:s}=this;if(e[0]===null){const o=s==null?void 0:s.get(),r=e[e.length-1];if(o!==void 0)e[0]=o;else if(i&&n){const a=i.readValue(n,r);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=r),s&&o===void 0&&s.set(e[0])}cl(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),Et.delete(this)}cancel(){this.state==="scheduled"&&(Et.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const xl=t=>t.startsWith("--");function bl(t,e,n){xl(e)?t.style.setProperty(e,n):t.style[e]=n}const vl=yn(()=>window.ScrollTimeline!==void 0),Tl={};function Sl(t,e){const n=yn(t);return()=>Tl[e]??n()}const lr=Sl(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Wt=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Li={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Wt([0,.65,.55,1]),circOut:Wt([.55,0,1,.45]),backIn:Wt([.31,.01,.66,-.59]),backOut:Wt([.33,1.53,.69,.99])};function cr(t,e){if(t)return typeof t=="function"?lr()?ir(t,e):"ease-out":Gs(t)?Wt(t):Array.isArray(t)?t.map(n=>cr(n,e)||Li.easeOut):Li[t]}function Cl(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},c=void 0){const u={[e]:n};l&&(u.offset=l);const h=cr(a,s);Array.isArray(h)&&(u.easing=h);const p={delay:i,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"};return c&&(p.pseudoElement=c),t.animate(u,p)}function ur(t){return typeof t=="function"&&"applyToOptions"in t}function Al({type:t,...e}){return ur(t)&&lr()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class El extends Mn{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:i,keyframes:s,pseudoElement:o,allowFlatten:r=!1,finalKeyframe:a,onComplete:l}=e;this.isPseudoElement=!!o,this.allowFlatten=r,this.options=e,gn(typeof e.type!="string");const c=Al(e);this.animation=Cl(n,i,s,c,o),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const u=Pn(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(u):bl(n,i,u),this.animation.cancel()}l==null||l(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,n;(n=(e=this.animation).finish)==null||n.call(e)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,n;this.isPseudoElement||(n=(e=this.animation).commitStyles)==null||n.call(e)}get duration(){var n,i;const e=((i=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:i.call(n).duration)||0;return at(Number(e))}get time(){return at(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=ot(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){var i;return this.allowFlatten&&((i=this.animation.effect)==null||i.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&vl()?(this.animation.timeline=e,it):n(this)}}const hr={anticipate:Ws,backInOut:Us,circInOut:Ks};function wl(t){return t in hr}function Pl(t){typeof t.ease=="string"&&wl(t.ease)&&(t.ease=hr[t.ease])}const Fi=10;class Ml extends El{constructor(e){Pl(e),rr(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:i,onComplete:s,element:o,...r}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new Dn({...r,autoplay:!1}),l=ot(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-Fi).value,a.sample(l).value,Fi),a.stop()}}const ki=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(mt.test(t)||t==="0")&&!t.startsWith("url("));function Dl(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function Vl(t,e,n,i){const s=t[0];if(s===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=ki(s,e),a=ki(o,e);return!r||!a?!1:Dl(t)||(n==="spring"||ur(n))&&i}function dr(t){return Os(t)&&"offsetHeight"in t}const Ll=new Set(["opacity","clipPath","filter","transform"]),Fl=yn(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function kl(t){var c;const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!dr((c=e==null?void 0:e.owner)==null?void 0:c.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Fl()&&n&&Ll.has(n)&&(n!=="transform"||!l)&&!a&&!i&&s!=="mirror"&&o!==0&&r!=="inertia"}const Bl=40;class Ol extends Mn{constructor({autoplay:e=!0,delay:n=0,type:i="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:r="loop",keyframes:a,name:l,motionValue:c,element:u,...h}){var x;super(),this.stop=()=>{var b,y;this._animation&&(this._animation.stop(),(b=this.stopTimeline)==null||b.call(this)),(y=this.keyframeResolver)==null||y.cancel()},this.createdAt=Q.now();const p={autoplay:e,delay:n,type:i,repeat:s,repeatDelay:o,repeatType:r,name:l,motionValue:c,element:u,...h},g=(u==null?void 0:u.KeyframeResolver)||Vn;this.keyframeResolver=new g(a,(b,y,m)=>this.onKeyframesResolved(b,y,p,!m),l,c,u),(x=this.keyframeResolver)==null||x.scheduleResolve()}onKeyframesResolved(e,n,i,s){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:l,isHandoff:c,onUpdate:u}=i;this.resolvedAt=Q.now(),Vl(e,o,r,a)||((ut.instantAnimations||!l)&&(u==null||u(Pn(e,i,n))),e[0]=e[e.length-1],i.duration=0,i.repeat=0);const p={startTime:s?this.resolvedAt?this.resolvedAt-this.createdAt>Bl?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...i,keyframes:e},g=!c&&kl(p)?new Ml({...p,element:p.motionValue.owner.current}):new Dn(p);g.finished.then(()=>this.notifyFinished()).catch(it),this.pendingTimeline&&(this.stopTimeline=g.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=g}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){var e;return this._animation||((e=this.keyframeResolver)==null||e.resume(),yl()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),(e=this.keyframeResolver)==null||e.cancel()}}const Rl=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Nl(t){const e=Rl.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}function fr(t,e,n=1){const[i,s]=Nl(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const r=o.trim();return Bs(r)?parseFloat(r):r}return Sn(s)?fr(s,e,n+1):s}function Ln(t,e){return(t==null?void 0:t[e])??(t==null?void 0:t.default)??t}const pr=new Set(["width","height","top","left","right","bottom",...jt]),jl={test:t=>t==="auto",parse:t=>t},mr=t=>e=>e.test(t),gr=[Nt,M,lt,ft,Da,Ma,jl],Bi=t=>gr.find(mr(t));function Il(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Rs(t):!0}const _l=new Set(["brightness","contrast","saturate","opacity"]);function $l(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[i]=n.match(Cn)||[];if(!i)return t;const s=n.replace(i,"");let o=_l.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Ul=/\b([a-z-]*)\(.*?\)/gu,nn={...mt,getAnimatableNone:t=>{const e=t.match(Ul);return e?e.map($l).join(" "):t}},Oi={...Nt,transform:Math.round},Wl={rotate:ft,rotateX:ft,rotateY:ft,rotateZ:ft,scale:he,scaleX:he,scaleY:he,scaleZ:he,skew:ft,skewX:ft,skewY:ft,distance:M,translateX:M,translateY:M,translateZ:M,x:M,y:M,z:M,perspective:M,transformPerspective:M,opacity:Qt,originX:Si,originY:Si,originZ:M},Fn={borderWidth:M,borderTopWidth:M,borderRightWidth:M,borderBottomWidth:M,borderLeftWidth:M,borderRadius:M,radius:M,borderTopLeftRadius:M,borderTopRightRadius:M,borderBottomRightRadius:M,borderBottomLeftRadius:M,width:M,maxWidth:M,height:M,maxHeight:M,top:M,right:M,bottom:M,left:M,padding:M,paddingTop:M,paddingRight:M,paddingBottom:M,paddingLeft:M,margin:M,marginTop:M,marginRight:M,marginBottom:M,marginLeft:M,backgroundPositionX:M,backgroundPositionY:M,...Wl,zIndex:Oi,fillOpacity:Qt,strokeOpacity:Qt,numOctaves:Oi},zl={...Fn,color:U,backgroundColor:U,outlineColor:U,fill:U,stroke:U,borderColor:U,borderTopColor:U,borderRightColor:U,borderBottomColor:U,borderLeftColor:U,filter:nn,WebkitFilter:nn},yr=t=>zl[t];function xr(t,e){let n=yr(t);return n!==nn&&(n=mt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Kl=new Set(["auto","none","0"]);function Yl(t,e,n){let i=0,s;for(;i<t.length&&!s;){const o=t[i];typeof o=="string"&&!Kl.has(o)&&Jt(o).values.length&&(s=t[i]),i++}if(s&&n)for(const o of e)t[o]=xr(n,s)}class Gl extends Vn{constructor(e,n,i,s,o){super(e,n,i,s,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:i}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),Sn(c))){const u=fr(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!pr.has(i)||e.length!==2)return;const[s,o]=e,r=Bi(s),a=Bi(o);if(r!==a)if(Vi(r)&&Vi(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else At[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,i=[];for(let s=0;s<e.length;s++)(e[s]===null||Il(e[s]))&&i.push(s);i.length&&Yl(e,i,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:i}=this;if(!e||!e.current)return;i==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=At[i](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const s=n[n.length-1];s!==void 0&&e.getValue(i,s).jump(s,!1)}measureEndState(){var a;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=At[n](e.measureViewportBox(),window.getComputedStyle(e.current)),r!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=r),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([l,c])=>{e.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function Hl(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let i=document;e&&(i=e.current);const s=(n==null?void 0:n[t])??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}const br=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Ri=30,Xl=t=>!isNaN(parseFloat(t));class Zl{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(i,s=!0)=>{var r,a;const o=Q.now();if(this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(i),this.current!==this.prev&&((r=this.events.change)==null||r.notify(this.current),this.dependents))for(const l of this.dependents)l.dirty();s&&((a=this.events.renderRequest)==null||a.notify(this.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=Q.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Xl(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new xn);const i=this.events[e].add(n);return e==="change"?()=>{i(),N.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,i){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;(e=this.events.change)==null||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=Q.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Ri)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Ri);return Ns(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,n;(e=this.dependents)==null||e.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ot(t,e){return new Zl(t,e)}const{schedule:kn,cancel:sd}=Hs(queueMicrotask,!1),st={x:!1,y:!1};function vr(){return st.x||st.y}function Ql(t){return t==="x"||t==="y"?st[t]?null:(st[t]=!0,()=>{st[t]=!1}):st.x||st.y?null:(st.x=st.y=!0,()=>{st.x=st.y=!1})}function Tr(t,e){const n=Hl(t),i=new AbortController,s={passive:!0,...e,signal:i.signal};return[n,s,()=>i.abort()]}function Ni(t){return!(t.pointerType==="touch"||vr())}function Jl(t,e,n={}){const[i,s,o]=Tr(t,n),r=a=>{if(!Ni(a))return;const{target:l}=a,c=e(l,a);if(typeof c!="function"||!l)return;const u=h=>{Ni(h)&&(c(h),l.removeEventListener("pointerleave",u))};l.addEventListener("pointerleave",u,s)};return i.forEach(a=>{a.addEventListener("pointerenter",r,s)}),o}const Sr=(t,e)=>e?t===e?!0:Sr(t,e.parentElement):!1,Bn=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,ql=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function tc(t){return ql.has(t.tagName)||t.tabIndex!==-1}const pe=new WeakSet;function ji(t){return e=>{e.key==="Enter"&&t(e)}}function Re(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const ec=(t,e)=>{const n=t.currentTarget;if(!n)return;const i=ji(()=>{if(pe.has(n))return;Re(n,"down");const s=ji(()=>{Re(n,"up")}),o=()=>Re(n,"cancel");n.addEventListener("keyup",s,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)};function Ii(t){return Bn(t)&&!vr()}function nc(t,e,n={}){const[i,s,o]=Tr(t,n),r=a=>{const l=a.currentTarget;if(!Ii(a))return;pe.add(l);const c=e(l,a),u=(g,x)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",p),pe.has(l)&&pe.delete(l),Ii(g)&&typeof c=="function"&&c(g,{success:x})},h=g=>{u(g,l===window||l===document||n.useGlobalTarget||Sr(l,g.target))},p=g=>{u(g,!1)};window.addEventListener("pointerup",h,s),window.addEventListener("pointercancel",p,s)};return i.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",r,s),dr(a)&&(a.addEventListener("focus",c=>ec(c,s)),!tc(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function Cr(t){return Os(t)&&"ownerSVGElement"in t}function ic(t){return Cr(t)&&t.tagName==="svg"}const G=t=>!!(t&&t.getVelocity),sc=[...gr,U,mt],rc=t=>sc.find(mr(t)),Ar=A.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});function oc(t=!0){const e=A.useContext(fn);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:i,register:s}=e,o=A.useId();A.useEffect(()=>{if(t)return s(o)},[t]);const r=A.useCallback(()=>t&&i&&i(o),[o,i,t]);return!n&&i?[!1,r]:[!0]}const Er=A.createContext({strict:!1}),_i={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Rt={};for(const t in _i)Rt[t]={isEnabled:e=>_i[t].some(n=>!!e[n])};function ac(t){for(const e in t)Rt[e]={...Rt[e],...t[e]}}const lc=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ce(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||lc.has(t)}let wr=t=>!Ce(t);function cc(t){typeof t=="function"&&(wr=e=>e.startsWith("on")?!Ce(e):t(e))}try{cc(require("@emotion/is-prop-valid").default)}catch{}function uc(t,e,n){const i={};for(const s in t)s==="values"&&typeof t.values=="object"||(wr(s)||n===!0&&Ce(s)||!e&&!Ce(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}function hc(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...i)=>t(...i);return new Proxy(n,{get:(i,s)=>s==="create"?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}const Ee=A.createContext({});function we(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function qt(t){return typeof t=="string"||Array.isArray(t)}const On=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Rn=["initial",...On];function Pe(t){return we(t.animate)||Rn.some(e=>qt(t[e]))}function Pr(t){return!!(Pe(t)||t.variants)}function dc(t,e){if(Pe(t)){const{initial:n,animate:i}=t;return{initial:n===!1||qt(n)?n:void 0,animate:qt(i)?i:void 0}}return t.inherit!==!1?e:{}}function fc(t){const{initial:e,animate:n}=dc(t,A.useContext(Ee));return A.useMemo(()=>({initial:e,animate:n}),[$i(e),$i(n)])}function $i(t){return Array.isArray(t)?t.join(" "):t}const pc=Symbol.for("motionComponentSymbol");function Vt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function mc(t,e,n){return A.useCallback(i=>{i&&t.onMount&&t.onMount(i),e&&(i?e.mount(i):e.unmount()),n&&(typeof n=="function"?n(i):Vt(n)&&(n.current=i))},[e])}const Nn=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),gc="framerAppearId",Mr="data-"+Nn(gc),Dr=A.createContext({});function yc(t,e,n,i,s){var b,y;const{visualElement:o}=A.useContext(Ee),r=A.useContext(Er),a=A.useContext(fn),l=A.useContext(Ar).reducedMotion,c=A.useRef(null);i=i||r.renderer,!c.current&&i&&(c.current=i(t,{visualState:e,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const u=c.current,h=A.useContext(Dr);u&&!u.projection&&s&&(u.type==="html"||u.type==="svg")&&xc(c.current,n,s,h);const p=A.useRef(!1);A.useInsertionEffect(()=>{u&&p.current&&u.update(n,a)});const g=n[Mr],x=A.useRef(!!g&&!((b=window.MotionHandoffIsComplete)!=null&&b.call(window,g))&&((y=window.MotionHasOptimisedAnimation)==null?void 0:y.call(window,g)));return ca(()=>{u&&(p.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),kn.render(u.render),x.current&&u.animationState&&u.animationState.animateChanges())}),A.useEffect(()=>{u&&(!x.current&&u.animationState&&u.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var m;(m=window.MotionHandoffMarkAsComplete)==null||m.call(window,g)}),x.current=!1))}),u}function xc(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:c,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Vr(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:!!r||a&&Vt(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:i,crossfade:u,layoutScroll:l,layoutRoot:c})}function Vr(t){if(t)return t.options.allowProjection!==!1?t.projection:Vr(t.parent)}function bc({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:i,Component:s}){t&&ac(t);function o(a,l){let c;const u={...A.useContext(Ar),...a,layoutId:vc(a)},{isStatic:h}=u,p=fc(a),g=i(a,h);if(!h&&dn){Tc();const x=Sc(u);c=x.MeasureLayout,p.visualElement=yc(s,g,u,e,x.ProjectionNode)}return ye.jsxs(Ee.Provider,{value:p,children:[c&&p.visualElement?ye.jsx(c,{visualElement:p.visualElement,...u}):null,n(s,a,mc(g,p.visualElement,l),g,h,p.visualElement)]})}o.displayName=`motion.${typeof s=="string"?s:`create(${s.displayName??s.name??""})`}`;const r=A.forwardRef(o);return r[pc]=s,r}function vc({layoutId:t}){const e=A.useContext(ks).id;return e&&t!==void 0?e+"-"+t:t}function Tc(t,e){A.useContext(Er).strict}function Sc(t){const{drag:e,layout:n}=Rt;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}const te={};function Cc(t){for(const e in t)te[e]=t[e],Tn(e)&&(te[e].isCSSVariable=!0)}function Lr(t,{layout:e,layoutId:n}){return It.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!te[t]||t==="opacity")}const Ac={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ec=jt.length;function wc(t,e,n){let i="",s=!0;for(let o=0;o<Ec;o++){const r=jt[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const c=br(a,Fn[r]);if(!l){s=!1;const u=Ac[r]||r;i+=`${u}(${c}) `}n&&(e[r]=c)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}function jn(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const c=e[l];if(It.has(l)){r=!0;continue}else if(Tn(l)){s[l]=c;continue}else{const u=br(c,Fn[l]);l.startsWith("origin")?(a=!0,o[l]=u):i[l]=u}}if(e.transform||(r||n?i.transform=wc(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;i.transformOrigin=`${l} ${c} ${u}`}}const In=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Fr(t,e,n){for(const i in e)!G(e[i])&&!Lr(i,n)&&(t[i]=e[i])}function Pc({transformTemplate:t},e){return A.useMemo(()=>{const n=In();return jn(n,e,t),Object.assign({},n.vars,n.style)},[e])}function Mc(t,e){const n=t.style||{},i={};return Fr(i,n,t),Object.assign(i,Pc(t,e)),i}function Dc(t,e){const n={},i=Mc(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Vc={offset:"stroke-dashoffset",array:"stroke-dasharray"},Lc={offset:"strokeDashoffset",array:"strokeDasharray"};function Fc(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Vc:Lc;t[o.offset]=M.transform(-i);const r=M.transform(e),a=M.transform(n);t[o.array]=`${r} ${a}`}function kr(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,c,u){if(jn(t,a,c),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:p}=t;h.transform&&(p.transform=h.transform,delete h.transform),(p.transform||h.transformOrigin)&&(p.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),p.transform&&(p.transformBox=(u==null?void 0:u.transformBox)??"fill-box",delete h.transformBox),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),i!==void 0&&(h.scale=i),s!==void 0&&Fc(h,s,o,r,!1)}const Br=()=>({...In(),attrs:{}}),Or=t=>typeof t=="string"&&t.toLowerCase()==="svg";function kc(t,e,n,i){const s=A.useMemo(()=>{const o=Br();return kr(o,e,Or(i),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};Fr(o,t.style,t),s.style={...o,...s.style}}return s}const Bc=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function _n(t){return typeof t!="string"||t.includes("-")?!1:!!(Bc.indexOf(t)>-1||/[A-Z]/u.test(t))}function Oc(t=!1){return(n,i,s,{latestValues:o},r)=>{const l=(_n(n)?kc:Dc)(i,o,r,n),c=uc(i,typeof n=="string",t),u=n!==A.Fragment?{...c,...l,ref:s}:{},{children:h}=i,p=A.useMemo(()=>G(h)?h.get():h,[h]);return A.createElement(n,{...u,children:p})}}function Ui(t){const e=[{},{}];return t==null||t.values.forEach((n,i)=>{e[0][i]=n.get(),e[1][i]=n.getVelocity()}),e}function $n(t,e,n,i){if(typeof e=="function"){const[s,o]=Ui(i);e=e(n!==void 0?n:t.custom,s,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[s,o]=Ui(i);e=e(n!==void 0?n:t.custom,s,o)}return e}function me(t){return G(t)?t.get():t}function Rc({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:Nc(n,i,s,t),renderState:e()}}const Rr=t=>(e,n)=>{const i=A.useContext(Ee),s=A.useContext(fn),o=()=>Rc(t,e,i,s);return n?o():la(o)};function Nc(t,e,n,i){const s={},o=i(t,{});for(const p in o)s[p]=me(o[p]);let{initial:r,animate:a}=t;const l=Pe(t),c=Pr(t);e&&c&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||r===!1;const h=u?a:r;if(h&&typeof h!="boolean"&&!we(h)){const p=Array.isArray(h)?h:[h];for(let g=0;g<p.length;g++){const x=$n(t,p[g]);if(x){const{transitionEnd:b,transition:y,...m}=x;for(const S in m){let v=m[S];if(Array.isArray(v)){const D=u?v.length-1:0;v=v[D]}v!==null&&(s[S]=v)}for(const S in b)s[S]=b[S]}}}return s}function Un(t,e,n){var o;const{style:i}=t,s={};for(const r in i)(G(i[r])||e.style&&G(e.style[r])||Lr(r,t)||((o=n==null?void 0:n.getValue(r))==null?void 0:o.liveStyle)!==void 0)&&(s[r]=i[r]);return s}const jc={useVisualState:Rr({scrapeMotionValuesFromProps:Un,createRenderState:In})};function Nr(t,e,n){const i=Un(t,e,n);for(const s in t)if(G(t[s])||G(e[s])){const o=jt.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;i[o]=t[s]}return i}const Ic={useVisualState:Rr({scrapeMotionValuesFromProps:Nr,createRenderState:Br})};function _c(t,e){return function(i,{forwardMotionProps:s}={forwardMotionProps:!1}){const r={..._n(i)?Ic:jc,preloadedFeatures:t,useRender:Oc(s),createVisualElement:e,Component:i};return bc(r)}}function ee(t,e,n){const i=t.getProps();return $n(i,e,n!==void 0?n:i.custom,t)}const sn=t=>Array.isArray(t);function $c(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Ot(n))}function Uc(t){return sn(t)?t[t.length-1]||0:t}function Wc(t,e){const n=ee(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const r in o){const a=Uc(o[r]);$c(t,r,a)}}function zc(t){return!!(G(t)&&t.add)}function rn(t,e){const n=t.getValue("willChange");if(zc(n))return n.add(e);if(!n&&ut.WillChange){const i=new ut.WillChange("auto");t.addValue("willChange",i),i.add(e)}}function jr(t){return t.props[Mr]}const Kc=t=>t!==null;function Yc(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(Kc),o=e&&n!=="loop"&&e%2===1?0:s.length-1;return!o||i===void 0?s[o]:i}const Gc={type:"spring",stiffness:500,damping:25,restSpeed:10},Hc=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Xc={type:"keyframes",duration:.8},Zc={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Qc=(t,{keyframes:e})=>e.length>2?Xc:It.has(t)?t.startsWith("scale")?Hc(e[1]):Gc:Zc;function Jc({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const Wn=(t,e,n,i={},s,o)=>r=>{const a=Ln(i,t)||{},l=a.delay||i.delay||0;let{elapsed:c=0}=i;c=c-ot(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:p=>{e.set(p),a.onUpdate&&a.onUpdate(p)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};Jc(a)||Object.assign(u,Qc(t,u)),u.duration&&(u.duration=ot(u.duration)),u.repeatDelay&&(u.repeatDelay=ot(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),(ut.instantAnimations||ut.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!o&&e.get()!==void 0){const p=Yc(u.keyframes,a);if(p!==void 0){N.update(()=>{u.onUpdate(p),u.onComplete()});return}}return a.isSync?new Dn(u):new Ol(u)};function qc({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,i}function Ir(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],c=s&&t.animationState&&t.animationState.getState()[s];for(const u in a){const h=t.getValue(u,t.latestValues[u]??null),p=a[u];if(p===void 0||c&&qc(c,u))continue;const g={delay:n,...Ln(o||{},u)},x=h.get();if(x!==void 0&&!h.isAnimating&&!Array.isArray(p)&&p===x&&!g.velocity)continue;let b=!1;if(window.MotionHandoffAnimation){const m=jr(t);if(m){const S=window.MotionHandoffAnimation(m,u,N);S!==null&&(g.startTime=S,b=!0)}}rn(t,u),h.start(Wn(u,h,p,t.shouldReduceMotion&&pr.has(u)?{type:!1}:g,t,b));const y=h.animation;y&&l.push(y)}return r&&Promise.all(l).then(()=>{N.update(()=>{r&&Wc(t,r)})}),l}function on(t,e,n={}){var l;const i=ee(t,e,n.type==="exit"?(l=t.presenceContext)==null?void 0:l.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Ir(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:p}=s;return tu(t,e,u+c,h,p,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[c,u]=a==="beforeChildren"?[o,r]:[r,o];return c().then(()=>u())}else return Promise.all([o(),r(n.delay)])}function tu(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=s===1?(c=0)=>c*i:(c=0)=>a-c*i;return Array.from(t.variantChildren).sort(eu).forEach((c,u)=>{c.notify("AnimationStart",e),r.push(on(c,e,{...o,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(r)}function eu(t,e){return t.sortNodePosition(e)}function nu(t,e,n={}){t.notify("AnimationStart",e);let i;if(Array.isArray(e)){const s=e.map(o=>on(t,o,n));i=Promise.all(s)}else if(typeof e=="string")i=on(t,e,n);else{const s=typeof e=="function"?ee(t,e,n.custom):e;i=Promise.all(Ir(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}function _r(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const iu=Rn.length;function $r(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?$r(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<iu;n++){const i=Rn[n],s=t.props[i];(qt(s)||s===!1)&&(e[i]=s)}return e}const su=[...On].reverse(),ru=On.length;function ou(t){return e=>Promise.all(e.map(({animation:n,options:i})=>nu(t,n,i)))}function au(t){let e=ou(t),n=Wi(),i=!0;const s=l=>(c,u)=>{var p;const h=ee(t,u,l==="exit"?(p=t.presenceContext)==null?void 0:p.custom:void 0);if(h){const{transition:g,transitionEnd:x,...b}=h;c={...c,...b,...x}}return c};function o(l){e=l(t)}function r(l){const{props:c}=t,u=$r(t.parent)||{},h=[],p=new Set;let g={},x=1/0;for(let y=0;y<ru;y++){const m=su[y],S=n[m],v=c[m]!==void 0?c[m]:u[m],D=qt(v),w=m===l?S.isActive:null;w===!1&&(x=y);let F=v===u[m]&&v!==c[m]&&D;if(F&&i&&t.manuallyAnimateOnMount&&(F=!1),S.protectedKeys={...g},!S.isActive&&w===null||!v&&!S.prevProp||we(v)||typeof v=="boolean")continue;const I=lu(S.prevProp,v);let L=I||m===l&&S.isActive&&!F&&D||y>x&&D,W=!1;const X=Array.isArray(v)?v:[v];let yt=X.reduce(s(m),{});w===!1&&(yt={});const{prevResolvedValues:ae={}}=S,Kn={...ae,...yt},le=z=>{L=!0,p.has(z)&&(W=!0,p.delete(z)),S.needsAnimating[z]=!0;const J=t.getValue(z);J&&(J.liveStyle=!1)};for(const z in Kn){const J=yt[z],_t=ae[z];if(g.hasOwnProperty(z))continue;let wt=!1;sn(J)&&sn(_t)?wt=!_r(J,_t):wt=J!==_t,wt?J!=null?le(z):p.add(z):J!==void 0&&p.has(z)?le(z):S.protectedKeys[z]=!0}S.prevProp=v,S.prevResolvedValues=yt,S.isActive&&(g={...g,...yt}),i&&t.blockInitialAnimation&&(L=!1),L&&(!(F&&I)||W)&&h.push(...X.map(z=>({animation:z,options:{type:m}})))}if(p.size){const y={};if(typeof c.initial!="boolean"){const m=ee(t,Array.isArray(c.initial)?c.initial[0]:c.initial);m&&m.transition&&(y.transition=m.transition)}p.forEach(m=>{const S=t.getBaseTarget(m),v=t.getValue(m);v&&(v.liveStyle=!0),y[m]=S??null}),h.push({animation:y})}let b=!!h.length;return i&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(b=!1),i=!1,b?e(h):Promise.resolve()}function a(l,c){var h;if(n[l].isActive===c)return Promise.resolve();(h=t.variantChildren)==null||h.forEach(p=>{var g;return(g=p.animationState)==null?void 0:g.setActive(l,c)}),n[l].isActive=c;const u=r(l);for(const p in n)n[p].protectedKeys={};return u}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Wi(),i=!0}}}function lu(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!_r(e,t):!1}function bt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Wi(){return{animate:bt(!0),whileInView:bt(),whileHover:bt(),whileTap:bt(),whileDrag:bt(),whileFocus:bt(),exit:bt()}}class gt{constructor(e){this.isMounted=!1,this.node=e}update(){}}class cu extends gt{constructor(e){super(e),e.animationState||(e.animationState=au(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();we(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)==null||e.call(this)}}let uu=0;class hu extends gt{constructor(){super(...arguments),this.id=uu++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;const s=this.node.animationState.setActive("exit",!e);n&&!e&&s.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const du={animation:{Feature:cu},exit:{Feature:hu}};function ne(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function oe(t){return{point:{x:t.pageX,y:t.pageY}}}const fu=t=>e=>Bn(e)&&t(e,oe(e));function Gt(t,e,n,i){return ne(t,e,fu(n),i)}function Ur({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function pu({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function mu(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}const Wr=1e-4,gu=1-Wr,yu=1+Wr,zr=.01,xu=0-zr,bu=0+zr;function H(t){return t.max-t.min}function vu(t,e,n){return Math.abs(t-e)<=n}function zi(t,e,n,i=.5){t.origin=i,t.originPoint=R(e.min,e.max,t.origin),t.scale=H(n)/H(e),t.translate=R(n.min,n.max,t.origin)-t.originPoint,(t.scale>=gu&&t.scale<=yu||isNaN(t.scale))&&(t.scale=1),(t.translate>=xu&&t.translate<=bu||isNaN(t.translate))&&(t.translate=0)}function Ht(t,e,n,i){zi(t.x,e.x,n.x,i?i.originX:void 0),zi(t.y,e.y,n.y,i?i.originY:void 0)}function Ki(t,e,n){t.min=n.min+e.min,t.max=t.min+H(e)}function Tu(t,e,n){Ki(t.x,e.x,n.x),Ki(t.y,e.y,n.y)}function Yi(t,e,n){t.min=e.min-n.min,t.max=t.min+H(e)}function Xt(t,e,n){Yi(t.x,e.x,n.x),Yi(t.y,e.y,n.y)}const Gi=()=>({translate:0,scale:1,origin:0,originPoint:0}),Lt=()=>({x:Gi(),y:Gi()}),Hi=()=>({min:0,max:0}),_=()=>({x:Hi(),y:Hi()});function nt(t){return[t("x"),t("y")]}function Ne(t){return t===void 0||t===1}function an({scale:t,scaleX:e,scaleY:n}){return!Ne(t)||!Ne(e)||!Ne(n)}function vt(t){return an(t)||Kr(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Kr(t){return Xi(t.x)||Xi(t.y)}function Xi(t){return t&&t!=="0%"}function Ae(t,e,n){const i=t-n,s=e*i;return n+s}function Zi(t,e,n,i,s){return s!==void 0&&(t=Ae(t,s,i)),Ae(t,n,i)+e}function ln(t,e=0,n=1,i,s){t.min=Zi(t.min,e,n,i,s),t.max=Zi(t.max,e,n,i,s)}function Yr(t,{x:e,y:n}){ln(t.x,e.translate,e.scale,e.originPoint),ln(t.y,n.translate,n.scale,n.originPoint)}const Qi=.999999999999,Ji=1.0000000000001;function Su(t,e,n,i=!1){const s=n.length;if(!s)return;e.x=e.y=1;let o,r;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&kt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Yr(t,r)),i&&vt(o.latestValues)&&kt(t,o.latestValues))}e.x<Ji&&e.x>Qi&&(e.x=1),e.y<Ji&&e.y>Qi&&(e.y=1)}function Ft(t,e){t.min=t.min+e,t.max=t.max+e}function qi(t,e,n,i,s=.5){const o=R(t.min,t.max,s);ln(t,e,n,o,i)}function kt(t,e){qi(t.x,e.x,e.scaleX,e.scale,e.originX),qi(t.y,e.y,e.scaleY,e.scale,e.originY)}function Gr(t,e){return Ur(mu(t.getBoundingClientRect(),e))}function Cu(t,e,n){const i=Gr(t,n),{scroll:s}=e;return s&&(Ft(i.x,s.offset.x),Ft(i.y,s.offset.y)),i}const Hr=({current:t})=>t?t.ownerDocument.defaultView:null,ts=(t,e)=>Math.abs(t-e);function Au(t,e){const n=ts(t.x,e.x),i=ts(t.y,e.y);return Math.sqrt(n**2+i**2)}class Xr{constructor(e,n,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=Ie(this.lastMoveEventInfo,this.history),p=this.startEvent!==null,g=Au(h.offset,{x:0,y:0})>=3;if(!p&&!g)return;const{point:x}=h,{timestamp:b}=K;this.history.push({...x,timestamp:b});const{onStart:y,onMove:m}=this.handlers;p||(y&&y(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),m&&m(this.lastMoveEvent,h)},this.handlePointerMove=(h,p)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=je(p,this.transformPagePoint),N.update(this.updatePoint,!0)},this.handlePointerUp=(h,p)=>{this.end();const{onEnd:g,onSessionEnd:x,resumeAnimation:b}=this.handlers;if(this.dragSnapToOrigin&&b&&b(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const y=Ie(h.type==="pointercancel"?this.lastMoveEventInfo:je(p,this.transformPagePoint),this.history);this.startEvent&&g&&g(h,y),x&&x(h,y)},!Bn(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=i,this.contextWindow=s||window;const r=oe(e),a=je(r,this.transformPagePoint),{point:l}=a,{timestamp:c}=K;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,Ie(a,this.history)),this.removeListeners=ie(Gt(this.contextWindow,"pointermove",this.handlePointerMove),Gt(this.contextWindow,"pointerup",this.handlePointerUp),Gt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),pt(this.updatePoint)}}function je(t,e){return e?{point:e(t.point)}:t}function es(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ie({point:t},e){return{point:t,delta:es(t,Zr(e)),offset:es(t,Eu(e)),velocity:wu(e,.1)}}function Eu(t){return t[0]}function Zr(t){return t[t.length-1]}function wu(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=Zr(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>ot(e)));)n--;if(!i)return{x:0,y:0};const o=at(s.timestamp-i.timestamp);if(o===0)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Pu(t,{min:e,max:n},i){return e!==void 0&&t<e?t=i?R(e,t,i.min):Math.max(t,e):n!==void 0&&t>n&&(t=i?R(n,t,i.max):Math.min(t,n)),t}function ns(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Mu(t,{top:e,left:n,bottom:i,right:s}){return{x:ns(t.x,n,s),y:ns(t.y,e,i)}}function is(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}function Du(t,e){return{x:is(t.x,e.x),y:is(t.y,e.y)}}function Vu(t,e){let n=.5;const i=H(t),s=H(e);return s>i?n=Zt(e.min,e.max-i,t.min):i>s&&(n=Zt(t.min,t.max-s,e.min)),ct(0,1,n)}function Lu(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const cn=.35;function Fu(t=cn){return t===!1?t=0:t===!0&&(t=cn),{x:ss(t,"left","right"),y:ss(t,"top","bottom")}}function ss(t,e,n){return{min:rs(t,e),max:rs(t,n)}}function rs(t,e){return typeof t=="number"?t:t[e]||0}const ku=new WeakMap;class Bu{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=_(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:i}=this.visualElement;if(i&&i.isPresent===!1)return;const s=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(oe(u).point)},o=(u,h)=>{const{drag:p,dragPropagation:g,onDragStart:x}=this.getProps();if(p&&!g&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Ql(p),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nt(y=>{let m=this.getAxisMotionValue(y).get()||0;if(lt.test(m)){const{projection:S}=this.visualElement;if(S&&S.layout){const v=S.layout.layoutBox[y];v&&(m=H(v)*(parseFloat(m)/100))}}this.originPoint[y]=m}),x&&N.postRender(()=>x(u,h)),rn(this.visualElement,"transform");const{animationState:b}=this.visualElement;b&&b.setActive("whileDrag",!0)},r=(u,h)=>{const{dragPropagation:p,dragDirectionLock:g,onDirectionLock:x,onDrag:b}=this.getProps();if(!p&&!this.openDragLock)return;const{offset:y}=h;if(g&&this.currentDirection===null){this.currentDirection=Ou(y),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",h.point,y),this.updateAxis("y",h.point,y),this.visualElement.render(),b&&b(u,h)},a=(u,h)=>this.stop(u,h),l=()=>nt(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)==null?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new Xr(e,{onSessionStart:s,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:Hr(this.visualElement)})}stop(e,n){const i=this.isDragging;if(this.cancel(),!i)return;const{velocity:s}=n;this.startAnimation(s);const{onDragEnd:o}=this.getProps();o&&N.postRender(()=>o(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,i){const{drag:s}=this.getProps();if(!i||!de(e,s,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(r=Pu(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var o;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(o=this.visualElement.projection)==null?void 0:o.layout,s=this.constraints;e&&Vt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=Mu(i.layoutBox,e):this.constraints=!1,this.elastic=Fu(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&nt(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=Lu(i.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!Vt(e))return!1;const i=e.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const o=Cu(i,s.root,this.visualElement.getTransformPagePoint());let r=Du(s.layout.layoutBox,o);if(n){const a=n(pu(r));this.hasMutatedConstraints=!!a,a&&(r=Ur(a))}return r}startAnimation(e){const{drag:n,dragMomentum:i,dragElastic:s,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=nt(u=>{if(!de(u,n,this.currentDirection))return;let h=l&&l[u]||{};r&&(h={min:0,max:0});const p=s?200:1e6,g=s?40:1e7,x={type:"inertia",velocity:i?e[u]:0,bounceStiffness:p,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,x)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const i=this.getAxisMotionValue(e);return rn(this.visualElement,e),i.start(Wn(e,i,0,n,this.visualElement,!1))}stopAnimation(){nt(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nt(e=>{var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps(),s=i[n];return s||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){nt(n=>{const{drag:i}=this.getProps();if(!de(n,i,this.currentDirection))return;const{projection:s}=this.visualElement,o=this.getAxisMotionValue(n);if(s&&s.layout){const{min:r,max:a}=s.layout.layoutBox[n];o.set(e[n]-R(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:i}=this.visualElement;if(!Vt(n)||!i||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};nt(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();s[r]=Vu({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),nt(r=>{if(!de(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:c}=this.constraints[r];a.set(R(l,c,s[r]))})}addListeners(){if(!this.visualElement.current)return;ku.set(this.visualElement,this);const e=this.visualElement.current,n=Gt(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),i=()=>{const{dragConstraints:l}=this.getProps();Vt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,o=s.addEventListener("measure",i);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),N.read(i);const r=ne(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(nt(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:o=!1,dragElastic:r=cn,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:i,dragPropagation:s,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function de(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Ou(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Ru extends gt{constructor(e){super(e),this.removeGroupControls=it,this.removeListeners=it,this.controls=new Bu(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||it}unmount(){this.removeGroupControls(),this.removeListeners()}}const os=t=>(e,n)=>{t&&N.postRender(()=>t(e,n))};class Nu extends gt{constructor(){super(...arguments),this.removePointerDownListener=it}onPointerDown(e){this.session=new Xr(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Hr(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:os(e),onStart:os(n),onMove:i,onEnd:(o,r)=>{delete this.session,s&&N.postRender(()=>s(o,r))}}}mount(){this.removePointerDownListener=Gt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const ge={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function as(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const $t={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(M.test(t))t=parseFloat(t);else return t;const n=as(t,e.target.x),i=as(t,e.target.y);return`${n}% ${i}%`}},ju={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=mt.parse(t);if(s.length>5)return i;const o=mt.createTransformer(t),r=typeof s[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const c=R(a,l,.5);return typeof s[2+r]=="number"&&(s[2+r]/=c),typeof s[3+r]=="number"&&(s[3+r]/=c),o(s)}};class Iu extends A.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:i,layoutId:s}=this.props,{projection:o}=e;Cc(_u),o&&(n.group&&n.group.add(o),i&&i.register&&s&&i.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),ge.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:i,drag:s,isPresent:o}=this.props,{projection:r}=i;return r&&(r.isPresent=o,s||e.layoutDependency!==n||n===void 0||e.isPresent!==o?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||N.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),kn.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:i}=this.props,{projection:s}=e;s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Qr(t){const[e,n]=oc(),i=A.useContext(ks);return ye.jsx(Iu,{...t,layoutGroup:i,switchLayoutGroup:A.useContext(Dr),isPresent:e,safeToRemove:n})}const _u={borderRadius:{...$t,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:$t,borderTopRightRadius:$t,borderBottomLeftRadius:$t,borderBottomRightRadius:$t,boxShadow:ju};function $u(t,e,n){const i=G(t)?t:Ot(t);return i.start(Wn("",i,e,n)),i.animation}const Uu=(t,e)=>t.depth-e.depth;class Wu{constructor(){this.children=[],this.isDirty=!1}add(e){pn(this.children,e),this.isDirty=!0}remove(e){mn(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Uu),this.isDirty=!1,this.children.forEach(e)}}function zu(t,e){const n=Q.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(pt(i),t(o-e))};return N.setup(i,!0),()=>pt(i)}const Jr=["TopLeft","TopRight","BottomLeft","BottomRight"],Ku=Jr.length,ls=t=>typeof t=="string"?parseFloat(t):t,cs=t=>typeof t=="number"||M.test(t);function Yu(t,e,n,i,s,o){s?(t.opacity=R(0,n.opacity??1,Gu(i)),t.opacityExit=R(e.opacity??1,0,Hu(i))):o&&(t.opacity=R(e.opacity??1,n.opacity??1,i));for(let r=0;r<Ku;r++){const a=`border${Jr[r]}Radius`;let l=us(e,a),c=us(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||cs(l)===cs(c)?(t[a]=Math.max(R(ls(l),ls(c),i),0),(lt.test(c)||lt.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=R(e.rotate||0,n.rotate||0,i))}function us(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Gu=qr(0,.5,zs),Hu=qr(.5,.95,it);function qr(t,e,n){return i=>i<t?0:i>e?1:n(Zt(t,e,i))}function hs(t,e){t.min=e.min,t.max=e.max}function et(t,e){hs(t.x,e.x),hs(t.y,e.y)}function ds(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function fs(t,e,n,i,s){return t-=e,t=Ae(t,1/n,i),s!==void 0&&(t=Ae(t,1/s,i)),t}function Xu(t,e=0,n=1,i=.5,s,o=t,r=t){if(lt.test(e)&&(e=parseFloat(e),e=R(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=R(o.min,o.max,i);t===o&&(a-=e),t.min=fs(t.min,e,n,a,s),t.max=fs(t.max,e,n,a,s)}function ps(t,e,[n,i,s],o,r){Xu(t,e[n],e[i],e[s],e.scale,o,r)}const Zu=["x","scaleX","originX"],Qu=["y","scaleY","originY"];function ms(t,e,n,i){ps(t.x,e,Zu,n?n.x:void 0,i?i.x:void 0),ps(t.y,e,Qu,n?n.y:void 0,i?i.y:void 0)}function gs(t){return t.translate===0&&t.scale===1}function to(t){return gs(t.x)&&gs(t.y)}function ys(t,e){return t.min===e.min&&t.max===e.max}function Ju(t,e){return ys(t.x,e.x)&&ys(t.y,e.y)}function xs(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function eo(t,e){return xs(t.x,e.x)&&xs(t.y,e.y)}function bs(t){return H(t.x)/H(t.y)}function vs(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class qu{constructor(){this.members=[]}add(e){pn(this.members,e),e.scheduleRender()}remove(e){if(mn(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(s=>e===s);if(n===0)return!1;let i;for(let s=n;s>=0;s--){const o=this.members[s];if(o.isPresent!==!1){i=o;break}}return i?(this.promote(i),!0):!1}promote(e,n){const i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,n&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:s}=e.options;s===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:i}=e;n.onExitComplete&&n.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function th(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(n==null?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:p,skewX:g,skewY:x}=n;c&&(i=`perspective(${c}px) ${i}`),u&&(i+=`rotate(${u}deg) `),h&&(i+=`rotateX(${h}deg) `),p&&(i+=`rotateY(${p}deg) `),g&&(i+=`skewX(${g}deg) `),x&&(i+=`skewY(${x}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(i+=`scale(${a}, ${l})`),i||"none"}const _e=["","X","Y","Z"],eh={visibility:"hidden"},nh=1e3;let ih=0;function $e(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function no(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=jr(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:s,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",N,!(s||o))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&no(i)}function io({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(r={},a=e==null?void 0:e()){this.id=ih++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(oh),this.nodes.forEach(uh),this.nodes.forEach(hh),this.nodes.forEach(ah)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Wu)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new xn),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r){if(this.instance)return;this.isSVG=Cr(r)&&!ic(r),this.instance=r;const{layoutId:a,layout:l,visualElement:c}=this.options;if(c&&!c.current&&c.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),t){let u;const h=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,u&&u(),u=zu(h,250),ge.hasAnimatedSinceResize&&(ge.hasAnimatedSinceResize=!1,this.nodes.forEach(Cs))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||l)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h,hasRelativeLayoutChanged:p,layout:g})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||gh,{onLayoutAnimationStart:b,onLayoutAnimationComplete:y}=c.getProps(),m=!this.targetLayout||!eo(this.targetLayout,g),S=!h&&p;if(this.options.layoutRoot||this.resumeFrom||S||h&&(m||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const v={...Ln(x,"layout"),onPlay:b,onComplete:y};(c.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v),this.setAnimationOrigin(u,S)}else h||Cs(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=g})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),pt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(dh),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&no(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Ts);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Ss);return}this.isUpdating||this.nodes.forEach(Ss),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(ch),this.nodes.forEach(sh),this.nodes.forEach(rh),this.clearAllSnapshots();const a=Q.now();K.delta=ct(0,1e3/60,a-K.timestamp),K.timestamp=a,K.isProcessing=!0,Le.update.process(K),Le.preRender.process(K),Le.render.process(K),K.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,kn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(lh),this.sharedNodes.forEach(fh)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,N.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){N.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!H(this.snapshot.measuredBox.x)&&!H(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=_(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&this.instance){const l=i(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!s)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!to(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;r&&this.instance&&(a||vt(this.latestValues)||u)&&(s(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),yh(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var c;const{visualElement:r}=this.options;if(!r)return _();const a=r.measureViewportBox();if(!(((c=this.scroll)==null?void 0:c.wasRoot)||this.path.some(xh))){const{scroll:u}=this.root;u&&(Ft(a.x,u.offset.x),Ft(a.y,u.offset.y))}return a}removeElementScroll(r){var l;const a=_();if(et(a,r),(l=this.scroll)!=null&&l.wasRoot)return a;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:p}=u;u!==this.root&&h&&p.layoutScroll&&(h.wasRoot&&et(a,r),Ft(a.x,h.offset.x),Ft(a.y,h.offset.y))}return a}applyTransform(r,a=!1){const l=_();et(l,r);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&kt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),vt(u.latestValues)&&kt(l,u.latestValues)}return vt(this.latestValues)&&kt(l,this.latestValues),l}removeTransform(r){const a=_();et(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!vt(c.latestValues))continue;an(c.latestValues)&&c.updateSnapshot();const u=_(),h=c.measurePageBox();et(u,h),ms(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return vt(this.latestValues)&&ms(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==K.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var p;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(r||l&&this.isSharedProjectionDirty||this.isProjectionDirty||(p=this.parent)!=null&&p.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:h}=this.options;if(!(!this.layout||!(u||h))){if(this.resolvedRelativeTargetAt=K.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=_(),this.relativeTargetOrigin=_(),Xt(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),et(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=_(),this.targetWithTransforms=_()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Tu(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):et(this.target,this.layout.layoutBox),Yr(this.target,this.targetDelta)):et(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=_(),this.relativeTargetOrigin=_(),Xt(this.relativeTargetOrigin,this.target,g.target),et(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||an(this.parent.latestValues)||Kr(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var x;const r=this.getLead(),a=!!this.resumingFrom||this!==r;let l=!0;if((this.isProjectionDirty||(x=this.parent)!=null&&x.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===K.timestamp&&(l=!1),l)return;const{layout:c,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||u))return;et(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,p=this.treeScale.y;Su(this.layoutCorrected,this.treeScale,this.path,a),r.layout&&!r.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(r.target=r.layout.layoutBox,r.targetWithTransforms=_());const{target:g}=r;if(!g){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ds(this.prevProjectionDelta.x,this.projectionDelta.x),ds(this.prevProjectionDelta.y,this.projectionDelta.y)),Ht(this.projectionDelta,this.layoutCorrected,g,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==p||!vs(this.projectionDelta.x,this.prevProjectionDelta.x)||!vs(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",g))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),r){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Lt(),this.projectionDelta=Lt(),this.projectionDeltaWithTransform=Lt()}setAnimationOrigin(r,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=Lt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const p=_(),g=l?l.source:void 0,x=this.layout?this.layout.source:void 0,b=g!==x,y=this.getStack(),m=!y||y.members.length<=1,S=!!(b&&!m&&this.options.crossfade===!0&&!this.path.some(mh));this.animationProgress=0;let v;this.mixTargetDelta=D=>{const w=D/1e3;As(h.x,r.x,w),As(h.y,r.y,w),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Xt(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),ph(this.relativeTarget,this.relativeTargetOrigin,p,w),v&&Ju(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=_()),et(v,this.relativeTarget)),b&&(this.animationValues=u,Yu(u,c,this.latestValues,w,S,m)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=w},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){var a,l,c;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(c=(l=this.resumingFrom)==null?void 0:l.currentAnimation)==null||c.stop(),this.pendingAnimation&&(pt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=N.update(()=>{ge.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Ot(0)),this.currentAnimation=$u(this.motionValue,[0,1e3],{...r,velocity:0,isSync:!0,onUpdate:u=>{this.mixTargetDelta(u),r.onUpdate&&r.onUpdate(u)},onStop:()=>{},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(nh),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=r;if(!(!a||!l||!c)){if(this!==r&&this.layout&&c&&so(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||_();const h=H(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const p=H(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+p}et(a,l),kt(a,u),Ht(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new qu),this.sharedNodes.get(r).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var a;const{layoutId:r}=this.options;return r?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:r}=this.options;return r?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&$e("z",r,c,this.animationValues);for(let u=0;u<_e.length;u++)$e(`rotate${_e[u]}`,r,c,this.animationValues),$e(`skew${_e[u]}`,r,c,this.animationValues);r.render();for(const u in c)r.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);r.scheduleRender()}getProjectionStyles(r){if(!this.instance||this.isSVG)return;if(!this.isVisible)return eh;const a={visibility:""},l=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,a.opacity="",a.pointerEvents=me(r==null?void 0:r.pointerEvents)||"",a.transform=l?l(this.latestValues,""):"none",a;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const g={};return this.options.layoutId&&(g.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,g.pointerEvents=me(r==null?void 0:r.pointerEvents)||""),this.hasProjected&&!vt(this.latestValues)&&(g.transform=l?l({},""):"none",this.hasProjected=!1),g}const u=c.animationValues||c.latestValues;this.applyTransformsToTarget(),a.transform=th(this.projectionDeltaWithTransform,this.treeScale,u),l&&(a.transform=l(u,a.transform));const{x:h,y:p}=this.projectionDelta;a.transformOrigin=`${h.origin*100}% ${p.origin*100}% 0`,c.animationValues?a.opacity=c===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:a.opacity=c===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const g in te){if(u[g]===void 0)continue;const{correct:x,applyTo:b,isCSSVariable:y}=te[g],m=a.transform==="none"?u[g]:x(u[g],c);if(b){const S=b.length;for(let v=0;v<S;v++)a[b[v]]=m}else y?this.options.visualElement.renderState.vars[g]=m:a[g]=m}return this.options.layoutId&&(a.pointerEvents=c===this?me(r==null?void 0:r.pointerEvents)||"":"none"),a}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(Ts),this.root.sharedNodes.clear()}}}function sh(t){t.updateLayout()}function rh(t){var n;const e=((n=t.resumeFrom)==null?void 0:n.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:i,measuredBox:s}=t.layout,{animationType:o}=t.options,r=e.source!==t.layout.source;o==="size"?nt(h=>{const p=r?e.measuredBox[h]:e.layoutBox[h],g=H(p);p.min=i[h].min,p.max=p.min+g}):so(o,e.layoutBox,i)&&nt(h=>{const p=r?e.measuredBox[h]:e.layoutBox[h],g=H(i[h]);p.max=p.min+g,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+g)});const a=Lt();Ht(a,i,e.layoutBox);const l=Lt();r?Ht(l,t.applyTransform(s,!0),e.measuredBox):Ht(l,i,e.layoutBox);const c=!to(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:p,layout:g}=h;if(p&&g){const x=_();Xt(x,e.layoutBox,p.layoutBox);const b=_();Xt(b,i,g.layoutBox),eo(x,b)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=b,t.relativeTargetOrigin=x,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:i}=t.options;i&&i()}t.options.transition=void 0}function oh(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ah(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function lh(t){t.clearSnapshot()}function Ts(t){t.clearMeasurements()}function Ss(t){t.isLayoutDirty=!1}function ch(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Cs(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function uh(t){t.resolveTargetDelta()}function hh(t){t.calcProjection()}function dh(t){t.resetSkewAndRotation()}function fh(t){t.removeLeadSnapshot()}function As(t,e,n){t.translate=R(e.translate,0,n),t.scale=R(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Es(t,e,n,i){t.min=R(e.min,n.min,i),t.max=R(e.max,n.max,i)}function ph(t,e,n,i){Es(t.x,e.x,n.x,i),Es(t.y,e.y,n.y,i)}function mh(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const gh={duration:.45,ease:[.4,0,.1,1]},ws=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Ps=ws("applewebkit/")&&!ws("chrome/")?Math.round:it;function Ms(t){t.min=Ps(t.min),t.max=Ps(t.max)}function yh(t){Ms(t.x),Ms(t.y)}function so(t,e,n){return t==="position"||t==="preserve-aspect"&&!vu(bs(e),bs(n),.2)}function xh(t){var e;return t!==t.root&&((e=t.scroll)==null?void 0:e.wasRoot)}const bh=io({attachResizeListener:(t,e)=>ne(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ue={current:void 0},ro=io({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Ue.current){const t=new bh({});t.mount(window),t.setOptions({layoutScroll:!0}),Ue.current=t}return Ue.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),vh={pan:{Feature:Nu},drag:{Feature:Ru,ProjectionNode:ro,MeasureLayout:Qr}};function Ds(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover",n==="Start");const s="onHover"+n,o=i[s];o&&N.postRender(()=>o(e,oe(e)))}class Th extends gt{mount(){const{current:e}=this.node;e&&(this.unmount=Jl(e,(n,i)=>(Ds(this.node,i,"Start"),s=>Ds(this.node,s,"End"))))}unmount(){}}class Sh extends gt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ie(ne(this.node.current,"focus",()=>this.onFocus()),ne(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Vs(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap",n==="Start");const s="onTap"+(n==="End"?"":n),o=i[s];o&&N.postRender(()=>o(e,oe(e)))}class Ch extends gt{mount(){const{current:e}=this.node;e&&(this.unmount=nc(e,(n,i)=>(Vs(this.node,i,"Start"),(s,{success:o})=>Vs(this.node,s,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const un=new WeakMap,We=new WeakMap,Ah=t=>{const e=un.get(t.target);e&&e(t)},Eh=t=>{t.forEach(Ah)};function wh({root:t,...e}){const n=t||document;We.has(n)||We.set(n,{});const i=We.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Eh,{root:t,...e})),i[s]}function Ph(t,e,n){const i=wh(e);return un.set(t,n),i.observe(t),()=>{un.delete(t),i.unobserve(t)}}const Mh={some:0,all:1};class Dh extends gt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:i,amount:s="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:i,threshold:typeof s=="number"?s:Mh[s]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),p=c?u:h;p&&p(l)};return Ph(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Vh(e,n))&&this.startObserver()}unmount(){}}function Vh({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Lh={inView:{Feature:Dh},tap:{Feature:Ch},focus:{Feature:Sh},hover:{Feature:Th}},Fh={layout:{ProjectionNode:ro,MeasureLayout:Qr}},hn={current:null},oo={current:!1};function kh(){if(oo.current=!0,!!dn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>hn.current=t.matches;t.addListener(e),e()}else hn.current=!1}const Bh=new WeakMap;function Oh(t,e,n){for(const i in e){const s=e[i],o=n[i];if(G(s))t.addValue(i,s);else if(G(o))t.addValue(i,Ot(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const r=t.getValue(i);r.liveStyle===!0?r.jump(s):r.hasAnimated||r.set(s)}else{const r=t.getStaticValue(i);t.addValue(i,Ot(r!==void 0?r:s,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const Ls=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Rh{scrapeMotionValuesFromProps(e,n,i){return{}}constructor({parent:e,props:n,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Vn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const p=Q.now();this.renderScheduledAt<p&&(this.renderScheduledAt=p,N.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Pe(n),this.isVariantNode=Pr(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const p in h){const g=h[p];l[p]!==void 0&&G(g)&&g.set(l[p],!1)}}mount(e){this.current=e,Bh.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,i)=>this.bindToMotionValue(i,n)),oo.current||kh(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:hn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),pt(this.notifyUpdate),pt(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const i=It.has(e);i&&this.onBindTransform&&this.onBindTransform();const s=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&N.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{s(),o(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in Rt){const n=Rt[e];if(!n)continue;const{isEnabled:i,Feature:s}=n;if(!this.features[e]&&s&&i(this.props)&&(this.features[e]=new s(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):_()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let i=0;i<Ls.length;i++){const s=Ls[i];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const o="on"+s,r=e[o];r&&(this.propEventSubscriptions[s]=this.on(s,r))}this.prevMotionValues=Oh(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const i=this.values.get(e);n!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return i===void 0&&n!==void 0&&(i=Ot(n===null?void 0:n,{owner:this}),this.addValue(e,i)),i}readValue(e,n){let i=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return i!=null&&(typeof i=="string"&&(Bs(i)||Rs(i))?i=parseFloat(i):!rc(i)&&mt.test(n)&&(i=xr(e,n)),this.setBaseTarget(e,G(i)?i.get():i)),G(i)?i.get():i}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var o;const{initial:n}=this.props;let i;if(typeof n=="string"||typeof n=="object"){const r=$n(this.props,n,(o=this.presenceContext)==null?void 0:o.custom);r&&(i=r[e])}if(n&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,e);return s!==void 0&&!G(s)?s:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new xn),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class ao extends Rh{constructor(){super(...arguments),this.KeyframeResolver=Gl}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:i}){delete n[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;G(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function lo(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}function Nh(t){return window.getComputedStyle(t)}class jh extends ao{constructor(){super(...arguments),this.type="html",this.renderInstance=lo}readValueFromInstance(e,n){var i;if(It.has(n))return(i=this.projection)!=null&&i.isProjecting?Qe(n):dl(e,n);{const s=Nh(e),o=(Tn(n)?s.getPropertyValue(n):s[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Gr(e,n)}build(e,n,i){jn(e,n,i.transformTemplate)}scrapeMotionValuesFromProps(e,n,i){return Un(e,n,i)}}const co=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ih(t,e,n,i){lo(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(co.has(s)?s:Nn(s),e.attrs[s])}class _h extends ao{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=_}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(It.has(n)){const i=yr(n);return i&&i.default||0}return n=co.has(n)?n:Nn(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,i){return Nr(e,n,i)}build(e,n,i){kr(e,n,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,n,i,s){Ih(e,n,i,s)}mount(e){this.isSVGTag=Or(e.tagName),super.mount(e)}}const $h=(t,e)=>_n(t)?new _h(e):new jh(e,{allowProjection:t!==A.Fragment}),Uh=_c({...du,...Lh,...vh,...Fh},$h),Wh=hc(Uh);var zn={},zh=Po;Object.defineProperty(zn,"__esModule",{value:!0});var uo=zn.default=void 0,Kh=zh(wo()),Yh=ye;uo=zn.default=(0,Kh.default)((0,Yh.jsx)("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"}),"Email");const Gh=()=>d(P,{sx:{mb:2,p:1,border:"1px solid #E2E8F0",borderRadius:2,bgcolor:"#F8FAFC"},children:C(P,{sx:{display:"flex",alignItems:"center",mb:1},children:[d(P,{sx:{mr:1},children:d($,{variant:"circular",width:24,height:24})}),C(P,{sx:{width:"100%"},children:[d($,{variant:"text",width:"40%"}),C(P,{sx:{display:"flex",alignItems:"center",mt:1},children:[d($,{variant:"rectangular",width:120,height:8,sx:{mr:1}}),d($,{variant:"text",width:"20%"})]})]})]})}),Hh=()=>d(Bt,{elevation:1,sx:{p:2,mb:3,border:"1px solid #E2E8F0",borderRadius:3,bgcolor:"#FFFFFF",boxShadow:"0 2px 6px rgba(0,0,0,0.03)"},children:C(Y,{spacing:2,children:[C(Y,{direction:"row",alignItems:"center",spacing:2,children:[d($,{variant:"text",width:"30%"}),d($,{variant:"rectangular",width:2,height:24}),C(Y,{direction:"row",alignItems:"center",spacing:1,children:[d($,{variant:"circular",width:24,height:24}),d($,{variant:"text",width:80}),d($,{variant:"text",width:120})]})]}),C(P,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[d($,{variant:"text",width:"25%"}),d($,{variant:"rectangular",width:80,height:24})]}),C(k,{container:!0,spacing:3,children:[C(k,{item:!0,xs:12,md:6,children:[d($,{variant:"text",width:"20%"}),d(Y,{direction:"row",spacing:1,children:d($,{variant:"rectangular",width:200,height:32})})]}),C(k,{item:!0,xs:12,md:6,children:[d($,{variant:"text",width:"20%"}),C(P,{sx:{display:"flex",alignItems:"center"},children:[d($,{variant:"circular",width:24,height:24,sx:{mr:1}}),C(P,{children:[d($,{variant:"text",width:150}),d($,{variant:"text",width:100})]})]})]})]})]})}),ho=()=>d(k,{container:!0,spacing:2,children:d(k,{item:!0,xs:12,children:C(k,{container:!0,spacing:2,children:[d(k,{item:!0,xs:12,md:4,lg:3,children:d(Bt,{elevation:0,sx:{p:2,border:"1px solid #E2E8F0",borderRadius:3,bgcolor:"#F9FAFB"},children:[...Array(5)].map((t,e)=>d(Gh,{},e))})}),d(k,{item:!0,xs:12,md:8,lg:9,children:C(Bt,{elevation:0,sx:{p:2,border:"1px solid #E2E8F0",borderRadius:3,bgcolor:"#FFFFFF"},children:[C(P,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[C(P,{children:[d($,{variant:"text",width:200}),d($,{variant:"text",width:100})]}),d($,{variant:"rectangular",width:100,height:36})]}),[...Array(1)].map((t,e)=>d(Hh,{},e))]})})]})})}),Xh=({item:t,initiator:e=!1})=>{var b,y,m;const n=Mo(),i=Do(n.breakpoints.down("sm")),s=zt(t.createdAt),o=e?zt(t.updatedAt):zt(t.completedAt),r=o.diff(s,"minutes"),a=s.format("MMM D, YYYY"),l=s.format("h:mm A"),c=o.format("MMM D, YYYY"),u=o.format("h:mm A"),h=e?t.updatedAt:t.completedAt,p=i?16:20,g=i?10:12,x=i?1.5:2;return C(rt,{children:[d(P,{sx:{px:2,py:1.5,borderBottom:"1px solid #E2E8F0",borderTop:{xs:"1px solid #E2E8F0",md:"none"},borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"space-between",bgcolor:(y=(b=T)==null?void 0:b.chip)==null?void 0:y.background},children:C(V,{variant:"subtitle2",sx:{fontWeight:600,color:(m=T)==null?void 0:m.text.greyishBlue,display:"flex",alignItems:"center"},children:[d(Vo,{sx:{fontSize:18,mr:1}}),"Task Timeline"]})}),C(P,{sx:{position:"relative",ml:x,pb:2,width:"100%",maxWidth:"100%",overflow:"hidden",pt:2},children:[d(P,{sx:{position:"absolute",left:p/2-1,top:p+4,height:h?`calc(100% - ${p*2.5}px)`:`${p*4}px`,width:2,bgcolor:h?"#16A34A":"#F97316",zIndex:0}}),C(P,{sx:{position:"relative",mb:3,display:"flex",alignItems:"center"},children:[d(P,{sx:{width:p,height:p,borderRadius:"50%",bgcolor:"#0284C7",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1,flexShrink:0},children:d(Lo,{sx:{color:"#FFFFFF",fontSize:g}})}),C(P,{sx:{ml:1.5,overflow:"hidden"},children:[d(V,{variant:i?"caption":"body2",color:"#0284C7",fontWeight:600,noWrap:!0,children:"Started"}),C(V,{variant:"caption",color:"text.secondary",sx:{display:"block",textOverflow:"ellipsis"},children:[a," ",l]})]})]}),C(P,{sx:{position:"relative",mb:h?3:0,display:"flex",alignItems:"center"},children:[d(P,{sx:{width:p,height:p,borderRadius:"50%",bgcolor:h?"#059669":"#F97316",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1,flexShrink:0},children:h?d(Fo,{sx:{color:"#FFFFFF",fontSize:g}}):d(ko,{sx:{color:"#FFFFFF",fontSize:g}})}),d(P,{sx:{ml:1.5,display:"flex",alignItems:"center",bgcolor:h?"#F0FDF4":"#FFF7ED",borderRadius:1.5,px:1,py:.5,border:h?"1px solid #D1FAE5":"1px solid #FFEDD5",maxWidth:"100%"},children:d(V,{variant:"caption",sx:{color:h?"#059669":"#EA580C",fontWeight:600,whiteSpace:"nowrap"},children:h?`${r} min`:"Pending"})})]}),h&&C(P,{sx:{position:"relative",display:"flex",alignItems:"center"},children:[d(P,{sx:{width:p,height:p,borderRadius:"50%",bgcolor:"#16A34A",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1,flexShrink:0},children:d(xe,{sx:{color:"#FFFFFF",fontSize:g}})}),C(P,{sx:{ml:1.5,overflow:"hidden"},children:[d(V,{variant:i?"caption":"body2",color:"#16A34A",fontWeight:600,noWrap:!0,children:"Completed"}),C(V,{variant:"caption",color:"text.secondary",sx:{display:"block",textOverflow:"ellipsis"},children:[c," ",u]})]})]})]})]})},E={COMPLETED:"COMPLETED",CANCELED:"CANCELED",READY:"READY",PENDING:"PENDING"},Zh=Bo(Ut)(({status:t})=>({fontWeight:600,fontSize:"0.7rem",height:"24px",borderRadius:"12px",backgroundColor:t===E.COMPLETED?T.success.completedBackground:t===E.CANCELED?T.background.canceled:t===E.READY?T.warning.light:T.background.subtle,color:t===E.COMPLETED?T.success.completedDark:t===E.CANCELED?T.error.deepRed:t===E.READY?T.warning.orange:T.text.darkGrey,border:t===E.COMPLETED?`1px solid ${T.success.pale}`:t===E.CANCELED?`1px solid ${T.error.pale}`:t===E.READY?`1px solid ${T.warning.orange}`:`1px solid ${T.border.light}`,"& .MuiChip-label":{letterSpacing:"1.4px"}})),Qh=({item:t,index:e,setOpenSnackbar:n})=>{var i,s,o,r,a,l;return d(Wh.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4,delay:e*.1},children:d(Bt,{elevation:0,sx:{pl:2,mb:2,borderRadius:"12px",border:"1px solid",borderColor:t.status===E.COMPLETED?T.success.pale:t.status===E.CANCELED?T.error.pale:T.border.cardBorder,backgroundColor:T.basic.white,position:"relative",overflow:"hidden",transition:"all 0.3s ease-in-out","&::before":{content:'""',position:"absolute",left:0,top:0,bottom:0,width:"5px",backgroundColor:t.status===E.COMPLETED?T.success.deepGreen:t.status===E.CANCELED?T.error.deepRed:t.status===E.READY?T.warning.orange:T.neutral[400]}},children:C(P,{sx:{display:"flex",flexDirection:"row"},children:[C(P,{sx:{width:"80%",pr:2,pt:2},children:[C(Y,{direction:"row",alignItems:"center",spacing:1,children:[d(V,{variant:"h6",sx:{fontSize:"1.125rem",fontWeight:700,color:T.text.darkBlue,pl:1,display:"flex",alignItems:"center"},children:t.subject}),d(Io,{orientation:"vertical",flexItem:!0,sx:{height:20,borderColor:T.border.cardBorder}}),C(Y,{direction:"row",alignItems:"center",spacing:.5,children:[d(_o,{fontSize:"small",sx:{color:T.text.darkGrey,pb:"2px"}}),d(V,{variant:"body2",sx:{fontWeight:500,color:T.text.greyishBlue,fontSize:"0.805rem"},children:"Initiated by"}),d(V,{variant:"body2",sx:{fontWeight:600,color:T.info.dark,fontSize:"0.755rem",letterSpacing:"0.5px"},children:t.createdBy})]})]}),d(P,{sx:{pt:2},children:C(k,{container:!0,spacing:3,alignItems:"flex-start",children:[C(k,{item:!0,xs:12,md:4,children:[d(V,{sx:{fontWeight:600,color:T.text.darkBlue,fontSize:"0.755rem",mb:1},children:(t==null?void 0:t.status)==="READY"?"Recipient Users":(t==null?void 0:t.status)==="RESERVED"?"Claimed by":(t==null?void 0:t.status)==="COMPLETED"?"Completed by":"Recipient Users"}),d(Y,{direction:"row",flexWrap:"wrap",useFlexGap:!0,gap:1,children:(t==null?void 0:t.status)==="COMPLETED"&&(t!=null&&t.processor)?d(Tt,{title:t==null?void 0:t.processor,children:d(Ut,{label:t==null?void 0:t.processor,avatar:d(Me,{sx:{width:18,height:18,bgcolor:T.background.subtle,color:T.text.darkBlue,fontSize:"0.55rem"},children:(i=t==null?void 0:t.processor)==null?void 0:i.split(" ").map(c=>c[0]).join("").toUpperCase()}),sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0"}})}):d(rt,{children:((s=t==null?void 0:t.recipientUsers)==null?void 0:s.length)>1?C(rt,{children:[d(Tt,{title:t==null?void 0:t.recipientUsers[0],children:d(Ut,{label:t==null?void 0:t.recipientUsers[0],avatar:d(Me,{sx:{width:18,height:18,bgcolor:T.background.subtle,color:T.text.darkBlue,fontSize:"0.55rem"},children:(o=t==null?void 0:t.recipientUsers[0])==null?void 0:o.split(" ").map(c=>c[0]).join("").toUpperCase()}),sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0"}})}),d(Tt,{title:d(Y,{spacing:.5,children:(r=t==null?void 0:t.recipientUsers)==null?void 0:r.slice(1).map((c,u)=>d(V,{variant:"caption",color:"inherit",children:c},u))}),arrow:!0,placement:"top",children:d(Ut,{label:`+ ${((a=t==null?void 0:t.recipientUsers)==null?void 0:a.length)-1}`,sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0",cursor:"pointer"}})})]}):(l=t==null?void 0:t.recipientUsers)==null?void 0:l.map((c,u)=>d(Tt,{title:c,children:d(Ut,{label:c,avatar:d(Me,{sx:{width:18,height:18,bgcolor:T.background.subtle,color:T.text.darkBlue,fontSize:"0.55rem"},children:c==null?void 0:c.split(" ").map(h=>h[0]).join("").toUpperCase()}),sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0"}})},u))})})]}),d(k,{item:!0,xs:12,md:2,children:C(P,{children:[d(V,{variant:"caption",sx:{color:"#64748B",fontWeight:600,fontSize:"0.75rem",display:"flex",alignItems:"center",justifyContent:"flex-start",mb:1},children:C(P,{sx:{display:"flex",alignItems:"center"},children:[d(aa,{sx:{fontSize:14,mr:.5}}),"Remarks"]})}),d(P,{sx:{maxHeight:120,overflowY:"auto",display:"flex",flexWrap:"wrap",gap:.5,p:.5,"&::-webkit-scrollbar":{height:"4px"},"&::-webkit-scrollbar-thumb":{background:T.box.scrollBackground,borderRadius:"4px"}},children:t.description?d($o,{text:t.description,maxChars:20,variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem"}}):d(V,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem",fontStyle:"italic"},children:"No remarks available"})})]})}),((t==null?void 0:t.status)===E.COMPLETED&&(t==null?void 0:t.processor)||(t==null?void 0:t.status)===E.CANCELED)&&C(k,{item:!0,xs:12,md:6,children:[d(V,{sx:{fontWeight:600,color:T.text.darkBlue,fontSize:"0.755rem"},children:"Status Info"}),C(P,{sx:{display:"flex",alignItems:"center",border:`1px solid ${T.border.cardBorder}`,borderRadius:"10px",p:2,bgcolor:T.chip.background,gap:2},children:[d(P,{sx:{display:"flex",alignItems:"center",justifyContent:"center",width:36,height:36,borderRadius:"50%",bgcolor:t.status===E.COMPLETED?"#D1FAE5":t.status===E.CANCELED?"#FEE2E2":"#E2E8F0",color:t.status===E.COMPLETED?"#059669":t.status===E.CANCELED?"#DC2626":"#64748B"},children:(t==null?void 0:t.status)===E.COMPLETED?d(xe,{fontSize:"small"}):d(ze,{fontSize:"small"})}),C(P,{children:[d(V,{variant:"body2",sx:{fontWeight:500,color:"#334155",mb:.5},children:t.status===E.COMPLETED?C(rt,{children:["Completed by"," ",d(P,{component:"span",sx:{color:"#1D4ED8",fontWeight:500},children:t.processor})]}):t.status===E.CANCELED?d(rt,{children:"Canceled"}):""}),d(V,{variant:"caption",sx:{color:"#64748B",fontSize:"0.75rem"},children:new Date(t.completedAt||t.createdAt).toLocaleString("en-US",{dateStyle:"medium",timeStyle:"short"})})]})]})]}),(t==null?void 0:t.status)===E.READY&&d(k,{item:!0,xs:12,children:C(P,{sx:{display:"flex",alignItems:"center",justifyContent:"center",py:1.5,px:2.5,borderRadius:"10px",backgroundColor:T.warning.pale,border:`1px dashed ${T.warning.orange}`,color:`${T.warning.orange} !important`,fontSize:"0.9375rem",letterSpacing:"0.5px",fontWeight:500,mt:1,mb:1},children:[d(Fs,{fontSize:"small",sx:{mr:1.5}}),"In Progress - Awaiting Completion"]})})]})})]}),d(P,{sx:{width:"20%",pl:2},children:d(Xh,{item:t})})]})})})},Jh=async t=>{if(!t)return[];const e=[];let n={};if(Array.isArray(t))t.forEach((i,s)=>{const o=Object.keys(i)[0];n[o]=s,(i[o]||[]).forEach(a=>{var h,p,g,x,b;const l=(p=(h=a==null?void 0:a.attributesDtos)==null?void 0:h[0])==null?void 0:p.currentLevel,c=((x=(g=a==null?void 0:a.attributesDtos)==null?void 0:g[0])==null?void 0:x.currentLevelName)||o;let u=(a==null?void 0:a.status)||E.PENDING;e.push({id:a.id,level:parseInt(l,10),levelName:c,status:u,subject:((b=a.subject)==null?void 0:b.replace(/ & \d+$/,""))||"",createdBy:a.createdBy,createdAt:a.createdAt,performedBy:a.processor,completedAt:a.completedAt,comment:a.description,recipients:a.recipientUsers||[],originalLevelKey:o,displayOrder:s})})});else if(t&&typeof t=="object"){const i=Object.keys(t);i.forEach((s,o)=>{n[s]=o}),i.forEach(s=>{(t[s]||[]).forEach(r=>{var u,h,p,g,x;const a=(h=(u=r==null?void 0:r.attributesDtos)==null?void 0:u[0])==null?void 0:h.currentLevel,l=((g=(p=r==null?void 0:r.attributesDtos)==null?void 0:p[0])==null?void 0:g.currentLevelName)||s;let c=(r==null?void 0:r.status)||E.PENDING;e.push({id:r.id,level:parseInt(a,10),levelName:l,status:c,subject:((x=r.subject)==null?void 0:x.replace(/ & \d+$/,""))||"",createdBy:r.createdBy,createdAt:r.createdAt,performedBy:r.processor,completedAt:r.completedAt,comment:r.description,recipients:r.recipientUsers||[],originalLevelKey:s,displayOrder:n[s]})})})}return e},qh=t=>!t||t.length===0?0:t.filter(e=>e.status!==E.READY&&e.status!==E.PENDING).length,td=t=>!t||(t==null?void 0:t.length)===0?E.PENDING:(t==null?void 0:t.every(s=>s.status===E.CANCELED))?E.CANCELED:(t==null?void 0:t.some(s=>s.status===E.READY))?E.READY:(t==null?void 0:t.every(s=>s.status===E.COMPLETED))?E.COMPLETED:E.PENDING,ed=({data:t,childRequestID:e})=>{const[n,i]=A.useState([]),[s,o]=A.useState(!1),[r,a]=A.useState(!0);A.useEffect(()=>{(n==null?void 0:n.length)==0?a(!0):a(!1)},[n]);const c=(()=>!t||!Array.isArray(t)||t.length===0?[]:t==null?void 0:t.map((S,v)=>{const[D,w]=Object.entries(S)[0];return{levelName:D,items:Array.isArray(w)?w:[],status:td(Array.isArray(w)?w:[]),completedCount:qh(Array.isArray(w)?w:[]),totalCount:Array.isArray(w)?w.length:0,originalLevelNumber:v,displayOrder:v}}))();if(t&&Object.keys(t).length>0){const m=Object.keys(t),S={};m.forEach((v,D)=>{const w=parseInt(v,10);S[w]=D}),c.sort((v,D)=>{const w=S[v.originalLevelNumber]!==void 0?S[v.originalLevelNumber]:999,F=S[D.originalLevelNumber]!==void 0?S[D.originalLevelNumber]:999;return w-F})}const[u,h]=A.useState(null);A.useEffect(()=>{var m;c!=null&&c.length&&u===null&&h((m=c[c.length-1])==null?void 0:m.originalLevelNumber)},[c,u]);const p=A.useRef({});A.useEffect(()=>{(async()=>{const S=await Jh(t);i(S)})()},[t]);const g=m=>{h(m)},x=m=>{switch(m){case E.COMPLETED:return"Completed";case E.CANCELED:return"Canceled";case E.READY:return"Ready";default:return"Pending"}},b=m=>{switch(m){case E.COMPLETED:return d(xe,{fontSize:"small",sx:{color:T.success.deepGreen}});case E.CANCELED:return d(ze,{fontSize:"small",sx:{color:T.error.deepRed}});case E.READY:return d(Fs,{fontSize:"small",sx:{color:T.warning.orange}});default:return d(Uo,{fontSize:"small",sx:{color:T.text.darkGrey}})}},y=c.find(m=>m.originalLevelNumber===u);return A.useEffect(()=>{u&&p.current[u]&&p.current[u].scrollIntoView({behavior:"smooth",block:"center"})},[u]),r===!0?d(ho,{}):C(P,{sx:{pt:2,height:"78vh",overflow:"hidden !important",display:"flex",flexDirection:"column",justifyContent:"flex-start"},children:[d(k,{container:!0,children:C(k,{container:!0,sx:{border:"1px solid #E2E8F0",overflow:"hidden",height:"100%",borderRadius:2,position:"relative",backgroundColor:"#FFFFFF"},children:[d(k,{item:!0,xs:12,md:4,lg:2,children:d(Bt,{elevation:0,sx:{p:0,overflow:"hidden",height:"100%",backgroundColor:"#FFFFFF"},children:C(P,{sx:{p:2,borderRight:"1px solid #E2E8F0",borderRadius:2,bgcolor:"#F8FAFC",height:"73vh",overflowY:"auto",overflowX:"hidden","&::-webkit-scrollbar":{width:"3.5px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#CBD5E1"},"&::-webkit-scrollbar-track":{backgroundColor:"#F1F5F9"}},children:["  ",(c==null?void 0:c.length)>0&&c.map((m,S)=>{const v=u===m.originalLevelNumber,D=S>0&&c[S-1].status===E.COMPLETED;return m.status===E.READY||(m.status,E.PENDING),C(P,{ref:w=>p.current[m.originalLevelNumber]=w,sx:{mb:2,position:"relative"},children:[C(P,{onClick:()=>g(m.originalLevelNumber),sx:{display:"flex",alignItems:"center",borderRadius:2,px:2,py:2,height:"auto",bgcolor:m.status===E.COMPLETED?v?T.success.completedBackground:T.success.pale:m.status===E.CANCELED?v?T.background.canceled:T.error.pale:m.status===E.READY?v?T.warning.light:T.warning.pale:T.background.subtle,border:v?"2px solid":"1px solid",borderColor:m.status===E.COMPLETED?T.success.deepGreen:m.status===E.CANCELED?T.error.deepRed:m.status===E.READY?T.warning.orange:T.border.cardBorder,cursor:"pointer",transition:"all 0.2s ease",boxShadow:v?"0 0 0 2px rgba(59, 130, 246, 0.2)":"none",position:"relative","&:hover":{bgcolor:m.status===E.COMPLETED?"#D1FAE5":m.status===E.CANCELED?"#FEE2E2":m.status===E.READY?"#FFEDD5":"#E2E8F0"}},children:[d(P,{sx:{width:40,height:40,borderRadius:"50%",display:"flex",justifyContent:"center",alignItems:"center",mr:2,bgcolor:m.status===E.COMPLETED?"#ECFDF5":m.status===E.CANCELED?"#FEF2F2":m.status===E.READY?"#FFF7ED":"#F1F5F9",border:"2px solid",borderColor:m.status===E.COMPLETED?"#10B981":m.status===E.CANCELED?"#EF4444":m.status===E.READY?"orange":"#CBD5E1",color:m.status===E.COMPLETED?"#059669":m.status===E.CANCELED?"#DC2626":m.status===E.READY?"orange":"#64748B",boxShadow:v?"0 0 0 2px rgba(59, 130, 246, 0.15)":"none",transform:v?"scale(1.1)":"scale(1)",transition:"all 0.2s ease"},children:m.status===E.COMPLETED?d(xe,{fontSize:"small"}):m.status===E.CANCELED?d(ze,{fontSize:"small"}):b(m.status)}),C(P,{sx:{flex:1},children:[d(V,{variant:"subtitle1",sx:{fontWeight:600,color:"#334155",fontSize:"0.95rem"},children:(m==null?void 0:m.levelName)||"undefined"}),d(P,{sx:{display:"flex",alignItems:"center"}}),C(P,{sx:{mt:.7,height:"30px"},children:[d(Oo,{variant:"determinate",value:m.completedCount/m.totalCount*100,sx:{borderRadius:6,height:.07,backgroundColor:"#e6e0d3","& .MuiLinearProgress-bar":{backgroundColor:"#3B82F6"}}},S),C(V,{variant:"caption",sx:{mt:.5,color:"#475569"},children:[m.completedCount," / ",m.totalCount," completed"]})]})]}),v&&d(P,{sx:{position:"absolute",right:12,display:"flex",alignItems:"center",justifyContent:"center",width:24,height:24,borderRadius:"50%",bgcolor:T.primary.main,color:T.basic.white,boxShadow:"0 2px 4px rgba(0,0,0,0.1)",animation:"fadeIn 0.3s ease-in-out","@keyframes fadeIn":{"0%":{opacity:0,transform:"scale(0.8)"},"100%":{opacity:1,transform:"scale(1)"}}},children:d(Ro,{fontSize:"small",sx:{fontSize:12}})})]}),S<c.length-1&&d(P,{sx:{position:"absolute",left:35,height:37,top:96,bottom:-18,width:2,bgcolor:m.status===E.COMPLETED?T.success.deepGreen:T.border.cardBorder,zIndex:0}})]},m.originalLevelNumber)})]})})}),d(k,{item:!0,xs:12,md:8,lg:10,children:d(No,{in:!0,timeout:300,children:d(Bt,{elevation:0,sx:{display:"flex",flexDirection:"column",height:"100%",overflow:"hidden"},children:y&&C(rt,{children:[C(P,{sx:{p:3,borderBottom:"1px solid #F1F5F9",bgcolor:"#F8FAFC",display:"flex",justifyContent:"space-between",alignItems:"center",flexShrink:0},children:[C(P,{children:[C(P,{sx:{display:"flex",alignItems:"center"},children:[d(V,{variant:"h6",sx:{fontWeight:600,color:"#334155"},children:(y==null?void 0:y.levelName)||"undefined"}),d(Zh,{label:x(y.status),status:y.status,size:"small",sx:{ml:2}})]}),C(V,{variant:"body2",sx:{color:"#334155",mt:1},children:["Child Request ID: ",e]}),C(V,{variant:"body2",sx:{mt:1,color:"#64748B"},children:[y.items.length," ",y.items.length===1?"activity":"activities"]})]}),C(P,{sx:{display:"flex",alignItems:"center"},children:[c.length>1&&c.findIndex(m=>m.originalLevelNumber===u)>0&&d(be,{size:"small",onClick:()=>{const m=c.findIndex(S=>S.originalLevelNumber===u);m>0&&g(c[m-1].originalLevelNumber)},variant:"outlined",sx:{mr:1,borderColor:"#CBD5E1",color:"#64748B","&:hover":{borderColor:"#94A3B8",bgcolor:"#F8FAFC"}},children:"Previous Level"}),c.length>1&&c.findIndex(m=>m.originalLevelNumber===u)<c.length-1&&d(be,{size:"small",onClick:()=>{const m=c.findIndex(S=>S.originalLevelNumber===u);m<c.length-1&&g(c[m+1].originalLevelNumber)},variant:"contained",sx:{bgcolor:"#3B82F6","&:hover":{bgcolor:"#2563EB"}},children:"Next Level"})]})]}),C(P,{sx:{p:3,flexGrow:1,height:"70px",overflowY:"auto"},children:[y.items.map((m,S)=>d(Qh,{item:m,index:S,setOpenSnackbar:o},m.id)),y.items.length===0&&d(P,{sx:{textAlign:"center",py:8,color:"#94A3B8"},children:d(V,{variant:"body1",children:"No activities found for this level"})})]},y.levelNumber)]})})})})]})}),d(jo,{openSnackBar:s,alertMsg:"Task ID Copied Successfully",alertType:"Success",handleSnackBarClose:()=>o(!1)})]})},od=()=>{var Xn,Zn,Qn,Jn,qn,ti,ei,ni,ii,si,ri,oi,ai,li;const[t,e]=A.useState(!0),i=Wo().state,[s,o]=A.useState([]),[r,a]=A.useState(""),[l,c]=A.useState([]),[u,h]=A.useState([]),[p,g]=A.useState(""),[x,b]=A.useState(!1),[y,m]=A.useState(""),[S,v]=A.useState([]),[D,w]=A.useState(!1),[F,I]=A.useState(!1),[L,W]=A.useState(!1),[X,yt]=A.useState(""),[ae,Kn]=A.useState(),[le,fo]=A.useState("");let xt=ci(B=>{var Z;return(Z=B==null?void 0:B.userManagement)==null?void 0:Z.taskData});const z=()=>{Hn(!0),w(!0)},J=()=>{b(!1)},_t=()=>{W(!0)},wt=()=>{W(!1),Yn(-1)},po={overflow:"scroll",position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"70%",height:"70%",bgcolor:(Zn=(Xn=T)==null?void 0:Xn.primary)==null?void 0:Zn.white,boxShadow:4,p:1};A.useEffect(()=>{a(zo("CC"))},[]);const Pt=Ko(pi.REQUEST_BENCH_TASK,!0,{}),mo=`${Pt==null?void 0:Pt.requestPrefix}${Pt==null?void 0:Pt.requestId}`,f=ci(B=>{var Z,q;return(q=(Z=B.commonSearchBar)==null?void 0:Z.RequestHistory)==null?void 0:q.reqId})||mo,Yn=Yo(),go=()=>{e(!1),c([]);const B=q=>{o(q==null?void 0:q.body),e(!1)},Z=()=>{e(!1)};ce(`/${Ve}/${tt.TASK_ACTION_DETAIL.FETCH_REQUEST_HISTORY}?requestId=${f}`,"get",B,Z)},yo=()=>{c([]);let B="";f!=null&&f.includes("NME")||f!=null&&f.includes("CME")||f!=null&&f.includes("EME")||f!=null&&f.includes("NMA")||f!=null&&f.includes("CMA")||f!=null&&f.includes("EMA")||f!=null&&f.includes("FCA")?B=`/${Ve}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${f}`:f!=null&&f.includes("NCS")||f!=null&&f.includes("CCS")||f!=null&&f.includes("NCM")||f!=null&&f.includes("CCM")?B=`/${gi}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${f}`:f!=null&&f.includes("NPS")||f!=null&&f.includes("CPS")||f!=null&&f.includes("NPM")||f!=null&&f.includes("CPM")?B=`/${yi}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${f}`:f!=null&&f.includes("NLS")||f!=null&&f.includes("CLS")||f!=null&&f.includes("ELS")||f!=null&&f.includes("NLM")||f!=null&&f.includes("CLM")||f!=null&&f.includes("ELM")?B=`/${xi}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${f}`:(f!=null&&f.includes("NBS")||f!=null&&f.includes("CBS")||f!=null&&f.includes("EBS")||f!=null&&f.includes("NBM")||f!=null&&f.includes("CBM")||f!=null&&f.includes("EBM"))&&(B=`/${bi}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${f}`),ce(B,"get",O=>{var dt;h(O==null?void 0:O.body[0]),I(!0),g((dt=O==null?void 0:O.body[0])==null?void 0:dt.comment)},()=>{var O;ra((O=oa)==null?void 0:O.ERROR_FETCHING_DATA,"error")})},xo=[{field:"id",headerName:"ID",flex:1,hide:!0},{field:"createdAt",headerName:"Notification Date",flex:.5,renderCell:B=>d(V,{sx:{fontSize:"12px"},children:zt(B.row.createdAt).format("DD MMM YYYY")})},{field:"subject",headerName:"Subject",flex:2},{field:"actions",align:"center",flex:1,headerAlign:"center",headerName:"Actions",sortable:!1,renderCell:B=>d("div",{children:d(Tt,{title:"View Mail Body",children:d(Mt,{onClick:()=>{m(ht.find(Z=>Z.id==B.row.id))},children:d(ea,{})})})})}],bo=()=>{b(!1)},vo=()=>{b(!0)},Gn=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"attachmentType",headerName:"Attachment Type",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",width:75,renderCell:B=>d(rt,{children:d(na,{index:B.row.id,name:B.row.docName})})}],To=()=>{xt!=null&&xt.requestId&&(xt==null||xt.requestId);let B=Z=>{var q=[];Z.documentDetailDtoList.forEach(O=>{var dt={id:O.documentId,docType:O.fileType,docName:O.fileName,uploadedOn:O.docCreationDate,uploadedBy:O.createdBy,attachmentType:O.attachmentType};q.push(dt)}),v(q)};ce(`/${ia}/${tt.TASK_ACTION_DETAIL.GET_DOCS}/${f}`,"get",B)},[So,Hn]=A.useState(!1),Co=()=>{Hn(!1),w(!1)},[ht,Ao]=A.useState([]),Eo=B=>{vo();let Z=dt=>{Ao(dt==null?void 0:dt.body)},q=()=>{},O="";f!=null&&f.includes("NME")||f!=null&&f.includes("CME")||f!=null&&f.includes("EME")||f!=null&&f.includes("NMA")||f!=null&&f.includes("CMA")||f!=null&&f.includes("EMA")||f!=null&&f.includes("FCA")?O=`/${Ve}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${f}`:f!=null&&f.includes("NCS")||f!=null&&f.includes("CCS")||f!=null&&f.includes("NCM")||f!=null&&f.includes("CCM")?O=`${gi}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${f}`:f!=null&&f.includes("NPS")||f!=null&&f.includes("CPS")||f!=null&&f.includes("NPM")||f!=null&&f.includes("CPM")?O=`${yi}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${f}`:f!=null&&f.includes("NLS")||f!=null&&f.includes("CLS")||f!=null&&f.includes("ELS")||f!=null&&f.includes("NLM")||f!=null&&f.includes("CLM")||f!=null&&f.includes("ELM")?O=`${xi}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${f}`:(f!=null&&f.includes("NBS")||f!=null&&f.includes("CBS")||f!=null&&f.includes("EBS")||f!=null&&f.includes("NBM")||f!=null&&f.includes("CBM")||f!=null&&f.includes("EBM"))&&(O=`${bi}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${f}`),O&&ce(O,"get",Z,q)};return A.useEffect(()=>(go(),yo(),To(),()=>{Go(pi.REQUEST_BENCH_TASK)}),[]),C("div",{id:"container_outermost",children:[C(ui,{fullWidth:!0,hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:So,sx:{"& .MuiDialog-container":{"& .MuiPaper-root":{width:"100%",maxWidth:"max-content"}}},children:[C(hi,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:(Jn=(Qn=T)==null?void 0:Qn.background)==null?void 0:Jn.header,display:"flex"},children:[d(V,{variant:"h6",children:"Attachments: "}),d(Mt,{sx:{width:"max-content"},onClick:Co,children:d(mi,{})})]}),d(fi,{sx:{padding:".5rem 1rem"},children:d(Y,{children:d(P,{sx:{minWidth:800},children:C(di,{sx:{height:"auto"},fullWidth:!0,children:[!!(S!=null&&S.length)&&d(De,{width:"800px",rows:S,columns:Gn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!(S!=null&&S.length)&&d(V,{variant:"body2",children:"No Attachments Found"})]})})})})]}),C(ui,{open:x,onClose:J,hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},children:[C(hi,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:(ti=(qn=T)==null?void 0:qn.background)==null?void 0:ti.header,display:"flex"},children:[" ",d(V,{variant:"h6",children:"Attachments"}),d(Mt,{sx:{width:"max-content"},onClick:J,children:d(mi,{})})]}),d(fi,{sx:{padding:".5rem 1rem"},children:d(Y,{children:d(P,{sx:{minWidth:800},children:C(di,{sx:{height:"auto"},fullWidth:!0,children:[!!S.length&&d(De,{width:"70vw",rows:S,columns:Gn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!S.length&&d(V,{variant:"body2",children:"No Attachments Found"})]})})})})]}),d("div",{className:"purchaseOrder",style:{...Ho,backgroundColor:(ni=(ei=T)==null?void 0:ei.background)==null?void 0:ni.container},children:d(Y,{spacing:1,children:C(rt,{children:[C(k,{container:!0,sx:Xo,children:[C(k,{item:!0,md:6,sx:{outerContainer_Information:Zo,display:"flex"},children:[d(k,{children:d(Mt,{color:"primary","aria-label":"upload picture",component:"label",sx:Qo,children:d(Jo,{sx:{fontSize:"25px",color:(si=(ii=T)==null?void 0:ii.basic)==null?void 0:si.black},onClick:()=>{Yn(-1)}})})}),C(k,{children:[d(V,{variant:"h3",children:d("strong",{children:"Request History"})}),d(V,{variant:"body2",color:T.secondary.grey,children:"This view displays the history of a Request"})]})]}),d(k,{item:!0,md:6,sx:{display:"flex"},children:C(k,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[d(Tt,{title:"Mail",children:d(Mt,{children:d(uo,{onClick:Eo})})}),d(Tt,{title:"Uploaded Attachments",arrow:!0,children:d(Mt,{onClick:z,sx:{"&:active":{backgroundColor:"rgba(17,52,166,0.3)",color:`${(oi=(ri=T)==null?void 0:ri.basic)==null?void 0:oi.black}`},backgroundColor:D?"rgba(17,52,166,0.3)":"transparent"},children:d(qo,{})})})]})})]}),d(k,{container:!0,spacing:1,children:d(ta,{open:x,onClose:bo,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:d(P,{sx:po,children:y!=""?d(rt,{children:(ht==null?void 0:ht.length)==0?C(Y,{justifyItems:"center",alignItems:"center",mt:5,children:[d(V,{variant:"h3",color:(li=(ai=T)==null?void 0:ai.basic)==null?void 0:li.black,children:"No Mail Found for this Request ID"}),d(be,{size:"small",variant:"contained",sx:{marginRight:"1rem"},onClick:()=>{m("")},children:"Close"})]}):d(rt,{children:C(k,{container:!0,sx:{height:"100%",p:2},children:[d(k,{item:!0,xs:12,children:C(P,{sx:{border:`1px solid ${T.basic.black}`,borderRadius:"8px",width:"100%",height:"100%",boxSizing:"border-box",display:"flex",flexDirection:"column",backgroundColor:T.basic.white},p:3,children:[d(V,{variant:"h6",sx:{fontSize:"18px",fontWeight:"600",color:T.text.primary,mb:2},children:(y==null?void 0:y.subject)||"No Subject"}),d(P,{sx:{mb:2},children:C(Y,{spacing:1.5,children:[C(Y,{direction:"row",spacing:1,alignItems:"flex-start",children:[d(V,{sx:{color:T.text.secondary,width:"40px",flexShrink:0,fontSize:"13px",fontWeight:"500"},children:"To:"}),d(V,{sx:{flex:1,fontSize:"13px",color:T.text.charcoal,wordBreak:"break-word"},children:y==null?void 0:y.toParticipant})]}),C(Y,{direction:"row",spacing:1,alignItems:"flex-start",children:[d(V,{sx:{color:T.text.secondary,width:"40px",flexShrink:0,fontSize:"13px",fontWeight:"500"},children:"Cc:"}),d(V,{sx:{flex:1,fontSize:"13px",color:T.text.charcoal,wordBreak:"break-word"},children:y==null?void 0:y.ccParticipant})]}),C(Y,{direction:"row",spacing:1,alignItems:"flex-start",children:[d(V,{sx:{color:T.text.secondary,width:"40px",flexShrink:0,fontSize:"13px",fontWeight:"500"},children:"From:"}),d(V,{sx:{flex:1,fontSize:"13px",color:T.text.charcoal,wordBreak:"break-word"},children:y==null?void 0:y.fromUser})]}),d(V,{sx:{fontSize:"12px",color:T.text.secondary,borderBottom:`1px solid ${T.border.light}`,pb:2},children:zt(y==null?void 0:y.createdAt).format("DD MMM YYYY hh:mm:ss a")})]})}),d(P,{sx:{flexGrow:1,overflowY:"auto",backgroundColor:T.background.default,borderRadius:"4px",p:2,minHeight:"200px","& *":{fontFamily:"inherit"}},children:d("div",{dangerouslySetInnerHTML:{__html:y==null?void 0:y.content}})})]})}),d(k,{item:!0,xs:12,sx:{display:"flex",justifyContent:"flex-end",mt:2},children:d(be,{size:"medium",variant:"contained",onClick:()=>m(""),sx:{minWidth:"100px",textTransform:"none",boxShadow:"none","&:hover":{boxShadow:"none"}},children:"Close"})})]})})}):d(rt,{children:d(De,{rows:ht,columns:xo,pageSize:10,getRowIdValue:"id",hideFooter:!1,title:`Email List (${ht==null?void 0:ht.length})`})})})})}),C(V,{variant:"h6",sx:{mt:2,mb:2},children:["Parent Request ID: ",i]}),(s==null?void 0:s.length)>0?d(ed,{data:s,childRequestID:f}):d(ho,{})]})})}),d(sa,{dialogState:L,openReusableDialog:_t,closeReusableDialog:wt,dialogTitle:X,dialogMessage:ae,handleDialogConfirm:wt,dialogOkText:"OK",dialogSeverity:le})]})};export{od as default};
