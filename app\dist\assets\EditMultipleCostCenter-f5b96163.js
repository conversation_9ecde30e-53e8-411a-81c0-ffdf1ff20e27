import{r as h,i as S,l as ge,u as ue,ir as ee,a as e,g_ as H,j as r,T as u,hy as Ce,a3 as se,gR as xe,gS as ye,hJ as ve,hA as ie,F as te,$ as s,is as be,hY as Ae,w as U,hK as V,hP as J,b as Se,ig as De,hp as ke,i7 as Ee,ia as we,B as Z,hd as ce,id as Fe,ie as Ne,ic as Be,h_ as je,gZ as Re,hE as de,a1 as _,gW as W,P as he,m6 as Q,hn as _e,W as Te,hv as Me,i8 as Pe,gU as pe,i9 as $e,ib as ze}from"./index-fdfa25a0.js";function Le(l,p){return Array.isArray(p)&&p.find(v=>v.code===l)||""}const qe=({label:l,value:p,length:P,fieldGroup:v,isEditMode:D,visibility:k,isExtendMode:fe,selectedRowData:X,type:x,activeTabIndex:F,ccTabs:K})=>{var q;const[b,T]=h.useState(p),[ae,G]=h.useState(!1),M=S(a=>a.AllDropDown.dropDown),d=S(a=>a.costCenter.MultipleCostCenterData),f=ge();Le(b,M),ue(),S(a=>a.edit.payload);let A={},E=-1;for(let a=0;a<(d==null?void 0:d.length);a++)if(d[a].costCenter===X){A=d[a],E=a;break}let i=K[F];console.log("activerow",E,A,i);const $=(a,o)=>{const y=a==null?void 0:a.find(n=>(n==null?void 0:n.fieldName)===o);return y?y.value:""},m=d[E];console.log(m,"costCenterInnerData"),h.useEffect(()=>{(k==="0"||k==="Required")&&(i==="Basic Data"?(console.log("active_tab_coming"),f(ee(N)),f(ee(["Name"]))):f(ee(N)))},[i]);const w=(a,o)=>{console.log("label",a,o),f(be({keyname:N.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:o}));let y=d==null?void 0:d.map((n,B)=>{let j=K[F];if(B===E){let I=n==null?void 0:n.viewData,Y=n==null?void 0:n.viewData[j];console.log("temp",Y);let R=n==null?void 0:n.viewData[j][v];return console.log("temp2",R),{...n,viewData:{...I,[j]:{...Y,[v]:R==null?void 0:R.map(O=>O.fieldName===a?{...O,value:o}:O)}}}}else return n});f(Ae(y))};let N=l.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");h.useEffect(()=>{T(p)},[p]);const z=a=>{const o=n=>{console.log("value",n),f(J({keyName:"Region",data:n.body}))},y=n=>{console.log(n,"error in dojax")};U(`/${V}/data/getRegionBasedOnCountry?country=${a}`,"get",o,y)},L=a=>{console.log("compcode1",a);const o=n=>{console.log(n,"data145"),f(J({keyName:"Currency",data:n.body}))},y=n=>{console.log(n,"error in dojax")};U(`/${V}/data/getCurrency?companyCode=${a}`,"get",o,y)};return h.useEffect(()=>{l==="Country/Reg"&&z(b),l==="Company Code"&&L(b)},[]),e(s,{item:!0,children:e(H,{children:D?r(te,{children:[r(u,{variant:"body2",color:"#777",children:[l," ",k==="Required"||k==="0"?e("span",{style:{color:"red"},children:"*"}):""]}),x==="Drop Down"?e(Ce,{options:M[N]??[],value:($(m==null?void 0:m.viewData[i][v],l)&&((q=M[N])==null?void 0:q.filter(a=>a.code===$(m==null?void 0:m.viewData[i][v],l))))[0]||"",onChange:(a,o)=>{l==="Country/Reg"&&z(o.code),l==="Comp Code"&&L(o.code),w(l,o==null?void 0:o.code),T(o==null?void 0:o.code),G(!0)},getOptionLabel:a=>a===""||(a==null?void 0:a.code)===""?"":`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`??"",renderOption:(a,o)=>(console.log("option vakue",o),e("li",{...a,children:e(u,{style:{fontSize:12},children:`${o==null?void 0:o.code} - ${o==null?void 0:o.desc}`})})),renderInput:a=>e(se,{...a,variant:"outlined",size:"small",label:null,placeholder:l})}):x==="Input"?e(se,{variant:"outlined",size:"small",value:$(m==null?void 0:m.viewData[i][v],l).toUpperCase(),inputProps:{maxLength:P},onChange:a=>{console.log("event",a.target.value);const o=a.target.value;if(o.length>0&&o[0]===" ")w(l,o.trimStart());else{let y=o.toUpperCase();w(l,y)}},placeholder:l}):x==="Calendar"?e(xe,{dateAdapter:ye,children:e(ve,{slotProps:{textField:{size:"small"}},placeholder:"Select Date Range"})}):x==="Radio Button"?e(ie,{sx:{borderRadius:"0 !important"},checked:b,onChange:(a,o)=>{w(l,o),T(o)}}):""]}):e(te,{children:r(te,{children:[r(u,{variant:"body2",color:"#777",children:[l," ",k==="Required"||k==="0"?e("span",{style:{color:"red"},children:"*"}):""]}),e(u,{variant:"body2",fontWeight:"bold",children:x==="Radio Button"?e(ie,{sx:{padding:0},checked:b,disabled:!0}):b})]})})})})},We=()=>{var oe,ne,re;const l=Se(),p=ge();h.useState({});const[P,v]=h.useState(0),[D,k]=h.useState(!1),[fe,X]=h.useState(!0),[x,F]=h.useState(0);h.useState(0);const[K,b]=h.useState(!1),[T,ae]=h.useState([]),[G,M]=h.useState(!1),d=ue();S(t=>t.initialData.EditMultipleMaterial);const f=S(t=>t.costCenter.MultipleCostCenterData);console.log(f,"costCenterData");const A=S(t=>t.appSettings),E=d.state.tabsData.viewData;console.log("tabsData",E);const i=d.state.rowData,$=d.state.requestNumber;S(t=>t.payload);let m=S(t=>t.edit.payload),w=S(t=>t.costCenter.requiredFields);console.log(m,w,"required_field_for_data");const N=()=>{k(!0),X(!1)},z=()=>{const t=R();D?t?(F(c=>c-1),p(Q())):q():(F(c=>c-1),p(Q()))},L=()=>{const t=R();D?t?(F(c=>c+1),p(Q())):q():(F(c=>c+1),p(Q()))},q=()=>{M(!0)};for(let t=0;t<((oe=f==null?void 0:f.tableData)==null?void 0:oe.length);t++)if(f.tableData[t].Description===i.description){f.tableData[t];break}const a=t=>{const c=g=>{p(J({keyName:"HierarchyArea",data:g.body}))},C=g=>{console.log(g)};U(`/${V}/data/getHierarchyArea?controllingArea=${t}`,"get",c,C)},o=t=>{const c=g=>{p(J({keyName:"CompanyCode",data:g.body}))},C=g=>{console.log(g)};U(`/${V}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${t}`,"get",c,C)},y=t=>{const c=g=>{p(J({keyName:"ProfitCenter",data:g.body}))},C=g=>{console.log(g)};U(`/${V}/data/getProfitCenterAsPerControllingArea?controllingArea=${t}`,"get",c,C)};h.useEffect(()=>{a(i.controllingArea),o(i.controllingArea),y(i.controllingArea)},[]);const n=Object.entries(E).filter(t=>typeof t[1]=="object"&&t[1]!=null).map(t=>t[0]),B=Object.entries(E).filter(t=>typeof t[1]=="object"&&t[1]!=null).map(t=>Object.entries(t[1]));console.log(B,"tabContents");const j={};B.map(t=>{t.forEach((c,C)=>{c.forEach((g,me)=>{me!==0&&g.forEach(le=>{j[le.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=le.value})})})}),h.useEffect(()=>{p(De(j))},[]),console.log(j,"tempHash"),console.log("activeTab",B);const I=t=>{b(t)},Y=()=>{b(!0)};console.log(m,w,"eror_arr");const R=()=>_e(m,w,ae),O=()=>{M(!1)};return r("div",{children:[r(s,{container:!0,style:{...ke,backgroundColor:"#FAFCFF"},children:[T.length!=0&&e(Ee,{openSnackBar:G,alertMsg:"Please fill the following Field: "+T.join(", "),handleSnackBarClose:O}),r(s,{sx:{width:"inherit"},children:[r(s,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[e(s,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(Te,{color:"primary","aria-label":"upload picture",component:"label",sx:Me,children:e(Pe,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{l("/masterDataCockpit/costCenter/createMultipleCostCenter")}})})}),r(s,{md:10,children:[e(u,{variant:"h3",children:r("strong",{children:["Cost Center: ",i.costCenter," "]})}),e(u,{variant:"body2",color:"#777",children:"This view creates a new Cost Center"})]}),(ne=d==null?void 0:d.state)!=null&&ne.requestNumber?e(s,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:e(_,{variant:"outlined",size:"small",sx:pe,onClick:Y,title:"Change Log",children:e($e,{sx:{padding:"2px"},fontSize:"small"})})}):e(s,{md:1,sx:{display:"flex",justifyContent:"flex-end"}}),K&&e(we,{open:!0,closeModal:I,requestId:$,requestType:"Mass",pageName:"costCenter",controllingArea:i.controllingArea,centerName:i.costCenter}),D?"":e(s,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:e(s,{item:!0,children:r(_,{variant:"outlined",size:"small",sx:pe,onClick:N,children:["Change",e(ze,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),e(s,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:r(Z,{width:"70%",sx:{marginLeft:"40px"},children:[e(s,{item:!0,sx:{paddingTop:"2px !important"},children:r(H,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(u,{variant:"body2",color:"#777",children:"Cost Center"})}),r(u,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",i.costCenter]})]})}),e(s,{item:!0,sx:{paddingTop:"2px !important"},children:r(H,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(u,{variant:"body2",color:"#777",children:"Controlling Area"})}),r(u,{variant:"body2",fontWeight:"bold",children:[": ",i.controllingArea]})]})}),e(s,{item:!0,sx:{paddingTop:"2px !important"},children:r(H,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(u,{variant:"body2",color:"#777",children:"Valid From"})}),r(u,{variant:"body2",fontWeight:"bold",children:[":"," ",ce(i.validFrom).format(A==null?void 0:A.dateFormat)]})]})}),e(s,{item:!0,sx:{paddingTop:"2px !important"},children:r(H,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(u,{variant:"body2",color:"#777",children:"Valid To"})}),r(u,{variant:"body2",fontWeight:"bold",children:[":"," ",ce(i.validTo).format(A==null?void 0:A.dateFormat)]})]})})]})}),r(s,{container:!0,style:{padding:"16px"},children:[e(Be,{activeStep:x,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:n.map((t,c)=>e(Fe,{children:e(Ne,{sx:{fontWeight:"700"},children:t})},t))}),e(s,{container:!0,children:B&&((re=B[x])==null?void 0:re.map((t,c)=>e(Z,{sx:{width:"100%"},children:r(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...je},children:[e(s,{container:!0,children:e(u,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:t[0]})}),e(Z,{children:e(Z,{sx:{width:"100%"},children:e(Re,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(s,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...t[1]].map(C=>e(qe,{activeTabIndex:x,fieldGroup:t[0],selectedRowData:i.costCenter,ccTabs:n,visibility:C.visibility,label:C.fieldName,value:C.value,length:C.maxLength,onSave:g=>handleFieldSave(C.fieldName,g),isEditMode:D,type:C.fieldType,field:C}))})})})})]})},c)))})]})]})]}),D?e(he,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:r(de,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:P,onChange:t=>{v(t)},children:[e(_,{size:"small",sx:{...W,mr:1},variant:"contained",onClick:()=>{l("/masterDataCockpit/costCenter/createMultipleCostCenter")},children:"Save"}),e(_,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:z,disabled:x===0,children:"Back"}),e(_,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:L,disabled:x===n.length-1,children:"Next"})]})}):"",D?"":e(he,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:r(de,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:P,onChange:t=>{v(t)},children:[e(_,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:z,disabled:x===0,children:"Back"}),e(_,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:L,disabled:x===n.length-1,children:"Next"})]})})]})};export{We as default};
