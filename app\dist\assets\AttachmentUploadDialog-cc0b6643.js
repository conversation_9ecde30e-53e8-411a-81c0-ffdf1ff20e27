import{r as o,i as F,a as t,j as a,h1 as I,T as l,W as x,hI as E,fX as O,B as s,iC as j,iE as W,iF as L,g_ as M,a1 as b,fV as N}from"./index-fdfa25a0.js";const H=({artifactId:U="",artifactName:T="",poNumber:_,isAnAttachment:$,setOpen:y,handleUpload:D})=>{const[v,q]=o.useState("Other"),[d,c]=o.useState(!1),[r,f]=o.useState([]);o.useState([]),o.useState(!1),o.useState(["Others"]);const[h,g]=o.useState(!1);o.useState({attachments:[],comments:[]}),F(e=>e.userManagement.userData);let p=()=>{f([]),y(!1)};const S=e=>{e.preventDefault(),c(!0)},B=()=>{c(!1)},C=e=>{e.preventDefault(),c(!1);const i=Array.from(e.dataTransfer.files);m(i)},k=()=>{document.getElementById("fileButton").click()},m=e=>{let i=[];e.forEach(n=>{n.metaData=v,i.push(n)}),f(n=>[...n,...i])};let w=()=>{if(!h&&r[0]){[...r],D(r);return}},u=()=>{p()},z=e=>{let i=r.filter(n=>n.id!==e);f(i)},R=()=>{let e=0;r.forEach(i=>{e+=i.size}),e>5e8?(promptAction_Functions.handleOpenPromptBox("ERROR",{title:"Warning",message:"Files size excceded",severity:"warning",cancelButton:!1}),g(!0)):g(!1)};return o.useEffect(()=>{R()},[r]),t("div",{style:{display:"flex",flexDirection:"row"},children:a(N,{fullWidth:!0,maxWidth:"sm",sx:{"& .MuiDialog-paper":{borderRadius:"12px",padding:"1rem"},overflow:"hidden"},open:!0,onClose:p,children:[a(I,{sx:{padding:"1rem 1.5rem"},children:[t(l,{variant:"h6",sx:{fontWeight:500},children:"Add New Attachment"}),t(x,{"aria-label":"close",onClick:u,sx:e=>({position:"absolute",right:12,top:10,color:e.palette.grey[500]}),children:t(E,{})})]}),a(O,{sx:{padding:"1rem",height:"max-content"},dividers:!0,children:[a(s,{className:`dropzone ${d?"dragover":""}`,sx:{width:"100%",border:`2px dashed ${d?"#3b30c8":"#d0d5dd"}`,borderRadius:"8px",padding:"2rem",backgroundColor:d?"#f8f9ff":"#fafbff",cursor:"pointer",minHeight:"200px",display:"flex",alignItems:"center",justifyContent:"center"},onDragOver:S,onDragLeave:B,onDrop:C,children:[!r[0]&&a(s,{sx:{padding:"2rem",display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column"},children:[t(j,{sx:{fontSize:48,color:"#3b30c8"}}),t(l,{children:"Drag and drop file here"}),t(l,{children:"or"}),t(l,{children:t("a",{onClick:k,children:"Browse file"})})]}),r.length>0&&a(s,{sx:{padding:"0rem",display:"flex",flexDirection:"column",gap:"1.5rem"},children:[r.map((e,i)=>{var n;return a(s,{sx:{display:"flex",alignItems:"center",padding:"1rem",borderRadius:"10px",backgroundColor:"#fff",border:"1px solid #ddd",boxShadow:"0 2px 6px rgba(0, 0, 0, 0.1)",transition:"background 0.2s ease-in-out, transform 0.1s ease-in-out","&:hover":{backgroundColor:"#f1f5f9"},width:"100%"},children:[t("img",{style:{width:"32px",height:"32px",marginRight:"1rem"},src:W[(n=e.name)==null?void 0:n.split(".")[1]],alt:"file-icon"}),t(l,{variant:"body1",sx:{flexGrow:1,fontWeight:500,fontSize:"1rem"},children:e.name}),a(l,{sx:{marginLeft:"auto",marginRight:"10%",color:h?"error.main":"gray",fontWeight:500,fontSize:"0.9rem"},children:[parseFloat(e.size/1e6).toFixed(2)," MB"]}),t(x,{id:`closeBtn-${e.id}`,size:"small",onClick:A=>{A.stopPropagation(),z(e.id)},sx:{marginLeft:"0.5rem",opacity:.8,"&:hover":{opacity:1}},children:t(L,{fontSize:"small",color:"error"})})]},i)}),t(l,{component:"div",sx:{padding:"",background:"#f9f9f9",borderRadius:"10px",fontSize:"0.95rem",lineHeight:"1.6"},children:a("ul",{style:{margin:"0",paddingLeft:"1.2rem"},children:[t("li",{style:{width:"100%",marginBottom:"0.2rem",padding:"0.5rem",background:"#f5f5f5",borderRadius:"4px"},children:"Mass Upload Process will start in the background. Once file is uploaded, you will receive a notification and an email containing the request ID number."}),a("li",{style:{width:"100%",marginBottom:"0.2rem",padding:"0.5rem",background:"#f5f5f5",borderRadius:"4px"},children:["You can visit the ",t("strong",{children:"Request Bench"})," tab, search for the request ID, and perform further actions on it."]}),a("li",{style:{width:"100%",marginBottom:"0.2rem",padding:"0.5rem",background:"#f5f5f5",borderRadius:"4px"},children:[t("strong",{children:"Note:"})," All request IDs generated in the background will initially have the status ",t("strong",{children:"Draft"})," and will be ",t("strong",{children:"Upload Successful"})," or ",t("strong",{children:"Upload Failed"})," after Uploading the Excel based on it's validation."]})]})})]}),t("input",{id:"fileButton",multiple:!1,accept:".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",type:"file",name:"files",onChange:e=>m([...e.target.files]),style:{display:"none"}})]}),a(M,{direction:"row",sx:{justifyContent:"end",marginTop:"1rem",position:"relative"},children:[t(l,{sx:e=>({color:e.palette.grey,position:"absolute",left:0,top:0}),children:"*Max file size 500 MB"}),t(b,{className:"btn-mr",variant:"contained",onClick:w,children:"Upload"}),t(b,{variant:"outlined",onClick:u,children:"Cancel"})]})]})]})})};export{H as A};
