import{i as s,l as h,b as L,r as t,a as c,F as k,v as R,q as w}from"./index-fdfa25a0.js";import{W as S,c as W,u as C}from"./propData-e8199bd0.js";import"./Workflow-0d087b9d.js";import"./index-3f2e0745.js";import"./DialogContentText-8ac052ae.js";import"./ListItemButton-db9eb0d0.js";import"./StepButton-fdbf0590.js";import"./ToggleButtonGroup-c02e6027.js";import"./makeStyles-1dfd4db4.js";import"./asyncToGenerator-88583e02.js";import"./dayjs.min-774e293a.js";import"./isBetween-fe8614a5.js";function J(){let a=s(e=>{var i;return(i=e.userManagement)==null?void 0:i.userData});s(e=>{var i;return(i=e.userManagement)==null?void 0:i.groups});const o=s(e=>e.applicationConfig);let l=h();const I=L();t.useState(null),t.useState(null);const[m,T]=t.useState(null),p={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},N={DateTimeFormat:{dateTimeFormat:"DD MMM YYYY||HH:mm",timeZone:"Asia/Calcutta"}},d=e=>{var n,r;l(w(e));var i={PRC:"/purchaseRequest/workbench/singleworkbench/",POC:"/purchaseOrder/confirmationTracker/taskDetail/",INC:"/invoices/workbench/singleInvoice/",RTC:"/ReturnManagement/SingleReturnWorkbench/",SEC:"/serviceSheet/workbench/singleServiceWorkbench/",PFC:"/planningManagement/singlePlanningTask/"};i[(n=e==null?void 0:e.taskDesc)==null?void 0:n.slice(0,4)]&&I(`${i[(r=e==null?void 0:e.taskDesc)==null?void 0:r.slice(0,4)]}${e==null?void 0:e.taskDesc}`)},U=()=>{console.log("fetchFilterView")},V=()=>{console.log("clearFilterView")},u=(e,i)=>{console.log("Success flag.",e),console.log("Task Payload.",i)};return c("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:c(k,{children:c(S,{token:"eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vY2EtZ2JkLmF1dGhlbnRpY2F0aW9uLnVzMTAuaGFuYS5vbmRlbWFuZC5jb20vdG9rZW5fa2V5cyIsImtpZCI6ImRlZmF1bHQtand0LWtleS1kZjk5ODA5MzZhIiwidHlwIjoiSldUIiwiamlkIjogInVyQWRCVkJPN3VkV0FPMmFVaHp1QTRZU0V4aEE5TU96L05rTHF0UzkvR0E9In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TomSTo1o_CU1U8ExR7p5kJOZmgQirKjQSsoyUKgSBHcyMZ7ZoUB3DiZSDANEeMYnYOv__ZcyBwnY4JmIBSTvQDiWrasSnlcd2rwAx6oVREZCH3lanUM3qQd0CuAKnJMWghGmj6_XSaBnJc2Kulk8LAusknZ87EpK1EbBby1Ajrua6LafLahVkaIj4KnQkHlIa4cSXbAGVXnqAecvpX7rUI1wmwcnE1f4az3oCFoNWC7LK_pF74pZoie6yTBP4s7aQyoOwk6q_ayJdVgMjvodvE-6h01nbEw-3oRlu3YgwZOTSib5apz8upBrRJ6DdKIinfas5Q_VAXXywRjWq5wswQ",configData:W,destinationData:p,userData:{...a,user_id:a==null?void 0:a.userName},userPreferences:N,userPermissions:C,userList:{},groupList:{},userListBySystem:m,useWorkAccess:o.environment==="localhost",useConfigServerDestination:o.environment==="localhost",inboxTypeKey:"ADMIN_COMPLETED_TASKS",workspaceLabel:"Admin Completed Tasks",workspaceFiltersByAPIDriven:!0,subInboxTypeKey:"COMPLETED",cachingBaseUrl:R,onTaskClick:d,onActionComplete:u,selectedFilterView:null,isFilterView:!1,fetchFilterViewList:U,savedFilterViewData:[],clearFilterView:V,filterViewList:[],selectedTabId:null,userProcess:[]})})})}export{J as default};
