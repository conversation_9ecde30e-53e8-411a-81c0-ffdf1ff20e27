import{fE as g,fD as f,fF as p,fS as b,r as h,nE as A,as as x,aj as u,at as C,nG as d,fG as m,T as v,nB as T}from"./index-fdfa25a0.js";function D(o){return g("MuiCardActionArea",o)}const R=f("MuiCardActionArea",["root","focusVisible","focusHighlight"]),l=R,w=["children","className","focusVisibleClassName"],N=o=>{const{classes:s}=o;return m({root:["root"],focusHighlight:["focusHighlight"]},D,s)},H=p(b,{name:"MuiCardActionArea",slot:"Root",overridesResolver:(o,s)=>s.root})(({theme:o})=>({display:"block",textAlign:"inherit",borderRadius:"inherit",width:"100%",[`&:hover .${l.focusHighlight}`]:{opacity:(o.vars||o).palette.action.hoverOpacity,"@media (hover: none)":{opacity:0}},[`&.${l.focusVisible} .${l.focusHighlight}`]:{opacity:(o.vars||o).palette.action.focusOpacity}})),M=p("span",{name:"MuiCardActionArea",slot:"FocusHighlight",overridesResolver:(o,s)=>s.focusHighlight})(({theme:o})=>({overflow:"hidden",pointerEvents:"none",position:"absolute",top:0,right:0,bottom:0,left:0,borderRadius:"inherit",opacity:0,backgroundColor:"currentcolor",transition:o.transitions.create("opacity",{duration:o.transitions.duration.short})})),$=h.forwardRef(function(s,e){const t=A({props:s,name:"MuiCardActionArea"}),{children:n,className:a,focusVisibleClassName:r}=t,y=x(t,w),i=t,c=N(i);return u.jsxs(H,C({className:d(c.root,a),focusVisibleClassName:d(r,c.focusVisible),ref:e,ownerState:i},y,{children:[n,u.jsx(M,{className:c.focusHighlight,ownerState:i})]}))}),_=$;function S(o){return g("MuiDialogContentText",o)}const j=f("MuiDialogContentText",["root"]),B=j,U=["children","className"],V=o=>{const{classes:s}=o,t=m({root:["root"]},S,s);return C({},s,t)},E=p(v,{shouldForwardProp:o=>T(o)||o==="classes",name:"MuiDialogContentText",slot:"Root",overridesResolver:(o,s)=>s.root})({}),F=h.forwardRef(function(s,e){const t=A({props:s,name:"MuiDialogContentText"}),{className:n}=t,a=x(t,U),r=V(a);return u.jsx(E,C({component:"p",variant:"body1",color:"text.secondary",ref:e,ownerState:a,className:d(r.root,n)},t,{classes:r}))}),k=F;export{_ as C,k as D,D as a,l as c,B as d,S as g};
