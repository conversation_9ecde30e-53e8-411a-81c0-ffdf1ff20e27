import{b as Te,l as Fe,r as c,i as u,u as Ne,ig as $e,j as a,$ as s,hp as De,a as o,i7 as Le,T as C,ia as Pe,B as E,g_ as se,id as ze,ie as Ve,ic as We,h_ as Ie,gZ as Oe,hE as ce,a1 as x,gW as T,P as de,ix as F,w as pe,hL as he,hn as Ge,W as He,hv as Ue,i8 as Ze,gU as ue,i9 as Je,ib as Ke,hP as ge}from"./index-fdfa25a0.js";import"./dayjs.min-774e293a.js";import{E as Qe}from"./EditFieldForMassProfitCenter-d87ebb29.js";import{C as Re}from"./CompCodeProfitCenter-a8989840.js";const ot=()=>{var K,Q,R,X,Y,k,ee,te,oe,ne,le,ie,re;const z=Te(),f=Fe();c.useState({});const[V,W]=c.useState(0);c.useState([]);const[b,fe]=c.useState(!1),[Xe,Ce]=c.useState(!0);c.useState([]),c.useState([]);const[xe,me]=c.useState([]),[g,B]=c.useState(0);c.useState(),u(e=>e.tabsData);const[be,I]=c.useState(!1),[O,ye]=c.useState([]),[ve,G]=c.useState(!1),N=Ne(),y=u(e=>e.profitCenter.MultipleProfitCenterRequestBench),h=N.state.tabsData,i=N.state.rowData,l=N.state.requestbenchRowData;let d=u(e=>{var n;return(n=e==null?void 0:e.initialData)==null?void 0:n.IWMMyTask});console.log(d,"taskData_in_mass"),console.log(l,"========massProfitRowData==========="),console.log("selectedrowdata",i,h);const M=h.viewData["Comp Codes"]["Company Code Assignment for Profit Center"];console.log("commpcodedata",M),u(e=>e.payload);let $=u(e=>e.edit.payload);console.log($,"singlePCPayloadAfterChange");let D=u(e=>e.profitCenter.requiredFields);console.log(D,"required_field_for_data");const Se=u(e=>e.profitCenter.profitCenterCompCodes);let t=u(e=>e.userManagement.taskData),Ae=u(e=>e.profitCenter.MultipleProfitCenterData),L=u(e=>e.userManagement.userData);console.log(Ae,"profitCenterMultiple"),console.log(t,"task_in_mass========================="),console.log(t==null?void 0:t.processDesc,"task?.processDesc"),console.log(t==null?void 0:t.subject,"task?.subject"),console.log(l==null?void 0:l.requestId,"massProfitRowData?.requestId"),console.log(l==null?void 0:l.requestType,"massProfitRowData?.requestType");let m="",v="",S="";(t==null?void 0:t.processDesc)==="Mass Change"?(m=t!=null&&t.subject?(K=t==null?void 0:t.subject)==null?void 0:K.slice(3):l==null?void 0:l.requestId.slice(3),v=(Q=d==null?void 0:d.body)!=null&&Q.controllingArea?"":i.controllingArea,S=(R=t==null?void 0:t.body)!=null&&R.profitCenter?"":i.profitCenter):(t==null?void 0:t.processDesc)==="Mass Create"?(m=t!=null&&t.subject?(X=t==null?void 0:t.subject)==null?void 0:X.slice(3):l==null?void 0:l.requestId.slice(3),v=(Y=d==null?void 0:d.body)!=null&&Y.controllingArea?"":i.controllingArea,S=(k=t==null?void 0:t.body)!=null&&k.profitCenter?"":i.profitCenter):(l==null?void 0:l.requestType)==="Mass Create"?(m=t!=null&&t.subject?(ee=t==null?void 0:t.subject)==null?void 0:ee.slice(3):l==null?void 0:l.requestId.slice(3),v=(te=d==null?void 0:d.body)!=null&&te.controllingArea?"":i.controllingArea,S=(oe=t==null?void 0:t.body)!=null&&oe.profitCenter?"":i.profitCenter):(l==null?void 0:l.requestType)==="Mass Change"&&(m=t!=null&&t.subject?(ne=t==null?void 0:t.subject)==null?void 0:ne.slice(3):l==null?void 0:l.requestId.slice(3),v=(le=d==null?void 0:d.body)!=null&&le.controllingArea?"":i.controllingArea,S=(ie=t==null?void 0:t.body)!=null&&ie.profitCenter?"":i.profitCenter);for(let e=0;e<(y==null?void 0:y.length);e++)if(y[e].profitCenter===i.profitCenter){y[e];break}const je=(e,n)=>{setActiveTab(n)},H=()=>{const e=J();b?e?(B(n=>n-1),f(F())):Z():(B(n=>n-1),f(F()))},U=()=>{const e=J();b?e?(B(n=>n+1),f(F())):Z():(B(n=>n+1),f(F()))},Z=()=>{G(!0)},q=Object.entries(h==null?void 0:h.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]),A=Object.entries(h.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),P={};A.map(e=>{e.forEach((n,r)=>{n.forEach((p,w)=>{w!==0&&p.forEach(j=>{P[j.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=j.value})})})}),console.log("tabcontentsnow",A,P);const Be=()=>{fe(!0),Ce(!1)},Me=e=>{I(e)},_e=()=>{I(!0)},qe=()=>{const e=r=>{f(ge({keyName:"ProfitCtrGroup",data:r.body}))},n=r=>{console.log(r)};pe(`/${he}/data/getProfitCtrGroup?controllingArea=${i.controllingArea}`,"get",e,n)},we=e=>{console.log("compcode",e);const n=p=>{console.log("value",p),f(ge({keyName:"Region",data:p.body}))},r=p=>{console.log(p,"error in dojax")};pe(`/${he}/data/getRegionBasedOnCountry?country=${e}`,"get",n,r)};c.useEffect(()=>{f($e(P))},[]),c.useEffect(()=>{var e;me(_.zip(M[0].value,M[1].value,M[2].value).map((n,r)=>{var p,w,j,ae;return{id:r,companyCodes:(p=n[0])!=null&&p.split("$$$")[0]?(w=n[0])==null?void 0:w.split("$$$")[0]:"",companyName:(j=n[1])!=null&&j.split("$$$")[0]?(ae=n[1])==null?void 0:ae.split("$$$")[0]:"",assigned:n[2]?n[2]:""}})),qe(),we((e=h==null?void 0:h.viewData.Address["Address Data"].find(n=>(n==null?void 0:n.fieldName)==="Country/Reg."))==null?void 0:e.value)},[]),console.log($,D,"requiredFieldTabWise");const J=()=>Ge($,D,ye),Ee=()=>{G(!1)};return console.log(m,v,S,"================"),console.log("tabcontents",h),a("div",{children:[a(s,{container:!0,style:{...De,backgroundColor:"#FAFCFF"},children:[O.length!=0&&o(Le,{openSnackBar:ve,alertMsg:"Please fill the following Field: "+O.join(", "),handleSnackBarClose:Ee}),a(s,{sx:{width:"inherit"},children:[a(s,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[o(s,{style:{display:"flex",justifyContent:"flex-end"},children:o(He,{color:"primary","aria-label":"upload picture",component:"label",sx:Ue,children:o(Ze,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{z(-1)}})})}),a(s,{md:10,children:[o(C,{variant:"h3",children:a("strong",{children:["Multiple Profit Center : ",i.profitCenter," "]})}),o(C,{variant:"body2",color:"#777",children:"This view displays details of uploaded Profit Center"})]}),o(s,{md:1,sx:{display:"flex",justifyContent:"flex-end",marginRight:"4px"},children:o(x,{variant:"outlined",size:"small",sx:ue,onClick:_e,title:"Chnage Log",children:o(Je,{sx:{padding:"2px"},fontSize:"small"})})}),be&&o(Pe,{open:!0,closeModal:Me,requestId:m,requestType:"Mass",pageName:"profitCenter",controllingArea:i.controllingArea,centerName:i.profitCenter}),b?"":(L==null?void 0:L.role)==="Finance"?o(s,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:o(s,{item:!0,children:a(x,{variant:"outlined",size:"small",sx:ue,onClick:Be,children:["Change",o(Ke,{sx:{padding:"2px"},fontSize:"small"})]})})}):""]}),o(s,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:a(E,{width:"70%",sx:{marginLeft:"40px"},children:[o(s,{item:!0,sx:{paddingTop:"2px !important"},children:a(se,{flexDirection:"row",children:[o("div",{style:{width:"15%"},children:o(C,{variant:"body2",color:"#777",children:"Profit Center"})}),a(C,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",i.profitCenter]})]})}),o(s,{item:!0,sx:{paddingTop:"2px !important"},children:a(se,{flexDirection:"row",children:[o("div",{style:{width:"15%"},children:o(C,{variant:"body2",color:"#777",children:"Controlling Area"})}),a(C,{variant:"body2",fontWeight:"bold",children:[": ",i.controllingArea]})]})})]})}),a(s,{container:!0,style:{padding:"16px"},children:[o(We,{activeStep:g,onChange:je,variant:"scrollable",sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:q.map((e,n)=>o(ze,{children:o(Ve,{sx:{fontWeight:"700"},children:e})},e))}),o(s,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:A&&((re=A[g])==null?void 0:re.map((e,n)=>g===2?o(Re,{compCodesTabDetails:Se,displayCompCode:xe}):o(E,{sx:{width:"100%"},children:a(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Ie},children:[o(s,{container:!0,children:o(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),o(E,{children:o(E,{sx:{width:"100%"},children:o(Oe,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:o(s,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(r=>(console.log("inneritem",e[1]),o(Qe,{activeTabIndex:g,fieldGroup:e[0],selectedRowData:i.profitCenter,pcTabs:q,label:r.fieldName,value:r.value,length:r.maxLength,visibility:r.visibility,onSave:p=>handleFieldSave(r.fieldName,p),isEditMode:b,type:r.fieldType,field:r})))})})})})]})},n)))},A)]})]})]}),b?o(de,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ce,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:V,onChange:e=>{W(e)},children:[o(x,{size:"small",variant:"contained",onClick:()=>{z(-1)},children:"Save"}),o(x,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:H,disabled:g===0,children:"Back"}),o(x,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:U,disabled:g===q.length-1,children:"Next"})]})}):o(de,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ce,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:V,onChange:e=>{W(e)},children:[o(x,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:H,disabled:g===0,children:"Back"}),o(x,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:U,disabled:g===q.length-1,children:"Next"})]})})]})};export{ot as default};
