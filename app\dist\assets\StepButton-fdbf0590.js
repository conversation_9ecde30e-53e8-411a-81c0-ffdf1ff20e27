import{fE as C,fD as g,fF as b,fS as j,at as a,r as n,nE as E,as as v,pm as w,pl as M,oP as P,aj as l,ie as y,nG as N,fG as U}from"./index-fdfa25a0.js";function $(t){return C("MuiStepButton",t)}const L=g("MuiStepButton",["root","horizontal","vertical","touchRipple"]),u=L,_=["children","className","icon","optional"],z=t=>{const{classes:e,orientation:o}=t;return U({root:["root",o],touchRipple:["touchRipple"]},$,e)},D=b(j,{name:"MuiStepButton",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[{[`& .${u.touchRipple}`]:e.touchRipple},e.root,e[o.orientation]]}})(({ownerState:t})=>a({width:"100%",padding:"24px 16px",margin:"-24px -16px",boxSizing:"content-box"},t.orientation==="vertical"&&{justifyContent:"flex-start",padding:"8px",margin:"-8px"},{[`& .${u.touchRipple}`]:{color:"rgba(0, 0, 0, 0.3)"}})),G=n.forwardRef(function(e,o){const s=E({props:e,name:"MuiStepButton"}),{children:i,className:d,icon:x,optional:S}=s,h=v(s,_),{disabled:f,active:m}=n.useContext(w),{orientation:R}=n.useContext(M),p=a({},s,{orientation:R}),r=z(p),c={icon:x,optional:S},B=P(i,["StepLabel"])?n.cloneElement(i,c):l.jsx(y,a({},c,{children:i}));return l.jsx(D,a({focusRipple:!0,disabled:f,TouchRippleProps:{className:r.touchRipple},className:N(r.root,d),ref:o,ownerState:p,"aria-current":m?"step":void 0},h,{children:B}))}),T=G;export{T as S,$ as g,u as s};
