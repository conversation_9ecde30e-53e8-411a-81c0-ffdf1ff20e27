import{r as d,b as ho,l as go,u as fo,i as Z,hZ as mo,hN as K,a as t,ht as vo,j as C,fV as oe,h1 as te,T as g,W as x,hI as ve,fX as ne,g_ as re,B as ae,gQ as fs,a3 as ms,fH as le,a1 as T,hC as ye,ho as yo,h8 as bo,i7 as Mo,J as So,hl as Ao,$ as B,hp as To,hv as Fo,i8 as Do,h as Io,kf as No,F as j,a0 as vs,i6 as Po,fh as wo,h$ as qo,i0 as Eo,i1 as ko,i2 as Bo,i3 as $o,i4 as Lo,i5 as Oo,hD as _o,P as zo,hE as jo,gU as ie,gW as G,ii as Go,ij as Vo,w as F,ik as be,hL as N,ih as ys,sv as Uo,hP as Wo}from"./index-fdfa25a0.js";const Qo=()=>{var je;const[$,z]=d.useState(!0),[bs,Jo]=d.useState("1"),[J,Ms]=d.useState([]);d.useState([]);const ce=ho(),Me=go(),o=fo().state,[Se,f]=d.useState(""),[Ss,de]=d.useState(!1),[Xo,m]=d.useState(!1),[As,p]=d.useState(!1),[Ts,v]=d.useState(!1),[Yo,y]=d.useState(!0),[Fs,b]=d.useState(!1),[Ds,Ae]=d.useState(!1),[Is,Te]=d.useState(!1),[Ns,Fe]=d.useState(!1),[Y,De]=d.useState(""),[ue,D]=d.useState(!1),[Ie,Ne]=d.useState(!0),[Pe,we]=d.useState(!0),[Ho,qe]=d.useState(!0),[Ps,Ee]=d.useState(!1),[Ce,ws]=d.useState([]),[pe,qs]=d.useState([]),[Es,he]=d.useState(!1),[ks,A]=d.useState(!1),[ge,Bs]=d.useState(""),[$s,fe]=d.useState(!1),[Q,ke]=d.useState([]),[Ls,Os]=d.useState([]);console.log("mouse",J);let r=Z(e=>e.userManagement.taskData),s=Z(e=>e.userManagement.userData),_s=Z(e=>{var l;return(l=e.userManagement.entitiesAndActivities)==null?void 0:l["Profit Center"]});const P=Z(e=>e.appSettings),V=Z(e=>e.profitCenter.MultipleProfitCenterData),E=()=>{de(!0)},zs=()=>{Es?(de(!1),he(!1)):(de(!1),ce("/masterDataCockpit/profitCenter"))},k=()=>{Ae(!0)},Be=()=>{Ae(!1)},js=()=>{},Gs=()=>{Ee(!0)},$e=()=>{Ee(!1)},Vs=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:e=>C(j,{children:[t(Go,{index:e.row.id,name:e.row.docName}),t(Vo,{index:e.row.id,name:e.row.docName})]})}],Us=()=>{let e=r!=null&&r.subject?r==null?void 0:r.subject:o==null?void 0:o.requestId,l=a=>{var n=[];a.documentDetailDtoList.forEach(i=>{var M={id:i.documentId,docType:i.fileType,docName:i.fileName,uploadedOn:K(i.docCreationDate).format(P.date),uploadedBy:i.createdBy};n.push(M)}),ws(n)};F(`/${be}/documentManagement/getDocByRequestId/${e}`,"get",l)},Ws=()=>{let e=r!=null&&r.subject?r==null?void 0:r.subject:o==null?void 0:o.requestId,l=n=>{console.log("commentsdata",n);var i=[];n.body.forEach(M=>{var I={id:M.requestId,comment:M.comment,user:M.createdByUser,createdAt:M.updatedAt};i.push(I)}),qs(i),console.log("commentrows",i)},a=n=>{console.log(n)};F(`/${N}/activitylog/fetchTaskDetailsForRequestId?requestId=${e}`,"get",l,a)};d.useEffect(()=>{Us(),Ws(),Bs(mo("PC"))},[]);const c=(e,l)=>{const a=e==null?void 0:e.find(n=>(n==null?void 0:n.fieldName)===l);return a?a.value:""},Js=()=>{var n,i,M,I;console.log("sdfkhdgkf"),z(!0);let e={};(r==null?void 0:r.processDesc)==="Mass Change"?e={massCreationId:"",massChangeId:r!=null&&r.subject?(n=r==null?void 0:r.subject)==null?void 0:n.slice(3):o==null?void 0:o.requestId.slice(3),screenName:"Change"}:(r==null?void 0:r.processDesc)==="Mass Create"?e={massCreationId:r!=null&&r.subject?(i=r==null?void 0:r.subject)==null?void 0:i.slice(3):o==null?void 0:o.requestId.slice(3),massChangeId:"",screenName:"Create"}:(o==null?void 0:o.requestType)==="Mass Create"?e={massCreationId:(M=o==null?void 0:o.requestId)==null?void 0:M.slice(3),massChangeId:"",screenName:"Create"}:(o==null?void 0:o.requestType)==="Mass Change"&&(e={massCreationId:"",massChangeId:(I=o==null?void 0:o.requestId)==null?void 0:I.slice(3),screenName:"Change"});const l=S=>{z(!1),S.body&&Me(Uo(S==null?void 0:S.body))},a=S=>{console.log(S)};F(`/${N}/data/displayMassProfitCenter`,"post",l,a,e)},[me,Rs]=d.useState(0),Xs=(e,l)=>{const a=i=>{Me(Wo({keyName:e,data:i.body})),Rs(M=>M+1)},n=i=>{console.log(i)};F(`/${N}/data/${l}`,"get",a,n)},Ys=()=>{var e,l;console.log("Calleddddd"),(l=(e=ys)==null?void 0:e.profitCenter)==null||l.map(a=>{Xs(a==null?void 0:a.keyName,a==null?void 0:a.endPoint)})},Hs=()=>{var e,l;console.log("apiCount",me),me==((l=(e=ys)==null?void 0:e.profitCenter)==null?void 0:l.length)?z(!1):z(!0)};d.useEffect(()=>{Hs()},[me]),d.useEffect(()=>{if(V.length===0)Js();else return},[]),d.useEffect(()=>{Ys()},[]);const Ks=e=>{e.length>0?(D(!0),console.log("selectedIds1",e)):D(!1),console.log("selectedIds",e),Ms(e)},L=V==null?void 0:V.map((e,l)=>{var i,M,I,S,R,X;const a=e,n=((i=e==null?void 0:e.viewData)==null?void 0:i["Basic Data"])||{};return{id:l,profitCenter:a==null?void 0:a.profitCenter,controllingArea:a==null?void 0:a.controllingArea,profitCenterName:((M=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Name"))==null?void 0:M.value)||"",personResponsible:((I=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Person Responsible"))==null?void 0:I.value)||"",profitCenterGroup:((S=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Profit Ctr Group"))==null?void 0:S.value)||"",analysisPeriodFrom:K((R=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Analysis Period From"))==null?void 0:R.value).format(P==null?void 0:P.dateFormat)||"",analysisPeriodTo:K((X=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Analysis Period To"))==null?void 0:X.value).format(P==null?void 0:P.dateFormat)||""}}),Qs=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1,renderCell:e=>{const l=Q.find(a=>a.profitCenter===e.value);return console.log(l,"isDirectMatch"),console.log(e,"params"),l&&l.code===400?t(g,{sx:{fontSize:"12px",color:"red"},children:e.value}):t(g,{sx:{fontSize:"12px"},children:e.value})}},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"profitCenterName",headerName:"Profit Center Name",editable:!1,flex:1,renderCell:e=>{const l=Ls.includes(e.row.profitCenterName);return t(g,{sx:{fontSize:"12px",color:l?"red":"inherit"},children:e.value})}},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"profitCenterGroup",headerName:"Profit Center Group",editable:!1,flex:1},{field:"analysisPeriodFrom",headerName:"Ananlysis Period From",editable:!1,flex:1,renderCell:e=>t(g,{sx:{fontSize:"12px"},children:K(e.row.analysisPeriodFrom).format(P==null?void 0:P.dateFormat)})},{field:"analysisPeriodTo",headerName:"Analysis Period To",editable:!1,flex:1,renderCell:e=>t(g,{sx:{fontSize:"12px"},children:K(e.row.analysisPeriodTo).format(P==null?void 0:P.dateFormat)})}];var w=V==null?void 0:V.map(e=>{var l,a,n,i,M,I,S,R,X,h,u,q,W,Ge,Ve,Ue,We,Je,Re,Xe,Ye,He,Ke,Qe,Ze,xe,es,ss,os,ts,ns,rs,as,ls,is,cs,ds,us,Cs,ps,hs,gs;return console.log("samsung",e),{ProfitCenterID:e==null?void 0:e.profitCenterId,Action:(r==null?void 0:r.processDesc)==="Mass Create"?"I":(r==null?void 0:r.processDesc)==="Mass Change"||(o==null?void 0:o.requestType)==="Mass Change"?"U":(o==null?void 0:o.requestType)==="Mass Create"?"I":"",RequestID:"",TaskStatus:"",TaskId:r!=null&&r.taskId?r==null?void 0:r.taskId:"",ReqCreatedBy:s==null?void 0:s.user_id,ReqCreatedOn:r!=null&&r.createdOn?"/Date("+(r==null?void 0:r.createdOn)+")/":"",RequestStatus:"",Remarks:Y||"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:(r==null?void 0:r.processDesc)==="Mass Create"?(l=r==null?void 0:r.subject)==null?void 0:l.slice(3):(o==null?void 0:o.requestType)==="Mass Create"?o==null?void 0:o.requestId.slice(3):"",MassEditId:(r==null?void 0:r.processDesc)==="Mass Change"?(a=r==null?void 0:r.subject)==null?void 0:a.slice(3):(o==null?void 0:o.requestType)==="Mass Change"?o==null?void 0:o.requestId.slice(3):"",MassDeleteId:"",RequestType:(r==null?void 0:r.processDesc)==="Mass Create"?"Mass Create":(r==null?void 0:r.processDesc)==="Mass Change"||(o==null?void 0:o.requestType)==="Mass Change"?"Mass Change":(o==null?void 0:o.requestType)==="Mass Create"?"Mass Create":"",MassRequestStatus:"",PrctrName:c((n=e==null?void 0:e.viewData["Basic Data"])==null?void 0:n["General Data"],"Name"),LongText:c((i=e==null?void 0:e.viewData["Basic Data"])==null?void 0:i["General Data"],"Long Text"),InChargeUser:c((M=e==null?void 0:e.viewData["Basic Data"])==null?void 0:M["General Data"],"User Responsible"),InCharge:c((I=e==null?void 0:e.viewData["Basic Data"])==null?void 0:I["General Data"],"Person Responsible"),Department:c((S=e==null?void 0:e.viewData["Basic Data"])==null?void 0:S["General Data"],"Department"),PrctrHierGrp:c((R=e==null?void 0:e.viewData["Basic Data"])==null?void 0:R["General Data"],"Profit Ctr Group"),Segment:c((X=e==null?void 0:e.viewData["Basic Data"])==null?void 0:X["General Data"],"Segment"),LockInd:c((h=e==null?void 0:e.viewData.Indicators)==null?void 0:h.Indicator,"Lock indicator")===!0?"X":"",Template:c((u=e==null?void 0:e.viewData.Indicators)==null?void 0:u["Formula Planning"],"Form. Planning Temp"),Title:c((q=e==null?void 0:e.viewData.Address)==null?void 0:q["Address Data"],"Title"),Name1:c((W=e==null?void 0:e.viewData.Address)==null?void 0:W["Address Data"],"Name 1"),Name2:c((Ge=e==null?void 0:e.viewData.Address)==null?void 0:Ge["Address Data"],"Name 2"),Name3:c((Ve=e==null?void 0:e.viewData.Address)==null?void 0:Ve["Address Data"],"Name 3"),Name4:c((Ue=e==null?void 0:e.viewData.Address)==null?void 0:Ue["Address Data"],"Name 4"),Street:c((We=e==null?void 0:e.viewData.Address)==null?void 0:We["Address Data"],"Street"),City:c((Je=e==null?void 0:e.viewData.Address)==null?void 0:Je["Address Data"],"City"),District:c((Re=e==null?void 0:e.viewData.Address)==null?void 0:Re["Address Data"],"District"),Country:c((Xe=e==null?void 0:e.viewData.Address)==null?void 0:Xe["Address Data"],"Country/Reg."),Taxjurcode:c((Ye=e==null?void 0:e.viewData.Address)==null?void 0:Ye["Address Data"],"Tax Jur."),PoBox:c((He=e==null?void 0:e.viewData.Address)==null?void 0:He["Address Data"],"P.O.Box"),PostlCode:c((Ke=e==null?void 0:e.viewData.Address)==null?void 0:Ke["Address Data"],"Postal Code"),PobxPcd:c((Qe=e==null?void 0:e.viewData.Address)==null?void 0:Qe["Address Data"],"PO Box PCode"),Region:c((Ze=e==null?void 0:e.viewData.Address)==null?void 0:Ze["Address Data"],"Region"),Langu:c((xe=e==null?void 0:e.viewData.Communication)==null?void 0:xe["Communication Data"],"Language"),Telephone:c((es=e==null?void 0:e.viewData.Communication)==null?void 0:es["Communication Data"],"Telephone 1"),Telephone2:c((ss=e==null?void 0:e.viewData.Communication)==null?void 0:ss["Communication Data"],"Telephone 2"),Telebox:c((os=e==null?void 0:e.viewData.Communication)==null?void 0:os["Communication Data"],"Telebox"),Telex:c((ts=e==null?void 0:e.viewData.Communication)==null?void 0:ts["Communication Data"],"Telex"),FaxNumber:c((ns=e==null?void 0:e.viewData.Communication)==null?void 0:ns["Communication Data"],"Fax Number"),Teletex:c((rs=e==null?void 0:e.viewData.Communication)==null?void 0:rs["Communication Data"],"Teletex"),Printer:c((as=e==null?void 0:e.viewData.Communication)==null?void 0:as["Communication Data"],"Printer name"),DataLine:c((ls=e==null?void 0:e.viewData.Communication)==null?void 0:ls["Communication Data"],"Data line"),ProfitCenter:e==null?void 0:e.profitCenter,ControllingArea:e==null?void 0:e.controllingArea,ValidfromDate:c((is=e==null?void 0:e.viewData["Basic Data"])==null?void 0:is["General Data"],"Analysis Period From"),ValidtoDate:c((cs=e==null?void 0:e.viewData["Basic Data"])==null?void 0:cs["General Data"],"Analysis Period To"),Testrun:ue,Countryiso:"",LanguIso:"",Logsystem:"",ToCompanycode:_.zip((us=(ds=e==null?void 0:e.viewData)==null?void 0:ds["Comp Codes"])==null?void 0:us["Company Code Assignment for Profit Center"][0].value,(ps=(Cs=e==null?void 0:e.viewData)==null?void 0:Cs["Comp Codes"])==null?void 0:ps["Company Code Assignment for Profit Center"][1].value,(gs=(hs=e==null?void 0:e.viewData)==null?void 0:hs["Comp Codes"])==null?void 0:gs["Company Code Assignment for Profit Center"][2].value).map(se=>({CompCodeID:se[0].split("$$$")[1],CompCode:se[0].split("$$$")[0],CompanyName:se[1],AssignToPrctr:(se[2]===!0,"X"),Venture:"",RecInd:"",EquityTyp:"",JvOtype:"",JvJibcl:"",JvJibsa:""}))}});console.log("massProfitRowData",o);const Zs=()=>{const e=w;console.log("isLoading1",$),console.log("paylaod",e);const l=n=>{A(!1),n.statusCode===200?(console.log("success"),p("Create"),f(`Mass Profit Center Submitted for Approval with ID NPM${n.body}`),v("success"),y(!1),b(!0),E(),m(!0),D(!0)):(p("Error"),b(!1),f("Failed Submitting the Mass Profit Center for Approval  "),v("danger"),y(!1),m(!0),k(),D(!0)),handleClose()},a=n=>{console.log(n)};F(`/${N}/massAction/profitCentersApprovalSubmit`,"post",l,a,e)},xs=()=>{L.filter((n,i)=>J.includes(i));const e=w;console.log("paylaod",e),console.log("isLoading2",$);const l=n=>{A(!1),D(!0),n.statusCode===200?(console.log("success"),p("Create"),f(`Mass Profit Center Change Submitted for Approval with ID CPM${n.body}`),v("success"),y(!1),b(!0),E(),m(!0)):(p("Error"),b(!1),f("Failed Submitting Mass Profit Center Data for Approval"),v("danger"),y(!1),m(!0),k())},a=n=>{console.log("error")};F(`/${N}/massAction/changeProfitCentersApprovalSubmit`,"post",l,a,e)},eo=()=>{L.filter((n,i)=>J.includes(i));const e=w;console.log("paylaod",e),console.log("isLoading3",$);const l=n=>{A(!1),n.statusCode===201?(console.log("success"),p("Create"),f("Mass Profit Center Approved & SAP Syndication Completed"),v("success"),y(!1),b(!0),E(),m(!0),D(!0)):(p("Error"),b(!1),f("Failed Submitting the Profit Center for Approval "),v("danger"),y(!1),m(!0),k(),D(!0)),handleClose()},a=n=>{console.log(n)};F(`/${N}/massAction/createProfitCentersApproved`,"post",l,a,e)},so=()=>{const e=w;console.log("paylaod",e),console.log("isLoading4",$);const l=n=>{A(!1),D(!0),n.statusCode===201?(console.log("success"),p("Create"),f("Mass Profit Center Change Approved & SAP Syndication Completed"),v("success"),y(!1),b(!0),E(),m(!0)):(p("Error"),b(!1),f("Failed Approving Mass Profit Center Change"),v("danger"),y(!1),m(!0),k())},a=n=>{console.log("error")};F(`/${N}/massAction/changeProfitCentersApproved`,"post",l,a,e)},oo=()=>{const e=w;console.log("paylaod",e),console.log("isLoading5",$);const l=n=>{if(A(!1),D(!0),n.statusCode===200){console.log("success"),p("Create"),f(`Mass Profit Center Submitted for Review with ID NPM${n.body}`),v("success"),y(!1),b(!0),E(),m(!0);const i={artifactId:ge,createdBy:s==null?void 0:s.emailId,artifactType:"ProfitCenter",requestId:`NPM${n==null?void 0:n.body}`},M=S=>{console.log("Second API success",S)},I=S=>{console.error("Second API error",S)};F(`/${be}/documentManagement/updateDocRequestId`,"post",M,I,i)}else p("Error"),b(!1),f("Failed Submitting the Mass Profit Center for Review  "),v("danger"),y(!1),m(!0),k();handleClose()},a=n=>{console.log(n)};F(`/${N}/massAction/profitCentersSubmitForReview`,"post",l,a,e)},to=()=>{const e=w;console.log("paylaod",e),console.log("isLoading5",$);const l=n=>{if(A(!1),D(!0),n.statusCode===200){console.log("success"),p("Create"),f(`Mass Profit Center Submitted for Review with ID CPM${n.body}`),v("success"),y(!1),b(!0),E(),m(!0);const i={artifactId:ge,createdBy:s==null?void 0:s.emailId,artifactType:"ProfitCenter",requestId:`CPM${n==null?void 0:n.body}`},M=S=>{console.log("Second API success",S)},I=S=>{console.error("Second API error",S)};F(`/${be}/documentManagement/updateDocRequestId`,"post",M,I,i)}else p("Error"),b(!1),f("Failed Submitting the Mass Profit Center for Review  "),v("danger"),y(!1),m(!0),k();handleClose()},a=n=>{console.log(n)};F(`/${N}/massAction/changeProfitCentersSubmitForReview`,"post",l,a,e)},Le=(e,l)=>{const a=e.target.value;if(a.length>0&&a[0]===" ")De(a.trimStart());else{let n=a.toUpperCase();De(n)}},U=()=>{D(!0),Fe(!1)},no=()=>{(s==null?void 0:s.role)==="MDM Steward"&&r.processDesc==="Mass Create"||(s==null?void 0:s.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Create"?(A(!0),U(),Zs()):(s==null?void 0:s.role)==="Approver"&&r.processDesc==="Mass Create"||(s==null?void 0:s.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Create"?(A(!0),U(),eo()):(s==null?void 0:s.role)==="Finance"&&r.processDesc==="Mass Create"||(s==null?void 0:s.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Create"?(A(!0),U(),oo()):(s==null?void 0:s.role)==="MDM Steward"&&r.processDesc==="Mass Change"||(s==null?void 0:s.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Change"?(A(!0),U(),xs()):(s==null?void 0:s.role)==="Approver"&&r.processDesc==="Mass Change"||(s==null?void 0:s.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Change"?(A(!0),U(),so()):((s==null?void 0:s.role)==="Finance"&&r.processDesc==="Mass Change"||(s==null?void 0:s.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Change")&&(A(!0),U(),to())},H=()=>{D(!1),Fe(!0)},O=()=>{Te(!1)},ee=()=>{D(!1),Te(!0)},ro=()=>{console.log("isLoading6",$),L.filter((n,i)=>J.includes(i));const e=w,l=n=>{z(!1),D(!0),n.statusCode===200?(console.log("success"),p("Create"),f(`Profit Center Submitted for Correction with ID NPS${n.body}`),v("success"),y(!1),b(!0),E(),m(!0)):(p("Error"),b(!1),f("Failed Submitting Profit Center for Correction"),v("danger"),y(!1),m(!0),k()),O()},a=n=>{console.log(n)};console.log("remarkssssssssss",Y),F(`/${N}/massAction/profitCentersSendForCorrection`,"post",l,a,e)},ao=()=>{L.filter((n,i)=>J.includes(i));const e=w,l=n=>{z(!1),D(!0),n.statusCode===200?(console.log("success"),p("Create"),f(`Profit Center Submitted for Correction with ID NPS${n.body}`),v("success"),y(!1),b(!0),E(),m(!0)):(p("Error"),b(!1),f("Failed Submitting Profit Center for Correction"),v("danger"),y(!1),m(!0),k()),O()},a=n=>{console.log(n)};console.log("remarkssssssssss",Y),F(`/${N}/massAction/changeProfitCentersSendForCorrection`,"post",l,a,e)},lo=()=>{console.log("isLoading7",$),L.filter((n,i)=>J.includes(i));const e=w,l=n=>{z(!1),D(!0),n.statusCode===200?(console.log("success"),p("Create"),f(`Profit Center Submitted for Correction with ID NCS${n.body}`),v("success"),y(!1),b(!0),E(),m(!0)):(p("Error"),b(!1),f("Failed Submitting Profit Center for Correction"),v("danger"),y(!1),m(!0),k()),O()},a=n=>{console.log(n)};F(`/${N}/massAction/profitCentersSendForReview`,"post",l,a,e)},io=()=>{L.filter((n,i)=>J.includes(i));const e=w,l=n=>{z(!1),D(!0),n.statusCode===200?(console.log("success"),p("Create"),f(`Profit Center Submitted for Correction with ID NCS${n.body}`),v("success"),y(!1),b(!0),E(),m(!0)):(p("Error"),b(!1),f("Failed Submitting Profit Center for Correction"),v("danger"),y(!1),m(!0),k()),O()},a=n=>{console.log(n)};F(`/${N}/massAction/changeProfitCentersSendForReview`,"post",l,a,e)},co=()=>{(s==null?void 0:s.role)==="MDM Steward"&&r.processDesc==="Mass Create"||(s==null?void 0:s.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Create"?(z(!0),O(),ro()):(s==null?void 0:s.role)==="Approver"&&r.processDesc==="Mass Create"||(s==null?void 0:s.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Create"?(z(!0),O(),lo()):(s==null?void 0:s.role)==="MDM Steward"&&r.processDesc==="Mass Change"||(s==null?void 0:s.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Change"?(O(),ao()):((s==null?void 0:s.role)==="Approver"&&r.processDesc==="Mass Change"||(s==null?void 0:s.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Change")&&(O(),io())},Oe=()=>{A(!0);let e=w;const l=n=>{n.statusCode===400?(ke(n.body),fe(!0),we(!0),A(!1)):(we(!1),qe(!1),p("Create"),console.log("success"),p("Create"),f("All Data has been Validated. Profit Center can be Sent for Review"),v("success"),y(!1),b(!0),E(),m(!0),he(!0),A(!1))},a=n=>{console.log(n)};F(`/${N}/massAction/validateMassProfitCenter`,"post",l,a,e)},uo=(e,l)=>e.every(a=>!l.includes(a)),_e=()=>{A(!0);const e=L.filter((u,q)=>J.includes(q));console.log("selectedData",e);const l=e.map(u=>({...w[u==null?void 0:u.id]}));console.log("selectedProfitCenterRows",l);const a=[],n=[];l.map(u=>{var W;var q={coArea:u==null?void 0:u.ControllingArea,name:u==null?void 0:u.PrctrName.toUpperCase()};a.push(q),n.push((W=u==null?void 0:u.PrctrName)==null?void 0:W.toUpperCase())}),console.log("duplicateCheckPayload",a);const i=[];V.map(u=>{var q;i.push((q=u.profitCenterName)==null?void 0:q.toUpperCase())}),console.log(i,n,"arrayofviewand");let M=uo(i,n),I=w;I=l;const S=u=>{u.statusCode===400?(ke(u.body),fe(!0),A(!1)):(qe(!1),p("Create"),console.log("success"),p("Create"),f("All Data has been Validated. Profit Center can be Sent for Review"),v("success"),y(!1),b(!0),E(),m(!0),he(!0),(a.coArea!==""||a.name!=="")&&((o==null?void 0:o.requestType)==="Mass Change"&&!M?A(!1):F(`/${N}/alter/fetchPCDescriptionsDupliChk`,"post",R,X,a)))},R=u=>{if(console.log("dataaaa",u),u.body.length===0||!u.body.some(q=>a.some(W=>W.name.toUpperCase()===q.matches[0])))A(!1),Ne(!1);else{const q=u.body.map(W=>W.matches[0]);A(!1),p("Duplicate Check"),b(!1),f("There is a direct match for the Profit Center name."),v("danger"),y(!1),m(!0),k(),Ne(!0),Os(q)}},X=u=>{console.log(u)},h=u=>{console.log(u)};F(`/${N}/massAction/validateMassProfitCenter`,"post",S,h,I)},ze=()=>{fe(!1)},Co=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}];console.log("profitValidationError",Q);const po=(je=Q==null?void 0:Q.filter(e=>(e==null?void 0:e.code)===400))==null?void 0:je.map((e,l)=>{var a;if(e.code===400)return{id:l,profitCenter:e==null?void 0:e.profitCenter,error:(a=e==null?void 0:e.status)==null?void 0:a.message}});return t(j,{children:$===!0?t(vo,{}):C("div",{style:{backgroundColor:"#FAFCFF"},children:[C(oe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Is,onClose:O,children:[C(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(g,{variant:"h6",children:"Remarks"}),t(x,{sx:{width:"max-content"},onClick:O,children:t(ve,{})})]}),t(ne,{sx:{padding:".5rem 1rem"},children:t(re,{children:t(ae,{sx:{minWidth:400},children:t(fs,{sx:{height:"auto"},fullWidth:!0,children:t(ms,{sx:{backgroundColor:"#F5F5F5"},onChange:Le,value:Y,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),C(le,{sx:{display:"flex",justifyContent:"end"},children:[t(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:O,children:"Cancel"}),t(T,{className:"button_primary--normal",type:"save",onClick:co,variant:"contained",children:"Submit"})]})]}),C(oe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Ns,onClose:U,children:[C(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(g,{variant:"h6",children:"Remarks"}),t(x,{sx:{width:"max-content"},onClick:U,children:t(ve,{})})]}),t(ne,{sx:{padding:".5rem 1rem"},children:t(re,{children:t(ae,{sx:{minWidth:400},children:t(fs,{sx:{height:"auto"},fullWidth:!0,children:t(ms,{sx:{backgroundColor:"#F5F5F5"},onChange:Le,value:Y,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),C(le,{sx:{display:"flex",justifyContent:"end"},children:[t(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:U,children:"Cancel"}),t(T,{className:"button_primary--normal",type:"save",onClick:no,variant:"contained",children:"Submit"})]})]}),C(oe,{open:$s,fullWidth:!0,onClose:ze,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[C(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(g,{variant:"h6",color:"red",children:"Errors"}),t(x,{sx:{width:"max-content"},onClick:ze,children:t(ve,{})})]}),t(ne,{sx:{padding:".5rem 1rem"},children:t(ye,{isLoading:$,width:"100%",rows:po,columns:Co,pageSize:10,getRowId:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),t(le,{sx:{display:"flex",justifyContent:"end"}})]}),t(yo,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+1},open:ks,children:t(bo,{color:"inherit"})}),Fs&&t(Mo,{openSnackBar:Ss,alertMsg:Se,handleSnackBarClose:zs}),t(So,{dialogState:Ds,openReusableDialog:k,closeReusableDialog:Be,dialogTitle:As,dialogMessage:Se,handleDialogConfirm:Be,dialogOkText:"OK",handleExtraButton:js,dialogSeverity:Ts}),C("div",{style:{...Ao,backgroundColor:"#FAFCFF"},children:[t(B,{container:!0,sx:To,children:C(B,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[C(B,{item:!0,md:11,sx:{display:"flex"},children:[t(B,{children:t(x,{color:"primary","aria-label":"upload picture",component:"label",sx:Fo,children:t(Do,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{ce(-1)}})})}),r.processDesc==="Mass Create"?C(B,{children:[t(g,{variant:"h3",children:t("strong",{children:"Create Multiple Profit Center"})}),t(g,{variant:"body2",color:"#777",children:"This view displays list of Profit Centers"})]}):(o==null?void 0:o.requestType)==="Mass Create"?C(B,{children:[t(g,{variant:"h3",children:t("strong",{children:"Create Multiple Profit Center"})}),t(g,{variant:"body2",color:"#777",children:"This view displays list of Profit Centers"})]}):(o==null?void 0:o.requestType)==="Mass Change"?C(B,{children:[t(g,{variant:"h3",children:t("strong",{children:"Change Multiple Profit Center"})}),t(g,{variant:"body2",color:"#777",children:"This view displays list of Profit Centers"})]}):(r==null?void 0:r.processDesc)==="Mass Change"?C(B,{children:[t(g,{variant:"h3",children:t("strong",{children:"Change Multiple Profit Center"})}),t(g,{variant:"body2",color:"#777",children:"This view displays list of Profit Centers"})]}):""]}),t(B,{item:!0,md:1,sx:{display:"flex"},children:t(Io,{title:"Uploaded documents",arrow:!0,children:t(x,{onClick:Gs,children:t(No,{})})})}),C(oe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Ps,onClose:$e,children:[t(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:(s==null?void 0:s.role)==="Finance"?t(j,{children:t(g,{variant:"h6",children:"Add Attachment"})}):""}),t(ne,{sx:{padding:".5rem 1rem"},children:C(vs,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[(s==null?void 0:s.role)==="Finance"?t(j,{children:t(re,{children:t(ae,{sx:{minWidth:400},children:t(Po,{title:"ProfitCenter",useMetaData:!1,artifactId:ge,artifactName:"ProfitCenter"})})})}):"",t(B,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:t(g,{variant:"h6",children:t("strong",{children:"Attachments"})})}),!!Ce.length&&t(ye,{width:"100%",rows:Ce,columns:Vs,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!Ce.length&&t(g,{variant:"body2",children:"No Attachments Found"}),t("br",{}),t(g,{variant:"h6",children:"Comments"}),!!pe.length&&t(wo,{sx:{[`& .${qo.root}:before`]:{flex:0,padding:0}},children:pe.map(e=>C(Eo,{children:[C(ko,{children:[t(Bo,{children:t($o,{sx:{color:"#757575"}})}),t(Lo,{})]}),t(Oo,{sx:{py:"12px",px:2},children:t(vs,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:t(ae,{sx:{padding:"1rem"},children:C(re,{spacing:1,children:[t(B,{sx:{display:"flex",justifyContent:"space-between"},children:t(g,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:K(e.createdAt).format("DD MMM YYYY")})}),t(g,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:e.user}),t(g,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:e.comment})]})})})})]}))}),!pe.length&&t(g,{variant:"body2",children:"No Comments Found"}),t("br",{})]})}),t(le,{children:t(T,{onClick:$e,children:"Close"})})]})]})}),t(B,{item:!0,sx:{position:"relative"},children:t(ye,{isLoading:$,width:"100%",title:"Mass Profit Center List ("+(L==null?void 0:L.length)+")",rows:L,columns:Qs,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:(s==null?void 0:s.role)==="Finance"&&r.processDesc==="Mass Create"||(s==null?void 0:s.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Create"||(s==null?void 0:s.role)==="Finance"&&r.processDesc==="Mass Change"||(s==null?void 0:s.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Change",disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Ks,callback_onRowSingleClick:e=>{const l=e.row.profitCenter,a=V.find(n=>n.profitCenter===l);console.log(a,"yo"),ce(`/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench/displayMultipleProfitCenterRequestBench/${l}`,{state:{rowData:e.row,requestNumber:o==null?void 0:o.requestId.slice(3),tabsData:a,requestbenchRowData:o}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})]}),_o(_s,"Profit Center","ChangePC")?t(zo,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:C(jo,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:bs,children:[(s==null?void 0:s.role)==="MDM Steward"&&r.processDesc==="Mass Create"||(s==null?void 0:s.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Create"?C(j,{children:[t(T,{variant:"outlined",size:"small",sx:{button_Outlined:ie,mr:1},onClick:ee,children:"Correction"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:H,children:"Submit For Approval"})]}):(s==null?void 0:s.role)==="Approver"&&r.processDesc==="Mass Create"||(s==null?void 0:s.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Create"?C(j,{children:[t(T,{variant:"outlined",size:"small",sx:{button_Outlined:ie,mr:1},onClick:ee,children:"Correction"}),t(T,{variant:"contained",size:"small",sx:{...G,mr:1},onClick:Oe,children:"Validate"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:H,disabled:Pe,children:"Approve"})]}):(s==null?void 0:s.role)==="Finance"&&r.processDesc==="Mass Create"||(s==null?void 0:s.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Create"?C(j,{children:[t(T,{variant:"contained",size:"small",sx:{...G,mr:1},onClick:_e,disabled:!ue,children:"Validate"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:H,disabled:Ie,children:"Submit For Review"})]}):"",(s==null?void 0:s.role)==="MDM Steward"&&r.processDesc==="Mass Change"||(s==null?void 0:s.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Change"?C(j,{children:[t(T,{variant:"outlined",size:"small",sx:{button_Outlined:ie,mr:1},onClick:ee,children:"Correction"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:H,children:"Submit For Approval"})]}):(s==null?void 0:s.role)==="Approver"&&r.processDesc==="Mass Change"||(s==null?void 0:s.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Change"?C(j,{children:[t(T,{variant:"outlined",size:"small",sx:{button_Outlined:ie,mr:1},onClick:ee,children:"Correction"}),t(T,{variant:"contained",size:"small",sx:{...G,mr:1},onClick:Oe,children:"Validate"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:H,disabled:Pe,children:"Approve"})]}):(s==null?void 0:s.role)==="Finance"&&r.processDesc==="Mass Change"||(s==null?void 0:s.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Change"?C(j,{children:[t(T,{variant:"contained",size:"small",sx:{...G,mr:1},onClick:_e,disabled:!ue,children:"Validate"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:H,disabled:Ie,children:"Submit For Review"})]}):""]})}):""]})})};export{Qo as default};
