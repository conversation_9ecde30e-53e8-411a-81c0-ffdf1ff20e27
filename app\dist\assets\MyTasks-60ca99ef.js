import{i as c,k as w,l as A,b as O,r as i,m as j,s as X,n as E,o as Z,p as $,q as I,t as P,j as B,a as T,v as G,w as x,x as Q,I as D,y as _,z as k,C as z,E as H,G as K,H as ee,J as ae}from"./index-fdfa25a0.js";import{c as se,u as oe,g as ie,W as re}from"./propData-e8199bd0.js";import{u as te}from"./useDisplayDataDto-69e1aee1.js";import"./index-3f2e0745.js";import"./DialogContentText-8ac052ae.js";import"./ListItemButton-db9eb0d0.js";import"./StepButton-fdbf0590.js";import"./ToggleButtonGroup-c02e6027.js";import"./makeStyles-1dfd4db4.js";import"./asyncToGenerator-88583e02.js";import"./dayjs.min-774e293a.js";import"./isBetween-fe8614a5.js";import"./useFinanceCostingRows-2aab0ea4.js";function Me(){let n=c(a=>a.userManagement.userData);const e=c(a=>a.userManagement.taskData),{customLog:l}=w(),h=c(a=>a.appSettings.language),p=c(a=>a.applicationConfig);let o=A();const S=O();i.useState({});const[U,ne]=i.useState(null),[M,u]=i.useState(!1),[g,y]=i.useState(""),[R,N]=i.useState(),[f,q]=i.useState(""),{getDisplayData:W}=te(),{showSnackbar:C}=j(),L={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},v={DateTimeFormat:{dateTimeFormat:"DD MMM YYYY||HH:mm",timeZone:"Asia/Calcutta"}};`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`;const V=a=>{const r={eventId:"TASK_FORWARDING",taskName:a==null?void 0:a.forwardedTasks.map(s=>s.taskDesc).join(","),requestId:a==null?void 0:a.forwardedTasks.map(s=>`${s.ATTRIBUTE_1}`).join(","),recipientGroup:a==null?void 0:a.recipientUsers.map(s=>s.ownerId).join(","),flowType:a==null?void 0:a.forwardedTasks.map(s=>s.ATTRIBUTE_2).join(",")},t=s=>{l(s)};x(`/${K}/mail/sendMail`,"post",t,r)},d=()=>{u(!0)},m=()=>{u(!1)},F=async a=>{var r;Q(ee.CURRENT_TASK,a);try{if((a==null?void 0:a.taskNature)==="Single-User"||(a==null?void 0:a.taskNature)!=="Single-User"&&(a==null?void 0:a.itmStatus)!=="Open"){if(o(I(a)),(a==null?void 0:a.processDisplayName)==="Material")if(!(a!=null&&a.ATTRIBUTE_1))C(D.FETCHING_REQUEST_ID,"info");else if(!(a!=null&&a.ATTRIBUTE_2))C(D.FETCHING_REQUEST_TYPE,"info");else{const t=await W(a==null?void 0:a.ATTRIBUTE_1,a==null?void 0:a.ATTRIBUTE_2,null,a,null);(t==null?void 0:t.statusCode)===_.STATUS_200&&S(`/requestBench/createRequest?RequestId=${(a==null?void 0:a.ATTRIBUTE_1)||(a==null?void 0:a.requestId)}&RequestType=${(a==null?void 0:a.ATTRIBUTE_2)||k(a==null?void 0:a.requestId)}`)}o(z({url:window.location.pathname,module:"ITMWorkbench"}))}else N("Kindly claim the task before proceeding"),y("Claim Task"),q("info"),d()}catch{l((r=H)==null?void 0:r.ERROR_SET_ROLE)}},Y=()=>{console.log("fetchFilterView")},b=()=>{console.log("clearFilterView")},J=(a,r)=>{console.log("Success flag.",a),console.log("Task Payload.",r)};return i.useEffect(()=>{X({}),o(E()),o(Z([])),o($({})),o(I({})),o(P([]))},[]),B("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:[T(re,{token:"********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T5zyx6YkX2_q4xFxCHj6mTXXr8A3aXv_lqWTxWBWx_dRNdQIwNdfoBdcM24Plzyz8tJ2Dpc7PM0wZmUi1BCF8mYCkh4lQgjnidQbZKBGpkSjYIWhyc1KgBHmsXddvXHm28KrW-UJblK3lEfPc5BWrYrefOhJQJxorOYtcdnjFfqduLHVA4tzxcr8rgF0uzRofKqx-8aNbuMRqIDAyli_yYN7AVwZYgGLi7ijlj7pnanAJDSqD7DM2RxsevRL2cARfOKYhTBO3fT7Km8IqrbqFMeprYHIPA8qejoxjstkx8j3Ej3U64mI2Z1AzEtKj16k3TIAen1xWp2Xt7z1dOBwFg",configData:se,destinationData:L,userData:{...n,user_id:n==null?void 0:n.emailId},userPreferences:v,userPermissions:oe,userList:{},groupList:{},languageTranslationData:ie(h),userListBySystem:U,useWorkAccess:p.environment==="localhost",useConfigServerDestination:p.environment==="localhost",inboxTypeKey:"MY_TASKS",workspaceLabel:"Open Tasks",workspaceFiltersByAPIDriven:!1,subInboxTypeKey:null,cachingBaseUrl:G,onTaskClick:F,onActionComplete:J,selectedFilterView:null,isFilterView:!1,fetchFilterViewList:Y,savedFilterViewData:[],clearFilterView:b,filterViewList:[],selectedTabId:null,forwardTaskData:V,userProcess:[]}),T(ae,{dialogState:M,openReusableDialog:d,closeReusableDialog:m,dialogTitle:g,dialogMessage:R,handleDialogConfirm:m,dialogOkText:"OK",dialogSeverity:f})]})}export{Me as default};
