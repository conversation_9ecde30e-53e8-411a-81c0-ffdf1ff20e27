import{b as ye,r as s,j as y,a as e,kI as Ce,i7 as xe,hx as ke,$ as M,g as Y,A as J,T as N,f as Q,gM as Fe,hE as De,a1 as ve,P as fe,w as X,hK as Z,sz as Me,l as Ae,F as me,hl as je,aB as qe,hp as We,hq as Ie,hu as we,h as pe,W as be,hv as Se,hw as He,sA as Le,aa as Ve,ab as _e,B as ze,sB as Ke}from"./index-fdfa25a0.js";import{R as Ee}from"./ReusableFieldCatalog-d0853fd5.js";const $e=({})=>{const h=ye();s.useState("");const[C,c]=s.useState(null),[F,D]=s.useState([]),[u,E]=s.useState({}),[Be,ee]=s.useState(null),[T,A]=s.useState({}),[se,te]=s.useState({}),[x,j]=s.useState({}),[ae,q]=s.useState(!1),[ie,oe]=s.useState(!1),[le,W]=s.useState(!1),[I,w]=s.useState(""),[ne,H]=s.useState(!1),[Re,L]=s.useState(!1),[Oe,V]=s.useState(!0),[ce,_]=s.useState(!1),[Ne,B]=s.useState(!1),z=()=>{h("/masterDataCockpit/costCenter")},de=()=>{h("/masterDataCockpit/costCenter")},re=()=>{setOpen(!1)},ge=()=>{q(!1),h("/masterDataCockpit/costCenter")},P=()=>{q(!0)},U=()=>{oe(!0)},he=()=>{const r=p=>{const o=[],v=[];Object.keys(p.body).map(t=>{const g=p.body[t];Object.keys(g).map(a=>{const n=p.body[t][a];if(Array.isArray(n)){let b={heading:a,fields:n.map(l=>l.fieldName),viewName:t,fieldVisibility:n.map(l=>({fieldName:l.fieldName,visibility:l.visibility}))};o.push(b),console.log(o,"hello"),n.forEach(l=>{console.log("Field Name:",l.fieldName),console.log("Is Required:",l.Required),l.Required==="true"&&v.push(l.fieldName)})}})}),D(o),console.log("Required Fields:",v);const k={},O={},i={};o.forEach(t=>{const{heading:g,fields:a,viewName:n,fieldVisibility:b}=t;k[n]||(k[n]={heading:n,subheadings:[]}),k[n].subheadings.push({heading:g,fields:a}),b.forEach(l=>{let R=l.visibility==="Required"?"Mandatory":l.visibility==="Hidden"?"Hide":l.visibility==="0"?"0":"Optional";O[l.fieldName]=R,l.visibility==="0"&&(i[l.fieldName]=!0)})}),E(k),j(O),te(i),A(i),console.log(k,"Fieldset")},f=p=>{console.log(p)};X(`/${Z}/data/getFieldCatalogueDetails?screenName=Change`,"get",r,f)};s.useEffect(()=>{he()},[]);const G=()=>{console.log("helloooo");let r={};Object.keys(u).forEach(o=>{u[o].subheadings.forEach(k=>{const{heading:O,fields:i}=k;i.forEach(t=>{if(x[t]!=="0"&&T[t]){const g=x[t]==="Mandatory"?"Required":x[t]==="Hide"?"Hidden":"Optional";r[o]||(r[o]=[]),r[o].some(n=>n.fieldName===t)||r[o].push({fieldName:t,cardName:O,viewName:o,visibility:g,screenName:"Change"})}})})});const f=o=>{console.log(o,"example"),B(),o.statusCode===200?(console.log("success"),W("Submit"),w("Field Catalog has been submitted successfully"),H("success"),V(!1),_(!0),P(),L(!0),B(!1)):(W("Submit"),_(!1),w("Submission Failed"),H("danger"),V(!1),L(!0),U(),B(!1)),re()},p=o=>{console.log(o)};Object.keys(r).forEach(o=>{const v=r[o];v.length>0?X(`/${Z}/alter/changeVisibility`,"post",f,p,v):console.log(`No payload data to send for viewName: ${o}`)}),dispatch(Me())};return y("div",{children:[e(Ce,{dialogState:ie,openReusableDialog:U,closeReusableDialog:z,dialogTitle:le,dialogMessage:I,handleDialogConfirm:z,dialogOkText:"OK",handleExtraButton:de,dialogSeverity:ne}),ce&&e(xe,{openSnackBar:ae,alertMsg:I,handleSnackBarClose:ge}),e(M,{container:!0,sx:ke,children:e(M,{item:!0,md:12,children:Object.keys(u).map(r=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(J,{sx:{backgroundColor:"#f5f5f5"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important"},children:r})}),e(Q,{children:u[r].subheadings.map((f,p)=>y(Y,{sx:{mb:2},children:[e(J,{expandIcon:e(Fe,{}),sx:{backgroundColor:"#F1F0FF"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:f.heading})}),e(Q,{children:e("div",{sx:{fontSize:"25px"},children:e(Ee,{fields:f.fields,heading:f.heading,childCheckedStates:T,setChildCheckedStates:A,childRadioValues:x,setChildRadioValues:j,onSubmitButtonClick:()=>G(),mandatoryFields:F,DisabledChildCheck:se})})})]},p))})]},r))})}),e(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(De,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(r,f)=>{ee(F[f]),c(f)},children:e(ve,{size:"small",variant:"contained",onClick:G,children:"Submit"})})})]})},Pe=()=>{const h=document.getElementsByTagName("HTML")[0],C=document.getElementsByTagName("BODY")[0];let c=h.clientWidth,F=C.clientWidth;const D=document.getElementById("e-invoice-export"),u=D.scrollWidth-D.clientWidth;u>D.clientWidth&&(c+=u,F+=u),h.style.width=c+"px",C.style.width=F+"px",Ke(D).then(E=>E.toDataURL("image/png",1)).then(E=>{Ue(E,"FieldCatalog.png"),h.style.width=null,C.style.width=null})},Ue=(h,C)=>{const c=window.document.createElement("a");c.href=h,c.download=C,(document.body||document.documentElement).appendChild(c),typeof c.click=="function"?c.click():(c.target="_blank",c.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),URL.revokeObjectURL(c.href),c.remove()},Je=({})=>{const h=ye();s.useState("");const[C,c]=s.useState(null),[F,D]=s.useState([]),[u,E]=s.useState({}),[Be,ee]=s.useState(null),[T,A]=s.useState({}),[se,te]=s.useState({}),[x,j]=s.useState({}),[ae,q]=s.useState(!1),[ie,oe]=s.useState(!1),[le,W]=s.useState(!1),[I,w]=s.useState(""),[ne,H]=s.useState(!1),[Re,L]=s.useState(!1),[Oe,V]=s.useState(!0),[ce,_]=s.useState(!1),[Ne,B]=s.useState(!1),[z,de]=s.useState(0),re=["For Create","For Change"],ge=Ae(),P=()=>{h("/masterDataCockpit/costCenter")},U=()=>{h("/masterDataCockpit/costCenter")},he=()=>{setOpen(!1)},G=()=>{q(!1),h("/masterDataCockpit/costCenter")},r=()=>{q(!0)},f=()=>{oe(!0)},p=()=>{const i=g=>{const a=[],n=[];Object.keys(g.body).map(m=>{const K=g.body[m];Object.keys(K).map($=>{const S=g.body[m][$];if(Array.isArray(S)){let ue={heading:$,fields:S.map(d=>d.fieldName),viewName:m,fieldVisibility:S.map(d=>({fieldName:d.fieldName,visibility:d.visibility}))};a.push(ue),console.log(a,"hello"),S.forEach(d=>{console.log("Field Name:",d.fieldName),console.log("Is Required:",d.Required),d.Required==="true"&&n.push(d.fieldName)})}})}),D(a),console.log("Required Fields:",n);const b={},l={},R={};a.forEach(m=>{const{heading:K,fields:$,viewName:S,fieldVisibility:ue}=m;b[S]||(b[S]={heading:S,subheadings:[]}),b[S].subheadings.push({heading:K,fields:$}),ue.forEach(d=>{let Te=d.visibility==="Required"?"Mandatory":d.visibility==="Hidden"?"Hide":d.visibility==="0"?"0":"Optional";l[d.fieldName]=Te,d.visibility==="0"&&(R[d.fieldName]=!0)})}),E(b),j(l),te(R),A(R),console.log(b,"Fieldset")},t=g=>{console.log(g)};X(`/${Z}/data/getFieldCatalogueDetails?screenName=Create`,"get",i,t)};s.useEffect(()=>{p()},[]);const o=()=>{console.log("Clicked");let i={};Object.keys(u).forEach(a=>{u[a].subheadings.forEach(b=>{const{heading:l,fields:R}=b;R.forEach(m=>{if(x[m]!=="0"&&T[m]){const K=x[m]==="Mandatory"?"Required":x[m]==="Hide"?"Hidden":"Optional";i[a]||(i[a]=[]),i[a].some(S=>S.fieldName===m)||i[a].push({fieldName:m,cardName:l,viewName:a,visibility:K,screenName:"Create"})}})})});const t=a=>{console.log(a,"example"),B(),a.statusCode===200?(console.log("success"),W("Submit"),w("Field Catalog has been submitted successfully"),H("success"),V(!1),_(!0),r(),L(!0),B(!1)):(W("Submit"),_(!1),w("Submission Failed"),H("danger"),V(!1),L(!0),f(),B(!1)),he()},g=a=>{console.log(a)};Object.keys(i).forEach(a=>{const n=i[a];n.length>0?X(`/${Z}/alter/changeVisibility`,"post",t,g,n):console.log(`No payload data to send for viewName: ${a}`)}),ge(Me())},v=[[e(me,{children:y(M,{container:!0,sx:ke,children:[e(M,{item:!0,md:12,children:Object.keys(u).map(i=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(J,{sx:{backgroundColor:"#f5f5f5"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important"},children:i})}),e(Q,{children:u[i].subheadings.map((t,g)=>y(Y,{sx:{mb:2},children:[e(J,{expandIcon:e(Fe,{}),sx:{backgroundColor:"#F1F0FF"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:t.heading})}),e(Q,{children:e("div",{sx:{fontSize:"25px"},children:e(Ee,{fields:t.fields,heading:t.heading,childCheckedStates:T,setChildCheckedStates:A,childRadioValues:x,setChildRadioValues:j,onSubmitButtonClick:()=>o(),mandatoryFields:F,DisabledChildCheck:se})})})]},g))})]},i))}),e(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(De,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(i,t)=>{ee(F[t]),c(t)},children:e(ve,{size:"small",variant:"contained",onClick:o,children:"Submit"})})})]})})],[e(me,{children:e($e,{})})]],k=(i,t)=>{de(t)};s.useState(""),s.useState([]);function O(){}return y("div",{children:[e(Ce,{dialogState:ie,openReusableDialog:f,closeReusableDialog:P,dialogTitle:le,dialogMessage:I,handleDialogConfirm:P,dialogOkText:"OK",handleExtraButton:U,dialogSeverity:ne}),ce&&e(xe,{openSnackBar:ae,alertMsg:I,handleSnackBarClose:G}),e("div",{style:{...je,backgroundColor:"#FAFCFF"},children:y(qe,{spacing:1,children:[y(M,{container:!0,sx:We,children:[y(M,{item:!0,md:5,sx:Ie,children:[e(N,{variant:"h3",children:e("strong",{children:"Field Configurations"})}),e(N,{variant:"body2",color:"#777",children:"This view displays the setiings for configuring the Fields"})]}),e(M,{item:!0,md:7,sx:{display:"flex"},children:y(M,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[e(we,{title:"Search for fields in different views",module:"FieldSelection",keyName:"string",message:"Search for fields in different views"}),e(pe,{title:"Reload",children:e(be,{sx:Se,children:e(He,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:O})})}),e(pe,{title:"Export",children:e(be,{sx:Se,children:e(Le,{onClick:Pe})})})]})})]}),e(fe,{children:e(Ve,{value:z,onChange:k,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:re.map((i,t)=>e(_e,{sx:{fontSize:"12px",fontWeight:"700"},label:i},t))})}),v[z].map((i,t)=>e(ze,{children:i},t))]})})]})};export{Je as default};
