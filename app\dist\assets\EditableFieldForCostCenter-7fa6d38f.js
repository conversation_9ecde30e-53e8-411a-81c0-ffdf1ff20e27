import{r as p,i as D,l as _,ir as G,a as o,$ as R,j as f,T as A,hA as P,g_ as z,hy as J,a3 as O,gR as K,gS as M,hJ as Q,F as X,is as S,w as U,hK as B,hP as H}from"./index-fdfa25a0.js";function Y(a,d){return Array.isArray(d)&&d.find(s=>s.code===a)||""}const V=({label:a,value:d,units:k,data:s,onSave:Z,isEditMode:x,visibility:t,length:I,type:g,taskRequestId:L})=>{var F,q;const[m,C]=p.useState(d),[b,T]=p.useState(!1),[$,E]=p.useState(!1),u=D(e=>e.AllDropDown.dropDown),h=_();Y(m,u),D(e=>e.edit.payload);let y=D(e=>e.userManagement.taskData);const v=y==null?void 0:y.subject;console.log("editField",a,m);const j={label:a,value:m,units:k,type:g,visibility:t};console.log("fieldData",s);let c=a.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");p.useEffect(()=>{C(d),console.log("mobile",a,m)},[d]),p.useEffect(()=>{(t==="0"||t==="Required")&&h(G(c))},[]);const i=e=>{h(S({keyname:c.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:e}))},N=e=>{console.log("compcode",e);const r=l=>{console.log("value",l),h(S({keyname:"Currency",data:""})),h(H({keyName:"Currency",data:l.body}))},n=l=>{console.log(l,"error in dojax")};U(`/${B}/data/getCurrency?companyCode=${e==null?void 0:e.code}`,"get",r,n)},W=e=>{console.log("countryyyyy",e);const r=l=>{console.log("value",l),h(S({keyname:"Region",data:""})),h(H({keyName:"Region",data:l.body}))},n=l=>{console.log(l,"error in dojax")};U(`/${B}/data/getRegionBasedOnCountry?country=${e==null?void 0:e.code}`,"get",r,n)};return console.log("dropDownData[key]",u[c]),o(X,{children:x?t==="Hidden"?null:o(R,{item:!0,children:o(z,{children:x?f("div",{children:[f(A,{variant:"body2",color:"#777",children:[a,t==="Required"||t==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),g==="Drop Down"?o(J,{options:u[c]??[],value:s[c]&&((F=u[c])==null?void 0:F.filter(e=>e.code===s[c]))&&((q=u[c])==null?void 0:q.filter(e=>e.code===s[c])[0])||"",onChange:(e,r,n)=>{console.log("reason",n),console.log(t,r,"visibility2"),(t==="Required"||t==="0")&&r===null&&E(!0),a==="Comp Code"&&N(r),a==="Country/Reg"&&W(r),i(n==="clear"?"":r==null?void 0:r.code),C(r.code),T(!0)},getOptionLabel:e=>(console.log("optionn",e),e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??""),renderOption:(e,r)=>(console.log("option vakue",r),o("li",{...e,children:o(A,{style:{fontSize:12},children:`${r==null?void 0:r.code} - ${r==null?void 0:r.desc}`})})),renderInput:e=>o(O,{...e,variant:"outlined",placeholder:`Select ${j.label}`,size:"small",error:$})}):g==="Input"?o(O,{variant:"outlined",size:"small",fullWidth:!0,value:s[c].toUpperCase(),placeholder:`Enter ${j.label}`,inputProps:{maxLength:I},onChange:e=>{const r=e.target.value;if(r.length>0&&r[0]===" ")i(r.trimStart());else{let n=r.toUpperCase();i(n)}(t==="Required"||t==="0")&&r.length<=0&&E(!0),C(r.toUpperCase())},error:$}):g==="Calendar"?o(K,{dateAdapter:M,children:o(Q,{slotProps:{textField:{size:"small"}},placeholder:"Select Date Range"})}):g==="Radio Button"?o(R,{item:!0,md:2,children:o(P,{sx:{padding:0},checked:s[c],onChange:(e,r)=>{i(r),C(r)}})}):""]}):""})}):L&&t==="Hidden"||v&&t==="Hidden"?null:o(R,{item:!0,children:f(z,{children:[f(A,{variant:"body2",color:"#777",children:[a,t==="Required"||t==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),f(A,{variant:"body2",fontWeight:"bold",children:[s[c],g==="Radio Button"?o(P,{sx:{padding:0},checked:s[c],disabled:!0}):""]})]})})})};export{V as E};
