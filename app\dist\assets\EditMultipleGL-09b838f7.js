import{b as ue,l as he,r as u,u as ge,i as v,ig as me,j as s,$ as d,hp as fe,a as t,i7 as ye,T as g,ia as xe,B as E,g_ as M,id as be,ie as Ce,ic as ve,h_ as Se,gZ as Ae,hE as U,a1 as f,gW as A,P as Z,w as y,io as x,ip as B,hn as Ne,W as Ee,hv as Be,i8 as Ge,gU as K,i9 as Te,ib as Fe,hP as b}from"./index-fdfa25a0.js";import{E as je}from"./EditFieldForMassGL-4415ec49.js";const Le=()=>{var H,I;const O=ue(),p=he();u.useState({});const[$,L]=u.useState(0),[C,Q]=u.useState(!1),[Me,X]=u.useState(!0);u.useState(0),u.useState([]);const[h,N]=u.useState(0),[Y,_]=u.useState(!1),[V,R]=u.useState([]),[D,W]=u.useState(!1),m=ge();v(e=>e.profitCenter.profitCenterCompCodes),console.log("location",m.state),v(e=>e.initialData.EditMultipleMaterial);let o=v(e=>{var i;return(i=e==null?void 0:e.initialData)==null?void 0:i.IWMMyTask}),c=m.state.rowViewData;const z=m.state.rowViewData.viewData,r=m.state.selectedRow;console.log("generalLedgerRowDataaaaa",r);const ee=m.state.requestNumber;let G=v(e=>e.edit.payload);console.log(G,"singleGLPayloadAfterChange");let T=v(e=>e.generalLedger.requiredFields);console.log(T,"required_field_for_data"),v(e=>e.payload);const te=()=>{var n,a;const e=l=>{p(b({keyName:"TaxCategory",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getTaxCategory?companyCode=${(n=o==null?void 0:o.body)!=null&&n.CompCode?(a=o==null?void 0:o.body)==null?void 0:a.CompCode:c==null?void 0:c.compCode}`,"get",e,i)},oe=()=>{var n,a;const e=l=>{p(b({keyName:"HouseBank",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getHouseBank?companyCode=${(n=o==null?void 0:o.body)!=null&&n.CompCode?(a=o==null?void 0:o.body)==null?void 0:a.CompCode:c==null?void 0:c.CompCode}`,"get",e,i)},ne=()=>{var n,a;const e=l=>{p(b({keyName:"FieldStatusGroup",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getFieldStatusGroup?companyCode=${(n=o==null?void 0:o.body)!=null&&n.CompCode?(a=o==null?void 0:o.body)==null?void 0:a.CompCode:c==null?void 0:c.CompCode}`,"get",e,i)},ie=()=>{var n,a;const e=l=>{p(b({keyName:"GroupAccountNumber",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getGroupAccountNumber?chartAccount=${(n=o==null?void 0:o.body)!=null&&n.chartOfAccount?(a=o==null?void 0:o.body)==null?void 0:a.chartOfAccount:c==null?void 0:c.ChartOfAccount}`,"get",e,i)},le=()=>{var n,a;const e=l=>{p(b({keyName:"AlternativeAccountNumber",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getAlternativeAccountNumber?chartAccount=${(n=o==null?void 0:o.body)!=null&&n.chartOfAccount?(a=o==null?void 0:o.body)==null?void 0:a.chartOfAccount:c==null?void 0:c.ChartOfAccount}`,"get",e,i)},ae=()=>{var n,a;const e=l=>{p(b({keyName:"AccountGroup",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getAccountGroupCodeDesc?chartAccount=${(n=o==null?void 0:o.body)!=null&&n.chartOfAccount?(a=o==null?void 0:o.body)==null?void 0:a.chartOfAccount:c==null?void 0:c.ChartOfAccount}`,"get",e,i)},re=()=>{var n,a;const e=l=>{p(b({keyName:"CostElementCategory",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getCostElementCategory?accountType=${(n=o==null?void 0:o.body)!=null&&n.accountType?(a=o==null?void 0:o.body)==null?void 0:a.accountType:r==null?void 0:r.accountType}`,"get",e,i)};u.useEffect(()=>{te(),ne(),oe(),ae(),le(),ie(),re()},[]),u.useEffect(()=>{p(me(j))},[]);const ce=()=>{Q(!0),X(!1)},k=()=>{const e=q();C?e?(N(i=>i-1),p(B())):P():(N(i=>i-1),p(B()))},w=()=>{const e=q();C?e?(N(i=>i+1),p(B())):P():(N(i=>i+1),p(B()))},P=()=>{W(!0)},se=e=>{_(e)},de=()=>{_(!0)},S=Object.entries(z).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]);console.log("tabsArray",S,h);const F=Object.entries(z).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),j={};F.map(e=>{e.forEach((i,n)=>{i.forEach((a,l)=>{l!==0&&a.forEach(J=>{j[J.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=J.value})})})}),console.log(JSON.stringify(j),"temphash"),console.log(G,T,"========deeced"),console.log(h,"activeStep");const q=()=>Ne(G,T,R),pe=()=>{W(!1)};return s("div",{children:[s(d,{container:!0,style:{...fe,backgroundColor:"#FAFCFF"},children:[V.length!=0&&t(ye,{openSnackBar:D,alertMsg:"Please fill the following Field: "+V.join(", "),handleSnackBarClose:pe}),s(d,{sx:{width:"inherit"},children:[s(d,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[t(d,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:t(Ee,{color:"primary","aria-label":"upload picture",component:"label",sx:Be,children:t(Ge,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{O("/masterDataCockpit/generalLedger/createMultipleGL")}})})}),s(d,{md:8,children:[t(g,{variant:"h3",children:s("strong",{children:["Multiple General Ledgers: ",r==null?void 0:r.glAccount," "]})}),t(g,{variant:"body2",color:"#777",children:"This view displays the detail of Multiple General Ledgers"})]}),(H=m==null?void 0:m.state)!=null&&H.requestNumber?t(d,{md:.5,sx:{display:"flex",justifyContent:"flex-end"},children:t(f,{variant:"outlined",size:"small",sx:K,onClick:de,title:"Change Log",children:t(Te,{sx:{padding:"2px"},fontSize:"small"})})}):t(d,{md:.5,sx:{display:"flex",justifyContent:"flex-end"}}),Y&&t(xe,{open:!0,closeModal:se,requestId:ee,requestType:"Mass",pageName:"generalLedger",controllingArea:r.companyCode,centerName:r.glAccount}),C?"":t(d,{md:4,sx:{display:"flex",justifyContent:"flex-end"},children:t(d,{item:!0,children:s(f,{variant:"outlined",size:"small",sx:K,onClick:ce,children:["Change",t(Fe,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),t(d,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:s(E,{width:"70%",sx:{marginLeft:"40px"},children:[t(d,{item:!0,sx:{paddingTop:"2px !important"},children:s(M,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(g,{variant:"body2",color:"#777",children:"General Ledger Account"})}),s(g,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",r==null?void 0:r.glAccount]})]})}),t(d,{item:!0,sx:{paddingTop:"2px !important"},children:s(M,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(g,{variant:"body2",color:"#777",children:"Company Code"})}),s(g,{variant:"body2",fontWeight:"bold",children:[": ",r==null?void 0:r.companyCode]})]})}),t(d,{item:!0,sx:{paddingTop:"2px !important"},children:s(M,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(g,{variant:"body2",color:"#777",children:"Chart of Account"})}),s(g,{variant:"body2",fontWeight:"bold",children:[": ",r==null?void 0:r.chartOfAccount]})]})})]})}),s(d,{container:!0,style:{padding:"16px"},children:[t(ve,{activeStep:h,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:S.map((e,i)=>t(be,{children:t(Ce,{sx:{fontWeight:"700"},children:e})},e))}),t(d,{container:!0,children:F&&((I=F[h])==null?void 0:I.map((e,i)=>t(E,{sx:{width:"100%"},children:s(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Se},children:[t(d,{container:!0,children:t(g,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),t(E,{children:t(E,{sx:{width:"100%"},children:t(Ae,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(n=>(console.log("inneritem",n),t(je,{activeTabIndex:h,fieldGroup:e[0],selectedRowData:r==null?void 0:r.glAccount,pcTabs:S,label:n.fieldName,value:n.value,length:n.maxLength,visibility:n.visibility,onSave:a=>handleFieldSave(n.fieldName,a),isEditMode:C,type:n.fieldType,field:n})))})})})})]})},i)))})]})]})]}),C?t(Z,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:$,onChange:e=>{L(e)},children:[t(f,{size:"small",sx:{...A,mr:1},variant:"contained",onClick:()=>{O("/masterDataCockpit/generalLedger/createMultipleGL")},children:"Save"}),t(f,{variant:"contained",size:"small",sx:{...A,mr:1},onClick:k,disabled:h===0,children:"Back"}),t(f,{variant:"contained",size:"small",sx:{...A,mr:1},onClick:w,disabled:h===S.length-1,children:"Next"})]})}):"",C?"":t(Z,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(U,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:$,onChange:e=>{L(e)},children:[t(f,{variant:"contained",size:"small",sx:{...A,mr:1},onClick:k,disabled:h===0,children:"Back"}),t(f,{variant:"contained",size:"small",sx:{...A,mr:1},onClick:w,disabled:h===S.length-1,children:"Next"})]})})]})};export{Le as default};
