import{b as ye,r as s,l as Ce,j as y,a as e,kI as xe,i7 as Fe,hx as ke,$ as E,g as Y,A as J,T as O,f as Q,gM as De,hE as ve,a1 as Me,P as fe,w as X,io as Z,sz as Ee,F as me,hl as je,aB as qe,hp as Le,hq as We,hu as Ie,h as pe,W as be,hv as Se,hw as we,sA as He,aa as Ve,ab as _e,B as ze,sB as $e}from"./index-fdfa25a0.js";import{R as Be}from"./ReusableFieldCatalog-d0853fd5.js";const Ge=({})=>{const u=ye();s.useState("");const[C,d]=s.useState(null),[k,D]=s.useState([]),[f,B]=s.useState({}),[Re,ee]=s.useState(null),[N,T]=s.useState({}),[se,te]=s.useState({}),[x,A]=s.useState({}),[ae,j]=s.useState(!1),[ie,oe]=s.useState(!1),[le,q]=s.useState(!1),[L,W]=s.useState(""),[ne,I]=s.useState(!1),[Oe,w]=s.useState(!1),[Ne,H]=s.useState(!0),[ce,V]=s.useState(!1),[Te,R]=s.useState(!1),$=Ce(),G=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},de=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},re=()=>{setOpen(!1)},K=()=>{j(!1),u("/masterDataCockpit/generalLedger")},ge=()=>{j(!0)},P=()=>{oe(!0)},he=()=>{const r=p=>{const n=[],v=[];Object.keys(p.body).map(o=>{const t=p.body[o];Object.keys(t).map(b=>{const c=p.body[o][b];if(Array.isArray(c)){let M={heading:b,fields:c.map(l=>l.fieldName),viewName:o,fieldVisibility:c.map(l=>({fieldName:l.fieldName,visibility:l.visibility}))};n.push(M),console.log(n,"hello"),c.forEach(l=>{console.log("Field Name:",l.fieldName),console.log("Is Required:",l.Required),l.Required==="true"&&v.push(l.fieldName)})}})}),D(n),console.log("Required Fields:",v);const F={},a={},i={};n.forEach(o=>{const{heading:t,fields:b,viewName:c,fieldVisibility:M}=o;F[c]||(F[c]={heading:c,subheadings:[]}),F[c].subheadings.push({heading:t,fields:b}),M.forEach(l=>{let h=l.visibility==="Required"?"Mandatory":l.visibility==="Hidden"?"Hide":l.visibility==="0"?"0":"Optional";a[l.fieldName]=h,l.visibility==="0"&&(i[l.fieldName]=!0)})}),B(F),A(a),te(i),T(i),console.log(F,"Fieldset")},m=p=>{console.log(p)};X(`/${Z}/data/getFieldCatalogueDetails?screenName=Change`,"get",r,m)};s.useEffect(()=>{he()},[]);const U=()=>{console.log("helloooo");let r={};Object.keys(f).forEach(n=>{f[n].subheadings.forEach(F=>{const{heading:a,fields:i}=F;i.forEach(o=>{if(x[o]!=="0"&&N[o]){const t=x[o]==="Mandatory"?"Required":x[o]==="Hide"?"Hidden":"Optional";r[n]||(r[n]=[]),r[n].some(c=>c.fieldName===o)||r[n].push({fieldName:o,cardName:a,viewName:n,visibility:t,screenName:"Change"})}})})});const m=n=>{console.log(n,"example"),R(),n.statusCode===200?(console.log("success"),q("Submit"),W("Field Catalog has been submitted successfully"),I("success"),H(!1),V(!0),ge(),w(!0),R(!1)):(q("Submit"),V(!1),W("Submission Failed"),I("danger"),H(!1),w(!0),P(),R(!1)),re()},p=n=>{console.log(n)};Object.keys(r).forEach(n=>{const v=r[n];v.length>0?X(`/${Z}/alter/changeVisibility`,"post",m,p,v):console.log(`No payload data to send for viewName: ${n}`)}),$(Ee())};return y("div",{children:[e(xe,{dialogState:ie,openReusableDialog:P,closeReusableDialog:G,dialogTitle:le,dialogMessage:L,handleDialogConfirm:G,dialogOkText:"OK",handleExtraButton:de,dialogSeverity:ne}),ce&&e(Fe,{openSnackBar:ae,alertMsg:L,handleSnackBarClose:K}),e(E,{container:!0,sx:ke,children:e(E,{item:!0,md:12,children:Object.keys(f).map(r=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(J,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:r})}),e(Q,{children:f[r].subheadings.map((m,p)=>y(Y,{sx:{mb:2},children:[e(J,{expandIcon:e(De,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:m.heading})}),e(Q,{children:e("div",{sx:{fontSize:"25px"},children:e(Be,{fields:m.fields,heading:m.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:A,onSubmitButtonClick:()=>U(),mandatoryFields:k,DisabledChildCheck:se})})})]},p))})]},r))})}),e(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(ve,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(r,m)=>{ee(k[m]),d(m)},children:e(Me,{size:"small",variant:"contained",onClick:U,children:"Submit"})})})]})},Ke=()=>{const u=document.getElementsByTagName("HTML")[0],C=document.getElementsByTagName("BODY")[0];let d=u.clientWidth,k=C.clientWidth;const D=document.getElementById("e-invoice-export"),f=D.scrollWidth-D.clientWidth;f>D.clientWidth&&(d+=f,k+=f),u.style.width=d+"px",C.style.width=k+"px",$e(D).then(B=>B.toDataURL("image/png",1)).then(B=>{Pe(B,"FieldCatalog.png"),u.style.width=null,C.style.width=null})},Pe=(u,C)=>{const d=window.document.createElement("a");d.href=u,d.download=C,(document.body||document.documentElement).appendChild(d),typeof d.click=="function"?d.click():(d.target="_blank",d.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),URL.revokeObjectURL(d.href),d.remove()},Je=({})=>{const u=ye();s.useState("");const[C,d]=s.useState(null),[k,D]=s.useState([]),[f,B]=s.useState({}),[Re,ee]=s.useState(null),[N,T]=s.useState({}),[se,te]=s.useState({}),[x,A]=s.useState({}),[ae,j]=s.useState(!1),[ie,oe]=s.useState(!1),[le,q]=s.useState(!1),[L,W]=s.useState(""),[ne,I]=s.useState(!1),[Oe,w]=s.useState(!1),[Ne,H]=s.useState(!0),[ce,V]=s.useState(!1),[Te,R]=s.useState(!1),[$,G]=s.useState(0),de=["For Create","For Change"],re=Ce(),K=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},ge=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},P=()=>{setOpen(!1)},he=()=>{j(!1),u("/masterDataCockpit/generalLedger")},U=()=>{j(!0)},r=()=>{oe(!0)},m=()=>{const a=o=>{const t=[],b=[];Object.keys(o.body).map(h=>{const _=o.body[h];Object.keys(_).map(z=>{const S=o.body[h][z];if(Array.isArray(S)){let ue={heading:z,fields:S.map(g=>g.fieldName),viewName:h,fieldVisibility:S.map(g=>({fieldName:g.fieldName,visibility:g.visibility}))};t.push(ue),console.log(t,"hello"),S.forEach(g=>{console.log("Field Name:",g.fieldName),console.log("Is Required:",g.Required),g.Required==="true"&&b.push(g.fieldName)})}})}),D(t),console.log("Required Fields:",b);const c={},M={},l={};t.forEach(h=>{const{heading:_,fields:z,viewName:S,fieldVisibility:ue}=h;c[S]||(c[S]={heading:S,subheadings:[]}),c[S].subheadings.push({heading:_,fields:z}),ue.forEach(g=>{let Ae=g.visibility==="Required"?"Mandatory":g.visibility==="Hidden"?"Hide":g.visibility==="0"?"0":"Optional";M[g.fieldName]=Ae,g.visibility==="0"&&(l[g.fieldName]=!0)})}),B(c),A(M),te(l),T(l),console.log(c,"Fieldset")},i=o=>{console.log(o)};X(`/${Z}/data/getFieldCatalogueDetails?screenName=Create`,"get",a,i)};s.useEffect(()=>{m()},[]);const p=()=>{let a={};console.log("update"),Object.keys(f).forEach(t=>{f[t].subheadings.forEach(c=>{const{heading:M,fields:l}=c;l.forEach(h=>{if(x[h]!=="0"&&N[h]){const _=x[h]==="Mandatory"?"Required":x[h]==="Hide"?"Hidden":"Optional";a[t]||(a[t]=[]),a[t].some(S=>S.fieldName===h)||a[t].push({fieldName:h,cardName:M,viewName:t,visibility:_,screenName:"Create"})}})})});const i=t=>{console.log(t,"example"),R(),t.statusCode===200?(console.log("success"),q("Submit"),W("Field Catalog has been submitted successfully"),I("success"),H(!1),V(!0),U(),w(!0),R(!1)):(q("Submit"),V(!1),W("Submission Failed"),I("danger"),H(!1),w(!0),r(),R(!1)),P()},o=t=>{console.log(t)};Object.keys(a).forEach(t=>{const b=a[t];b.length>0?X(`/${Z}/alter/changeVisibility`,"post",i,o,b):console.log(`No payload data to send for viewName: ${t}`)}),re(Ee())},n=[[e(me,{children:y(E,{container:!0,sx:ke,children:[e(E,{item:!0,md:12,children:Object.keys(f).map(a=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(J,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:a})}),e(Q,{children:f[a].subheadings.map((i,o)=>y(Y,{sx:{mb:2},children:[e(J,{expandIcon:e(De,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:i.heading})}),e(Q,{children:e("div",{sx:{fontSize:"25px"},children:e(Be,{fields:i.fields,heading:i.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:A,onSubmitButtonClick:()=>p(),mandatoryFields:k,DisabledChildCheck:se})})})]},o))})]},a))}),e(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(ve,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(a,i)=>{ee(k[i]),d(i)},children:e(Me,{size:"small",variant:"contained",onClick:p,children:"Submit"})})})]})})],[e(me,{children:e(Ge,{})})]],v=(a,i)=>{G(i)};s.useState(""),s.useState([]);function F(){}return y("div",{children:[e(xe,{dialogState:ie,openReusableDialog:r,closeReusableDialog:K,dialogTitle:le,dialogMessage:L,handleDialogConfirm:K,dialogOkText:"OK",handleExtraButton:ge,dialogSeverity:ne}),ce&&e(Fe,{openSnackBar:ae,alertMsg:L,handleSnackBarClose:he}),e("div",{style:{...je,backgroundColor:"#FAFCFF"},children:y(qe,{spacing:1,children:[y(E,{container:!0,sx:Le,children:[y(E,{item:!0,md:5,sx:We,children:[e(O,{variant:"h3",children:e("strong",{children:"Field Configurations"})}),e(O,{variant:"body2",color:"#777",children:"This view displays the setiings for configuring the Fields"})]}),e(E,{item:!0,md:7,sx:{display:"flex"},children:y(E,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[e(Ie,{title:"Search for fields in different views",module:"FieldSelection",keyName:"string",message:"Search for fields in different views"}),e(pe,{title:"Reload",children:e(be,{sx:Se,children:e(we,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:F})})}),e(pe,{title:"Export",children:e(be,{sx:Se,children:e(He,{onClick:Ke})})})]})})]}),e(fe,{children:e(Ve,{value:$,onChange:v,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:de.map((a,i)=>e(_e,{sx:{fontSize:"12px",fontWeight:"700"},label:a},i))})}),n[$].map((a,i)=>e(ze,{children:a},i))]})})]})};export{Je as default};
