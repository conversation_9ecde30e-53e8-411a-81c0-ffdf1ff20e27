import{r as l,i as D,l as Zr,b as el,cK as de,q as tl,ix as rl,w as m,a as r,ht as ll,j as o,J as al,hl as nl,aB as Me,$ as d,hp as sl,hq as ol,T as u,hu as il,h as yt,W as ue,hv as Pt,hw as cl,R as he,hx as dl,g as ul,A as hl,gM as fl,f as gl,gO as J,gQ as N,a3 as P,hy as E,a1 as b,gU as Cl,gW as ml,hB as pl,hC as Sl,hD as xl,P as fe,hE as yl,hF as ze,fI as Oe,hG as Te,hH as Ie,h4 as Le,fV as bt,h1 as vt,hI as At,fX as wt,fH as Nt,hi as Pl,F as bl,hL as C,gL as ge,gV as vl,hM as Al,hN as Fe,sK as Et,hP as I,sL as wl,sM as Nl,sN as El,sO as $l,sP as kl,sQ as Dl,sv as Ml,hW as $t}from"./index-fdfa25a0.js";import{A as zl}from"./AttachmentUploadDialog-cc0b6643.js";const _l=()=>{var mt,pt,St;l.useState(!1);const[Ol,kt]=l.useState(!1),[Dt,L]=l.useState("");D(e=>e.appSettings.Format);const g=Zr(),Q=el();l.useState(!1);const[Mt,A]=l.useState(!1),[zt,X]=l.useState(!1),[Ce,Ot]=l.useState(null),[F,Be]=l.useState([]);de.useState(""),l.useState(!1),l.useState("");const[Tl,Tt]=l.useState("");l.useState(!0);const[Il,Re]=l.useState(!1),[Ll,We]=l.useState(!0);l.useState([]),l.useState([]),l.useState([]),l.useState([]),l.useState(!0),l.useState([]),l.useState([]),l.useState(!1);const[Ge,Fl]=l.useState([]);l.useState([]);const[Ve,It]=l.useState({});l.useState([]),l.useState([]),l.useState(!1),l.useState([]);const[Lt,Bl]=l.useState([]);l.useState([]);const[Ft,Ue]=l.useState(!1),[Bt,_e]=l.useState(!1);l.useState(!0),l.useState("sm");const[Rt,Y]=l.useState(!1),[Rl,Ye]=l.useState(!1),[S,Z]=l.useState(""),[M,He]=l.useState(""),[me,ee]=l.useState(0),[qe,pe]=l.useState(10),[Ke,te]=l.useState(0);l.useState([]),l.useState(null),l.useState(null);const[Wt,je]=l.useState(0),[Gt,Je]=l.useState(0);l.useState(!1),l.useState(!1),l.useState(!1),l.useState(!1),l.useState(!1),l.useState(!1),l.useState(""),l.useState("");const[Vt,Qe]=l.useState(!1),[Ut,B]=l.useState(""),[_t,R]=l.useState(),[Xe,Yt]=l.useState(!1),[re,Ht]=l.useState(!1);l.useState(null);const H=de.useRef(null),[Se,xe]=l.useState(!1),[qt,Kt]=l.useState(0),[ye,Pe]=l.useState(!1),q=de.useRef(null),K=de.useRef(null),[$,be]=l.useState(""),[z,jt]=l.useState(""),[v,W]=l.useState(""),[Jt,Qt]=l.useState(0),[ve,Ae]=l.useState(!1),[O,Xt]=l.useState(""),[Zt,er]=l.useState(0),[Ze,tr]=l.useState([]),[rr,le]=l.useState(!1),[lr,ae]=l.useState(!1),[et,ne]=l.useState(!1),[tt,se]=l.useState(!1),[rt,ar]=l.useState([]),[Wl,nr]=l.useState(!0),we=["Create Multiple","Upload Template ","Download Template "],Ne=["Change Multiple","Upload Template ","Download Template "],Ee=["Create Single","With Copy","Without Copy"];console.log("newProfitCenterName",v);let sr=D(e=>{var t;return(t=e.userManagement.entitiesAndActivities)==null?void 0:t["Display Material"]}),G=D(e=>e.userManagement.userData);const[lt,at]=l.useState(!1),c=D(e=>e.commonFilter.ProfitCenter);console.log("newCompanyCode",$);const oe=D(e=>e.commonSearchBar.ProfitCenter),nt=D(e=>e.profitCenter.handleMassMode);console.log("formcontroller_SearchBar",oe);const h=D(e=>{var t;return(t=e==null?void 0:e.AllDropDown)==null?void 0:t.dropDown}),V=D(e=>e.AllDropDown.dropDown);console.log("dropDownData",h==null?void 0:h.ProfitCenter);const x={profitCenter:{newProfitCenter:M},companyCode:{newCompanyCode:$},companyCodeCopy:{newCompanyCodeCopy:z},profitCenterName:{newProfitCenterName:v},controllingArea:{newControllingArea:S},controllingAreaDataCopy:{newControllingAreaCopyFrom:O}};console.log("typepc",typeof x);const or=()=>{Ue(!0)},ir=()=>{_e(!0)},$e=()=>{_e(!1),ae(!1),se(!1),Z(""),be(""),W(""),He("")},st=e=>{console.log("first",Ce);const t=a=>{g(I({keyName:"CompCodeBasedOnControllingArea",data:a.body.map((s,i)=>({id:i,companyCodes:s.code,companyName:s.desc,assigned:"X"}))}))},n=a=>{console.log(a)};m(`/${C}/data/getCompCodeBasedOnControllingArea?controllingArea=${e.code}`,"get",t,n)},ot=e=>{console.log("first",e);const t=a=>{g(I({keyName:"CompCode",data:a.body}))},n=a=>{console.log(a)};m(`/${C}/data/getCompCodeBasedOnControllingArea?controllingArea=${e.code}`,"get",t,n)},cr=e=>{console.log("first",e);const t=a=>{g(I({keyName:"ProfitCenter",data:a.body}))},n=a=>{console.log(a)};m(`/${C}/data/getProfitCenterAsPerControllingArea?controllingArea=${e.code}`,"get",t,n)},dr=()=>{ur()},ur=()=>{var f,p,w;if((S==null?void 0:S.code)===void 0||(S==null?void 0:S.code)===""||(z==null?void 0:z.code)===void 0||(z==null?void 0:z.code)===""||v===void 0||v===""||(O==null?void 0:O.code)===void 0||(O==null?void 0:O.code)===""||(M==null?void 0:M.code)===void 0||(M==null?void 0:M.code)===""){se(!1),ae(!0);return}else{if(v.length!==5){se(!0),ae(!1);return}else se(!1);ae(!1)}let e=(f=x==null?void 0:x.controllingArea)==null?void 0:f.newControllingArea.code,t=(p=x==null?void 0:x.companyCodeCopy)==null?void 0:p.newCompanyCodeCopy.code,n=(w=x==null?void 0:x.profitCenterName)==null?void 0:w.newProfitCenterName,a=e.concat("$$","P",t,n);console.log("sendNewProfitCenterData",x),A(!0);const s=y=>{var T,k;A(!1),console.log("dupli",y),y.body.length>0?at(!0):Q(`/masterDataCockpit/profitCenter/displayCopyProfitCenter/${(k=(T=x==null?void 0:x.profitCenterName)==null?void 0:T.newProfitCenterName)==null?void 0:k.code}`,{state:x})},i=y=>{console.log(y)};m(`/${C}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${a}`,"get",s,i)},hr=()=>{if((S==null?void 0:S.code)===void 0||(S==null?void 0:S.code)===""||($==null?void 0:$.code)===void 0||($==null?void 0:$.code)===""||v===void 0||v===""){ne(!1),le(!0);return}else{if(v.length!==5){ne(!0),le(!1);return}else ne(!1);le(!1)}st(S);let e=S.code,t=$.code,n=v,a=e.concat("$$","P",t,n);A(!0);const s=f=>{A(!1),console.log("data",f),f.body.length>0?(console.log("kfdkfgk"),at(!0)):(console.log("gyggu"),Q("/masterDataCockpit/profitCenter/newSingleProfitCenter",{state:x}))},i=f=>{console.log(f)};m(`/${C}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${a}`,"get",s,i)},fr=e=>{K.current&&K.current.contains(e.target)||Ae(t=>!t)},ke=()=>{Yt(!1),Ht(!1),Ue(!1),le(!1),ne(!1),Z(""),be(""),W("")},gr=e=>{if(e.target.value!==null){var t=e.target.value;let n={...c,profitCenterName:t};g(ge({module:"ProfitCenter",filterData:n}))}},Cr=(e,t)=>{{var n=t;let a={...c,controllingArea:n};g(ge({module:"ProfitCenter",filterData:a})),br(a)}},mr=()=>{ir()},pr=()=>{or()},Sr=(e,t)=>{{var n=t;let a={...c,profitCenterGroup:n};g(ge({module:"ProfitCenter",filterData:a}))}},xr=(e,t)=>{{var n=t;let a={...c,segment:n};g(ge({module:"ProfitCenter",filterData:a}))}},yr={"Task ID":"taskId",Status:"status",SalesOrganization:"salesOrg",Division:"division",OldMaterialNumber:"oldMaterialNumber","Lab/Office":"labOffice","Transportation Group":"transportationGroup","Batch management":"batchManagement"},Pr=()=>{const e=n=>{g(I({keyName:"ControllingArea",data:n.body}))},t=n=>{console.log(n)};m(`/${C}/data/getControllingArea`,"get",e,t)},br=e=>{var a;const t=s=>{g(I({keyName:"ProfitCtrGroupSearch",data:s.body}))},n=s=>{console.log(s)};m(`/${C}/data/getProfitCtrGroup?controllingArea=${(a=e==null?void 0:e.controllingArea)==null?void 0:a.code}`,"get",t,n)},it=e=>{console.log("first",Ce);const t=a=>{g(I({keyName:"ProfitCtrGroup",data:a.body}))},n=a=>{console.log(a)};m(`/${C}/data/getProfitCtrGroup?controllingArea=${e.code}`,"get",t,n)},vr=e=>{const t=a=>{g(I({keyName:"Segment",data:a.body}))},n=a=>{console.log(a)};m(`/${C}/data/getSegment`,"get",t,n)},Ar=()=>{let e="Basic Data";const t=a=>{g(wl(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},wr=()=>{let e="Indicators";const t=a=>{console.log("profit",a),g(Nl(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},Nr=()=>{let e="Comp Codes";const t=a=>{g(El(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},Er=()=>{let e="Address";const t=a=>{g($l(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},$r=()=>{let e="Communication";const t=a=>{g(kl(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},kr=()=>{let e="History";const t=a=>{g(Dl(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},Dr=()=>{kt(!0)},Mr=e=>{A(!0),console.log(e);const t=new FormData;if([...e].forEach(i=>t.append("files",i)),console.log(nt,"handleMassModePC"),nt==="Change")var n=`/${C}/massAction/getAllProfitCenterFromExcelForMassChange`;else var n=`/${C}/massAction/getAllProfitCenterFromExcel`;m(n,"postformdata",i=>{console.log(i,"example"),A(!1),i.statusCode===200?(Y(!1),g(Ml(i==null?void 0:i.body)),B("Create"),R(`${e.name} has been Uploaded Succesfully`),L("success"),We(!1),Ye(!0),Dr(),Re(!0),A(!1),Q("/masterDataCockpit/profitCenter/createMultipleProfitCenter")):(Y(!1),B("Create"),Ye(!1),R("Creation Failed"),L("danger"),We(!1),Re(!0),U(),A(!1)),Wr()},i=>{console.log(i)},t)},zr=e=>{console.log("newselection",e),ar(e);let t=dt.map(s=>s.field);const n=F.filter(s=>e.includes(s.id));let a=[];n.map(s=>{console.log("sssssss",s);let i={};t.forEach(f=>{console.log("yyyyy",s[f]),s[f]!==null&&(i[f]=s[f]||"")}),a.push(i),tr(a),console.log("requiredArrayDetails",a)})};l.useEffect(()=>{Pr(),vr(),Ar(),wr(),Nr(),Er(),kr(),$r(),g(tl({})),g(rl())},[]);const Or=()=>{Tt("")},Tr=e=>{console.log("pcSearchForm",c),X(!0),e||(ee(0),pe(10),te(0));let t={controllingArea:"",profitCenter:(oe==null?void 0:oe.number)??"",profitCenterName:"",createdBy:"",segment:"",profitCenterGroup:"",top:"1000",skip:e??0};const n=s=>{var w,y,T,k;var i=[];for(let _=0;_<((y=(w=s==null?void 0:s.body)==null?void 0:w.list)==null?void 0:y.length);_++){var f=(T=s==null?void 0:s.body)==null?void 0:T.list[_];{var p={id:$t(),description:f.Description,controllingArea:f.ControllingArea,companyCode:f.CompanyCode,profitCenter:f.ProfitCenter,profitCenterGroup:f.ProfitCenterGroup,profitCenterName:f.ProfitCenterName,createdBy:f.CreatedBy,segment:f.Segment};i.push(p)}}console.log("rowsss",i),Be(i.reverse()),X(!1),je(i.length),Je((k=s==null?void 0:s.body)==null?void 0:k.count)};let a=s=>{console.log(s)};m(`/${C}/data/getProfitCentersBasedOnAdditionalParams`,"post",n,a,t)},De=new Date,ie=new Date;ie.setDate(ie.getDate()-15),l.useState([ie,De]),l.useState([ie,De]),l.useEffect(()=>{(parseInt(me)+1)*parseInt(qe)>=parseInt(Ke)+1e3&&(ce(Ke+1e3),te(e=>e+1e3))},[me,qe]),console.log("pcsearchform",c);const ce=e=>{var s,i,f;X(!0),e||(ee(0),pe(10),te(0));let t={controllingArea:((s=c==null?void 0:c.controllingArea)==null?void 0:s.code)??"",profitCenter:(c==null?void 0:c.profitCenter)??"",profitCenterName:(c==null?void 0:c.profitCenterName)??"",createdBy:(c==null?void 0:c.createdBy)??"",segment:((i=c==null?void 0:c.segment)==null?void 0:i.code)??"",profitCenterGroup:((f=c==null?void 0:c.profitCenterGroup)==null?void 0:f.code)??"",top:1e3,skip:e??0};const n=p=>{var k,_,xt;console.log("searchdata",p);var w=[];for(let j=0;j<((_=(k=p==null?void 0:p.body)==null?void 0:k.list)==null?void 0:_.length);j++){var y=p==null?void 0:p.body.list[j],T={id:$t(),description:y.Description,controllingArea:y.ControllingArea,companyCode:y.CompanyCode,profitCenter:y.ProfitCenter,profitCenterGroup:y.ProfitCenterGroup,profitCenterName:y.ProfitCenterName,createdBy:y.CreatedBy,segment:y.Segment};w.push(T)}w.sort((j,Xr)=>Fe(j.createdOn,"DD MMM YYYY HH:mm")-Fe(Xr.createdOn,"DD MMM YYYY HH:mm")),Be(w.reverse()),X(!1),je(w.length),Je((xt=p==null?void 0:p.body)==null?void 0:xt.count)},a=p=>{console.log(p)};m(`/${C}/data/getProfitCentersBasedOnAdditionalParams`,"post",n,a,t)};l.useEffect(()=>{ce()},[]),l.useState([]);const U=()=>{Qe(!0)},ct=()=>{Qe(!1)},Ir=(e,t)=>{ee(t)},Lr=e=>{const t=e.target.value;pe(t),ee(0),te(0)};l.useState(null),l.useState(null);const Fr=()=>{g(vl({module:"ProfitCenter"}))};function Br(){ce()}l.useState([]),l.useState([]);const[Gl,Rr]=l.useState(!1);l.useState(null),l.useState(null),l.useState([]);const Wr=()=>{Rr(!1)};l.useState(null),l.useState("");const dt=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"profitCenterName",headerName:"Profit Center Name",editable:!1,flex:1},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"segment",headerName:"Segment",editable:!1,flex:1},{field:"profitCenterGroup",headerName:"Profit Center Group",editable:!1,flex:1},{field:"createdBy",headerName:"Created By",editable:!1,flex:1}],Gr=Ge.map(e=>{const t=yr[e];return t?{field:t,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null),ut=[...dt,...Gr],ht={convertJsonToExcel:()=>{let e=[];ut.forEach(t=>{t.headerName.toLowerCase()!=="action"&&!t.hide&&e.push({header:t.headerName,key:t.field})}),Al({fileName:`Profit Center Data-${Fe(De).format("DD-MMM-YYYY")}`,columns:e,rows:F})},button:()=>r(b,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>ht.convertJsonToExcel(),children:"Download"})};let Vr=l.useRef(null);const Ur=async()=>{var e=Ze.map(a=>({profitCenter:a.profitCenter,controllingArea:a.controllingArea}));console.log("downloadPayload",e);let t=a=>{A(!1);const s=URL.createObjectURL(a),i=document.createElement("a");i.href=s,i.setAttribute("download","Profit Center_Mass Change.xls"),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(s),U(),B("Success"),R("Profit Center_Mass Change.xls has been downloaded successfully"),L("success")},n=a=>{a.message&&(U(),B("Error"),R(`${a.message}`),L("danger"))};m(`/${C}/excel/downloadExcelWithData`,"postandgetblob",t,n,e)},_r=async()=>{var e=Ze.map(a=>({profitCenter:a.profitCenter,controllingArea:a.controllingArea}));console.log("downloadPayload",e);let t=a=>{const s=URL.createObjectURL(a),i=document.createElement("a");i.href=s,i.setAttribute("download","Profit Center_Mass Create.xls"),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(s),U(),B("Success"),R("Profit Center_Mass Create.xls has been downloaded successfully"),L("success")},n=a=>{a.message&&(U(),B("Error"),R(`${a.message}`),L("danger"))};m(`/${C}/excel/downloadExcel`,"getblobfile",t,n)},ft=(e,t)=>{t!==0&&(Qt(t),Ae(!1),t===1?mr():t===2&&pr())},Yr=()=>{Y(!0),g(Et("Create"))},Hr=()=>{xe(e=>!e)},qr=e=>{H.current&&H.current.contains(e.target)||xe(t=>!t)},Kr=()=>{Pe(e=>!e)},jr=()=>{Ae(e=>!e)},Jr=e=>{q.current&&q.current.contains(e.target)||Pe(t=>!t)},gt=(e,t)=>{t!==0&&(Kt(t),xe(!1),t===1?Yr():t===2&&_r())},Ct=(e,t)=>{t!==0&&(er(t),Pe(!1),t===1?Qr():t===2&&(rt.length>0?(console.log("selectedRows",rt),A(!0),nr(!1),Ur()):console.log("Please select at least one row to download Excel.")))},Qr=()=>{Y(!0),g(Et("Change"))};return r(bl,{children:Mt===!0?r(ll,{}):o("div",{ref:Vr,children:[r(al,{dialogState:Vt,openReusableDialog:U,closeReusableDialog:ct,dialogTitle:Ut,dialogMessage:_t,handleDialogConfirm:ct,dialogOkText:"OK",dialogSeverity:Dt}),r("div",{style:{...nl,backgroundColor:"#FAFCFF"},children:o(Me,{spacing:1,children:[o(d,{container:!0,sx:sl,children:[o(d,{item:!0,md:5,sx:ol,children:[r(u,{variant:"h3",children:r("strong",{children:"Profit Center"})}),r(u,{variant:"body2",color:"#777",children:"This view displays the list of Profit Centers"})]}),r(d,{item:!0,md:7,sx:{display:"flex"},children:o(d,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[r(il,{title:"Search for multiple Profit Center numbers separated by comma",handleSearchAction:()=>Tr(),module:"ProfitCenter",keyName:"number",message:"Search Profit Center ",clearSearchBar:Or}),r(yt,{title:"Reload",children:r(ue,{sx:Pt,children:r(cl,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:Br})})}),r(yt,{title:"Export Table",children:r(ue,{sx:Pt,onClick:ht.convertJsonToExcel,children:r(he,{iconName:"IosShare"})})})]})})]}),r(d,{container:!0,sx:dl,children:r(d,{item:!0,md:12,children:o(ul,{className:"filter-accordian",children:[r(hl,{expandIcon:r(fl,{}),"aria-controls":"panel1a-content",id:"panel1a-header",sx:{minHeight:"2rem !important",margin:"0px !important"},children:r(u,{sx:{fontWeight:"700"},children:"Search Profit Center"})}),o(gl,{sx:{padding:"0.5rem 1rem 0.5rem"},children:[o(d,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:[o(d,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[o(d,{item:!0,md:2,children:[r(u,{sx:J,children:"Profit Center Name"}),r(N,{size:"small",fullWidth:!0,children:r(P,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:c==null?void 0:c.profitCenterName,onChange:gr,placeholder:"Enter Profit Center"})})]}),o(d,{item:!0,md:2,children:[r(u,{sx:J,children:"Controlling Area"}),r(N,{size:"small",fullWidth:!0,children:r(E,{sx:{height:"31px"},fullWidth:!0,size:"small",value:c==null?void 0:c.controllingArea,onChange:Cr,options:(h==null?void 0:h.ControllingArea)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Controlling Area"})})})]}),o(d,{item:!0,md:2,children:[r(u,{sx:J,children:"Segment"}),r(N,{fullWidth:!0,size:"small",children:r(E,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:xr,options:(h==null?void 0:h.Segment)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.segment,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Segment"})})})]}),o(d,{item:!0,md:2,children:[r(u,{sx:J,children:"Profit Center Group"}),r(N,{fullWidth:!0,size:"small",children:r(E,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:Sr,value:c==null?void 0:c.profitCenterGroup,options:(h==null?void 0:h.ProfitCtrGroupSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Profit Center Group"})})})]})]}),r(d,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:Ge.map((e,t)=>r(d,{item:!0,children:o(Me,{children:[r(u,{sx:{fontSize:"12px"},children:e}),r(E,{sx:J,size:"small",options:Lt??[],getOptionLabel:(n,a)=>{var s,i;return`${(s=n[a])==null?void 0:s.code} - ${(i=n[a])==null?void 0:i.desc}`},placeholder:`Enter ${e}`,value:Ve[e],onChange:(n,a)=>It({...Ve,[e]:a}),renderInput:n=>r(P,{sx:{fontSize:"12px !important"},...n,size:"small",placeholder:`Enter ${e}`,variant:"outlined"})},e[t])]})}))})]}),r(d,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:o(d,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:[r(b,{variant:"outlined",sx:Cl,onClick:Fr,children:"Clear"}),r(b,{variant:"contained",sx:{...ml,...pl},onClick:()=>ce(),children:"Search"})]})})]})]})})}),r(d,{item:!0,sx:{position:"relative"},children:r(Me,{children:r(Sl,{isLoading:zt,module:"ProfitCenter",width:"100%",title:"List of Profit Centers ("+Wt+")",rows:F,columns:ut,page:me,pageSize:10,rowCount:Gt??(F==null?void 0:F.length)??0,onPageChange:Ir,onPageSizeChange:Lr,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:zr,callback_onRowSingleClick:e=>{console.log(e,"params");const t=e.row.profitCenter;Q(`/masterDataCockpit/profitCenter/displayProfitCenter/${t}`,{state:e.row})},stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})})}),xl(sr,"Profit Center","CreatePC")&&(G==null?void 0:G.role)==="Super User"||(G==null?void 0:G.role)==="Finance"?r(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:o(yl,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:Ce,onChange:e=>{Ot(e)},children:[o(ze,{variant:"contained",ref:K,"aria-label":"split button",children:[r(b,{size:"small",variant:"contained",onClick:()=>ft(Ee[0],0),children:Ee[0]}),r(b,{size:"small","aria-controls":ve?"split-button-menu":void 0,"aria-expanded":ve?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:jr,children:r(he,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),r(Oe,{sx:{zIndex:1},open:ve,anchorEl:K.current,placement:"top-end",children:r(fe,{style:{width:(mt=K.current)==null?void 0:mt.clientWidth},children:r(Te,{onClickAway:fr,children:r(Ie,{id:"split-button-menu",autoFocusItem:!0,children:Ee.slice(1).map((e,t)=>r(Le,{selected:t===Jt-1,onClick:()=>ft(e,t+1),children:e},e))})})})}),o(bt,{open:Ft,onClose:ke,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[o(vt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(u,{variant:"h6",children:"New Profit Center"}),r(ue,{sx:{width:"max-content"},onClick:ke,children:r(At,{})})]}),r(wt,{sx:{padding:".5rem 1rem"},children:o(d,{container:!0,spacing:1,children:[o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Controlling Area",r("span",{style:{color:"red"},children:"*"})]}),r(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:r(E,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:(e,t)=>{Z(t),st(t),it(t),ot(t)},options:(h==null?void 0:h.ControllingArea)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.costCenterCategory,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA",error:re})})}),re&&r(u,{variant:"caption",color:"error",children:"Please Select a Controlling Area."})]}),o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Profit Center",r("span",{style:{color:"red"},children:"*"})]}),o(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[r(d,{md:2,children:r(P,{sx:{fontSize:"12px !important",height:"31px"},value:"P",fullWidth:!0,size:"small",editable:!1})}),r(d,{md:5,children:r(E,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{be(t)},options:(h==null?void 0:h.CompCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})}),o(d,{md:5,children:[r(P,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",value:v,onChange:e=>{let t=e.target.value;if(t.length>0&&t[0]===" ")W(t.trimStart());else{let n=t.toUpperCase();W(n)}},inputProps:{maxLength:5,style:{textTransform:"uppercase"}},placeholder:"Enter Profit Center",required:!0,error:et}),et&&r(u,{variant:"caption",color:"error",children:"Cost Center must be 10 digits"})]})]}),Xe&&r(u,{variant:"caption",color:"error",children:"Please enter a Profit Center."})]}),rr&&r(d,{children:r(u,{style:{color:"red"},children:"Please Enter Mandatory Fields"})}),lt&&r(d,{children:r(u,{style:{color:"red"},children:"*The Profit Center with Controlling Area already exists. Please enter different Profit Center or Controlling Area"})})]})}),o(Nt,{sx:{display:"flex",justifyContent:"end"},children:[r(b,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ke,children:"Cancel"}),r(b,{className:"button_primary--normal",type:"save",onClick:hr,variant:"contained",children:"Proceed"})]})]}),o(bt,{open:Bt,onClose:$e,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[o(vt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(u,{variant:"h6",children:"New Profit Center"}),r(ue,{sx:{width:"max-content"},onClick:$e,children:r(At,{})})]}),o(wt,{sx:{padding:".5rem 1rem"},children:[o(d,{container:!0,spacing:1,children:[o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Controlling Area",r("span",{style:{color:"red"},children:"*"})]}),r(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:r(E,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:(e,t)=>{Z(t),ot(t),it(t)},options:(h==null?void 0:h.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA",error:re})})}),re&&r(u,{variant:"caption",color:"error",children:"Please Select a Controlling Area."})]}),o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Profit Center",r("span",{style:{color:"red"},children:"*"})]}),o(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[r(d,{md:2,children:r(P,{sx:{fontSize:"12px !important",height:"31px"},value:"P",fullWidth:!0,size:"small",editable:!1})}),r(d,{md:5,children:r(E,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{jt(t)},options:(h==null?void 0:h.CompCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})}),o(d,{md:5,children:[r(P,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",value:v,onChange:e=>{let t=e.target.value;if(t.length>0&&t[0]===" ")W(t.trimStart());else{let n=t.toUpperCase();W(n)}},inputProps:{maxLength:5,style:{textTransform:"uppercase"}},placeholder:"Enter Profit Center",required:!0,error:tt}),tt&&r(u,{variant:"caption",color:"error",children:"Profit Center must be 10 digits"})]})]}),Xe&&r(u,{variant:"caption",color:"error",children:"Please enter a Profit Center."})]}),r(Pl,{sx:{width:"100%",marginLeft:"2%"},children:r("b",{children:"Copy From"})}),o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Controlling Area",r("span",{style:{color:"red"},children:"*"})]}),r(N,{fullWidth:!0,sx:{margin:".5em 0px"},children:r(E,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{Xt(t),cr(t)},options:(V==null?void 0:V.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),error:S==="",renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA"})})})]}),o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Profit Center",r("span",{style:{color:"red"},children:"*"})]}),r(N,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:r(d,{md:12,children:r(E,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{He(t)},options:(V==null?void 0:V.ProfitCenter)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})})})]}),lt&&r(d,{children:r(u,{style:{color:"red"},children:"*The Profit Center with Controlling Area already exists. Please enter different Profit Center or Controlling Area"})})]}),lr&&r(d,{children:r(u,{style:{color:"red"},children:"Please Enter Mandatory Fields"})})]}),o(Nt,{sx:{display:"flex",justifyContent:"end"},children:[r(b,{sx:{width:"max-content",textTransform:"capitalize"},onClick:$e,children:"Cancel"}),r(b,{className:"button_primary--normal",type:"save",onClick:dr,variant:"contained",children:"Proceed"})]})]}),o(ze,{variant:"contained",ref:H,"aria-label":"split button",children:[r(b,{size:"small",variant:"contained",onClick:()=>gt(we[0],0),sx:{cursor:"default"},children:we[0]}),r(b,{size:"small","aria-controls":Se?"split-button-menu":void 0,"aria-expanded":Se?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:Hr,children:r(he,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),r(Oe,{sx:{zIndex:1},open:Se,anchorEl:H.current,placement:"top-end",children:r(fe,{style:{width:(pt=H.current)==null?void 0:pt.clientWidth},children:r(Te,{onClickAway:qr,children:r(Ie,{id:"split-button-menu",autoFocusItem:!0,children:we.slice(1).map((e,t)=>r(Le,{selected:t===qt-1,onClick:()=>gt(e,t+1),children:e},e))})})})}),o(ze,{variant:"contained",ref:q,"aria-label":"split button",children:[r(b,{size:"small",onClick:()=>Ct(Ne[0],0),sx:{cursor:"default"},children:Ne[0]}),r(b,{size:"small","aria-controls":ye?"split-button-menu":void 0,"aria-expanded":ye?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:Kr,children:r(he,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),r(Oe,{sx:{zIndex:1},open:ye,anchorEl:q.current,placement:"top-end",children:r(fe,{style:{width:(St=q.current)==null?void 0:St.clientWidth},children:r(Te,{onClickAway:Jr,children:r(Ie,{id:"split-button-menu",autoFocusItem:!0,children:Ne.slice(1).map((e,t)=>r(Le,{selected:t===Zt-1,onClick:()=>Ct(e,t+1),children:e},e))})})})}),Rt&&r(zl,{artifactId:"",artifactName:"",setOpen:Y,handleUpload:Mr})]})}):""]})})]})})};export{_l as default};
