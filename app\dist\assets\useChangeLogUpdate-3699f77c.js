import{l as b,i as l,se as j,sf as m,sg as v,sh as D,jt as h,si as N,sj as S,sk as U,l0 as R,sl as G,sm as $,sn as k}from"./index-fdfa25a0.js";const H=()=>{const g=b(),t=l(e=>e.userManagement.userData),s=l(e=>e.changeLog.createPayloadCopyForChangeLog||[]),p=l(e=>e.changeLog.createTemplateArray);return{updateChangeLog:({materialID:e,viewName:o,plantData:u,fieldName:n,jsonName:c,currentValue:d,requestId:i,childRequestId:r,isDescriptionData:A=!1,isUnitOfMeasure:L=!1,isAdditionalEAN:y=!1,uomId:P=null,eanId:V=null,language:E})=>{let a;A?a=j(e,c,s,E):L?a=m(e,P,n,s):y?a=v(e,V,n,s):a=D(e,o,u,c,s);const F=h(N,o),T=S(u,F),f=h(U,o),C={ObjectNo:`${e}${T}`,ChangedBy:t==null?void 0:t.emailId,ChangedOn:R,FieldName:n,PreviousValue:a,CurrentValue:d,SAPValue:a,tableName:f};g(G(C));const O=[...p,C],_=$(O);let M={RequestId:i,changeLogId:null,ChildRequestId:r==null?void 0:r.slice(3),..._};g(k(M))}}};export{H as u};
