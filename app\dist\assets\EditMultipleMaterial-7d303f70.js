import{r as p,i as g,l as P,u as I,is as $,a as e,g_ as b,j as i,T as r,hy as H,a3 as z,gR as J,gS as U,hJ as Z,hA as q,F,$ as o,kB as K,b as Q,hp as X,W as Y,hv as G,i8 as ee,a1 as _,gU as te,ib as ae,B as C,aa as ie,ab as le,h_ as ne,gZ as oe,m4 as re,hE as de,P as se}from"./index-fdfa25a0.js";function ce(d,s){return Array.isArray(s)&&s.find(u=>u.code===d)||""}const he=({label:d,value:s,fieldGroup:x,units:u,onSave:D,isEditMode:S,isExtendMode:T,selectedRowData:k,options:E=[],type:c})=>{var W;const[n,f]=p.useState(s),[v,l]=p.useState(!1),h=g(t=>t.AllDropDown.dropDown),w=P(),N=ce(n,h);console.log("dropdownData",n),console.log("value e",s),console.log("label",d),console.log("units",u),console.log("transformedValue",N),I();const M=g(t=>t.initialData.MultipleMaterial),O=g(t=>t.edit.payload);g(t=>t.initialData.MultipleMaterial[0].Description);let j=-1;for(let t=0;t<M.length;t++)if(M[t].Description===k){M[t],j=t;break}console.log("editField",O),console.log("fieldData",{label:d,value:n,units:u,type:c});let y=d.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");p.useEffect(()=>{f(s)},[s]);const A=(t,a)=>{w(K(M.map((m,R)=>{if(R==j){let L=m["Basic Data"],V=m["Basic Data"][x];return{...m,"Basic Data":{...L,[x]:V.map(B=>B.fieldName===t?{...B,value:a}:B)}}}else return m})))};return p.useEffect(()=>{console.log("lkey",y),console.log("data",s),w($({keyname:y.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:s||""}))},[]),console.log("editedValue[key] ",h[y]),console.log("editedValue[key] ",n),e(o,{item:!0,children:e(b,{children:S||T?i(F,{children:[e(r,{variant:"body2",color:"#777",children:d}),c==="Drop Down"?e(H,{options:h[y]??[],value:n&&((W=h[y])==null?void 0:W.filter(t=>t.code===n))||"",onChange:(t,a)=>{A(d,a.code),console.log("newValue",a),f(a.code),l(!0),console.log("keys",y)},getOptionLabel:t=>{var a,m;return console.log("optionoptionoption",t),t===""?"":`${t&&((a=t[0])==null?void 0:a.code)} - ${t&&((m=t[0])==null?void 0:m.desc)}`},renderOption:(t,a)=>(console.log("option vakue",a),e("li",{...t,children:e(r,{style:{fontSize:12},children:`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`})})),renderInput:t=>e(z,{...t,variant:"outlined",size:"small",label:null})}):c==="Input"?e(z,{variant:"outlined",size:"small",value:n,onChange:t=>{const a=t.target.value;A(d,a),f(a)}}):c==="Calendar"?e(J,{dateAdapter:U,children:e(Z,{slotProps:{textField:{size:"small"}},placeholder:"Select Date Range"})}):c==="Radio Button"?e(q,{sx:{borderRadius:"0 !important"},checked:n,onChange:(t,a)=>{A(d,a),f(a)}}):""]}):e(F,{children:i(F,{children:[e(r,{variant:"body2",color:"#777",children:d}),i(r,{variant:"body2",fontWeight:"bold",children:[n," ",u]})]})})})})},me=()=>{const d=Q(),s=P();p.useState({});const[x,u]=p.useState(0),[D,S]=p.useState(!1),[T,k]=p.useState(!0),E=I();p.useState(!1),g(l=>l.initialData.EditMultipleMaterial);const c=g(l=>l.initialData.MultipleMaterial),n=E.state;g(l=>l.payload);const f=()=>{S(!0),re(),k(!1)};for(let l=0;l<c.length;l++)if(c[l].Description===n.description){c[l];break}const v=c.filter(l=>l.Description===n.description)[0]["Basic Data"];return console.log(v,"lololol"),i("div",{children:[e(o,{container:!0,style:{...X,backgroundColor:"#FAFCFF"},children:i(o,{sx:{width:"inherit"},children:[i(o,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[e(o,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(Y,{color:"primary","aria-label":"upload picture",component:"label",sx:G,children:e(ee,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{setTimeout(()=>{d(-1)},1e3),s(clearPayload()),s(clearOrgData())}})})}),i(o,{md:8,children:[e(r,{variant:"h3",children:i("strong",{children:["Multiple Material : ",n.description," "]})}),e(r,{variant:"body2",color:"#777",children:"This view displays details of uploaded material"})]}),D?"":e(o,{md:4,sx:{display:"flex",justifyContent:"flex-end"},children:e(o,{item:!0,children:i(_,{variant:"outlined",size:"small",sx:te,onClick:f,children:["Change",e(ae,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),i(o,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[i(C,{width:"70%",sx:{marginLeft:"40px"},children:[e(o,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Material"})}),i(r,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",n.material]})]})}),e(o,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Material Type"})}),i(r,{variant:"body2",fontWeight:"bold",children:[": ",n.materialType]})]})}),e(o,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Description"})}),i(r,{variant:"body2",fontWeight:"bold",children:[": ",n.description]})]})}),e(o,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Industry Sector"})}),i(r,{variant:"body2",fontWeight:"bold",children:[": ",n.industrySector]})]})})]}),e(C,{width:"30%",sx:{marginLeft:"40px"},children:e(o,{item:!0,children:i(b,{flexDirection:"row",children:[e(r,{variant:"body2",color:"#777",style:{width:"30%"}}),e(r,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"}}),e(r,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start"})]})})})]}),i(o,{container:!0,style:{padding:"16px"},children:[e(C,{sx:{borderBottom:1,borderColor:"divider"},children:e(ie,{value:x,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:e(le,{sx:{fontSize:"12px",fontWeight:"700"},label:"Basic Data"},0)})}),e(o,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(v).map(l=>i(o,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ne},children:[e(r,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:l}),e(C,{sx:{width:"100%"},children:e(oe,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(o,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:v[l].map(h=>e(he,{fieldGroup:l,selectedRowData:n.description,label:h.fieldName,value:h.value,onSave:w=>handleFieldSave(h.fieldName,w),isEditMode:D,type:h.fieldType,field:h}))})})})]},l))},v)]})]})}),D?e(se,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(de,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:x,onChange:l=>{u(l)},children:e(_,{size:"small",variant:"contained",onClick:()=>{d("/masterDataCockpit/materialMaster/massMaterialTable")},children:"Save"})})}):""]})};export{me as default};
