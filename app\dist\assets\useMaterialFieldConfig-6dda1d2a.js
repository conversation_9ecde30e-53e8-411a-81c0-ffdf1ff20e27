import{l as _e,k as re,i as q,r as j,u as le,jZ as ne,H as oe,hj as g,hg as C,w as y,y as Q,kM as W,kN as $,kO as Y,kP as x,kQ as B,kS as H,mu as U,mv as K,mw as X,mx as Z,my as J,mz as Me,kA as ce}from"./index-fdfa25a0.js";const z=t=>{let S={},a=t==null?void 0:t.sort((r,N)=>r.MDG_MAT_VIEW_SEQUENCE-N.MDG_MAT_VIEW_SEQUENCE);const L=J(a,"MDG_MAT_VIEW_NAME");let m=[];Object.entries(L).forEach(([r,N])=>{let E=J(N,"MDG_MAT_CARD_NAME"),l=[];Object.entries(E).forEach(([G,D])=>{D.sort((M,v)=>M.MDG_MAT_SEQUENCE_NO-v.MDG_MAT_SEQUENCE_NO);let P=D.map(M=>({fieldName:M.MDG_MAT_UI_FIELD_NAME,sequenceNo:M.MDG_MAT_SEQUENCE_NO,fieldType:M.MDG_MAT_FIELD_TYPE,maxLength:M.MDG_MAT_MAX_LENGTH,dataType:M.MDG_MAT_DATA_TYPE,viewName:M.MDG_MAT_VIEW_NAME,cardName:M.MDG_MAT_CARD_NAME,cardSeq:M.MDG_MAT_CARD_SEQUENCE,value:M.MDG_MAT_DEFAULT_VALUE,visibility:M.MDG_MAT_VISIBILITY,jsonName:M.MDG_MAT_JSON_FIELD_NAME,fieldPriority:M.MDG_MAT_MATERIAL_FLD_PRT}));l.push({cardName:G,cardSeq:D[0].MDG_MAT_CARD_SEQUENCE,cardDetails:P})}),l.sort((G,D)=>G.cardSeq-D.cardSeq),m.push({viewName:r,cards:l})});let b=Me(m),p={};return b.forEach(r=>{let N={};r.cards.forEach(E=>{N[E.cardName]=E.cardDetails,r.viewName!=="Request Header"&&E.cardDetails.forEach(l=>{l.visibility===ce.MANDATORY&&(S[l.viewName]||(S[l.viewName]=[]),S[l.viewName].push({jsonName:l==null?void 0:l.jsonName,fieldName:l==null?void 0:l.fieldName}))})}),p[r.viewName]=N}),{transformedData:p,mandatoryFields:S}},Te=()=>{const t=_e(),{customError:S}=re(),a=q(_=>{var n;return(n=_.payload)==null?void 0:n.payloadData}),L=q(_=>_.applicationConfig),{userData:m,taskData:b}=q(_=>_.userManagement),[p,r]=j.useState(!0),[N,E]=j.useState(null);le();const l=window.location.href.includes("DisplayMaterialSAPView"),G=ne(oe.CURRENT_TASK);let D=null;D=typeof G=="string"?JSON.parse(G):G;let P=D==null?void 0:D.ATTRIBUTE_5;const M=async(_,n)=>{if(a!=null&&a.RequestType||l){const T={decisionTableId:null,decisionTableName:"MDG_MAT_MATERIAL_FIELD_CONFIG",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(a==null?void 0:a.RequestType)||"Display","MDG_CONDITIONS.MDG_MAT_MATERIAL_TYPE":l?_.split("-")[0]:_||"VERP","MDG_CONDITIONS.MDG_MAT_REGION":(a==null?void 0:a.Region)||"US","MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":l?"Z_MAT_REQ_DISPLAY":b.ATTRIBUTE_5?P:"Z_MAT_REQ_INITIATE"}],systemFilters:null,systemOrders:null,filterString:null},i=s=>{var O,I,u,d,h;if(s.statusCode===Q.STATUS_200){if(Array.isArray((O=s==null?void 0:s.data)==null?void 0:O.result)&&((I=s==null?void 0:s.data)!=null&&I.result.every(c=>Object.keys(c).length!==0))){let c=(d=(u=s==null?void 0:s.data)==null?void 0:u.result[0])==null?void 0:d.MDG_MAT_MATERIAL_FIELD_CONFIG;const{transformedData:o,mandatoryFields:V}=z(c),F=Object.keys(o).map(e=>(e==="Basic Data"?t(W(o["Basic Data"])):e==="Sales"?t($(o.Sales)):e==="Purchasing"?t(Y(o.Purchasing)):e==="MRP"?t(x(o.MRP)):e==="Accounting"&&t(B(o.Accounting)),e!=="Request Header"&&t(H({tab:e,data:o[e]})),{[e]:o[e]}));t(U({[(a==null?void 0:a.Region)||n||"US"]:{[_]:{allfields:K(F),mandatoryFields:V}}}));let f=[...new Set((h=c==null?void 0:c.sort((e,k)=>e.MDG_MAT_VIEW_SEQUENCE-k.MDG_MAT_VIEW_SEQUENCE))==null?void 0:h.map(e=>e.MDG_MAT_VIEW_NAME))];const w=["Header","Sales-General","Sales-Plant","Purchasing-General","BOM","Source List","PIR"];f=[...f.filter(e=>!w.includes(e)),"Storage"],t(X({matType:_,views:f}))}else t(U({[(a==null?void 0:a.Region)||n||"US"]:{[_]:{}}}));r(!1)}},A=s=>{S(s),E(s),r(!1)},R=L.environment==="localhost"?`/${g}${C.INVOKE_RULES.LOCAL}`:`/${g}${C.INVOKE_RULES.PROD}`;y(R,"post",i,A,T)}},v=(_,n,T)=>{r(!0);try{M(_,n,T)}catch(i){E(i),r(!1)}},ee=async(_,n)=>{if(a!=null&&a.RequestType){const T={decisionTableId:null,decisionTableName:"MDG_MAT_EXTEND_TEMPLATE_DT",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":"Extend","MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":"CA-MDG-MD-TEAM-US"}],systemFilters:null,systemOrders:null,filterString:null},i=s=>{var O,I,u,d,h;if(s.statusCode===Q.STATUS_200){if(Array.isArray((O=s==null?void 0:s.data)==null?void 0:O.result)&&((I=s==null?void 0:s.data)!=null&&I.result.every(c=>Object.keys(c).length!==0))){let c=(d=(u=s==null?void 0:s.data)==null?void 0:u.result[0])==null?void 0:d.MDG_MAT_EXTEND_TEMPLATE_DT;const{transformedData:o,mandatoryFields:V}=z(c),F=Object.keys(o).map(e=>(e==="Basic Data"?t(W(o["Basic Data"])):e==="Sales"?t($(o.Sales)):e==="Purchasing"?t(Y(o.Purchasing)):e==="MRP"?t(x(o.MRP)):e==="Accounting"&&t(B(o.Accounting)),e!=="Request Header"&&t(H({tab:e,data:o[e]})),{[e]:o[e]}));t(U({[(a==null?void 0:a.Region)||n||"US"]:{[_]:{allfields:K(F),mandatoryFields:V}}}));let f=[...new Set((h=c==null?void 0:c.sort((e,k)=>e.MDG_MAT_VIEW_SEQUENCE-k.MDG_MAT_VIEW_SEQUENCE))==null?void 0:h.map(e=>e.MDG_MAT_VIEW_NAME))];const w=["Header","Sales-General","Sales-Plant","Purchasing-General"];f=f.filter(e=>!w.includes(e)),t(X({matType:_,views:f}))}else t(U({[(a==null?void 0:a.Region)||n||"US"]:{[_]:{}}}));r(!1)}},A=s=>{S(s),E(s),r(!1)},R=L.environment==="localhost"?`/${g}${C.INVOKE_RULES.LOCAL}`:`/${g}${C.INVOKE_RULES.PROD}`;y(R,"post",i,A,T)}},se=(_,n)=>{r(!0);try{ee(_,n)}catch(T){E(T),r(!1)}},ae=_=>{let n={decisionTableId:null,decisionTableName:"MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US"}],systemFilters:null,systemOrders:null,filterString:null};const T=A=>{var R,s;if(A.statusCode===Q.STATUS_200){let O=(s=(R=A==null?void 0:A.data)==null?void 0:R.result[0])==null?void 0:s.MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING;const I=Array.from(new Map(O.map(u=>[u.MDG_MAT_SALES_ORG,{code:u.MDG_MAT_SALES_ORG,desc:u.MDG_MAT_SALES_ORG_DESC}])).values());t(Z({keyName:"uniqueSalesOrgList",data:I})),t(Z({keyName:"salesOrgData",data:O}))}},i=A=>{S(A)};L.environment==="localhost"?y(`/${g}${C.INVOKE_RULES.LOCAL}`,"post",T,i,n):y(`/${g}${C.INVOKE_RULES.PROD}`,"post",T,i,n)};return{loading:p,error:N,fetchMaterialFieldConfig:v,fetchMaterialFieldConfigExtend:se,fetchOrgData:_=>{r(!0);try{ae(_)}catch(n){E(n),r(!1)}}}};export{Te as u};
