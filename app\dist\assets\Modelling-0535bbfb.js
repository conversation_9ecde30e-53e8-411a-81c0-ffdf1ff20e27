import{i as a,r as l,a as t,B as s,hj as o}from"./index-fdfa25a0.js";import{i as c}from"./index-7377b8bc.js";import"./index-3f2e0745.js";import"./DialogContentText-8ac052ae.js";import"./ListItemButton-db9eb0d0.js";import"./StepButton-fdbf0590.js";import"./ToggleButtonGroup-c02e6027.js";import"./makeStyles-1dfd4db4.js";import"./Check-aa258898.js";import"./DeleteOutline-1f72afa8.js";import"./asyncToGenerator-88583e02.js";import"./FileUploadOutlined-bf833d2e.js";import"./DeleteOutlineOutlined-9e9a8646.js";import"./dayjs.min-774e293a.js";import"./CheckBox-6243e871.js";const j=d=>{const m=[{Description:"",Name:"WorkRulesServices",URL:o},{Description:"",Name:"CW_Worktext",URL:o},{Description:"",Name:"WorkRuleEngineServices",URL:o},{Description:"",Name:"WorkUtilsServices",URL:o}],r=a(e=>e==null?void 0:e.userManagement),i=r==null?void 0:r.userData;let n={emailId:i==null?void 0:i.emailId,user_id:i==null?void 0:i.user_id};const p=l.useMemo(()=>t(c.Modelling,{translationDataObjects:[],userDetails:n,destinations:m}),[i]);return t(s,{className:"content",sx:{padding:"8px 16px"},children:t(s,{sx:{height:"100%","& .MuiSnackbar-root":{left:"50% !important"}},children:p})})};export{j as default};
