import{i as ce,r as L,e as we,j as J,a,a3 as Ae,h as Ie,fV as Pe,h1 as Ve,fX as He,T as Ee,iX as ee,so as Y,gK as te,F as Ne,fH as ze,a1 as Me,E as fe,l as _e,u as xe,iD as Re,w as qe,G as Ge,hg as Be,mt as De,h2 as je,h4 as Ye,gQ as Je,W as Se,iQ as Ze,B as Fe,gU as Xe,$ as Z,ny as oe,iZ as ue,lQ as he,sp as Ke,hC as We,j6 as $e,mf as Qe,hW as Te,sq as me,sr as pe,h_ as et,hA as tt,ss as le,st as Ue,su as at,hp as Le,aa as nt,ab as it}from"./index-fdfa25a0.js";import{d as Oe}from"./DeleteOutline-1f72afa8.js";import{u as ye}from"./useChangeLogUpdate-3699f77c.js";import{u as lt}from"./useCustomDtCall-04c3c72a.js";const rt=({params:e,disabled:m=!1,handleCellEdit:i,isAdd:s=!0})=>{var k,u,v,o,h,A,d,q,t,f,E,D,$,P,T,V,w,de,Q;const r=ce(_=>_.payload.payloadData),l=r==null?void 0:r.Region,[U,M]=L.useState(!1),[N,S]=L.useState(""),[O,b]=L.useState(""),[X,ae]=L.useState(""),{t:F}=we(),C=s?(k=e==null?void 0:e.row)==null?void 0:k.materialDescription:((u=e==null?void 0:e.row)==null?void 0:u.globalMaterialDescription)||"",re=()=>{var _;S(C.slice(0,(_=Y)==null?void 0:_.US).trim()),b(C.slice(27).trim()),ae(""),M(!0)},W=()=>{M(!1)},j=()=>{var n,c,g,x,R,ne,ie,se,Ce;if(!N.trim()){ae((n=fe)==null?void 0:n.MAIN_DESCR_MANDATORY);return}let _=N.toUpperCase().padEnd((c=Y)==null?void 0:c.US," "),y=O.toUpperCase(),H=y?`${_} ${y}`:_.trim(),z;if(l===((g=ee)==null?void 0:g.US)?z=/^[A-Z0-9\/"\-\s]*$/:l===((x=ee)==null?void 0:x.EUR)&&(z=/^[A-Z0-9"\-\s]*$/),!z.test(H)){ae(l===((R=ee)==null?void 0:R.US)?(ne=fe)==null?void 0:ne.DESCRIPTION_VALIDITY_US:(ie=fe)==null?void 0:ie.DESCRIPTION_VALIDITY_EUR);return}(se=e==null?void 0:e.row)!=null&&se.id&&(s?i((Ce=e==null?void 0:e.row)==null?void 0:Ce.id,H):i({id:e.row.id,field:"globalMaterialDescription",value:H})),M(!1)};return J(Ne,{children:[a(Ie,{title:a("span",{style:{whiteSpace:"pre"},children:C}),arrow:!0,children:a("span",{children:a(Ae,{fullWidth:!0,variant:"outlined",disabled:m,size:"small",value:C,placeholder:F("ENTER MATERIAL DESCRIPTION"),onClick:re,InputProps:{readOnly:!0}})})}),J(Pe,{open:U,onClose:W,maxWidth:"sm",fullWidth:!0,children:[a(Ve,{children:F("Enter Material Description")}),J(He,{children:[J(Ee,{children:[F("Material Description")," (",l===((v=ee)==null?void 0:v.EUR)?`Max ${(o=Y)==null?void 0:o.EUR} Chars, Only Alphabets`:`Max ${(h=Y)==null?void 0:h.US} Chars`,") ",a("span",{style:{color:(d=(A=te)==null?void 0:A.error)==null?void 0:d.red},children:"*"})]}),a(Ae,{variant:"outlined",fullWidth:!0,size:"small",placeholder:l===((q=ee)==null?void 0:q.EUR)?`Enter description (Max ${(t=Y)==null?void 0:t.EUR} chars)`:`Enter First Part (Max ${(f=Y)==null?void 0:f.US} chars)`,value:N,onChange:_=>{var H,z,n,c,g;const y=_.target.value.toUpperCase();S(l===((H=ee)==null?void 0:H.EUR)?(n=y==null?void 0:y.replace(/[^A-Z0-9"\-\s]/g,""))==null?void 0:n.slice(0,(z=Y)==null?void 0:z.EUR):(g=y==null?void 0:y.replace(/[^A-Z0-9\/"\-\s]/g,""))==null?void 0:g.slice(0,(c=Y)==null?void 0:c.US))},helperText:l===((E=ee)==null?void 0:E.EUR)?`${N.length}/${(D=Y)==null?void 0:D.EUR} characters used (Only letters, numbers, quotes, hyphen and spaces allowed)`:`${N.length}/${($=Y)==null?void 0:$.US} characters used (Only letters, numbers, /, ", - and spaces allowed)`}),l===((P=ee)==null?void 0:P.US)&&J(Ne,{children:[a(Ee,{sx:{marginTop:2},children:`Special Material Description (Max ${(T=Y)==null?void 0:T.US_SPECIAL} Chars)`}),a(Ae,{variant:"outlined",fullWidth:!0,size:"small",placeholder:`Enter special Description (Max ${(V=Y)==null?void 0:V.US_SPECIAL} chars)`,value:O,onChange:_=>{var H,z;const y=_.target.value.toUpperCase();b((z=y==null?void 0:y.replace(/[^A-Z0-9\/"\-\s]/g,""))==null?void 0:z.slice(0,(H=Y)==null?void 0:H.US_SPECIAL))},helperText:`${O.length}/${(w=Y)==null?void 0:w.US_SPECIAL} characters used (Optional)`})]}),X&&a(Ee,{color:(Q=(de=te)==null?void 0:de.error)==null?void 0:Q.dark,sx:{marginTop:1},children:X})]}),J(ze,{children:[a(Me,{onClick:W,color:"secondary",children:"Cancel"}),a(Me,{onClick:j,color:"primary",variant:"contained",disabled:!N.trim(),children:"Save"})]})]})]})},dt=({materialID:e,selectedMaterialNumber:m,disabled:i})=>{var v;const{t:s}=we(),r=_e(),l=xe(),{updateChangeLog:U}=ye(),N=new URLSearchParams(l.search).get("RequestId"),S=ce(o=>o.payload.payloadData),O=ce(o=>o.payload),b=((v=O[e])==null?void 0:v.additionalData)||[],[X,ae]=L.useState([]);L.useEffect(()=>{F(),C()},[e]);const F=async()=>{var o,h,A;try{const d=t=>ae((t==null?void 0:t.body)||[]),q=()=>{var t;return Re((t=fe)==null?void 0:t.ERROR_FETCHING_LANGU,"error")};qe(`/${Ge}${(h=(o=Be)==null?void 0:o.DATA)==null?void 0:h.GET_LANGUAGE}`,"get",d,q)}catch{Re((A=fe)==null?void 0:A.ERROR_FETCHING_LANGU,"error")}},C=()=>{var A,d,q,t,f;const o=((d=(A=O[e])==null?void 0:A.headerData)==null?void 0:d.globalMaterialDescription)||((f=(t=(q=O[e])==null?void 0:q.additionalData)==null?void 0:t[0])==null?void 0:f.materialDescription)||"";let h=[...b];b.length?h[0]={...h[0],materialDescription:o}:h=[{id:1,language:"EN",materialDescription:o}],r(De({materialID:e,data:h}))},re=(o,h)=>{const A=b.map(d=>d.id===h.id?{...d,language:o.target.value}:d);r(De({materialID:e,data:A}))},W=(o,h)=>{const A=b.map(d=>d.id===o?{...d,materialDescription:h}:d);if(r(De({materialID:e,data:A})),N&&!oe.includes(S==null?void 0:S.RequestStatus)){const d=b.find(q=>q.id===o);d&&U({materialID:m,viewName:ue.DESCRIPTION,plantData:d.language,fieldName:"Material Description",jsonName:"materialDescription",currentValue:h,requestId:S==null?void 0:S.RequestId,childRequestId:N,isDescriptionData:!0,language:d.language})}},j=()=>{const o=Array.isArray(b)?b:[],A={id:o.length>0?Math.max(...o.map(d=>d.id))+1:1,language:"",materialDescription:"",isNew:!0};r(De({materialID:e,data:[...o,A]}))},k=o=>{if(o===1)return;const h=b.filter(A=>A.id!==o);r(De({materialID:e,data:h}))},u=L.useMemo(()=>[{field:"id",headerName:s("ID"),flex:.2,align:"center",headerAlign:"center"},{field:"language",headerName:s("Language"),flex:.4,type:"singleSelect",align:"center",headerAlign:"center",editable:!0,renderCell:o=>{const h=o.id===1,A=b.filter(d=>d.id!==o.id).map(d=>d.language);return a(Je,{fullWidth:!0,children:a(je,{sx:{height:"31px",width:"100%"},value:o.value||(h?"EN":""),onChange:d=>!h&&re(d,o),displayEmpty:!0,renderValue:d=>{var q;return d?`${d} - ${((q=X.find(t=>t.code===d))==null?void 0:q.desc)||""}`:a("span",{style:{color:te.primary.grey,fontSize:"12px"},children:"Select Language"})},disabled:h||i,children:X.map(d=>a(Ye,{value:d.code,disabled:A.includes(d.code),children:`${d.code} - ${d.desc}`},d.code))})})}},{field:"materialDescription",headerName:s("Material Description"),flex:1,align:"center",headerAlign:"center",editable:!1,renderCell:o=>a(rt,{disabled:i,params:o,handleCellEdit:W})},{field:"actions",headerName:s("Actions"),flex:.3,align:"center",headerAlign:"center",sortable:!1,renderCell:o=>(o.row.id,a(Ie,{title:o.row.isNew?s("Delete row"):s("Cannot delete existing row"),children:a("span",{children:a(Se,{onClick:()=>k(o.row.id),disabled:!o.row.isNew||i,size:"small",color:"error",children:a(Oe,{fontSize:"small"})})})}))}],[b,X,i]);return J("div",{children:[a(Fe,{sx:{width:"50%",height:"50vh"},className:"confirmOrder-lineItem",children:a(Ze,{rows:b??[],columns:u,getRowId:o=>o.id,hideFooter:!0,disableSelectionOnClick:!0,style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%"}})}),a(Z,{children:!i&&a(Me,{variant:"outlined",sx:{...Xe,mt:2},onClick:j,children:s("Add Row")})})]})},st=e=>{var o,h,A,d,q;const m=_e(),i=ce(t=>t.AllDropDown.dropDown),s=ce(t=>t.payload),r=((o=s[e.materialID])==null?void 0:o.unitsOfMeasureData)||[];let l=(A=(h=s[e==null?void 0:e.materialID])==null?void 0:h.payloadData)==null?void 0:A["Basic Data"],U=(d=s[e.materialID])==null?void 0:d.headerData;const M=((q=s[e.materialID])==null?void 0:q.UniqueAltUnit)||[],{updateChangeLog:N}=ye(),{getDtCall:S,dtData:O}=lt(),b=xe(),ae=new URLSearchParams(b.search).get("RequestId"),F=ce(t=>t.payload.payloadData),{t:C}=we(),re=L.useCallback((t,f,E)=>{var P;const D={...r==null?void 0:r.find(T=>T.id===t),[f]:E};if(f==="aUnit"){const T=(P=i==null?void 0:i.BaseUom)==null?void 0:P.find(V=>V.code===E);D.measureUnitText=(T==null?void 0:T.desc)||""}const $=r.map(T=>T.id===t?D:T);m(he({materialID:e.materialID,data:$})),ae&&!oe.includes(F==null?void 0:F.RequestStatus)&&N({materialID:e.selectedMaterialNumber,viewName:ue.ADDITIONAL_DATA,plantData:D.aUnit||"",fieldName:f,jsonName:f,currentValue:D[f],requestId:F==null?void 0:F.RequestId,childRequestId:e.requestId,isUnitOfMeasure:!0,uomId:D.UomId||null})},[r,e.materialID,e.selectedMaterialNumber,m,N]),W=(t,f)=>{const E=t!=null&&t.value?f==null?void 0:f.find(D=>D.code===t.value):null;return a(Ie,{title:(E==null?void 0:E.desc)||C("No value selected"),arrow:!0,placement:"top",children:a("div",{style:{width:"100%"},children:a($e,{options:t.field==="aUnit"?i==null?void 0:i.BaseUom:t.field==="eanCategory"?i==null?void 0:i.CategoryOfInternationalArticleNumberEAN:t.field==="unitsOfDimension"?i==null?void 0:i.BaseUom:t.field==="volumeUnit"?i==null?void 0:i.Volumeunit:t.field==="weightUnit"?i==null?void 0:i.UnitOfWt:[],value:E,onChange:D=>{re(t.row.id,t.field,D==null?void 0:D.code)},disabled:(e==null?void 0:e.disabled)||(t==null?void 0:t.field)==="eanCategory",placeholder:C("Select Option"),isOptionDisabled:D=>M.includes(D.code)})})})},j=t=>{var $;const f=t.row.aUnit===(($=l==null?void 0:l.basic)==null?void 0:$.BaseUom),E=t.field==="xValue"||t.field==="yValue",D=(e==null?void 0:e.disabled)||f&&E||(t==null?void 0:t.field)==="eanUpc";return a(Ae,{fullWidth:!0,size:"small",value:f&&E?"1":t.value||"",onChange:P=>re(t.row.id,t.field,P.target.value),disabled:D,InputProps:{sx:{"&.Mui-disabled":{"& input":{WebkitTextFillColor:te.text.primary,color:te.text.primary}}}}})},k=[{field:"id",headerName:C("ID"),width:80,hide:!0},{field:"xValue",headerName:"X",width:150,editable:!1,renderCell:j},{field:"aUnit",headerName:C("AUn"),width:150,editable:!1,renderCell:t=>W(t,i==null?void 0:i.BaseUom)},{field:"yValue",headerName:C("Y"),width:150,editable:!1,renderCell:j},{field:"eanUpc",headerName:C("EAN/UPC"),width:150,editable:!1,renderCell:j},{field:"eanCategory",headerName:C("EAN Category"),width:160,editable:!1,renderCell:t=>W(t,i==null?void 0:i.CategoryOfInternationalArticleNumberEAN)},{field:"length",headerName:C("Length"),width:120,editable:!1,renderCell:j},{field:"width",headerName:C("Width"),width:120,editable:!1,renderCell:j},{field:"height",headerName:C("Height"),width:120,editable:!1,renderCell:j},{field:"unitsOfDimension",headerName:C("Unit of Dimension"),width:160,editable:!1,renderCell:t=>W(t,i==null?void 0:i.BaseUom)},{field:"volume",headerName:C("Volume"),width:120,editable:!1,renderCell:j},{field:"volumeUnit",headerName:C("Volume Unit"),width:160,editable:!1,renderCell:t=>W(t,i==null?void 0:i.Volumeunit)},{field:"grossWeight",headerName:C("Gross Weight"),width:140,editable:!1,renderCell:j},{field:"netWeight",headerName:C("Net Weight"),width:140,editable:!1,renderCell:j},{field:"weightUnit",headerName:C("Weight Unit"),width:160,editable:!1,renderCell:t=>W(t,i==null?void 0:i.UnitOfWt)},{field:"actions",headerName:C("Actions"),width:100,sortable:!1,renderCell:t=>a(Ie,{title:t.row.isNew?"Delete row":"Cannot delete existing row",children:a("span",{children:a(Se,{onClick:()=>v(t.row.id),disabled:!t.row.isNew||e.disabled,size:"small",color:"error",children:a(Oe,{fontSize:"small"})})})})}],u=()=>{var f,E,D,$,P,T,V,w;let t={decisionTableId:null,decisionTableName:Qe.MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(f=s==null?void 0:s.payloadData)==null?void 0:f.Region,"MDG_CONDITIONS.MDG_MAT_MATERIAL_TYPE":((E=s==null?void 0:s.payloadData)==null?void 0:E.Region)==="EUR"?"ALL":(D=U==null?void 0:U.materialType)==null?void 0:D.code,"MDG_CONDITIONS.MDG_MAT_PLANT":(($=s==null?void 0:s.payloadData)==null?void 0:$.Region)==="US"?"ALL":((P=U==null?void 0:U.orgData)==null?void 0:P.length)>1?"1610":(w=(V=(T=U==null?void 0:U.orgData[0])==null?void 0:T.plant)==null?void 0:V.value)==null?void 0:w.code}]};S(t)};L.useEffect(()=>{r!=null&&r.length||u()},[]),L.useEffect(()=>{var t,f,E,D,$,P;if((f=(t=O==null?void 0:O.data)==null?void 0:t.result)!=null&&f[0]){let T=(D=(E=O==null?void 0:O.data)==null?void 0:E.result)==null?void 0:D[0].MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT;if(!T||!Array.isArray(T)){Re(($=fe)==null?void 0:$.NO_DATA_AVAILABLE,"error");return}let V=T.map((w,de)=>{var y,H,z,n;const Q=(y=i==null?void 0:i.BaseUom)==null?void 0:y.find(c=>c.code===w.MDG_MAT_UOM),_=w.MDG_MAT_UOM===((H=l==null?void 0:l.basic)==null?void 0:H.BaseUom);return{id:de+1,uomId:null,xValue:"1",aUnit:w.MDG_MAT_UOM||"",measureUnitText:(Q==null?void 0:Q.desc)||"",yValue:"1",bUnit:w.MDG_MAT_UOM||"",measurementUnitText:((n=(z=l==null?void 0:l.basic)==null?void 0:z.BaseUom)==null?void 0:n.desc)||"",eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:w.MDG_MAT_LENGTH||"",width:w.MDG_MAT_WIDTH||"",height:w.MDG_MAT_HEIGHT||"",unitsOfDimension:w.MDG_MAT_UNIT_DIMENSIONS||"",volume:w.MDG_MAT_VOLUME||"",volumeUnit:w.MDG_MAT_VOLUME_UNIT||"",grossWeight:w.MDG_MAT_GROSS_NET_WEIGHT||"",netWeight:"",weightUnit:w.MDG_MAT_WEIGHT_UNIT||"",noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""}});V=V.sort((w,de)=>{var Q,_;return w.aUnit===((Q=l==null?void 0:l.basic)==null?void 0:Q.BaseUom)?-1:de.aUnit===((_=l==null?void 0:l.basic)==null?void 0:_.BaseUom)?1:0}),JSON.stringify(V)!==JSON.stringify((P=s[e.materialID])==null?void 0:P.unitsOfMeasureData)&&m(he({materialID:e==null?void 0:e.materialID,data:V}))}},[O]),L.useEffect(()=>{if(r!=null&&r.length){const t=[...new Set(r.map(f=>f.aUnit).filter(Boolean))];m(Ke({materialID:e==null?void 0:e.materialID,data:t}))}},[r]);const v=t=>{const f=r.filter(E=>E.id!==t);m(he({materialID:e==null?void 0:e.materialID,data:f}))};return a("div",{children:a(Z,{container:!0,direction:"row",sx:{backgroundColor:"white",padding:2},children:J(Z,{item:!0,xs:12,mt:4,children:[a(We,{rows:r,columns:k,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!1,disableSelectionOnClick:!0,onCellEditCommit:t=>{re(t.id,t.field,t.value)},width:"100%",title:C("Units of Measure/ EANs/ Dimensions"),showSearch:!1,showRefresh:!1,showExport:!1,showFilter:!0,showColumns:!0}),!(e!=null&&e.disabled)&&a(Me,{variant:"outlined",sx:{mt:2},onClick:()=>{const f={id:r.length>0?Math.max(...r.map(E=>E.id))+1:1,isNew:!0,uomId:null,xValue:"1",aUnit:"",measureUnitText:"",yValue:"1",bUnit:"",measurementUnitText:"",eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:"",width:"",height:"",unitsOfDimension:"",volume:"",volumeUnit:"",grossWeight:"",netWeight:"",weightUnit:"",noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""};m(he({materialID:e==null?void 0:e.materialID,data:[...r,f]}))},children:C("Add Row")})]})})})},ke=({value:e,onChange:m,disabled:i})=>a(tt,{checked:e||!1,onChange:s=>m(s.target.checked),disabled:i}),ct=({value:e,onChange:m,disabled:i,maxLength:s})=>a(Ae,{variant:"outlined",fullWidth:!0,size:"small",value:e||"",onChange:r=>{const l=r.target.value;/^\d*$/.test(l)&&(!s||l.length<=s)&&m(l)},disabled:i,inputProps:{maxLength:s,pattern:"[0-9]*",inputMode:"numeric"},sx:{"& .MuiInputBase-input":{color:te.black.dark,fontSize:"12px"},"& .MuiInputBase-input.Mui-disabled":{WebkitTextFillColor:te.black.dark,color:te.black.dark},"& .MuiOutlinedInput-root":{"&.Mui-disabled":{"& > input":{WebkitTextFillColor:te.black.dark,color:te.black.dark}}}}}),ve=({options:e=[],value:m,onChange:i,disabled:s,placeholder:r,isOptionDisabled:l})=>{const U=e.find(N=>N.code===m),M=U?`${U.code} - ${U.desc||""}`:"";return a($e,{options:e,value:M,onChange:i,disabled:s,placeholder:r,isOptionDisabled:l})},ot=(e,m)=>{var i,s;if(m===((i=ee)==null?void 0:i.EUR))switch(e){case le.EA:return Ue.IE;case le.CA:case le.CT:case le.PA:return Ue.IC;default:return""}else if(m===((s=ee)==null?void 0:s.US))switch(e){case le.EA:return Ue.UC;case le.CA:return Ue.MB;case le.CT:return Ue.MB;default:return""}return""},be=e=>{switch(e){case le.CA:return"7";case le.CT:return"3";case le.PA:return"4";default:return null}},ut=(e,m)=>{const i=be(e);return i?m.startsWith(i):!0},ht=e=>{var $,P,T,V,w,de,Q,_,y,H,z;const m=_e(),i=!1,s=ce(n=>n.AllDropDown.dropDown),r=ce(n=>n.payload),l=(($=r[e.materialID])==null?void 0:$.eanData)||[],U=((P=r[e.materialID])==null?void 0:P.UniqueAltUnit)||[],M=((T=r[e.materialID])==null?void 0:T.unitsOfMeasureData)||[],[N,S]=L.useState(((V=r[e.materialID])==null?void 0:V.ManufacturerID)||""),O=((w=r==null?void 0:r.payloadData)==null?void 0:w.Region)||"";let b=(Q=(de=r[e==null?void 0:e.materialID])==null?void 0:de.payloadData)==null?void 0:Q["Basic Data"];const X=(_=b==null?void 0:b.basic)==null?void 0:_.BaseUom,ae=L.useRef(null),F=L.useRef(null),C=L.useRef(null),re=xe(),{updateChangeLog:W}=ye(),k=new URLSearchParams(re.search).get("RequestId"),u=ce(n=>n.payload.payloadData),v=n=>{m(at({materialID:e.materialID,data:n}))},h=!((y=r[e.materialID])==null?void 0:y.ManufacturerID),{t:A}=we(),d=(n,c,g)=>{var x,R,ne,ie,se,Ce;if(c===((x=me)==null?void 0:x.EAN_UPC)){const G=l.find(I=>I.id===n),K=G.altunit,B=l.some(I=>I.id!==n&&I.altunit===K&&I.MainEan===!0),ge=l.map(I=>I.id===n?{...I,[c]:g,au:!1,MainEan:B?!1:!!g}:I);if(v(ge),M.length>0&&g&&!B){const I=M.map(p=>p.aUnit===K?{...p,eanUpc:g,eanCategory:G.eanCategory}:p);JSON.stringify(I)!==JSON.stringify(M)&&m(he({materialID:e.materialID,data:I}))}k&&!oe.includes(u==null?void 0:u.RequestStatus)&&W({materialID:e.materialID,viewName:ue.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"EanUpc",jsonName:(R=me)==null?void 0:R.EAN_UPC,currentValue:g,requestId:u==null?void 0:u.RequestId,isAdditionalEAN:!0,eanId:G.EanId||null,childRequestId:k})}else if(c==="MainEan"&&g===!0){const G=l.find(I=>I.id===n),K=G.altunit,ge=l.map(I=>I.altunit===K?{...I,MainEan:!1}:I).map(I=>I.id===n?{...I,MainEan:!0}:I);if(v(ge),M.length>0){const I=M.map(p=>p.aUnit===K?{...p,eanUpc:G.eanUpc,eanCategory:G.eanCategory}:p);JSON.stringify(I)!==JSON.stringify(M)&&m(he({materialID:e.materialID,data:I}))}k&&!oe.includes(u==null?void 0:u.RequestStatus)&&W({materialID:e.materialID,viewName:ue.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"MainEan",jsonName:(ne=me)==null?void 0:ne.MAIN_EAN,currentValue:g,requestId:u==null?void 0:u.RequestId,childRequestId:k,isAdditionalEAN:!0,eanId:G.EanId||null})}else if(c==="eanCategory"){const G=l.find(B=>B.id===n),K=l.map(B=>B.id===n?{...B,[c]:g,MainEan:!1}:B);v(K),k&&!oe.includes(u==null?void 0:u.RequestStatus)&&W({materialID:e.materialID,viewName:ue.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"EanCat",jsonName:(ie=me)==null?void 0:ie.EAN_CATEGORY,currentValue:g,requestId:u==null?void 0:u.RequestId,childRequestId:k,isAdditionalEAN:!0,eanId:G.EanId||null})}else if(c==="au"&&g===!0){const G=l.find(K=>K.id===n);if(G.eanUpc){const K=ge=>{const I=l.map(p=>p.id===n?{...p,[c]:g,eanUpc:p.eanUpc+ge.body,MainEan:!1}:p);v(I),k&&!oe.includes(u==null?void 0:u.RequestStatus)&&W({materialID:e.materialID,viewName:ue.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"other",jsonName:"au",currentValue:g,requestId:u==null?void 0:u.RequestId,childRequestId:k,isAdditionalEAN:!0,eanId:G.EanId||null})},B=ge=>{var I;Re((I=fe)==null?void 0:I.ERROR_FETCHING_DATA,"error")};qe(`/${Ge}${(Ce=(se=Be)==null?void 0:se.DATA)==null?void 0:Ce.GET_CHECK_DIGIT}?number=${G.eanUpc}`,"get",K,B,{eanUpc:G.eanUpc})}}else{const G=l.map(B=>B.id===n?{...B,[c]:g}:B),K=l.find(B=>B.id===n);v(G),k&&!oe.includes(u==null?void 0:u.RequestStatus)&&W({materialID:e.materialID,viewName:ue.ADDITIONAL_EAN_DATA,plantData:"",fieldName:c,jsonName:c,currentValue:g,requestId:u==null?void 0:u.RequestId,childRequestId:k,isAdditionalEAN:!0,eanId:K.EanId||null})}},q=n=>{const c=l.filter(g=>g.id!==n);v(c)},t=()=>{const n={id:Te(),EanId:null,altunit:"",MainEan:!1,eanUpc:"",eanCategory:"",au:!1,isNew:!0};v([...l,n])};L.useEffect(()=>{if(U.length){const n=l.filter(R=>U.includes(R.altunit)||R.altunit===""),c=n.map(R=>R.altunit),x=U.filter(R=>!c.includes(R)).map(R=>({id:Te(),EanId:null,altunit:R,MainEan:!1,eanUpc:"",eanCategory:ot(R,O),au:!1}));v([...n,...x])}},[U]),L.useEffect(()=>{var c;const n=(c=r[e.materialID])==null?void 0:c.ManufacturerID;if(n&&X&&!C.current&&!l.some(x=>x.altunit===X&&x.eanUpc===n)){const x={id:Te(),EanId:null,altunit:X,MainEan:!0,eanUpc:n,eanCategory:"MI",au:!1},R=l.map(ie=>({...ie,MainEan:!1})),ne=[x,...R];if(v(ne),M.length>0){const ie=M.map(se=>se.aUnit===X?{...se,eanUpc:n,eanCategory:"MI"}:se);JSON.stringify(ie)!==JSON.stringify(M)&&m(he({materialID:e.materialID,data:ie}))}}C.current=n},[(H=r[e.materialID])==null?void 0:H.ManufacturerID,X]);const f=[{field:"altunit",headerName:A("Alt. Unit"),width:200,renderCell:n=>a(ve,{options:(s==null?void 0:s.BaseUom)||[],value:n.row.altunit,onChange:c=>d(n.row.id,"altunit",c==null?void 0:c.code),disabled:(e==null?void 0:e.disabled)||h,placeholder:"SELECT Alt. Unit",isOptionDisabled:c=>!U.includes(c.code)})},{field:"eanUpc",headerName:A("EAN/UPC"),width:180,renderCell:n=>{var g,x;const c=O===((g=ee)==null?void 0:g.EUR)&&n.row.eanUpc&&be(n.row.altunit)&&!ut(n.row.altunit,n.row.eanUpc);return J("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[a(ct,{value:n.row[(x=me)==null?void 0:x.EAN_UPC],onChange:R=>{var ne;return d(n.row.id,(ne=me)==null?void 0:ne.EAN_UPC,R)},disabled:(e==null?void 0:e.disabled)||h,maxLength:13,onClick:R=>R.stopPropagation(),onDoubleClick:R=>R.stopPropagation()}),c&&a(Ie,{title:`For ${n.row.altunit}, EAN/UPC must start with ${be(n.row.altunit)}`,arrow:!0,children:a(pe,{sx:{color:"warning.main",fontSize:"20px"}})})]})}},{field:"eanCategory",headerName:A("EAN Cat."),width:200,renderCell:n=>a(ve,{options:(s==null?void 0:s.EanCat)||[],value:n.row.eanCategory,onChange:c=>d(n.row.id,"eanCategory",c==null?void 0:c.code),disabled:(e==null?void 0:e.disabled)||h,placeholder:"SELECT EAN Cat.",isOptionDisabled:c=>l.filter(x=>x.altunit===n.row.altunit&&x.id!==n.row.id).some(x=>x.eanCategory===c.code)})},{field:"au",headerName:A("Au"),width:150,renderCell:n=>a(ke,{value:n.row.au,onChange:c=>d(n.row.id,"au",c),disabled:(e==null?void 0:e.disabled)||n.row.au||h})},{field:"MainEan",headerName:A("Main EAN"),width:160,renderCell:n=>a(ke,{value:n.row.MainEan,onChange:c=>d(n.row.id,"MainEan",c),disabled:(e==null?void 0:e.disabled)||h})},{field:"actions",headerName:A("Actions"),width:180,renderCell:n=>a(Ie,{title:n.row.isNew?"Delete row":"Cannot delete existing row",children:a("span",{children:a(Se,{onClick:()=>q(n.row.id),disabled:(e==null?void 0:e.disabled)||h||!n.row.isNew,color:"error",size:"small",children:a(Oe,{fontSize:"small"})})})})}],E=n=>{const c=n.target.value;S(c),F.current&&clearTimeout(F.current),F.current=setTimeout(()=>{var g;(g=ae.current)==null||g.blur()},800)},D=async()=>{N!=null&&N.trim()};return a("div",{children:a(Z,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:.25,...et},children:J(Z,{container:!0,display:"block",sx:{marginLeft:"-10px"},children:[a(Z,{item:!0,xs:4,children:J(Z,{container:!0,display:"flex",flexDirection:"column",children:[J(Ee,{children:[A("Manufacturer ID / GS1 Prefix"),"  ",O===((z=ee)==null?void 0:z.EUR)&&a("span",{style:{color:te.error.darkRed,marginLeft:"2px"},children:"*"})]}),a(Ae,{name:"manufacturerId",variant:"outlined",fullWidth:!0,size:"small",value:N,onChange:E,onBlur:D,inputRef:ae})]})}),J(Z,{item:!0,xs:10,mt:4,children:[a(We,{title:"Additional EANs/Units of Measure",isLoading:i,rows:l,columns:f,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:["eanUpc","action"],status_onRowDoubleClick:!0,width:"100%"}),!(e!=null&&e.disabled)&&a(Me,{variant:"outlined",sx:{mt:2},onClick:t,disabled:!(M!=null&&M.length)||h,children:A("Add Row")})]})]})})})},It=e=>{const{t:m}=we(),[i,s]=L.useState(0),r=window.location.href.includes("DisplayMaterialSAPView"),l=[m("Description"),m("Units of Measure"),...r?[]:[m("Additional EANs")]],U=[[a(Ne,{children:a(dt,{materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,disabled:e.disableCheck})})],[a(Ne,{children:a(st,{materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,disabled:e.disableCheck})})],...r?[]:[[a(Ne,{children:a(ht,{materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,disabled:e.disableCheck})})]]],M=(N,S)=>{s(S)};return a("div",{children:a(Z,{container:!0,style:{...Le,backgroundColor:"#FAFCFF"},children:a(Z,{sx:{width:"inherit"},children:a(Z,{container:!0,style:{padding:"0 1rem 0 1rem"},children:a(Z,{container:!0,sx:Le,children:a(Z,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:J(Z,{container:!0,children:[a(nt,{value:i,onChange:M,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:l.map((N,S)=>a(it,{sx:{fontSize:"12px",fontWeight:"700"},label:N},S))}),U[i].map((N,S)=>a(Fe,{sx:{mb:2,width:"100%"},children:a(Ee,{variant:"body2",children:N})},S))]})})})})})})})};export{It as default};
