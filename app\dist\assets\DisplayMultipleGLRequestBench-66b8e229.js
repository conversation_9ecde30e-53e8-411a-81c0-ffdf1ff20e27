import{b as Be,l as Le,r as h,i as g,u as Te,ig as Fe,j as s,$ as u,hp as Ge,a as l,i7 as Me,T as y,ia as $e,B as L,g_ as $,id as Oe,ie as qe,ic as _e,h_ as We,gZ as Ve,hE as re,a1 as b,gW as T,P as de,ip as F,w as x,io as f,hn as ze,W as Ie,hv as He,i8 as Ue,gU as ae,i9 as Ze,ib as Je,hP as C}from"./index-fdfa25a0.js";import{E as Ke}from"./EditFieldForMassGL-4415ec49.js";const we=()=>{var X,Y,w,P,R,k,D,ee,ne,oe,te,le,ce;const O=Be(),p=Le();h.useState({});const[q,_]=h.useState(0);h.useState([]);const[A,se]=h.useState(!1),[Qe,ue]=h.useState(!0);h.useState([]),h.useState([]),h.useState([]);const[m,N]=h.useState(0);h.useState(),g(e=>e.tabsData);const[he,W]=h.useState(!1),[V,pe]=h.useState([]),[ge,z]=h.useState(!1),G=Te(),v=g(e=>e.generalLedger.MultipleGLRequestBench),S=G.state.tabsData,o=G.state.rowData,a=G.state.requestbenchRowData;let n=g(e=>{var c;return(c=e==null?void 0:e.initialData)==null?void 0:c.IWMMyTask});g(e=>e.payload);let I=g(e=>e.edit.payload),H=g(e=>e.generalLedger.requiredFields);console.log(n==null?void 0:n.body,"taskData"),g(e=>e.profitCenter.profitCenterCompCodes);let t=g(e=>e.userManagement.taskData);g(e=>e.generalLedger.MultipleGLData);let M=g(e=>e.userManagement.userData),E="";(t==null?void 0:t.processDesc)==="Mass Change"?(E=t!=null&&t.subject?(X=t==null?void 0:t.subject)==null?void 0:X.slice(3):a==null?void 0:a.requestId.slice(3),(Y=n==null?void 0:n.body)!=null&&Y.companyCode||o.controllingArea,(w=t==null?void 0:t.body)!=null&&w.glAccount||o.profitCenter):(t==null?void 0:t.processDesc)==="Mass Create"?(E=t!=null&&t.subject?(P=t==null?void 0:t.subject)==null?void 0:P.slice(3):a==null?void 0:a.requestId.slice(3),(R=n==null?void 0:n.body)!=null&&R.companyCode||o.controllingArea,(k=t==null?void 0:t.body)!=null&&k.glAccount||o.glAccount):(a==null?void 0:a.requestType)==="Mass Create"?(E=t!=null&&t.subject?(D=t==null?void 0:t.subject)==null?void 0:D.slice(3):a==null?void 0:a.requestId.slice(3),(ee=n==null?void 0:n.body)!=null&&ee.companyCode||o.companyCode,(ne=t==null?void 0:t.body)!=null&&ne.glAccount||o.glAccount):(a==null?void 0:a.requestType)==="Mass Change"&&(E=t!=null&&t.subject?(oe=t==null?void 0:t.subject)==null?void 0:oe.slice(3):a==null?void 0:a.requestId.slice(3),(te=n==null?void 0:n.body)!=null&&te.companyCode||o.companyCode,(le=t==null?void 0:t.body)!=null&&le.glAccount||o.glAccount);for(let e=0;e<(v==null?void 0:v.length);e++)if(v[e].GLAccount===o.glAccount){v[e];break}const ye=(e,c)=>{setActiveTab(c)},U=()=>{const e=Q();A?e?(N(c=>c-1),p(F())):J():(N(c=>c-1),p(F()))},Z=()=>{const e=Q();A?e?(N(c=>c+1),p(F())):J():(N(c=>c+1),p(F()))},J=()=>{z(!0)},j=Object.entries(S==null?void 0:S.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]),B=Object.entries(S.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),K={};B.map(e=>{e.forEach((c,i)=>{c.forEach((d,r)=>{r!==0&&d.forEach(ie=>{K[ie.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=ie.value})})})});const me=()=>{var i,d;const e=r=>{p(C({keyName:"TaxCategory",data:r.body}))},c=r=>{console.log(r)};x(`/${f}/data/getTaxCategory?companyCode=${(i=n==null?void 0:n.body)!=null&&i.CompCode?(d=n==null?void 0:n.body)==null?void 0:d.CompCode:o==null?void 0:o.companyCode}`,"get",e,c)},be=()=>{var i,d;const e=r=>{p(C({keyName:"HouseBank",data:r.body}))},c=r=>{console.log(r)};x(`/${f}/data/getHouseBank?companyCode=${(i=n==null?void 0:n.body)!=null&&i.CompCode?(d=n==null?void 0:n.body)==null?void 0:d.CompCode:o==null?void 0:o.companyCode}`,"get",e,c)},xe=()=>{var i,d;const e=r=>{p(C({keyName:"FieldStatusGroup",data:r.body}))},c=r=>{console.log(r)};x(`/${f}/data/getFieldStatusGroup?companyCode=${(i=n==null?void 0:n.body)!=null&&i.CompCode?(d=n==null?void 0:n.body)==null?void 0:d.CompCode:o==null?void 0:o.companyCode}`,"get",e,c)},fe=()=>{var i,d;const e=r=>{p(C({keyName:"GroupAccountNumber",data:r.body}))},c=r=>{console.log(r)};x(`/${f}/data/getGroupAccountNumber?chartAccount=${(i=n==null?void 0:n.body)!=null&&i.chartOfAccount?(d=n==null?void 0:n.body)==null?void 0:d.chartOfAccount:o==null?void 0:o.chartOfAccount}`,"get",e,c)},Ce=()=>{var i,d;const e=r=>{p(C({keyName:"AlternativeAccountNumber",data:r.body}))},c=r=>{console.log(r)};x(`/${f}/data/getAlternativeAccountNumber?chartAccount=${(i=n==null?void 0:n.body)!=null&&i.chartOfAccount?(d=n==null?void 0:n.body)==null?void 0:d.chartOfAccount:o==null?void 0:o.chartOfAccount}`,"get",e,c)},Ae=()=>{var i,d;const e=r=>{p(C({keyName:"AccountGroup",data:r.body}))},c=r=>{console.log(r)};x(`/${f}/data/getAccountGroupCodeDesc?chartAccount=${(i=n==null?void 0:n.body)!=null&&i.chartOfAccount?(d=n==null?void 0:n.body)==null?void 0:d.chartOfAccount:o==null?void 0:o.chartOfAccount}`,"get",e,c)},ve=()=>{var i,d;const e=r=>{p(C({keyName:"CostElementCategory",data:r.body}))},c=r=>{console.log(r)};x(`/${f}/data/getCostElementCategory?accountType=${(i=n==null?void 0:n.body)!=null&&i.accountType?(d=n==null?void 0:n.body)==null?void 0:d.accountType:o==null?void 0:o.accountType}`,"get",e,c)};h.useEffect(()=>{me(),xe(),be(),Ae(),Ce(),fe(),ve()},[]);const Se=()=>{se(!0),ue(!1)},Ee=e=>{W(e)},Ne=()=>{W(!0)};h.useEffect(()=>{p(Fe(K))},[]),console.log(I,H,"requiredFieldTabWise");const Q=()=>ze(I,H,pe),je=()=>{z(!1)};return console.log("tabcontents",S),s("div",{children:[s(u,{container:!0,style:{...Ge,backgroundColor:"#FAFCFF"},children:[V.length!=0&&l(Me,{openSnackBar:ge,alertMsg:"Please fill the following Field: "+V.join(", "),handleSnackBarClose:je}),s(u,{sx:{width:"inherit"},children:[s(u,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[l(u,{style:{display:"flex",justifyContent:"flex-end"},children:l(Ie,{color:"primary","aria-label":"upload picture",component:"label",sx:He,children:l(Ue,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{O(-1)}})})}),s(u,{md:10,children:[l(y,{variant:"h3",children:s("strong",{children:["Multiple General Ledgers: ",o.glAccount," "]})}),l(y,{variant:"body2",color:"#777",children:"This view displays the detail of Multiple General Ledgers"})]}),l(u,{md:1,sx:{display:"flex",justifyContent:"flex-end",marginRight:"4px"},children:l(b,{variant:"outlined",size:"small",sx:ae,onClick:Ne,title:"Chnage Log",children:l(Ze,{sx:{padding:"2px"},fontSize:"small"})})}),he&&l($e,{open:!0,closeModal:Ee,requestId:E,requestType:"Mass",pageName:"generalLedger",controllingArea:o.companyCode,centerName:o.glAccount}),A?"":(M==null?void 0:M.role)==="Finance"?l(u,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:l(u,{item:!0,children:s(b,{variant:"outlined",size:"small",sx:ae,onClick:Se,children:["Change",l(Je,{sx:{padding:"2px"},fontSize:"small"})]})})}):""]}),l(u,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:s(L,{width:"70%",sx:{marginLeft:"40px"},children:[l(u,{item:!0,sx:{paddingTop:"2px !important"},children:s($,{flexDirection:"row",children:[l("div",{style:{width:"15%"},children:l(y,{variant:"body2",color:"#777",children:"General Ledger Account"})}),s(y,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",o==null?void 0:o.glAccount]})]})}),l(u,{item:!0,sx:{paddingTop:"2px !important"},children:s($,{flexDirection:"row",children:[l("div",{style:{width:"15%"},children:l(y,{variant:"body2",color:"#777",children:"Company Code"})}),s(y,{variant:"body2",fontWeight:"bold",children:[": ",o==null?void 0:o.companyCode]})]})}),l(u,{item:!0,sx:{paddingTop:"2px !important"},children:s($,{flexDirection:"row",children:[l("div",{style:{width:"15%"},children:l(y,{variant:"body2",color:"#777",children:"Chart of Account"})}),s(y,{variant:"body2",fontWeight:"bold",children:[": ",o==null?void 0:o.chartOfAccount]})]})})]})}),s(u,{container:!0,style:{padding:"16px"},children:[l(_e,{activeStep:m,onChange:ye,variant:"scrollable",sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:j.map((e,c)=>l(Oe,{children:l(qe,{sx:{fontWeight:"700"},children:e})},e))}),l(u,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:B&&((ce=B[m])==null?void 0:ce.map((e,c)=>l(L,{sx:{width:"100%"},children:s(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...We},children:[l(u,{container:!0,children:l(y,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),l(L,{children:l(L,{sx:{width:"100%"},children:l(Ve,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:l(u,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(i=>(console.log("inneritem",e[1]),l(Ke,{activeTabIndex:m,fieldGroup:e[0],selectedRowData:o.glAccount,pcTabs:j,label:i.fieldName,value:i.value,length:i.maxLength,visibility:i.visibility,onSave:d=>handleFieldSave(i.fieldName,d),isEditMode:A,type:i.fieldType,field:i})))})})})})]})},c)))},B)]})]})]}),A?l(de,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(re,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:q,onChange:e=>{_(e)},children:[l(b,{size:"small",variant:"contained",onClick:()=>{O(-1)},children:"Save"}),l(b,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:U,disabled:m===0,children:"Back"}),l(b,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:Z,disabled:m===j.length-1,children:"Next"})]})}):l(de,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(re,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:q,onChange:e=>{_(e)},children:[l(b,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:U,disabled:m===0,children:"Back"}),l(b,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:Z,disabled:m===j.length-1,children:"Next"})]})})]})};export{we as default};
