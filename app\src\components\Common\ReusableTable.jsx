import * as React from "react";
import {
  DataGrid,
  GridToolbarColumnsButton,
  GridToolbarContainer,
  GridToolbarExport,
  GridToolbarFilterButton,
  GridToolbarDensitySelector,
} from "@mui/x-data-grid";
import { 
  LinearProgress, 
  TablePagination, 
  Typography, 
  Paper,
  IconButton,
  Tooltip,
  Chip,
  Stack,
  TextField,
  InputAdornment,
} from "@mui/material";
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  ViewColumn as ViewColumnIcon,
  FilterList as FilterListIcon,
  Download as DownloadIcon,
  TableRows as TableRowsIcon,
  IosShare
} from '@mui/icons-material';
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setHistoryPath } from "../../app/utilitySlice";
import { styled, useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import SearchBar from "@components/Common/SearchBar";
import { saveExcel } from '../../functions';
import moment from 'moment';
import LastPageIcon from "@mui/icons-material/LastPage";
import useLang from "../../hooks/useLang";

// Enhanced Loading Overlay
const StyledLoadingOverlay = styled('div')(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'rgba(255, 255, 255, 0.7)',
  zIndex: 1,
}));

function CustomLoadingOverlay() {
  const theme = useTheme();

  return (
    <StyledLoadingOverlay>
      <LinearProgress
        sx={{
          width: '200px',
          '& .MuiLinearProgress-bar': {
            backgroundColor: theme.palette.primary.main,
          }
        }}
      />
      <Typography
        variant="body2"
        sx={{ mt: 2, color: theme.palette.text.secondary }}
      >
        Loading data...
      </Typography>
    </StyledLoadingOverlay>
  );
}


const StyledGridToolbar = styled(GridToolbarContainer)(({ theme }) => ({
  padding: "12px 20px",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  borderBottom: `1px solid ${theme.palette.grey[200]}`,
  backgroundColor: theme.palette.primary.light,
  gap: '16px',
}));

const ToolbarButton = styled(IconButton)(({ theme }) => ({
  color: theme.palette.primary.main,
  '&:hover': {
    backgroundColor: `${theme.palette.primary.light}40`,
  },
}));

function CustomToolbar({
  title,
  onRefresh,
  onSearch,
  totalRows,
  selectedRows,
  showSearch = false,
  showRefresh = false,
  showSelectedCount = false,
  showExport = false,
  showFilter = true,
  showColumns = true,
  checkboxSelection = false,
  module,
  columns,
  rows
}) {
  const { t } = useLang();
  const theme = useTheme();
  
  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = columns
        .filter(col => 
          !col.field.startsWith('__') &&
          col.field !== 'actions' &&
          !col.hide &&
          col.headerName
        )
        .map(col => ({
          header: col.headerName,
          key: col.field,
          width: col.width ? Math.min(Math.ceil(col.width / 6), 50) : 20,
          style: { 
            alignment: { horizontal: col.align || 'left', vertical: 'middle' },
          }
        }));

      const cleanedRows = rows.map(row => {
        const cleanRow = {};
        excelColumns.forEach(col => {
          let value = row[col.key];
          if (value && !isNaN(Date.parse(value))) {
            try {
              value = moment(value).format("DD-MMM-YYYY");
            } catch (e) {
            }
          }
          cleanRow[col.key] = value || '';
        });
        return cleanRow;
      });

      saveExcel({
        fileName: `${title || 'Table Data'}-${moment(new Date()).format("DD-MMM-YYYY")}`,
        columns: excelColumns,
        rows: cleanedRows,
      });
    },
  };

  return (
    <StyledGridToolbar>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography variant="h6" sx={{ fontSize: "0.875rem", fontWeight: 600 }}>
          {t(title)}
        </Typography>
        <Chip
          label={`${totalRows} ${t("records")}`}
          size="small"
          sx={{
            backgroundColor: theme.palette.primary.light,
            color: theme.palette.primary.main,
            height: '24px',
          }}
        />
        {checkboxSelection && showSelectedCount && selectedRows > 0 && (
          <Chip
            label={`${selectedRows} ${t("selected")}`}
            size="small"
            sx={{
              backgroundColor: theme.palette.secondary.light,
              color: theme.palette.secondary.main,
              height: '24px',
            }}
          />
        )}
      </Box>

      <Stack direction="row" spacing={1} alignItems="center">
        {showSearch && (
          <SearchBar
            title={t("Search for Table data")}
            message={`${t("Search")}...`}
            handleSearchAction={onSearch}
            module={module}
            keyName="search"
          />
        )}
        
        {showRefresh && (
          <Tooltip title={t("Refresh")}>
            <ToolbarButton size="small" sx={{ padding: '4px' }}>
              <RefreshIcon onClick={onRefresh} sx={{ fontSize: '1.2rem' }} />
            </ToolbarButton>
          </Tooltip>
        )}

        {showFilter && (
          <Tooltip title={t("Show Filters")}>
            <ToolbarButton size="small" sx={{ padding: '4px' }}>
              <GridToolbarFilterButton sx={{ padding: 0 }} />
            </ToolbarButton>
          </Tooltip>
        )}

        {showColumns && (
          <Tooltip title={t("Columns")}>
            <ToolbarButton size="small" sx={{ padding: '4px' }}>
              <GridToolbarColumnsButton sx={{ padding: 0 }} />
            </ToolbarButton>
          </Tooltip>
        )}

        {showExport && (
          <Tooltip title={t("Export")}>
            <ToolbarButton 
              size="small" 
              sx={{ 
                padding: '4px',
                display: 'flex',
                alignItems: 'center',
                gap: '0.4rem'
              }}
              onClick={functions_ExportAsExcel.convertJsonToExcel}
            >
              <IosShare fontSize="small" sx={{ marginBottom: '4px' }} />
              <Typography sx={{ fontSize: '0.8125rem' }} variant="body2">{t("Export")}</Typography>
            </ToolbarButton>
          </Tooltip>
        )}
      </Stack>
    </StyledGridToolbar>
  );
}

const StyledDataGridContainer = styled(Paper)(({ theme }) => ({
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  borderRadius: '16px',
  overflow: 'hidden',
  border: `1px solid ${theme.palette.grey[200]}`,
  '& .MuiDataGrid-root': {
    border: 'none',
    '& .MuiDataGrid-cell': {
      borderBottom: `1px solid ${theme.palette.grey[100]}`,
      padding: '8px 16px',
      '&:focus': {
        outline: 'none',
      },
      '&:focus-within': {
        outline: `2px solid ${theme.palette.primary.main}`,
        outlineOffset: '-2px',
      },
    },
    '& .MuiDataGrid-columnHeaders': {
      backgroundColor: theme.palette.grey[50],
      borderBottom: `2px solid ${theme.palette.grey[200]}`,
      '& .MuiDataGrid-columnHeader': {
        padding: '12px 16px',
        '&:focus': {
          outline: 'none',
        },
        '&:focus-within': {
          outline: `2px solid ${theme.palette.primary.main}`,
          outlineOffset: '-2px',
        },
      },
    },
    '& .MuiDataGrid-row': {
      '&:nth-of-type(even)': {
        backgroundColor: theme.palette.grey[25] || theme.palette.grey[50],
      },
      '&:hover': {
        backgroundColor: `${theme.palette.primary.light}40`,
      },
      '&.Mui-selected': {
        backgroundColor: `${theme.palette.primary.main}20`,
        '&:hover': {
          backgroundColor: `${theme.palette.primary.main}30`,
        },
      },
    },
  },
}));


const StyledNoRowsOverlay = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  height: '100%',
  padding: '32px',
  backgroundColor: theme.palette.grey[25] || theme.palette.grey[50],
  '& svg': {
    color: theme.palette.text.secondary,
    fontSize: '48px',
    marginBottom: '16px',
  },
}));

function CustomNoRowsOverlay() {
  const { t } = useLang();
  const theme = useTheme();

  return (
    <StyledNoRowsOverlay>
      <TableRowsIcon />
      <Typography
        variant="h6"
        sx={{
          color: theme.palette.text.secondary,
          fontWeight: 500,
          marginBottom: '8px',
        }}
      >
        {t("No Data Available")}
      </Typography>
      <Typography
        variant="body2"
        sx={{
          color: theme.palette.text.secondary,
          textAlign: 'center',
          opacity: 0.7,
        }}
      >
        {t("There are no records to display at the moment")}
      </Typography>
    </StyledNoRowsOverlay>
  );
}

export default function ReusableDataTable({
  field_name,
  url_onRowClick,
  stopPropagation_Column,
  redirecOnDoubleClick = null,
  status_onRowDoubleClick = false,
  status_onRowSingleClick = false,
  title,
  getRowIdValue,
  getRowHeight,
  rowHeight = 50,
  rows,
  columns,
  hideFooter,
  checkboxSelection,
  disableSelectionOnClick,
  onRowsSelectionHandler = () => {},
  showConfig,
  setShowWork,
  fieldName_onCellClick,
  onEditCellPropsChange,
  experimentalFeatures,
  isRowSelectable,
  module,
  isLoading,
  paginationLoading,
  rowsPerPageOptions,
  noOfColumns,
  callback_onRowDoubleClick = null,
  callback_onRowSingleClick = null,
  sortModel,
  onSortModelChange,
  pageSize,
  setPageSize,
  page,
  rowCount,
  onPageChange,
  onPageSizeChange,
  showCustomNavigation,
  handleCheckValidationError,
  width,
  tempheight='calc(100vh - 250px)',
  selectionModel,
  onCellEditCommit = () => {},
  onRefresh,
  onSearch,
  showSearch = false,
  showRefresh = false,
  showSelectedCount = false,
  showExport = false,
  showFilter = true,
  showColumns = true,
  showFirstPageoptions,
  showSelectAllOptions,
  onSelectAllOptions
}) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const theme = useTheme();
  const [selectedCount, setSelectedCount] = useState(0);


  return (
    <StyledDataGridContainer>
      <DataGrid
        autoHeight={false}
        style={{ height: tempheight, width: width }}
        loading={isLoading}
        getRowId={(param) => (getRowIdValue ? param[getRowIdValue] : "id")}
        rows={rows}
        columns={columns}
        pageSize={pageSize}
        page={page}
        rowCount={rowCount}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        rowsPerPageOptions={rowsPerPageOptions ?? [10, 25, 50]}
        disableExtendRowFullWidth={false}
        hideFooter={hideFooter}
        sortModel={sortModel}
        onSortModelChange={onSortModelChange}
        selectionModel={selectionModel}
        checkboxSelection={checkboxSelection}
        disableSelectionOnClick={disableSelectionOnClick}
        experimentalFeatures={experimentalFeatures}
        onCellEditCommit={onCellEditCommit}
        rowHeight={rowHeight}
        getRowHeight={getRowHeight}
        onRowDoubleClick={
          !callback_onRowDoubleClick && status_onRowDoubleClick //if there is no callback func for onrow double click and status is true
            ? (params, event) => {
                //default redirection function
                event.stopPropagation();
                if (redirecOnDoubleClick) {
                  redirecOnDoubleClick();
                }
                dispatch(
                  setHistoryPath({
                    url: window.location.pathname,
                    module: module,
                  })
                );
                navigate(url_onRowClick);
                setShowWork && setShowWork(true);
              }
            : callback_onRowDoubleClick
            ? (params) => callback_onRowDoubleClick(params)
            : null //executes a custom callback if there else
        }
        onRowClick={
          status_onRowSingleClick
            ? (params, event) => {
                callback_onRowSingleClick(params);
              }
            : null
        }
        onCellClick={(param, event) => {
          if (typeof stopPropagation_Column !== "object") {
            if (stopPropagation_Column === param.field) event.stopPropagation();
          } else {
            if (stopPropagation_Column.includes(param.field))
              event.stopPropagation();
          }
        }}
        onEditCellPropsChange={(params, event) =>
          onEditCellPropsChange(params, event)
        }
        onSelectionModelChange={(ids) => {
          setSelectedCount(ids.length);
          onRowsSelectionHandler(ids);
        }}
        sx={{
          "& .MuiDataGrid-row": {
            transition: "background-color 0.2s ease",
            "&:hover": {
              backgroundColor: `${theme.palette.primary.light}40`,
              cursor: status_onRowSingleClick ? "pointer" : "default",
            },
          },
          "& .MuiDataGrid-cell:focus": {
            outline: "none",
          },
          "& .MuiDataGrid-columnHeader:focus": {
            outline: "none",
          },
          "& .MuiDataGrid-virtualScroller": {
            "&::-webkit-scrollbar": {
              width: "8px",
              height: "8px",
            },
            "&::-webkit-scrollbar-track": {
              backgroundColor: theme.palette.grey[50],
            },

            "&::-webkit-scrollbar-thumb": {
              backgroundColor: theme.palette.grey[300],
              borderRadius: "4px",
              "&:hover": {
                backgroundColor: theme.palette.grey[400],
              },
            },
          },
          "& .MuiDataGrid-columnHeaderCheckbox": {
            width: "60px !important",
            minWidth: "60px !important",
            maxWidth: "60px !important",
            "& .MuiCheckbox-root": {
              padding: "0px",
              marginLeft: "0px",
            },
          },
          "& .MuiDataGrid-cellCheckbox": {
            width: "60px !important",
            minWidth: "60px !important",
            maxWidth: "60px !important",
            "& .MuiCheckbox-root": {
              padding: "0px",
              marginLeft: "0px",
            },
          },
        }}
        components={{
          LoadingOverlay: CustomLoadingOverlay,
          NoRowsOverlay: CustomNoRowsOverlay,
          Toolbar: (props) => (
            <CustomToolbar
              {...props}
              title={title}
              onRefresh={onRefresh}
              onSearch={onSearch}
              totalRows={rowCount || rows?.length || 0}
              selectedRows={selectedCount}
              showSearch={showSearch}
              showRefresh={showRefresh}
              showSelectedCount={showSelectedCount}
              showExport={showExport}
              showFilter={showFilter}
              showColumns={showColumns}
              checkboxSelection={checkboxSelection}
              columns={columns}
              rows={rows}
              module={module}
            />
          ),
        }}
        isRowSelectable={isRowSelectable}
      />
      {/* {showCustomNavigation && (
        <TablePagination
          component="div"
          count={Math.max(0, parseInt(rowCount) || rows?.length || 0)}
          page={Math.max(0, Math.min(page, Math.ceil((parseInt(rowCount) || rows?.length || 0) / pageSize) - 1))}
          rowsPerPage={pageSize > 0 ? pageSize : 10}
          onPageChange={(_, newPage) => {
            const totalPages = Math.ceil(
              (parseInt(rowCount) || rows?.length || 0) / pageSize
            );
            if (newPage >= 0 && newPage < totalPages) {
              onPageChange(_, newPage);
            }
          }}
          onRowsPerPageChange={(event) => {
            const newSize = parseInt(event.target.value, 10) || 10;
            onPageSizeChange(event);
            onPageChange(null, 0);
          }}
          disabled={
            paginationLoading || (parseInt(rowCount) || rows?.length || 0) === 0
          }
          rowsPerPageOptions={[10, 25, 50, 100]}
          sx={{
            borderTop: `1px solid ${theme.palette.grey[200]}`,
            '& .MuiTablePagination-select': {
              marginRight: '8px',
            }
          }}
          showFirstButton={showFirstPageoptions}
        />
      )} */}
      {showCustomNavigation && (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="right"
          width="100%"
        >
          <TablePagination
            component="div"
            count={Math.max(0, parseInt(rowCount) || rows?.length || 0)}
            page={Math.max(
              0,
              Math.min(
                page,
                Math.ceil(
                  (parseInt(rowCount) || rows?.length || 0) / pageSize
                ) - 1
              )
            )}
            rowsPerPage={pageSize > 0 ? pageSize : 10}
            onPageChange={(_, newPage) => {
              const totalPages = Math.ceil(
                (parseInt(rowCount) || rows?.length || 0) / pageSize
              );
              if (newPage >= 0 && newPage < totalPages) {
                onPageChange(_, newPage);
              }
            }}
            onRowsPerPageChange={(event) => {
              const newSize = parseInt(event.target.value, 10) || 10;
              onPageSizeChange(event);
              onPageChange(null, 0);
            }}
            disabled={
              paginationLoading ||
              (parseInt(rowCount) || rows?.length || 0) === 0
            }
            rowsPerPageOptions={[10, 25, 50, 100]}
            sx={{
              borderTop: `1px solid ${theme.palette.grey[200]}`,
              "& .MuiTablePagination-select": {
                marginRight: "8px",
              },
            }}
            showFirstButton={showFirstPageoptions}
          />
          {showSelectAllOptions && (
            <Tooltip title="Go to Last page">
              <IconButton
                onClick={onSelectAllOptions}
                disabled={page >= Math.floor(rowCount / pageSize) - 1}
              >
                <LastPageIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      )}
    </StyledDataGridContainer>
  );
}
