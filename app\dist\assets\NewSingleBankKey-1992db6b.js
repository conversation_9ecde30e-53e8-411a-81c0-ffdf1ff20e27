import{l as tt,i as A,r as o,it as Ht,a as t,j as s,T as S,a3 as P,t4 as $,g_ as L,$ as u,hy as _t,hA as Ut,gR as Jt,gS as Gt,hJ as Zt,w as F,iu as q,hP as G,h_ as rt,B as z,F as Z,u as gt,b as Xt,hZ as Qt,t5 as Yt,ht as yt,J as Bt,i7 as ye,hp as Be,W as Pe,hv as Pt,i8 as Kt,ic as er,id as tr,ie as rr,P as nr,hE as cr,a1 as O,gW as W,fV as or,h1 as ir,hI as sr,fX as ar,gQ as ur,fH as dr,ho as hr,h8 as lr,ih as Ke,i6 as fr,hn as mr,ik as et}from"./index-fdfa25a0.js";import"./AutoCompleteType-3a9c9c9d.js";import"./dayjs.min-774e293a.js";import"./useChangeLogUpdate-3699f77c.js";function nt(r){var f,b,g,T;console.log("maxlength",r.field);const l=tt();var m=r.field.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").replaceAll("%","").split(" ").join("");const v=A(i=>i.bankKey.singleBKPayload),x=i=>{const d=C=>{l(G({keyName:"Region1",data:C.body}))},N=C=>{console.log(C,"error in dojax")};F(`/${q}/data/getRegionBasedOnCountry?country=${i.code}`,"get",d,N)},R=i=>{const d=C=>{l(G({keyName:"Region2",data:C.body}))},N=C=>{console.log(C,"error in dojax")};F(`/${q}/data/getRegionBasedOnCountry?country=${i.code}`,"get",d,N)};o.useEffect(()=>{var i,d;(((i=r==null?void 0:r.field)==null?void 0:i.visibility)==="0"||((d=r==null?void 0:r.field)==null?void 0:d.visibility)==="Required")&&l(Ht(m))},[]);const k=A(i=>i.AllDropDown.dropDown);if(((f=r.field)==null?void 0:f.fieldType)==="Input")return t(u,{item:!0,md:2,children:r.field.visibility==="Hidden"?null:s(L,{children:[s(S,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(P,{size:"small",type:r.field.dataType==="QUAN"?"number":"",placeholder:`Enter ${r.field.fieldName}`,inputProps:{maxLength:r.field.maxLength},value:v[m],onChange:(i,d)=>{const N=i.target.value;Object.keys(v).length>0?(console.log("0"),N.length>0&&N[0]===" "?(console.log("1"),l($({keyName:m,data:N.trimStart()}))):(console.log("2"),l($({keyName:m,data:N.toUpperCase()})))):(console.log("3"),l($({keyName:m,data:N.trimStart()})))},required:r.field.visibility==="Required"||r.field.visibility==="0"})]})});if(((b=r.field)==null?void 0:b.fieldType)==="Drop Down")return t(u,{item:!0,md:2,children:r.field.visibility==="Hidden"?null:s(L,{children:[s(S,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(_t,{sx:{height:"31px"},fullWidth:!0,size:"small",value:v[m],onChange:(i,d)=>{r.field.fieldName==="Country 1"&&x(d),r.field.fieldName==="Country 2"&&R(d),l($({keyName:m,data:d}))},options:k[m]??[],required:r.field.visibility==="0"||r.field.visibility==="Required",getOptionLabel:i=>`${i==null?void 0:i.code} - ${i==null?void 0:i.desc}`,renderOption:(i,d)=>t("li",{...i,children:s(S,{style:{fontSize:12},children:[d==null?void 0:d.code," - ",d==null?void 0:d.desc]})}),renderInput:i=>t(P,{...i,variant:"outlined",placeholder:`Select ${r.field.fieldName}`})})]})});if(((g=r.field)==null?void 0:g.fieldType)==="Radio Button")return s(u,{item:!0,md:2,children:[s(S,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Ut,{sx:{padding:0},checked:v[m]==!0,onChange:i=>{l($({keyName:m,data:i.target.checked}))}})]});if(((T=r.field)==null?void 0:T.fieldType)==="Calendar")return t(u,{item:!0,md:2,children:s(L,{children:[s(S,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Jt,{dateAdapter:Gt,children:t(Zt,{slotProps:{textField:{size:"small"}},value:v[m],onChange:i=>l($({keyName:m,data:"/Date("+Date.parse(i)+")/"})),required:r.field.visibility==="0"||r.field.visibility==="Required"})})]})})}const br=r=>{let l=Object==null?void 0:Object.entries(r==null?void 0:r.bankDataTabDetails);console.log("basic",r,l);const[m,v]=o.useState([]);return o.useEffect(()=>{v(l==null?void 0:l.map(x=>{var R,k;return s(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...rt},children:[t(u,{container:!0,children:t(S,{sx:{fontSize:"12px",fontWeight:"700"},children:x[0]})}),t(z,{children:t(u,{container:!0,spacing:1,children:(k=(R=[...x[1]].filter(f=>(f==null?void 0:f.visibility)!="Hidden"))==null?void 0:R.sort((f,b)=>(f==null?void 0:f.sequenceNo)-(b==null?void 0:b.sequenceNo)))==null?void 0:k.map(f=>t(nt,{field:f,dropDownData:r==null?void 0:r.dropDownData,country:r.country}))})})]})}))},[r==null?void 0:r.basicDataTabDetails]),t(Z,{children:m})},Sr=r=>{let l=Object==null?void 0:Object.entries(r==null?void 0:r.addressDataTabDetails);console.log("basic",r,l);const[m,v]=o.useState([]);return o.useEffect(()=>{v(l==null?void 0:l.map(x=>{var R,k;return s(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...rt},children:[t(u,{container:!0,children:t(S,{sx:{fontSize:"12px",fontWeight:"700"},children:x[0]})}),t(z,{children:t(u,{container:!0,spacing:1,children:(k=(R=[...x[1]].filter(f=>(f==null?void 0:f.visibility)!="Hidden"))==null?void 0:R.sort((f,b)=>(f==null?void 0:f.sequenceNo)-(b==null?void 0:b.sequenceNo)))==null?void 0:k.map(f=>t(nt,{field:f,dropDownData:r==null?void 0:r.dropDownData}))})})]})}))},[r==null?void 0:r.basicDataTabDetails]),t(Z,{children:m})},Rr=()=>{var me,be,Se,xe,ke,Ce,De,ve,Re,Ne,we,Te,Ee,Ae,Fe,Ie,pe,Me,Oe,qe,Le,$e,Ve,je,We,ze,He,_e,Ue,Je,Ge,Ze,ge,Xe,Qe,Ye;const r=gt(),[l,m]=o.useState(0),[v,x]=o.useState(!1),[R,k]=o.useState(""),[f,b]=o.useState(!1),[g,T]=o.useState(!0),[i,d]=o.useState(!1),[N,C]=o.useState(!1),[ct,X]=o.useState(!1),[ot,I]=o.useState(!1),[it,K]=o.useState(!1),[st,Q]=o.useState(!1),[H,at]=o.useState([]),[ut,ee]=o.useState(!1),[te,dt]=o.useState(!0),[ht,re]=o.useState(!0),[lt,ne]=o.useState(!1),[ft,mt]=o.useState(!1),[bt,St]=o.useState(""),[Y,xt]=o.useState(""),[_,ce]=o.useState(""),[kt,oe]=o.useState(!1),[Ct,ie]=o.useState(!1),U=Xt(),h=r.state;console.log("displayData",h),console.log("submitForReviewDisabled",te);const e=A(c=>c.bankKey.singleBKPayload),se=A(c=>c.AllDropDown.dropDown),Dt=A(c=>c.bankKey.bankKeyBankData),ae=A(c=>c.bankKey.singleBKPayload),vt=A(c=>c.bankKey.requiredFields);console.log("payloadFields",ae),console.log("requiredFields",(me=e==null?void 0:e.TimeZone)==null?void 0:me.code);const Rt=A(c=>c.bankKey.bankKeyAddressData);let E=A(c=>c.userManagement.userData);const V=tt(),[ue,Nt]=o.useState(0),wt=(c,a)=>{const n=w=>{V(G({keyName:c,data:w.body})),Nt(p=>p+1)},D=w=>{console.log(w)};F(`/${q}/data/${a}`,"get",n,D)},Tt=()=>{var c,a;(a=(c=Ke)==null?void 0:c.bankKey)==null||a.map(n=>{wt(n==null?void 0:n.keyName,n==null?void 0:n.endPoint)})},Et=()=>{var c,a;ue==((a=(c=Ke)==null?void 0:c.bankKey)==null?void 0:a.length)?I(!1):I(!0)};o.useEffect(()=>{Et()},[ue]),o.useEffect(()=>{Tt()},[]),o.useEffect(()=>{xt(Qt("BK"))},[]);let de=["BANK DETAILS","ADDRESS DETAILS","ATTACHMENTS & COMMENTS"];const At=c=>{var a,n;switch(c){case 0:return t(br,{bankDataTabDetails:Dt,dropDownData:se,country:(n=(a=h==null?void 0:h.bankCtryReg)==null?void 0:a.newBankCtryReg)==null?void 0:n.code});case 1:return t(Sr,{addressDataTabDetails:Rt,dropDownData:se});case 2:return t(fr,{title:"BankKey",useMetaData:!1,artifactId:Y,artifactName:"BankKey"});default:return"Unknown step"}},Ft=()=>{fe()?m(a=>a+1):le()},It=()=>{fe()?m(a=>a-1):le()},y=()=>{X(!0)},pt=()=>{kt?(X(!1),oe(!1)):(X(!1),U("/masterDataCockpit/bankKey"))},j=()=>{Q(!0)},Mt=()=>{ee(!1)},he=()=>{Q(!1)},le=()=>{ee(!0)},fe=()=>mr(ae,vt,at);o.useEffect(()=>{V(Yt(H))},[H]),console.log("singleBKPayload",e);var B={AddressDto:{AddressID:"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Name2:e!=null&&e.Name1?e==null?void 0:e.Name1:"",Name3:e!=null&&e.Name2?e==null?void 0:e.Name2:"",Name4:e!=null&&e.Name3?e==null?void 0:e.Name3:"",Sort1:e!=null&&e.SearchTerm1?e==null?void 0:e.SearchTerm1:"",Sort2:e!=null&&e.SearchTerm2?e==null?void 0:e.SearchTerm2:"",BuildLong:e!=null&&e.BuildingCode?e==null?void 0:e.BuildingCode:"",RoomNo:e!=null&&e.RoomNumber?e==null?void 0:e.RoomNumber:"",Floor:e!=null&&e.Floor?e==null?void 0:e.Floor:"",COName:e!=null&&e.co?e==null?void 0:e.co:"",StrSuppl1:e!=null&&e.Street1?e==null?void 0:e.Street1:"",StrSuppl2:e!=null&&e.Street2?e==null?void 0:e.Street2:"",Street:e!=null&&e.Street3?e==null?void 0:e.Street3:"",HouseNo:e!=null&&e.HouseNumber?e==null?void 0:e.HouseNumber:"",HouseNo2:e!=null&&e.Supplement?e==null?void 0:e.Supplement:"",StrSuppl3:e!=null&&e.Street4?e==null?void 0:e.Street4:"",Location:e!=null&&e.Street5?e==null?void 0:e.Street5:"",District:e!=null&&e.District?e==null?void 0:e.District:"",HomeCity:e!=null&&e.OtherCity?e==null?void 0:e.OtherCity:"",PostlCod1:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",PostlCod2:e!=null&&e.PostalCode1?e==null?void 0:e.PostalCode1:"",PostlCod3:e!=null&&e.CompanyPostCd?e==null?void 0:e.CompanyPostCd:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PoBoxCit:e!=null&&e.POBoxCity?e==null?void 0:e.POBoxCity:"",PoBoxReg:(be=e==null?void 0:e.Region2)!=null&&be.code?(Se=e==null?void 0:e.Region2)==null?void 0:Se.code:"",PoboxCtry:(xe=e==null?void 0:e.Country2)!=null&&xe.code?(ke=e==null?void 0:e.Country2)==null?void 0:ke.code:"",Country:(Ce=e==null?void 0:e.Country1)!=null&&Ce.code?(De=e==null?void 0:e.Country1)==null?void 0:De.code:"",TimeZone:(ve=e==null?void 0:e.TimeZone)!=null&&ve.code?(Re=e==null?void 0:e.TimeZone)==null?void 0:Re.code:"",Taxjurcode:(Ne=e==null?void 0:e.TaxJurisdiction)!=null&&Ne.code?(we=e==null?void 0:e.TaxJurisdiction)==null?void 0:we.code:"",Transpzone:(Te=e==null?void 0:e.TransportZone)!=null&&Te.code?(Ee=e==null?void 0:e.TransportZone)==null?void 0:Ee.code:"",Regiogroup:(Ae=e==null?void 0:e.StructureGroup)!=null&&Ae.code?(Fe=e==null?void 0:e.StructureGroup)==null?void 0:Fe.code:"",DontUseS:(Ie=e==null?void 0:e.Undeliverable)!=null&&Ie.code?(pe=e==null?void 0:e.Undeliverable)==null?void 0:pe.code:"",DontUseP:(Me=e==null?void 0:e.Undeliverable1)!=null&&Me.code?(Oe=e==null?void 0:e.Undeliverable1)==null?void 0:Oe.code:"",PoWONo:(e==null?void 0:e.POBoxwoNo)===!0?"X":"",DeliServType:(qe=e==null?void 0:e.DeliveryServiceType)!=null&&qe.code?(Le=e==null?void 0:e.DeliveryServiceType)==null?void 0:Le.code:"",DeliServNumber:e!=null&&e.DeliveryServiceNo?e==null?void 0:e.DeliveryServiceNo:"",Township:e!=null&&e.Township?e==null?void 0:e.Township:"",Langu:($e=e==null?void 0:e.Language)!=null&&$e.code?(Ve=e==null?void 0:e.Language)==null?void 0:Ve.code:"",Tel1Numbr:e!=null&&e.Telephone?e==null?void 0:e.Telephone:"",Tel1Ext:e!=null&&e.Extension?e==null?void 0:e.Extension:"",MobilePhone:e!=null&&e.MobilePhone?e==null?void 0:e.MobilePhone:"",FaxNumber:e!=null&&e.Fax?e==null?void 0:e.Fax:"",FaxExtens:e!=null&&e.Extension1?e==null?void 0:e.Extension1:"",EMail:e!=null&&e.EMailAddress?e==null?void 0:e.EMailAddress:"",AdrNotes:e!=null&&e.Notes?e==null?void 0:e.Notes:"",Region:(je=e==null?void 0:e.Region1)!=null&&je.code?(We=e==null?void 0:e.Region1)==null?void 0:We.code:"",PoBoxLobby:e!=null&&e.PoBoxLobby?e==null?void 0:e.PoBoxLobby:""},BankKeyID:"",ReqCreatedBy:E==null?void 0:E.user_id,ReqCreatedOn:"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",TaskId:"",Remarks:_||"",Action:"I",Validation:ht===!0?"X":"",BankCtry:(He=(ze=h==null?void 0:h.bankCtryReg)==null?void 0:ze.newBankCtryReg)!=null&&He.code?(Ue=(_e=h==null?void 0:h.bankCtryReg)==null?void 0:_e.newBankCtryReg)==null?void 0:Ue.code:"",BankKey:(Je=h==null?void 0:h.bankKey)!=null&&Je.newBankKey?(Ge=h==null?void 0:h.bankKey)==null?void 0:Ge.newBankKey:"",BankName:e!=null&&e.BankName?e==null?void 0:e.BankName:"",BankRegion:(Ze=e==null?void 0:e.Region)!=null&&Ze.code?(ge=e==null?void 0:e.Region)==null?void 0:ge.code:"",BankStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",City:e!=null&&e.City?e==null?void 0:e.City:"",BankBranch:e!=null&&e.BankBranch?e==null?void 0:e.BankBranch:"",SwiftCode:e!=null&&e.SWIFTBIC?e==null?void 0:e.SWIFTBIC:"",BankGroup:e!=null&&e.BankGroup?e==null?void 0:e.BankGroup:"",PobkCurac:(e==null?void 0:e.PostbankAcct)===!0?"X":"",BankNo:e!=null&&e.BankNumber?e==null?void 0:e.BankNumber:""};const Ot=()=>{K(!0);const c=n=>{var D,w,p;K(!1),n.statusCode===201?(dt(!1),x("Create"),x("Create"),k("All Data has been Validated. Bank Key can be Sent for Review"),b("success"),T(!1),d(!0),y(),C(!0),oe(!0)):(x("Error"),d(!1),k(`${(D=n==null?void 0:n.body)!=null&&D.message[0]?(w=n==null?void 0:n.body)==null?void 0:w.message[0]:(p=n==null?void 0:n.body)==null?void 0:p.value}`),b("danger"),T(!1),C(!0),j())},a=n=>{console.log(n)};F(`/${q}/alter/validateSingleBankKey`,"post",c,a,B)},qt=()=>{b(!1),j(),x("Confirm"),k("Do You Want to Save as Draft ?"),mt(!0),St("proceed")},Lt=()=>{const c=n=>{if(I(!0),n.statusCode===200){console.log("success"),x("Create"),k(`Bank Key has been saved NBS${n.body}`),b("success"),T(!1),d(!0),y(),C(!0),I(!1);const D={artifactId:Y,createdBy:E==null?void 0:E.emailId,artifactType:"BankKey",requestId:`CBS${n==null?void 0:n.body}`},w=M=>{console.log("Second API success",M)},p=M=>{console.error("Second API error",M)};F(`/${et}/documentManagement/updateDocRequestId`,"post",w,p,D),U("/masterDataCockpit/bankKey")}else x("Save"),d(!1),k("Failed Saving the Data "),b("danger"),T(!1),C(!0),j(),I(!1);handleClose()},a=n=>{console.log(n)};F(`/${q}/alter/bankKeyAsDraft`,"post",c,a,B)},$t=()=>{_.length<=0?ie(!0):Vt()},Vt=()=>{J(),I(!0);const c=n=>{if(n.statusCode===200){console.log("success"),x("Create"),k(`Bank Key has been submitted for review NBS${n.body}`),b("success"),T(!1),d(!0),y(),C(!0),I(!1);const D={artifactId:Y,createdBy:E==null?void 0:E.emailId,artifactType:"BankKey",requestId:`NBS${n==null?void 0:n.body}`},w=M=>{console.log("Second API success",M)},p=M=>{console.error("Second API error",M)};F(`/${et}/documentManagement/updateDocRequestId`,"post",w,p,D),U("/masterDataCockpit/bankKey")}else x("Create"),d(!1),k("Creation Failed"),b("danger"),T(!1),C(!0),j(),I(!1);handleClose()},a=n=>{console.log(n)};F(`/${q}/alter/bankKeySubmitForReview`,"post",c,a,B)},jt=()=>{re(!1),ne(!0)},J=()=>{ie(!1),re(!0),ne(!1)},Wt=(c,a)=>{const n=c.target.value;if(n.length>0&&n[0]===" ")ce(n.trimStart());else{let D=n.toUpperCase();ce(D)}},zt=c=>{const a=D=>{V(G({keyName:"Region",data:D.body}))},n=D=>{console.log(D,"error in dojax")};F(`/${q}/data/getRegionBasedOnCountry?country=${c}`,"get",a,n)};return o.useEffect(()=>{var c,a;zt((a=(c=h==null?void 0:h.bankCtryReg)==null?void 0:c.newBankCtryReg)==null?void 0:a.code)},[]),t(Z,{children:ot===!0?t(yt,{}):s("div",{children:[t(Bt,{dialogState:st,openReusableDialog:j,closeReusableDialog:he,dialogTitle:v,dialogMessage:R,handleDialogConfirm:he,dialogOkText:"OK",handleExtraButton:Lt,dialogSeverity:f,showCancelButton:!0,showExtraButton:ft,handleDialogReject:()=>{Q(!1)},handleExtraText:bt}),H.length!=0&&t(ye,{openSnackBar:ut,alertMsg:"Please fill the following Field: "+H.join(", "),handleSnackBarClose:Mt}),i&&t(ye,{openSnackBar:ct,alertMsg:R,handleSnackBarClose:pt}),t(u,{container:!0,style:{...Be,backgroundColor:"#FAFCFF"},children:s(u,{sx:{width:"inherit"},children:[t(u,{item:!0,md:7,style:{padding:"16px",display:"flex"},children:s(u,{item:!0,md:5,sx:{display:"flex"},children:[t(u,{children:t(Pe,{color:"primary","aria-label":"upload picture",component:"label",sx:Pt,children:t(Kt,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{U("/masterDataCockpit/bankKey"),V(clearPayload()),V(clearOrgData())}})})}),s(u,{children:[t(S,{variant:"h3",children:t("strong",{children:"Create Bank Key"})}),t(S,{variant:"body2",color:"#777",children:"This view creates a new Bank Key"})]})]})}),t(u,{container:!0,style:{padding:"0 1rem 0 1rem"},children:s(u,{container:!0,sx:Be,children:[s(u,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[s(z,{width:"70%",sx:{marginLeft:"40px"},children:[t(u,{item:!0,sx:{paddingTop:"2px !important"},children:s(L,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(S,{variant:"body2",color:"#777",children:"Bank Country"})}),s(S,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",(Qe=(Xe=h==null?void 0:h.bankCtryReg)==null?void 0:Xe.newBankCtryReg)==null?void 0:Qe.code]})]})}),t(u,{item:!0,sx:{paddingTop:"2px !important"},children:s(L,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(S,{variant:"body2",color:"#777",children:"Bank Key"})}),s(S,{variant:"body2",fontWeight:"bold",children:[":",(Ye=h==null?void 0:h.bankKey)==null?void 0:Ye.newBankKey]})]})})]}),t(z,{width:"30%",sx:{marginLeft:"40px"},children:t(u,{item:!0,children:s(L,{flexDirection:"row",children:[t(S,{variant:"body2",color:"#777",style:{width:"30%"}}),t(S,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"}}),t(S,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start"})]})})})]}),t(u,{container:!0,children:t(er,{activeStep:l,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},children:de.map((c,a)=>t(tr,{children:t(rr,{sx:{fontWeight:"700"},children:c})},c))})}),t(u,{container:!0,children:t(u,{container:!0,children:At(l)})})]})})]})}),t(nr,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(cr,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:qt,children:"Save As Draft"}),t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:It,disabled:l===0,children:"Back"}),l===de.length-1?s(Z,{children:[t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:Ot,children:"Validate"}),t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:jt,disabled:te,children:"Submit For Review"})]}):t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:Ft,children:"Next"})]})}),s(or,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:lt,onClose:J,children:[s(ir,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(S,{variant:"h6",children:"REMARKS"}),t(Pe,{sx:{width:"max-content"},onClick:J,children:t(sr,{})})]}),t(ar,{sx:{padding:".5rem 1rem"},children:s(L,{children:[t(z,{sx:{minWidth:400},children:t(ur,{sx:{height:"auto"},fullWidth:!0,children:t(P,{sx:{backgroundColor:"#F5F5F5"},value:_,onChange:Wt,multiline:!0,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})})}),Ct&&t(u,{children:t(S,{style:{color:"red"},children:"Please Enter Remarks"})})]})}),s(dr,{sx:{display:"flex",justifyContent:"end"},children:[t(O,{sx:{width:"max-content",textTransform:"capitalize"},onClick:J,children:"Cancel"}),t(O,{className:"button_primary--normal",type:"save",onClick:$t,variant:"contained",children:"Submit"})]})]}),t(hr,{sx:{color:"#fff",zIndex:c=>c.zIndex.drawer+1},open:it,children:t(lr,{color:"inherit"})})]})})};export{Rr as default};
