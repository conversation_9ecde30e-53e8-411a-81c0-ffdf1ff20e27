import{r as l,i as p,j as c,a as r,$ as s,h_ as le,m8 as wi,T as b,g_ as j,hy as pn,a3 as ee,hi as Pn,gU as We,a1 as _,l as Qe,B as H,hC as ki,m9 as Ri,F as ae,kK as Di,iR as Oi,a8 as Ui,iS as Ei,ma as Mn,b as Gi,u as Vi,mb as yi,j1 as ue,hZ as _i,jb as Bi,mc as Li,md as Fi,lQ as Nn,w as q,i6 as An,i7 as Wi,hp as wn,i8 as Qi,kv as _e,kw as Be,kx as Le,gV as Hi,fV as Te,h1 as ve,fX as pe,fH as Pe,aa as zi,hD as $i,ab as qi,hE as ji,gW as Me,W as kn,hI as Rn,gQ as Dn,P as Ji,hj as <PERSON>,me as <PERSON>,G as Q,J as Yi,hn as <PERSON>,ik as Fe}from"./index-fdfa25a0.js";import{d as Xi}from"./Description-5b38f787.js";import{F as Ne}from"./FilterField-6f6e20f9.js";import{A as En}from"./AutoCompleteType-3a9c9c9d.js";import{R as Zi}from"./ReusableToast-9d9c90a5.js";import{G as eo}from"./GenericTabs-8e261948.js";import"./useChangeLogUpdate-3699f77c.js";import"./dayjs.min-774e293a.js";import"./AdapterDayjs-cd3745c6.js";import"./isBetween-fe8614a5.js";import"./useCustomDtCall-04c3c72a.js";import"./GenericViewGeneral-4b82ad14.js";const to=n=>{const[N,R]=l.useState(1);p(S=>S.payloadS);const D=p(S=>S.AllDropDown.dropDown);return c("div",{style:{display:"flex",flexDirection:"column",width:"100%"},children:[new Array(N).fill(1).map(S=>{var P,O,g,U,B;return r(s,{children:c(s,{sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,...le,...wi},children:[r(s,{children:r(b,{sx:{fontSize:"12px",fontWeight:"700"},children:"Classification Data"})}),r(s,{children:c(s,{marginBottom:5,display:"flex",children:[c(s,{width:"50%",children:[r(s,{item:!0,md:6,mt:1,children:c(j,{children:[r(b,{variant:"body2",color:"#777",children:"Class Type:"}),r(pn,{sx:{height:"31px"},fullWidth:!0,size:"small",value:(P=n==null?void 0:n.basicData)==null?void 0:P.ClassType,onChange:(f,x)=>{n==null||n.setBasicData({...n==null?void 0:n.basicData,ClassType:x})},options:(D==null?void 0:D.ClassType)??[],getOptionLabel:f=>`${f==null?void 0:f.Class} `,renderOption:(f,x)=>r("li",{...f,children:r(b,{style:{fontSize:12},children:x==null?void 0:x.Class})}),renderInput:f=>r(ee,{...f,variant:"outlined",placeholder:"Select Class Type"})})]})}),r(s,{item:!0,md:6,mt:1,children:c(j,{children:[r(b,{variant:"body2",color:"#777",children:"Class:"}),r(pn,{sx:{height:"31px"},fullWidth:!0,size:"small",disablePortal:!0,value:(O=n.basicData)==null?void 0:O.matGroup,onChange:(f,x)=>{n==null||n.setBasicData({...n==null?void 0:n.basicData,matGroup:x})},options:((g=n==null?void 0:n.dropDownData)==null?void 0:g.matGroup)??[],getOptionLabel:f=>`${f==null?void 0:f.MaterialGroup} - ${f==null?void 0:f.MatlGrpDesc}`,renderOption:(f,x)=>r("li",{...f,children:c(b,{style:{fontSize:12},children:[x==null?void 0:x.MaterialGroup," - ",x==null?void 0:x.MatlGrpDesc]})}),renderInput:f=>r(ee,{...f,variant:"outlined",placeholder:"Select Class"})})]})})]}),r(Pn,{orientation:"vertical",flexItem:!0}),c(s,{container:!0,sx:{ml:"30px"},children:[c(b,{sx:{fontSize:"12px",fontWeight:"700",mb:2},children:["Values for ",(B=(U=n==null?void 0:n.basicData)==null?void 0:U.ClassType)==null?void 0:B.Class]}),c(s,{container:!0,display:"flex",flexDirection:"row",children:[r(s,{item:!0,width:"40%",children:r(b,{variant:"body2",color:"#777",children:"Characteristic Description"})}),r(s,{item:!0,width:"40%",children:r(b,{variant:"body2",color:"#777",children:"Value"})})]}),r(Pn,{sx:{width:"60%"},orientation:"horizontal"}),c(s,{container:!0,marginTop:2,display:"flex",children:[r(s,{item:!0,width:"40%",children:r(b,{variant:"body2",color:"#777",fontWeight:"bold",children:"Expiration Date Shelf Number"})}),r(s,{item:!0,children:r(ee,{size:"small",placeholder:"Enter Value",onChange:f=>{n.setBasicData(x=>({...x,oldMaterialGroup:f.target.value}))}})})]})]})]})})]})})}),r(_,{variant:"outlined",sx:{button_Outlined:We,mt:2,alignSelf:"flex-end"},onClick:()=>{R(N+1)},children:"Add"})]})},ro=n=>{var V;const N=Qe(),R=p(h=>h.AllDropDown.dropDown);let D=p(h=>h.payload.taxData);const[S,P]=l.useState([{id:1,TaxCategory:"",TaxCategoryName:"",TaxClassification:"",TaxClassificationDescription:""}]),[O,g]=l.useState({});n.columns,l.useEffect(()=>{if(O){let C=[];for(let d=0;d<O.length;d++){O[d];var h={TaxCategory:"",TaxCategoryName:"",TaxClassification:"",TaxClassificationDescription:""};C.push(h)}P(C)}},[O]);const U=()=>{const h=[...D,{id:D.length+1,TaxCategory:"",TaxCategoryName:"",TaxClassification:"",TaxClassificationDescription:""}];N(Ri(h))},B=h=>{const C=S.map(d=>{var z,ne;return console.log(h,"params"),d.id===h.target.name.id&&(d.batch=h.target.value,d.batchQuantity=(ne=(z=h.target.name.Batch)==null?void 0:z.filter(Ae=>Ae.batchNumber==h.target.value)[0])==null?void 0:ne.batchQuantity),d});P(C)},f=(h,C)=>{const d=S.map(z=>(z.sequenceNo===h.id&&(h.field==="TaxCategory"&&(z[h.field]=h.props.value),h.field==="TaxCategoryName"&&(z[h.field]=C.target.value),h.field==="TaxClassification"&&(z[h.field]=C.target.value),h.field==="TaxClassificationDescription"&&(z[h.field]=C.target.value)),z));P(d)},x=[{field:"id",headerName:"ID",type:"text",hide:"true"},{field:"TaxCategory",headerName:"Tax Category",width:150,renderCell:h=>r(En,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:B,options:(R==null?void 0:R.TaxCategory)??[],getOptionLabel:C=>`${C==null?void 0:C.code} - ${C==null?void 0:C.desc}`,renderOption:(C,d)=>r("li",{...C,children:c(b,{style:{fontSize:12},children:[d==null?void 0:d.code," - ",d==null?void 0:d.desc]})}),renderInput:C=>r(ee,{...C,variant:"outlined"})})},{field:"TaxCategoryName",headerName:"Tax Category Name",type:"text",width:150},{field:"TaxClassification",headerName:"Tax Classification",type:"singleSelect",width:150,valueOptions:(V=R==null?void 0:R.TaxCategory)==null?void 0:V.map(h=>h.code),renderCell:h=>r(En,{sx:{height:"31px"},fullWidth:!0,size:"small",value:valueFromPayload[n==null?void 0:n.keyName],onChange:(C,d)=>{N(setPayload({keyName:n.keyName,data:d}))},options:R[n.keyName]??[],required:n.details.visibility==="0"||n.details.visibility==="Required",getOptionLabel:C=>`${C==null?void 0:C.code} - ${C==null?void 0:C.desc}`,renderOption:(C,d)=>r("li",{...C,children:c(b,{style:{fontSize:12},children:[d==null?void 0:d.code," - ",d==null?void 0:d.desc]})}),renderInput:C=>r(ee,{...C,variant:"outlined",placeholder:`Select ${n.details.fieldName}`,error:errorFields.includes(n==null?void 0:n.keyName)})})},{field:"TaxClassificationDescription",headerName:"Tax Classification Description",type:"text",width:150}];return r("div",{children:c(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...le},children:[r(s,{container:!0,children:r(b,{sx:{fontSize:"12px",fontWeight:"700"},children:n.cardName})}),r(H,{width:"100%",className:"confirmOrder-lineItem",mt:0,children:r(ki,{width:"100%",rows:S??[],columns:x,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,onEditCellPropsChange:f})}),r(_,{variant:"outlined",sx:{button_Outlined:We},onClick:U,children:"Add Row"})]})})},ao=n=>{let N=Object.entries(n==null?void 0:n.salesDataTabDetails);const[R,D]=l.useState([]);return l.useEffect(()=>{D(N==null?void 0:N.map(S=>r(ae,{children:S[0]==="Tax Data"?r(ro,{columns:S[1],cardName:S[0]}):c(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...le},children:[r(s,{container:!0,children:r(b,{sx:{fontSize:"12px",fontWeight:"700"},children:S[0]})}),r(H,{children:r(s,{container:!0,spacing:1,children:[...S[1]].sort((P,O)=>P.sequenceNo-O.sequenceNo).map(P=>r(Ne,{materialID:n==null?void 0:n.materialID,field:P,dropDownData:n.dropDownData}))})})]})})))},[n.salesDataTabDetails,n==null?void 0:n.materialID]),r(ae,{children:R})},no=n=>{let N=Object.entries(n==null?void 0:n.purchasingDataTabDetails);const[R,D]=l.useState([]);return l.useEffect(()=>{D(N==null?void 0:N.map(S=>(console.log("item",N),c(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...le},children:[r(s,{container:!0,children:r(b,{sx:{fontSize:"12px",fontWeight:"700"},children:S[0]})}),r(H,{children:r(s,{container:!0,spacing:1,children:[...S[1]].sort((P,O)=>P.sequenceNo-O.sequenceNo).map(P=>r(Ne,{materialID:n==null?void 0:n.materialID,field:P,dropDownData:n.dropDownData}))})})]}))))},[n.purchasingDataTabDetails,n==null?void 0:n.materialID]),r(ae,{children:R})},io=n=>{const[N,R]=l.useState([]);let D=Object.entries(n==null?void 0:n.MRPDataTabDetails);return l.useEffect(()=>{R(D==null?void 0:D.map(S=>c(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...le},children:[r(s,{container:!0,children:r(b,{sx:{fontSize:"12px",fontWeight:"700"},children:S[0]})}),r(H,{children:r(s,{container:!0,spacing:1,children:[...S[1]].sort((P,O)=>P.sequenceNo-O.sequenceNo).map(P=>r(Ne,{field:P,dropDownData:n.dropDownData}))})})]})))},[n.MRPDataTabDetails]),r(ae,{children:N})},oo=n=>{console.log("accounting",n.accountingTabDetails);let N=Object==null?void 0:Object.entries(n==null?void 0:n.accountingTabDetails);const[R,D]=l.useState([]);return l.useEffect(()=>{D(N==null?void 0:N.map(S=>c(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...le},children:[r(s,{container:!0,children:r(b,{sx:{fontSize:"12px",fontWeight:"700"},children:S[0]})}),r(H,{children:r(s,{container:!0,spacing:1,children:[...S[1]].sort((P,O)=>P.sequenceNo-O.sequenceNo).map(P=>r(Ne,{field:P,dropDownData:n.dropDownData}))})})]})))},[n.accountingTabDetails]),r(ae,{children:R})},lo=({questions:n})=>{const[N,R]=l.useState(!1),D=Qe(),S=p(g=>g.payload.singleMatPayload);console.log("questions",n);const P=(g,U)=>{let B=g.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");console.log("labell",g.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")),D(Mn({keyName:B,data:U}))},O=(g,U)=>{console.log("newlabel",U,g);const f=n.filter(x=>x.visibility===" Mandatory").every(x=>S[x.MDG_GI_QUESTION_TYPE]!=="");R(f),D(Mn({keyName:g.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:U}))};return l.useEffect(()=>{n.map(g=>{var U;console.log("questionstest",g==null?void 0:g.MDG_GI_VISIBILITY),((g==null?void 0:g.MDG_GI_VISIBILITY)===" Mandatory"||(g==null?void 0:g.MDG_GI_VISIBILITY)==="0")&&(console.log("rakesh",g),D(Di((U=g==null?void 0:g.MDG_GI_QUESTION_TYPE)==null?void 0:U.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").replaceAll("%","").split(" ").join(""))))})},[n]),r(ae,{children:r(s,{container:!0,children:n.map((g,U)=>{var B,f;return r(s,{item:!0,md:12,children:r(s,{container:!0,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",padding:"16px"},children:c(j,{sx:{width:"100%"},children:[c(b,{sx:{fontSize:"12px",fontWeight:"700"},children:[g.MDG_GI_QUESTION_TYPE,r("span",{style:{color:"red"},children:"*"})]}),g.MDG_GI_INPUT_OPTION==="Radio Button"?r(Oi,{"aria-labelledby":`radio-group-label-${U}`,defaultValue:"",name:`radio-group-${U}`,row:!0,value:S==null?void 0:S[(B=g.MDG_GI_QUESTION_TYPE)==null?void 0:B.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")],children:g.MDG_GI_INPUT_VALUE.split(",").map((x,V)=>r(Ui,{value:x,control:r(Ei,{}),label:x,onChange:h=>{let C=g.MDG_GI_QUESTION_TYPE,d=h.target.value;console.log("newValue",h.target.value,g.MDG_GI_QUESTION_TYPE),O(C,d)}},V))}):r(ee,{fullWidth:!0,placeholder:"PLEASE ENTER...",multiline:!0,value:S==null?void 0:S[(f=g.MDG_GI_QUESTION_TYPE)==null?void 0:f.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")],onChange:x=>{let V=g.MDG_GI_QUESTION_TYPE;console.log("label",V);const h=x.target.value;if(h.length>0&&h[0]===" ")P(V,h.trimStart());else{let C=h.toUpperCase();P(V,C)}}})]})})},U)})})})},No=()=>{var nt,it,ot,lt,st,ct,ut,dt,mt,ht,gt,ft,Ct,xt,bt,St,It,Tt,vt,pt,Pt,Mt,Nt,At,wt,kt,Rt,Dt,Ot,Ut,Et,Gt,Vt,yt,_t,Bt,Lt,Ft,Wt,Qt,Ht,zt,$t,qt,jt,Jt,Yt,Kt,Xt,Zt,er,tr,rr,ar,nr,ir,or,lr,sr,cr,ur,dr,mr,hr,gr,fr,Cr,xr,br,Sr,Ir,Tr,vr,pr,Pr,Mr,Nr,Ar,wr,kr,Rr,Dr,Or,Ur,Er,Gr,Vr,yr,_r,Br,Lr,Fr,Wr,Qr,Hr,zr,$r,qr,jr,Jr,Yr,Kr,Xr,Zr,ea,ta,ra,aa,na,ia,oa,la,sa,ca,ua,da,ma,ha,ga,fa,Ca,xa,ba,Sa,Ia,Ta,va,pa,Pa,Ma,Na,Aa,wa,ka,Ra,Da,Oa,Ua,Ea,Ga,Va,ya,_a,Ba,La,Fa,Wa,Qa,Ha,za,$a,qa,ja,Ja,Ya,Ka,Xa,Za,en,tn,rn,an,nn,on,ln,sn,cn,un,dn,mn,hn,gn,fn,Cn,xn,bn,Sn,In,Tn;const n=Qe(),N=Gi();Vi();const[R,D]=l.useState("1"),[S,P]=l.useState(!1);l.useState(!1);const[O,g]=l.useState(!1),[U,B]=l.useState(!1),[f,x]=l.useState({}),[V,h]=l.useState({}),[C,d]=l.useState(!1),[z,ne]=l.useState(!1),[Ae,we]=l.useState(""),[Gn,ke]=l.useState(!1),[so,de]=l.useState(!1),[He,Vn]=l.useState(""),[yn,_n]=l.useState(!1),[co,Re]=l.useState(!0),[uo,De]=l.useState(!1),[ze,Bn]=l.useState(!1),[mo,Ln]=l.useState([]),[Fn,Wn]=l.useState([]),Qn=p(a=>a.applicationConfig);let e=p(a=>{var i;return(i=a==null?void 0:a.tabsData)==null?void 0:i.dataToSend});console.log("displaydata",e);const J=p(a=>a.commonFilter.NewMaterial);console.log("nihar",J);let m=p(a=>{var i;return(i=a==null?void 0:a.tabsData)==null?void 0:i.orgData});const[A,Hn]=l.useState(0),[me,zn]=l.useState([]),[$n,$e]=l.useState(!1),[qn,qe]=l.useState(!1),o=p(a=>a.payload.payloadData),jn=p(a=>a.payload.requiredFields),Oe=p(a=>a.tabsData.basicData),Jn=p(a=>a.tabsData.salesData),Yn=p(a=>a.tabsData.purchasingData),Kn=p(a=>a.tabsData.mrpData),Xn=p(a=>a.tabsData.accountingData),Ue=p(a=>{var i;return(i=a==null?void 0:a.payload)==null?void 0:i.additionalData}),t=p(a=>a.payload);console.log("pppp1",t);const he=p(a=>a.payload.unitsOfMeasureData);console.log("asdd",e==null?void 0:e.selectedViews),console.log("uomTabData",he);let ie=(nt=e==null?void 0:e.selectedViews)==null?void 0:nt.value.map(a=>a.label),Zn=(it=e==null?void 0:e.selectedViews)==null?void 0:it.value.map(a=>a.value);console.log("facccc",ie);const L=p(a=>a.payload.singleMatPayload),[ei,je]=l.useState(!1),[ti,Je]=l.useState(!1),[ge,Ye]=l.useState(""),se=p(a=>a.tabsData.tabsStatus),te=p(a=>a.tabsData);console.log("seltabs",te.value.length,A),console.log("tabsStatus",se),console.log("hiii",Zn,se);let ri=p(a=>{var i;return(i=a.userManagement.entitiesAndActivities)==null?void 0:i["Create Single Material"]});const Ee=p(a=>a.tabsData.tabsStatusEffect);let F=p(a=>a.userManagement.userData);console.log("userdata",F);let ai=p(a=>a.tabsData.rolesTabs);const[ni,$]=l.useState(!1),[ii,K]=l.useState(""),[oi,X]=l.useState(""),[li,Ge]=l.useState(!1);l.useEffect(()=>{n(yi(me))},[me]),p(a=>a.payload.singleMatPayload),l.useEffect(()=>{si(),Ci()},[]),l.useEffect(()=>{var i;let a="";(i=te==null?void 0:te.value)==null||i.map(u=>a+=`${u.label},`),n(ue({keyName:"selectedViews",data:a.slice(0,-1)}))},[]),l.useEffect(()=>{Vn(_i("MM"))},[]);const si=()=>{var i;let a=[];(i=e==null?void 0:e.orgData)==null||i.map(u=>{u.info&&a.push(u.desc)}),e&&e.orgData&&e.orgData.forEach(function(u){a.includes(u.desc)&&n(Bi({keyName:u.desc.split(" ").join(""),data:u.info.code}))})},fe=["General Information","Basic Data","Sales","Purchasing","Accounting","Attachments & Comments"],Ke=p(a=>a.initialData.ArtifactId),Ce=[...new Set(Ke)];console.log("artifactID",Ke,Ce),l.useEffect(()=>{te.value.map(a=>{Ee&&(n(Li()),n(Fi({keyname:a.value,status:!1})))})},[Ee]),console.log("souma",Ee);const Xe=()=>{qe(!1)},ci=()=>{handleSubmitCorrection(),qe(!1)},ui=p(a=>a.payload.unitsOfMeasureData);l.useEffect(()=>{var a,i,u,I,T,k,w,G,y,E,Y,W,Z;ui.length==0?n(Nn([{id:1,xValue:"1",aUnit:(a=o==null?void 0:o.BaseUnit)==null?void 0:a.code,measureUnitText:(i=o==null?void 0:o.BaseUnit)==null?void 0:i.desc,yValue:"1",bUnit:(u=o==null?void 0:o.BaseUnit)==null?void 0:u.code,measurementUnitText:(I=o==null?void 0:o.BaseUnit)==null?void 0:I.desc,eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:"",width:"",height:"",unitsOfDimension:"",volume:o==null?void 0:o.Volume,volumeUnit:(T=o==null?void 0:o.VolumeUnit)==null?void 0:T.code,grossWeight:o==null?void 0:o.GrossWeight,netWeight:o==null?void 0:o.NetWeight,weightUnit:(k=o==null?void 0:o.NetWeight)==null?void 0:k.code,noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""}])):(w=t==null?void 0:t.payloadData)!=null&&w.BaseUnit&&n(Nn([{id:1,xValue:"1",aUnit:(G=o==null?void 0:o.BaseUnit)==null?void 0:G.code,measureUnitText:(y=o==null?void 0:o.BaseUnit)==null?void 0:y.desc,yValue:"1",bUnit:(E=o==null?void 0:o.BaseUnit)==null?void 0:E.code,measurementUnitText:(Y=o==null?void 0:o.BaseUnit)==null?void 0:Y.desc,eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:"",width:"",height:"",unitsOfDimension:"",volume:o==null?void 0:o.Volume,volumeUnit:(W=o==null?void 0:o.VolumeUnit)==null?void 0:W.code,grossWeight:o==null?void 0:o.GrossWeight,netWeight:o==null?void 0:o.NetWeight,weightUnit:(Z=o==null?void 0:o.NetWeight)==null?void 0:Z.code,noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""}]))},[o.BaseUnit]),console.log((lt=(ot=t==null?void 0:t.payloadData)==null?void 0:ot.ANPCode)==null?void 0:lt.code,"anp");var oe={ProductID:"",BasicDataID:"",Product:(st=e==null?void 0:e.materialNo)!=null&&st.materialNumber?(ct=e==null?void 0:e.materialNo)==null?void 0:ct.materialNumber:"",ProductType:((ut=e==null?void 0:e.materialType)==null?void 0:ut.fieldReference)??"",ViewNames:((dt=t==null?void 0:t.payloadData)==null?void 0:dt.selectedViews)??"",CorrectionViewNames:"",CreationID:((mt=t==null?void 0:t.payloadData)==null?void 0:mt.creationId)??"",Description:J==null?void 0:J.description,Remarks:ge||"",EditID:"",ExtendID:"",MassCreationID:"",MassEditID:"",MassExtendID:"",ReqCreatedOn:"",ReqCreatedBy:F.user_id??"",CrossPlantStatus:((gt=(ht=t==null?void 0:t.payloadData)==null?void 0:ht.CrossPlantMaterialStatus)==null?void 0:gt.code)??"",CrossPlantStatusValidityDate:"/Date("+Date.parse((ft=t==null?void 0:t.payloadData)==null?void 0:ft.ValidFrom)+")/",IsMarkedForDeletion:!1,ProductOldID:((Ct=t==null?void 0:t.payloadData)==null?void 0:Ct.OldMaterialNumber)??"",GrossWeight:((xt=t==null?void 0:t.payloadData)==null?void 0:xt.GrossWeight)??"",PurchaseOrderQuantityUnit:"",SourceOfSupply:"",WeightUnit:((St=(bt=t==null?void 0:t.payloadData)==null?void 0:bt.WeightUnit)==null?void 0:St.code)??"",NetWeight:((It=t==null?void 0:t.payloadData)==null?void 0:It.NetWeight)??"",CountryOfOrigin:"",CompetitorID:"",ProductGroup:((vt=(Tt=t==null?void 0:t.payloadData)==null?void 0:Tt.MaterialGroup)==null?void 0:vt.code)??"",BaseUnit:((Pt=(pt=t==null?void 0:t.payloadData)==null?void 0:pt.BaseUnit)==null?void 0:Pt.code)??"",ItemCategoryGroup:((Nt=(Mt=t==null?void 0:t.payloadData)==null?void 0:Mt.ItemCategoryGroup)==null?void 0:Nt.code)??"NORM",ProductHierarchy:((wt=(At=t==null?void 0:t.payloadData)==null?void 0:At.ProductHierarchy)==null?void 0:wt.code)??"",Division:((Rt=(kt=t==null?void 0:t.payloadData)==null?void 0:kt.Division)==null?void 0:Rt.code)??"",VarblPurOrdUnitIsActive:"",VolumeUnit:((Ot=(Dt=t==null?void 0:t.payloadData)==null?void 0:Dt.VolumeUnit)==null?void 0:Ot.code)??"",MaterialVolume:((Ut=t==null?void 0:t.payloadData)==null?void 0:Ut.Volume)??"",ANPCode:((Gt=(Et=t==null?void 0:t.payloadData)==null?void 0:Et.ANPCode)==null?void 0:Gt.code)??"",Brand:"",ProcurementRule:"",ValidityStartDate:null,LowLevelCode:"",ProdNoInGenProdInPrepackProd:"",SerialIdentifierAssgmtProfile:"",SizeOrDimensionText:((Vt=t==null?void 0:t.payloadData)==null?void 0:Vt.SizeDimensions)??"",IndustryStandardName:((yt=t==null?void 0:t.payloadData)==null?void 0:yt.IndStdName)??"",ProductStandardID:((_t=t==null?void 0:t.payloadData)==null?void 0:_t.InternationalArticleNumberEANUPC)??"",InternationalArticleNumberCat:((Lt=(Bt=t==null?void 0:t.payloadData)==null?void 0:Bt.CategoryOfInternationalArticleNumberEAN)==null?void 0:Lt.code)??"",ProductIsConfigurable:((Ft=t.payloadData)==null?void 0:Ft.ConfigurableMaterial)??!1,IsBatchManagementRequired:!1,ExternalProductGroup:((Qt=(Wt=t==null?void 0:t.payloadData)==null?void 0:Wt.ExtMatlGroup)==null?void 0:Qt.code)??"",CrossPlantConfigurableProduct:((Ht=t.payloadData)==null?void 0:Ht.CrossPlantConfMaterial)??"",SerialNoExplicitnessLevel:"",ProductManufacturerNumber:"",ManufacturerNumber:"",ManufacturerPartProfile:"",QltyMgmtInProcmtIsActive:!1,IndustrySector:((zt=e==null?void 0:e.industrySector)==null?void 0:zt.industryReference)??"",ChangeNumber:"",MaterialRevisionLevel:"",HandlingIndicator:((qt=($t=t==null?void 0:t.payloadData)==null?void 0:$t.HandlingIndicator)==null?void 0:qt.code)??"",WarehouseProductGroup:((Jt=(jt=t==null?void 0:t.payloadData)==null?void 0:jt.WarehouseMaterialGroup)==null?void 0:Jt.code)??"",WarehouseStorageCondition:((Kt=(Yt=t==null?void 0:t.payloadData)==null?void 0:Yt.WarehouseStorageCondition)==null?void 0:Kt.code)??"",StandardHandlingUnitType:((Zt=(Xt=t==null?void 0:t.payloadData)==null?void 0:Xt.StdHUType)==null?void 0:Zt.code)??"",SerialNumberProfile:((tr=(er=t==null?void 0:t.payloadData)==null?void 0:er.SerialNumberProfile)==null?void 0:tr.code)??"",AdjustmentProfile:"",PreferredUnitOfMeasure:"",IsPilferable:((rr=t==null?void 0:t.payloadData)==null?void 0:rr.Pilferable)??!1,IsRelevantForHzdsSubstances:((ar=t==null?void 0:t.payloadData)==null?void 0:ar.RelevantForHazardousSubstances)??!1,QuarantinePeriod:((ir=(nr=t==null?void 0:t.payloadData)==null?void 0:nr.QuarantinePeriod)==null?void 0:ir.code)??"",TimeUnitForQuarantinePeriod:"",QualityInspectionGroup:((lr=(or=t==null?void 0:t.payloadData)==null?void 0:or.QualityInspectionGroup)==null?void 0:lr.code)??"",AuthorizationGroup:((sr=t==null?void 0:t.payloadData)==null?void 0:sr.AuthorizationGroup)??"",HandlingUnitType:((ur=(cr=t==null?void 0:t.payloadData)==null?void 0:cr.HandlingUnitType)==null?void 0:ur.code)??"",HasVariableTareWeight:((dr=t==null?void 0:t.payloadData)==null?void 0:dr.VariableTareWeight)??!1,MaximumPackagingLength:((mr=t==null?void 0:t.payloadData)==null?void 0:mr.MaxPackagingLength)??"",MaximumPackagingWidth:((hr=t==null?void 0:t.payloadData)==null?void 0:hr.MaxPackagingWidth)??"",MaximumPackagingHeight:((gr=t==null?void 0:t.payloadData)==null?void 0:gr.MaxPackagingHeight)??"",UnitForMaxPackagingDimensions:"",IsFirstChangeLogCommit:!0,RequestPriority:L!=null&&L.ChoosePriorityLevel?L==null?void 0:L.ChoosePriorityLevel:"",BusinessJustification:L!=null&&L.BusinessJustification?L==null?void 0:L.BusinessJustification:"",to_Description:Ue==null?void 0:Ue.map(a=>{var i,u;return{DescriptionID:"",Product:(i=e==null?void 0:e.materialNo)!=null&&i.materialNumber?(u=e==null?void 0:e.materialNo)==null?void 0:u.materialNumber:"",Language:(a==null?void 0:a.language)==""?"EN":a==null?void 0:a.language,ProductDescription:(a==null?void 0:a.materialDescription)==""?J==null?void 0:J.description:a==null?void 0:a.materialDescription}}),to_ProductUnitsOfMeasure:he==null?void 0:he.map(a=>{var i,u;return console.log(a,"itemmm"),{Product:(i=e==null?void 0:e.materialNo)!=null&&i.materialNumber?(u=e==null?void 0:e.materialNo)==null?void 0:u.materialNumber:"",AlternativeUnit:"PC",QuantityNumerator:"1",QuantityDenominator:"1",MaterialVolume:"12.000",VolumeUnit:"M3",GrossWeight:"18.000",WeightUnit:"KG",GlobalTradeItemNumber:"2150000000000",GlobalTradeItemNumberCategory:"EA",UnitSpecificProductLength:"0.000",UnitSpecificProductWidth:"0.000",UnitSpecificProductHeight:"0.000",ProductMeasurementUnit:"",LowerLevelPackagingUnit:"",RemainingVolumeAfterNesting:"0",MaximumStackingFactor:0,CapacityUsage:"0.000",BaseUnit:"EA"}}),to_ProductStorage:{StorageID:"",Product:(fr=e==null?void 0:e.materialNo)!=null&&fr.materialNumber?(Cr=e==null?void 0:e.materialNo)==null?void 0:Cr.materialNumber:"",StorageConditions:"",TemperatureConditionInd:"",HazardousMaterialNumber:"",NmbrOfGROrGISlipsToPrintQty:"",LabelType:"",LabelForm:"",MinRemainingShelfLife:"",ExpirationDate:"",ShelfLifeExpirationDatePeriod:null,TotalShelfLife:"",BaseUnit:((br=(xr=t==null?void 0:t.payloadData)==null?void 0:xr.BaseUnit)==null?void 0:br.code)??""},to_ProductSalesTax:[{SalesTaxID:"",Product:"",Country:"US",TaxCategory:"UTXJ",TaxClassification:"1",Status:""},{SalesTaxID:"",Product:"",Country:"IN",TaxCategory:"JOSG",TaxClassification:"1",Status:""},{SalesTaxID:"",Product:"",Country:"IN",TaxCategory:"JOIG",TaxClassification:"1",Status:""},{SalesTaxID:"",Product:"",Country:"IN",TaxCategory:"JOUG",TaxClassification:"1",Status:""},{SalesTaxID:"",Product:"",Country:"IN",TaxCategory:"JOCG",TaxClassification:"1",Status:""}],to_ProductSales:{SalesID:"",Product:(Sr=e==null?void 0:e.materialNo)!=null&&Sr.materialNumber?(Ir=e==null?void 0:e.materialNo)==null?void 0:Ir.materialNumber:"",SalesStatus:"",SalesStatusValidityDate:null,TaxClassification:"",TransportationGroup:""},to_ProductQualityMgmt:{QualityMgmtID:"",Product:(Tr=e==null?void 0:e.materialNo)!=null&&Tr.materialNumber?(vr=e==null?void 0:e.materialNo)==null?void 0:vr.materialNumber:"",QltyMgmtInProcmtIsActive:!1},to_ProductPurchaseText:[{PurchaseTextID:"",Product:(pr=e==null?void 0:e.materialNo)!=null&&pr.materialNumber?(Pr=e==null?void 0:e.materialNo)==null?void 0:Pr.materialNumber:"",Language:"",LongText:""}],to_ProductProcurement:{ProcurementID:"",Product:(Mr=e==null?void 0:e.materialNo)!=null&&Mr.materialNumber?(Nr=e==null?void 0:e.materialNo)==null?void 0:Nr.materialNumber:"",PurchaseOrderQuantityUnit:((wr=(Ar=t==null?void 0:t.payloadData)==null?void 0:Ar.OrderUnit)==null?void 0:wr.code)??"",VarblPurOrdUnitStatus:((Rr=(kr=t==null?void 0:t.payloadData)==null?void 0:kr.VarPurOrderUnitActive)==null?void 0:Rr.code)??"",PurchasingAcknProfile:""},to_ProductInspectionText:[{InspectionTextID:"",Product:(Dr=e==null?void 0:e.materialNo)!=null&&Dr.materialNumber?(Or=e==null?void 0:e.materialNo)==null?void 0:Or.materialNumber:"",Language:"",LongText:""}],to_ProductBasicText:[{BasicDataTextID:"",Product:(Ur=e==null?void 0:e.materialNo)!=null&&Ur.materialNumber?(Er=e==null?void 0:e.materialNo)==null?void 0:Er.materialNumber:"",Language:"",LongText:""}],to_Plant:[{PlantID:"",Product:(Gr=e==null?void 0:e.materialNo)!=null&&Gr.materialNumber?(Vr=e==null?void 0:e.materialNo)==null?void 0:Vr.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",PurchasingGroup:((_r=(yr=t==null?void 0:t.payloadData)==null?void 0:yr.PurchasingGroup)==null?void 0:_r.code)??"",CountryOfOrigin:"",RegionOfOrigin:"",ProductionInvtryManagedLoc:"",ProfileCode:((Lr=(Br=t==null?void 0:t.payloadData)==null?void 0:Br.PlantSpMatStatus)==null?void 0:Lr.code)??"",ProfileValidityStartDate:null,AvailabilityCheckType:"",FiscalYearVariant:"",PeriodType:"",ProfitCenter:((Wr=(Fr=t==null?void 0:t.payloadData)==null?void 0:Fr.ProfitCenter)==null?void 0:Wr.code)??"",Commodity:"",GoodsReceiptDuration:"",MaintenanceStatusName:"",IsMarkedForDeletion:!1,MRPType:((Qr=t==null?void 0:t.payloadData.MRPType)==null?void 0:Qr.code)??"",MRPResponsible:"",ABCIndicator:"",MinimumLotSizeQuantity:"",MaximumLotSizeQuantity:"",FixedLotSizeQuantity:"",ConsumptionTaxCtrlCode:"",IsCoProduct:!1,ProductIsConfigurable:"",StockDeterminationGroup:"",StockInTransferQuantity:"",StockInTransitQuantity:"",HasPostToInspectionStock:!1,IsBatchManagementRequired:((Hr=t==null?void 0:t.payloadData)==null?void 0:Hr.BatchManagementPlant)??!1,SerialNumberProfile:"",IsNegativeStockAllowed:!1,GoodsReceiptBlockedStockQty:"",HasConsignmentCtrl:"",FiscalYearCurrentPeriod:"",FiscalMonthCurrentPeriod:"",ProcurementType:"",IsInternalBatchManaged:((zr=t==null?void 0:t.payloadData)==null?void 0:zr.BatchManagement)??"",ProductCFOPCategory:"",ProductIsExciseTaxRelevant:!1,BaseUnit:((qr=($r=t==null?void 0:t.payloadData)==null?void 0:$r.BaseUnit)==null?void 0:qr.code)??"",ConfigurableProduct:"",GoodsIssueUnit:"",MaterialFreightGroup:((Jr=(jr=t==null?void 0:t.payloadData)==null?void 0:jr.MaterialFreightGroup)==null?void 0:Jr.code)??"",OriginalBatchReferenceMaterial:"",OriglBatchManagementIsRequired:"",ProductIsCriticalPrt:!1,ProductLogisticsHandlingGroup:"",to_PlantMRPArea:[{PlantMRPAreaID:""}],to_PlantQualityMgmt:{PlantQualityMgmtID:"",Product:(Yr=e==null?void 0:e.materialNo)!=null&&Yr.materialNumber?(Kr=e==null?void 0:e.materialNo)==null?void 0:Kr.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",MaximumStoragePeriod:"",QualityMgmtCtrlKey:"",MatlQualityAuthorizationGroup:"",HasPostToInspectionStock:!1,InspLotDocumentationIsRequired:!1,SuplrQualityManagementSystem:"",RecrrgInspIntervalTimeInDays:"",ProductQualityCertificateType:""},to_PlantSales:{PlantSalesID:"",Product:(Xr=e==null?void 0:e.materialNo)!=null&&Xr.materialNumber?(Zr=e==null?void 0:e.materialNo)==null?void 0:Zr.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",LoadingGroup:"",ReplacementPartType:"",CapPlanningQuantityInBaseUoM:"",ProductShippingProcessingTime:"",WrkCentersShipgSetupTimeInDays:"",AvailabilityCheckType:"",BaseUnit:((ta=(ea=t==null?void 0:t.payloadData)==null?void 0:ea.BaseUnit)==null?void 0:ta.code)??""},to_PlantStorage:{PlantStorageID:"",Product:(ra=e==null?void 0:e.materialNo)!=null&&ra.materialNumber?(aa=e==null?void 0:e.materialNo)==null?void 0:aa.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",InventoryForCycleCountInd:"",ProvisioningServiceLevel:"",CycleCountingIndicatorIsFixed:!1,ProdMaximumStoragePeriodUnit:"",WrhsMgmtPtwyAndStkRemovalStrgy:""},to_PlantText:[{PlantTextID:""}],to_ProdPlantInternationalTrade:{PlantInternationalTradeID:"",Product:(na=e==null?void 0:e.materialNo)!=null&&na.materialNumber?(ia=e==null?void 0:e.materialNo)==null?void 0:ia.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",CountryOfOrigin:"",RegionOfOrigin:"",ConsumptionTaxCtrlCode:"",ProductCASNumber:"",ProdIntlTradeClassification:"",ExportAndImportProductGroup:""},to_ProductPlantCosting:{PlantCostingID:"",Product:(oa=e==null?void 0:e.materialNo)!=null&&oa.materialNumber?(la=e==null?void 0:e.materialNo)==null?void 0:la.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",IsCoProduct:!1,CostingLotSize:"",VarianceKey:"",BaseUnit:((ca=(sa=t==null?void 0:t.payloadData)==null?void 0:sa.BaseUnit)==null?void 0:ca.code)??"",TaskListGroupCounter:"",TaskListGroup:"",TaskListType:"",CostingProductionVersion:"",IsFixedPriceCoProduct:!1,CostingSpecialProcurementType:"",SourceBOMAlternative:"",ProductBOMUsage:"",ProductIsCostingRelevant:!1},to_ProductPlantForecast:{PlantForecastID:"",Product:(ua=e==null?void 0:e.materialNo)!=null&&ua.materialNumber?(da=e==null?void 0:e.materialNo)==null?void 0:da.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",ConsumptionRefUsageEndDate:null,ConsumptionQtyMultiplier:"",ConsumptionReferenceProduct:"",ConsumptionReferencePlant:""},to_ProductPlantProcurement:{PlantProcurementID:"",Product:(ma=e==null?void 0:e.materialNo)!=null&&ma.materialNumber?(ha=e==null?void 0:e.materialNo)==null?void 0:ha.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",IsAutoPurOrdCreationAllowed:((ga=t==null?void 0:t.payloadData)==null?void 0:ga.AutomaticPO)??!1,IsSourceListRequired:!1,SourceOfSupplyCategory:"",ItmIsRlvtToJITDelivSchedules:""},to_ProductSupplyPlanning:{PlantSupplyPlanningID:"",Product:(fa=e==null?void 0:e.materialNo)!=null&&fa.materialNumber?(Ca=e==null?void 0:e.materialNo)==null?void 0:Ca.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",FixedLotSizeQuantity:"",MaximumLotSizeQuantity:"",MinimumLotSizeQuantity:"",LotSizeRoundingQuantity:"",LotSizingProcedure:"",MRPType:"",MRPResponsible:"",SafetyStockQuantity:"",MinimumSafetyStockQuantity:"",PlanningTimeFence:"",ABCIndicator:((ba=(xa=t==null?void 0:t.payloadData)==null?void 0:xa.ABCIndicator)==null?void 0:ba.code)??"",MaximumStockQuantity:"",ReorderThresholdQuantity:"",PlannedDeliveryDurationInDays:"",SafetyDuration:"",PlanningStrategyGroup:"",TotalReplenishmentLeadTime:"",ProcurementType:"",ProcurementSubType:"",AssemblyScrapPercent:"",AvailabilityCheckType:"",GoodsReceiptDuration:"",MRPGroup:"",DfltStorageLocationExtProcmt:"",ProdRqmtsConsumptionMode:"",BackwardCnsmpnPeriodInWorkDays:"",FwdConsumptionPeriodInWorkDays:"",BaseUnit:((Ia=(Sa=t==null?void 0:t.payloadData)==null?void 0:Sa.BaseUnit)==null?void 0:Ia.code)??"",PlanAndOrderDayDetermination:"",RoundingProfile:"",LotSizeIndependentCosts:"",MRPPlanningCalendar:"",RangeOfCvrgPrflCode:"",IsSafetyTime:"",PerdPrflForSftyTme:"",IsMRPDependentRqmt:"",InHouseProductionTime:"",ProductIsForCrossProject:"",StorageCostsPercentageCode:"",SrvcLvl:"",MRPAvailabilityType:"",FollowUpProduct:"",RepetitiveManufacturingIsAllwd:!1,DependentRequirementsType:"",IsBulkMaterialComponent:!1,RepetitiveManufacturingProfile:"",RqmtQtyRcptTaktTmeInWrkgDays:"",ForecastRequirementsAreSplit:"",EffectiveOutDate:null,MRPProfile:(m==null?void 0:m.MRPProfile)??"",ComponentScrapInPercent:"",ProductIsToBeDiscontinued:"",ProdRqmtsAreConsolidated:"",MatlCompIsMarkedForBackflush:"",ProposedProductSupplyArea:"",Currency:"",PlannedOrderActionControl:""},to_ProductWorkScheduling:{PlantWorkSchedulingID:"",Product:(Ta=e==null?void 0:e.materialNo)!=null&&Ta.materialNumber?(va=e==null?void 0:e.materialNo)==null?void 0:va.materialNumber:"",Plant:(m==null?void 0:m.Plant)??"",MaterialBaseQuantity:"",UnlimitedOverDelivIsAllowed:!1,OverDelivToleranceLimit:"",UnderDelivToleranceLimit:"",ProductionInvtryManagedLoc:"",BaseUnit:((Pa=(pa=t==null?void 0:t.payloadData)==null?void 0:pa.BaseUnit)==null?void 0:Pa.code)??"",ProductProcessingTime:"",ProductionSupervisor:"",ProductProductionQuantityUnit:"",ProdnOrderIsBatchRequired:"",TransitionMatrixProductsGroup:"",OrderChangeManagementProfile:"",MatlCompIsMarkedForBackflush:"",SetupAndTeardownTime:"",ProductionSchedulingProfile:"",TransitionTime:""},to_StorageLocation:[{StorageLocationID:""}]}],to_SalesDelivery:[{SalesDeliveryID:"",Product:(Ma=e==null?void 0:e.materialNo)!=null&&Ma.materialNumber?(Na=e==null?void 0:e.materialNo)==null?void 0:Na.materialNumber:"",ProductSalesOrg:(m==null?void 0:m.SalesOrganization)??"",ProductDistributionChnl:(m==null?void 0:m.DistributionChannel)??"",MinimumOrderQuantity:((Aa=t==null?void 0:t.payloadData)==null?void 0:Aa.MinOrderQty)??"",SupplyingPlant:"",PriceSpecificationProductGroup:"",AccountDetnProductGroup:"",DeliveryNoteProcMinDelivQty:((wa=t==null?void 0:t.payloadData)==null?void 0:wa.MinDelQty)??"",ItemCategoryGroup:((Ra=(ka=t==null?void 0:t.payloadData)==null?void 0:ka.Itemcategorygroup)==null?void 0:Ra.code)??"NORM",DeliveryQuantityUnit:"",DeliveryQuantity:((Oa=(Da=t==null?void 0:t.payloadData)==null?void 0:Da.DeliveryUnit)==null?void 0:Oa.code)??"",ProductSalesStatus:"",ProductSalesStatusValidityDate:null,SalesMeasureUnit:((Ea=(Ua=t==null?void 0:t.payloadData)==null?void 0:Ua.Salesunit)==null?void 0:Ea.code)??"",IsMarkedForDeletion:!1,ProductHierarchy:"",FirstSalesSpecProductGroup:((Va=(Ga=t==null?void 0:t.payloadData)==null?void 0:Ga.MaterialGroup1)==null?void 0:Va.code)??"",SecondSalesSpecProductGroup:((_a=(ya=t==null?void 0:t.payloadData)==null?void 0:ya.MaterialGroup2)==null?void 0:_a.code)??"",ThirdSalesSpecProductGroup:((La=(Ba=t==null?void 0:t.payloadData)==null?void 0:Ba.MaterialGroup3)==null?void 0:La.code)??"",FourthSalesSpecProductGroup:((Wa=(Fa=t==null?void 0:t.payloadData)==null?void 0:Fa.MaterialGroup4)==null?void 0:Wa.code)??"",FifthSalesSpecProductGroup:((Ha=(Qa=t==null?void 0:t.payloadData)==null?void 0:Qa.MaterialGroup5)==null?void 0:Ha.code)??"",MinimumMakeToOrderOrderQty:((za=t==null?void 0:t.payloadData)==null?void 0:za.MakeToOrderQty)??"",BaseUnit:((qa=($a=t==null?void 0:t.payloadData)==null?void 0:$a.BaseUnit)==null?void 0:qa.code)??"",LogisticsStatisticsGroup:"",VolumeRebateGroup:"",ProductCommissionGroup:"",CashDiscountIsDeductible:!1,PricingReferenceProduct:"",RoundingProfile:"",ProductUnitGroup:"",VariableSalesUnitIsNotAllowed:!1,ProductHasAttributeID01:!1,ProductHasAttributeID02:!1,ProductHasAttributeID03:!1,ProductHasAttributeID04:!1,ProductHasAttributeID05:!1,ProductHasAttributeID06:!1,ProductHasAttributeID07:!1,ProductHasAttributeID08:!1,ProductHasAttributeID09:!1,ProductHasAttributeID10:!1,to_SalesTax:[{SalesTaxID:""}],to_SalesText:[{SalesTextID:""}]}],to_Valuation:[{ValuationID:"",Product:(ja=e==null?void 0:e.materialNo)!=null&&ja.materialNumber?(Ja=e==null?void 0:e.materialNo)==null?void 0:Ja.materialNumber:"",ValuationArea:"",ValuationType:"",ValuationClass:((Ka=(Ya=t==null?void 0:t.payloadData)==null?void 0:Ya.ValuationClass)==null?void 0:Ka.code)??"",PriceDeterminationControl:"",StandardPrice:((Xa=t==null?void 0:t.payloadData)==null?void 0:Xa.StandardPrice)??"",PriceUnitQty:((en=(Za=t==null?void 0:t.payloadData)==null?void 0:Za.PriceUnit)==null?void 0:en.code)??"",InventoryValuationProcedure:"",IsMarkedForDeletion:!1,MovingAveragePrice:((tn=t==null?void 0:t.payloadData)==null?void 0:tn.PerUnitPrice)??"",ValuationCategory:"",ProductUsageType:"",ProductOriginType:"",IsProducedInhouse:!1,ProdCostEstNumber:"",ProjectStockValuationClass:"",ValuationClassSalesOrderStock:"",PlannedPrice1InCoCodeCrcy:"",PlannedPrice2InCoCodeCrcy:"",PlannedPrice3InCoCodeCrcy:"",FuturePlndPrice1ValdtyDate:null,FuturePlndPrice2ValdtyDate:null,FuturePlndPrice3ValdtyDate:null,TaxBasedPricesPriceUnitQty:"",PriceLastChangeDate:null,PlannedPrice:"",PrevInvtryPriceInCoCodeCrcy:"",Currency:"",BaseUnit:"",to_MLAccount:[{MLAccountID:""}],to_MLPrices:[{MLPricesID:""}],to_ValuationAccount:{ValuationAccountID:"",Product:(rn=e==null?void 0:e.materialNo)!=null&&rn.materialNumber?(an=e==null?void 0:e.materialNo)==null?void 0:an.materialNumber:"",ValuationArea:"",ValuationType:"",CommercialPrice1InCoCodeCrcy:"",CommercialPrice2InCoCodeCrcy:"",CommercialPrice3InCoCodeCrcy:"",DevaluationYearCount:"",FutureEvaluatedAmountValue:"",FuturePriceValidityStartDate:null,IsLIFOAndFIFORelevant:!1,LIFOValuationPoolNumber:"",TaxPricel1InCoCodeCrcy:"",TaxPrice2InCoCodeCrcy:"",TaxPrice3InCoCodeCrcy:"",Currency:""},to_ValuationCosting:{ValuationCostingID:"",Product:(nn=e==null?void 0:e.materialNo)!=null&&nn.materialNumber?(on=e==null?void 0:e.materialNo)==null?void 0:on.materialNumber:"",ValuationArea:"",ValuationType:"",IsMaterialCostedWithQtyStruc:!1,IsMaterialRelatedOrigin:"",CostOriginGroup:"",CostingOverheadGroup:""}}]};const di=(a,i)=>{Hn(i)},Ze=()=>{N("/masterDataCockpit/materialMaster/materialSingle")},mi=()=>{N("/masterDataCockpit/materialMaster/materialSingle")},re=()=>{P(!1)},hi=()=>{$e(!1)},Ve=()=>{g(!0)},xe=()=>{$e(!0)},gi=()=>{B(!0)},ce=()=>Ki(o,jn,zn),et=p(a=>a.payload.generalInformation);console.log("giinfo",et);const[fi,ho]=l.useState([]);console.log("qqqqq",fi);const Ci=()=>{let a={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};d(!0);const i=I=>{var T,k,w,G;if(d(!1),I.statusCode===200){let y=(k=(T=I==null?void 0:I.data)==null?void 0:T.result[0])==null?void 0:k.MDG_ATTACHMENTS_ACTION_TYPE;console.log("resss1",y);let E=[];y.map((W,Z)=>{console.log("resss2",W);var M={id:Z};E.push(M)}),console.log("resss3",E),Ln(E);const Y=((G=(w=I==null?void 0:I.data)==null?void 0:w.result[0])==null?void 0:G.MDG_ATTACHMENTS_ACTION_TYPE)||[];Wn(Y)}re()},u=I=>{console.log(I)};Qn.environment==="localhost"?q(`/${On}/rest/v1/invoke-rules`,"post",i,u,a):q(`/${On}/v1/invoke-rules`,"post",i,u,a)},tt=()=>{_n(!1)},rt=(a,i)=>{const u=a.target.value;if(u.length>0&&u[0]===" ")Ye(u.trimStart());else{let I=u.toUpperCase();Ye(I)}},xi=()=>{var I,T,k,w,G,y,E,Y,W,Z;if(ce())if(A===((T=(I=e==null?void 0:e.selectedViews)==null?void 0:I.value)==null?void 0:T.map(M=>M.value).findIndex(M=>M==="basicData"))){var i=`/${Q}/alter/saveBasicDataViewAsDraft`;Ge(!0),$(!0),q(i,"post",v=>{console.log(v,"example"),d();let Ie={};v.statusCode===200?(Ie.keyName="creationId",Ie.data=v.body,console.log(Ie),n(ue(Ie)),de(!0),$(!1),K("success"),X(`${v.message} With ID: ${v.body}`),Ce.map(vn=>{console.log(vn,"artifactId=====");const Mi={artifactId:vn,createdBy:F==null?void 0:F.emailId,artifactType:"Material",requestId:`NMS${v==null?void 0:v.body}`},Ni=ye=>{console.log("Second API success",ye)},Ai=ye=>{console.error("Second API error",ye)};q(`/${Fe}/documentManagement/updateDocRequestId`,"post",Ni,Ai,Mi)})):($(!1),K("error"),X("Failed to save Basic Data"),n(_e()),n(Be()),n(Le())),re()},v=>{console.log(v)},oe)}else{if(A===((w=(k=e==null?void 0:e.selectedViews)==null?void 0:k.value)==null?void 0:w.map(v=>v.value).findIndex(v=>v==="sales")))var u=`/${Q}/alter/saveSalesViewAsDraft`;else if(A===((y=(G=e==null?void 0:e.selectedViews)==null?void 0:G.value)==null?void 0:y.map(v=>v.value).findIndex(v=>v==="purchasing")))var u=`/${Q}/alter/savePurchaseViewAsDraft`;else if(A===((Y=(E=e==null?void 0:e.selectedViews)==null?void 0:E.value)==null?void 0:Y.map(v=>v.value).findIndex(v=>v==="mrp")))var u=`/${Q}/alter/saveMRPViewAsDraft`;else if(A===((Z=(W=e==null?void 0:e.selectedViews)==null?void 0:W.value)==null?void 0:Z.map(v=>v.value).findIndex(v=>v==="accounting")))var u=`/${Q}/alter/saveAccountingAsDraft`;q(u,"post",v=>{d(),v.statusCode===201&&(console.log("success"),ne("Create"),we("Data has been saved successfully"),ke("success"),Re(!1),de(!0),Ve(),De(!0),d(!1)),re()},v=>{console.log(v)},oe)}else xe()},bi=()=>{var u,I;if(Ge(!0),$(!0),Se(),ce()){if(Bn(!0),A===((I=(u=e==null?void 0:e.selectedViews)==null?void 0:u.value)==null?void 0:I.map(T=>T.value).findIndex(T=>T==="basicData"))){n(Un(A));var i=`/${Q}/alter/createBasicDataAndForward`;q(i,"post",w=>{console.log(w,"example"),d(),console.log("TESTDATA"),w.statusCode===200?(n(ue({keyName:"creationId",data:w.body})),console.log("success"),$(!1),K("success"),X(`Material  has been created successfully with creation ID ${w.body}`),Ce.map(G=>{console.log(G,"artifactId=====");const y={artifactId:G,createdBy:F==null?void 0:F.emailId,artifactType:"Material",requestId:`NMS${w==null?void 0:w.body}`},E=W=>{console.log("Second API success",W)},Y=W=>{console.error("Second API error",W)};q(`/${Fe}/documentManagement/updateDocRequestId`,"post",E,Y,y)})):($(!1),K("error"),X("Creation Failed")),re()},w=>{console.log(w),$(!1),K("error"),X("Creation Failed")},oe)}}else xe()},Si=()=>{var u,I,T,k,w,G,y,E;if(ce()){if(n(Un(A)),ce()){if(A===((I=(u=e==null?void 0:e.selectedViews)==null?void 0:u.value)==null?void 0:I.map(M=>M.value).findIndex(M=>M==="sales")))var i=`/${Q}/alter/createSalesAndSubmitForReview`;else if(A===((k=(T=e==null?void 0:e.selectedViews)==null?void 0:T.value)==null?void 0:k.map(M=>M.value).findIndex(M=>M==="purchasing")))var i=`/${Q}/alter/createPurchaseAndSubmitForReview`;else if(A===((G=(w=e==null?void 0:e.selectedViews)==null?void 0:w.value)==null?void 0:G.map(M=>M.value).findIndex(M=>M==="mrp")))var i=`/${Q}/alter/createMRPAndSubmitForReview`;else if(A===((E=(y=e==null?void 0:e.selectedViews)==null?void 0:y.value)==null?void 0:E.map(M=>M.value).findIndex(M=>M==="accounting")))var i=`/${Q}/alter/createAccountingAndSubmitForReview`;q(i,"post",M=>{d(),M.statusCode===201&&(console.log("success"),ne("Create"),we("Material has been submitted for review"),ke("success"),Re(!1),de(!0),Ve(),De(!0),d(!1)),re()},M=>{console.log(M)},oe)}}else xe()},Ii=()=>{je(!0)},Ti=()=>{Je(!0)},be=()=>{je(!1)},Se=()=>{Je(!1)},vi=()=>{if(Ge(!0),$(!0),be(),ce()){if(te.value.length===1){var i=`/${Q}/alter/createBasicDataApprovalSubmit`;q(i,"post",T=>{T.statusCode===200?(n(ue({keyName:"creationId",data:T.body})),$(!1),K("success"),X(`Material  has been created successfully with Material No ${T.body}`),n(_e()),n(Be()),n(Le()),Ce.map(k=>{const w={artifactId:k,createdBy:F==null?void 0:F.emailId,artifactType:"Material",requestId:`NMS${T==null?void 0:T.body}`},G=E=>{console.log("Second API success",E)},y=E=>{console.error("Second API error",E)};q(`/${Fe}/documentManagement/updateDocRequestId`,"post",G,y,w)})):($(!1),K("error"),X("Creation Failed")),re()},T=>{$(!1),K("error"),X("Creation Failed")},oe)}else if(ie.length>1){var i=`/${Q}/alter/allViewsApproved`;q(i,"post",k=>{console.log(k,"example"),d(),k.statusCode===201&&(n(ue({keyName:"creationId",data:k.body})),console.log("success"),ne("Create"),we(`Material  has been created successfully with creation ID ${k.body}`),ke("success"),Re(!1),de(!0),Ve(),De(!0),d(!1)),re()},k=>{console.log(k)},oe)}}else xe()};l.useEffect(()=>{console.log("pay",t==null?void 0:t.payloadData)},[t]);const at=(sn=(ln=e==null?void 0:e.selectedViews)==null?void 0:ln.value)==null?void 0:sn.map(a=>(console.log("TabContents",a.value),(a==null?void 0:a.value)=="basicData"?[r(eo,{basicData:f,setBasicData:x,dropDownData:V,basicDataTabDetails:Oe})]:(a==null?void 0:a.value)=="classification"?[r(to,{basicData:f,setBasicData:x,dropDownData:V})]:(a==null?void 0:a.value)=="sales"?[r(ao,{basicData:f,setBasicData:x,dropDownData:V,salesDataTabDetails:Jn})]:a.value=="purchasing"?[r(no,{basicData:f,setBasicData:x,dropDownData:V,purchasingDataTabDetails:Yn})]:a.value=="mrp"?[r(io,{basicData:f,setBasicData:x,dropDownData:V,MRPDataTabDetails:Kn})]:a.value=="accounting"?[r(oo,{basicData:f,setBasicData:x,dropDownData:V,accountingTabDetails:Xn})]:a.value=="storageLocationStocks"?[r("h1",{children:"Storage Location Stocks Data"})]:a.value=="storage"?[r("h1",{children:"Storage Data"})]:a.value=="qualityManagement"?[r("h1",{children:'"Quality Management" Data'})]:a.value=="attachments&comments"?[r(ae,{children:Fn.map((i,u)=>c(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",padding:"1rem 1.5rem"},children:[r(s,{container:!0,children:r(b,{sx:{fontSize:"12px",fontWeight:"700"},children:i.MDG_ATTACHMENTS_NAME})}),r(s,{container:!0,children:r(s,{item:!0,children:r(An,{title:"Material",useMetaData:!1,artifactId:`${i.MDG_ATTACHMENTS_NAME}_${He}`,artifactName:"Material",attachmentType:i.MDG_ATTACHMENTS_NAME})})})]},u))})]:a.value=="generalInformation"?[r(lo,{questions:et})]:[r("h1",{children:"attach"})])),pi=(a,i,u)=>{if(ze)return!1;const I=u[u.length-1],T=i.indexOf(I);return!(a===0||a===1||a===T)};return console.log("factor",ie),c("div",{children:[r(Yi,{dialogState:U,openReusableDialog:gi,closeReusableDialog:Ze,dialogTitle:z,dialogMessage:Ae,handleDialogConfirm:Ze,dialogOkText:"OK",handleExtraButton:mi,dialogSeverity:Gn}),r(Te,{open:qn,onClose:Xe,sx:{display:"flex",justifyContent:"center"},children:c(H,{sx:{width:"600px !important"},children:[c(ve,{children:[r(Xi,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),r("span",{children:"Enter Comments for Correction"})]}),r(pe,{children:r(s,{container:!0,columnSpacing:1,children:r("textarea",{})})}),c(Pe,{children:[r(_,{onClick:Xe,children:"Cancel"}),r(_,{onClick:ci,children:"OK"})]})]})}),r(Zi,{isLoadingToast:ni,type:ii,message:oi,isToastPromise:li}),me.length!=0&&r(Wi,{openSnackBar:$n,alertMsg:"Please fill the following Field: "+me.join(", "),handleSnackBarClose:hi}),r(s,{container:!0,style:{...wn,backgroundColor:"#FAFCFF"},children:c(s,{sx:{width:"inherit"},children:[c(s,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[r(s,{item:!0,md:11,style:{padding:"16px",display:"flex"},children:c("div",{style:{display:"flex"},children:[r("div",{children:r(Qi,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{N("/masterDataCockpit/materialMaster/newMaterial"),n(_e()),n(Be()),n(Le()),n(Hi({module:"DuplicateDesc"}))}})}),c("div",{children:[r(b,{variant:"h3",children:r("strong",{children:"Create Material"})}),r(b,{variant:"body2",color:"#777",children:"This view creates a new material"})]})]})}),c(Te,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:yn,onClose:tt,children:[r(ve,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:r(b,{variant:"h6",children:"Add Attachment"})}),r(pe,{sx:{padding:".5rem 1rem"},children:r(j,{children:r(H,{sx:{minWidth:400},children:r(An,{title:"Material",useMetaData:!1,artifactId:He,artifactName:"Material",attachmentType:"SAP Error"})})})}),r(Pe,{children:r(_,{onClick:tt,children:"Close"})})]})]}),r(s,{container:!0,style:{padding:"0 1rem 0 1rem"},children:c(s,{container:!0,sx:wn,children:[c(s,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[c(H,{width:"70%",sx:{marginLeft:"40px"},children:[r(s,{item:!0,sx:{paddingTop:"2px !important"},children:c(j,{flexDirection:"row",children:[r("div",{style:{width:"15%"},children:r(b,{variant:"body2",color:"#777",children:"Material"})}),c(b,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",(cn=e==null?void 0:e.materialNo)==null?void 0:cn.materialNumber]})]})}),r(s,{item:!0,sx:{paddingTop:"2px !important"},children:c(j,{flexDirection:"row",children:[r("div",{style:{width:"15%"},children:r(b,{variant:"body2",color:"#777",children:"Material Description"})}),c(b,{variant:"body2",fontWeight:"bold",children:[": ",J==null?void 0:J.description]})]})}),r(s,{item:!0,sx:{paddingTop:"2px !important"},children:c(j,{flexDirection:"row",children:[r("div",{style:{width:"15%"},children:r(b,{variant:"body2",color:"#777",children:"Industry Sector"})}),c(b,{variant:"body2",fontWeight:"bold",children:[": ",(un=e==null?void 0:e.industrySector)!=null&&un.industryReference?`${(dn=e==null?void 0:e.industrySector)==null?void 0:dn.industryReference} - ${(mn=e==null?void 0:e.industrySectorDesc)==null?void 0:mn.industrySectorDesc}`:""]})]})}),r(s,{item:!0,sx:{paddingTop:"2px !important"},children:c(j,{flexDirection:"row",children:[r("div",{style:{width:"15%"},children:r(b,{variant:"body2",color:"#777",children:"Material Type"})}),c(b,{variant:"body2",fontWeight:"bold",children:[": ",(hn=e==null?void 0:e.materialType)==null?void 0:hn.fieldReference," - ",(gn=e==null?void 0:e.materialTypeDesc)==null?void 0:gn.materialTypeDesc]})]})})]}),r(H,{width:"30%",sx:{marginLeft:"40px"},children:(fn=e==null?void 0:e.orgData)==null?void 0:fn.map(a=>{var i,u,I,T;return r(s,{item:!0,children:c(j,{flexDirection:"row",children:[r(b,{variant:"body2",color:"#777",style:{width:"30%"},children:a.info?a.desc:""}),r(b,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"},children:a.info?":":""}),c(b,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[(i=a==null?void 0:a.info)==null?void 0:i.code,(u=a==null?void 0:a.info)!=null&&u.code&&((I=a==null?void 0:a.info)!=null&&I.desc)?" - ":null,(T=a==null?void 0:a.info)==null?void 0:T.desc]})]})})})})]}),Oe&&r(s,{container:!0,children:r(zi,{value:A,onChange:di,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:fe==null?void 0:fe.map((a,i)=>(console.log("allmate",a,i),$i(ri,"Create Single Material",a)&&(ai[F.role]=="MDM Steward",ie.includes(a))&&r(qi,{sx:{fontSize:"12px",fontWeight:"700"},label:a,disabled:pi(i,fe,ie)},i)))})}),r(s,{container:!0,children:Oe&&at&&((Cn=at[A])==null?void 0:Cn.map((a,i)=>r(H,{sx:{width:"100%"},children:a},i)))})]})})]})}),r(Ji,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:c(ji,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:R,children:[A===1?r(_,{variant:"outlined",size:"small",sx:{button_Outlined:We,mr:1},onClick:()=>{N("/masterDataCockpit/materialMaster/createMaterialDetail/additionalData")},children:"Additional Data"}):null,A!==0&&A!==((xn=e==null?void 0:e.selectedViews)==null?void 0:xn.value.length)-1?r(_,{variant:"contained",size:"small",sx:{...Me,mr:1},onClick:xi,disabled:se.filter(a=>a.status==!0).findIndex(a=>{var i,u;return a.keyname==((u=(i=e==null?void 0:e.selectedViews)==null?void 0:i.value)==null?void 0:u.map(I=>I.value)[A])})!=-1,children:"Save As Draft"}):null,A===((Sn=(bn=e==null?void 0:e.selectedViews)==null?void 0:bn.value)==null?void 0:Sn.map(a=>a.value).findIndex(a=>a==="basicData"))&&((In=e==null?void 0:e.selectedViews)==null?void 0:In.value.length)>3&&se.filter(a=>a.status===!0).length!==se.length?r(_,{variant:"contained",size:"small",disabled:!!ze,sx:{...Me,mr:1},onClick:Ti,children:"Forward"}):null,A!==0&&A!==1&&A!==((Tn=e==null?void 0:e.selectedViews)==null?void 0:Tn.value.length)-1?r(_,{variant:"contained",size:"small",sx:{...Me},onClick:Si,children:"Submit for Review"}):null,A===1&&te.value.length===1?r(_,{variant:"contained",size:"small",sx:{...Me},onClick:Ii,children:"Submit for Approval"}):null,c(Te,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none",marginBottom:"8px",borderRadius:"8px"}},open:ei,onClose:be,children:[c(ve,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",borderBottom:"3px ",display:"flex",marginBottom:"8px"},children:[r(b,{variant:"h6",children:"Remarks"}),r(kn,{sx:{width:"max-content"},onClick:be,children:r(Rn,{})})]}),r(pe,{sx:{padding:".5rem 1rem"},children:r(j,{children:r(H,{sx:{minWidth:400},children:r(Dn,{sx:{height:"auto"},fullWidth:!0,children:r(ee,{sx:{backgroundColor:"#F5F5F5"},value:ge,onChange:rt,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),c(Pe,{sx:{display:"flex",justifyContent:"end"},children:[r(_,{sx:{width:"max-content",textTransform:"capitalize"},onClick:be,variant:"outlined",children:"Cancel"}),r(_,{className:"button_primary--normal",type:"save",onClick:vi,variant:"contained",children:"Submit"})]})]}),c(Te,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none",marginBottom:"8px",borderRadius:"8px"}},open:ti,onClose:Se,children:[c(ve,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",borderBottom:"3px",display:"flex",marginBottom:"8px"},children:[r(b,{variant:"h6",children:"Remarks"}),r(kn,{sx:{width:"max-content"},onClick:Se,children:r(Rn,{})})]}),r(pe,{sx:{padding:".5rem 1rem"},children:r(j,{children:r(H,{sx:{minWidth:400},children:r(Dn,{sx:{height:"auto"},fullWidth:!0,children:r(ee,{sx:{backgroundColor:"#F5F5F5"},value:ge,onChange:rt,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),c(Pe,{sx:{display:"flex",justifyContent:"end"},children:[r(_,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Se,variant:"outlined",children:"Cancel"}),r(_,{className:"button_primary--normal",type:"save",onClick:bi,variant:"contained",children:"Submit"})]})]})]})})]})};export{No as default};
