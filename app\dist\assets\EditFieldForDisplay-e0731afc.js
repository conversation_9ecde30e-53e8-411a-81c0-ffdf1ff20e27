import{r as i,i as A,l as z,is as f,a,g_ as O,j as S,T as p,hy as P,a3 as v,gR as b,gS as w,hJ as R,hA as T,F as m,$ as I}from"./index-fdfa25a0.js";import{d as L}from"./dayjs.min-774e293a.js";function B(r,t){return Array.isArray(t)&&t.find(y=>y.code===r)||""}const H=({label:r,value:t,units:g,onSave:y,isEditMode:F,isExtendMode:E,options:G=[],type:c})=>{var D;const[n,d]=i.useState(t),[J,x]=i.useState(!1),s=A(e=>e.AllDropDown.dropDown),h=z(),j=B(n,s);console.log("dropdownData",n),console.log("value e",t),console.log("label",r),console.log("units",g),console.log("transformedValue",j);const C=A(e=>e.edit.payload);let $=A(e=>e.payload.errorFields);console.log("editField",C),console.log("fieldData",{label:r,value:n,units:g,type:c});let o=r.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");i.useEffect(()=>{d(t)},[t]);const u=e=>{console.log("checkonedit"),h(f({keyname:o.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:e}))};i.useEffect(()=>{console.log("lkey",o),console.log("data",t),h(f({keyname:o.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:t||""}))},[]);const V=L();return console.log("editedValue[key] ",s[o]),console.log("editedValue[key] ",n),a(I,{item:!0,children:a(O,{children:F||E?S(m,{children:[a(p,{variant:"body2",color:"#777",children:r}),c==="Drop Down"?a(P,{options:s[o]??[],value:n&&((D=s[o])==null?void 0:D.filter(e=>e.code===n))||"",onChange:(e,l)=>{u(l.code),console.log("newValue",l),d(l.code),x(!0),console.log("keys",o)},getOptionLabel:e=>{var l,k;return console.log("optionoptionoption",e),e===""?"":`${e&&((l=e[0])==null?void 0:l.code)} - ${e&&((k=e[0])==null?void 0:k.desc)}`},renderOption:(e,l)=>(console.log("option vakue",l),a("li",{...e,children:a(p,{style:{fontSize:12},children:`${l==null?void 0:l.code} - ${l==null?void 0:l.desc}`})})),renderInput:e=>a(v,{...e,variant:"outlined",size:"small",label:null,placeholder:`Select ${r}`})}):c==="Input"?a(v,{variant:"outlined",size:"small",value:n,onChange:e=>{const l=e.target.value;u(l),d(l)},placeholder:`Enter ${r}`}):c==="Calendar"?a(b,{dateAdapter:w,children:a(R,{slotProps:{textField:{size:"small"}},value:s[o]?s[o]:"",defaultValue:V,placeholder:"Select Date Range",onChange:e=>h(f({keyname:o,data:"/Date("+Date.parse(e)+")/"})),onError:$.includes(o)})}):c==="Radio Button"?a(T,{sx:{padding:0},checked:n,onChange:(e,l)=>{u(l),d(l)}}):""]}):a(m,{children:S(m,{children:[a(p,{variant:"body2",color:"#777",children:r}),a(p,{variant:"body2",fontWeight:"bold",children:n})]})})})})};export{H as E};
