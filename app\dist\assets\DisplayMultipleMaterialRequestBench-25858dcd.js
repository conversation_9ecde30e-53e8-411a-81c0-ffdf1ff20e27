import{b as P,l as V,r as d,i as u,u as $,m5 as j,a as t,j as a,h_ as K,T as r,B as x,gZ as Z,$ as n,hp as G,W as J,hv as Q,i8 as U,g_ as m,aa as X,ab as Y,w as ee,G as te,hE as ae,a1 as ie,P as ne}from"./index-fdfa25a0.js";import{E as re}from"./EditFieldForDisplay-e0731afc.js";import"./dayjs.min-774e293a.js";const he=()=>{var S;const v=P(),w=V();d.useState({});const[I,E]=d.useState(0),[W,F]=d.useState([]),[N,oe]=d.useState(!1);d.useState(!0);let[g,k]=d.useState([]);const[C,_]=d.useState([]),[M,A]=d.useState(0),[le,R]=d.useState(),q=u(e=>e.tabsData),L={basicData:"Basic Data"},D=$();u(e=>e.initialData.EditMultipleMaterial);const y=u(e=>e.initialData.MultipleMaterialRequestBench);u(e=>{var l;return(l=e==null?void 0:e.initialData)==null?void 0:l.IWMMyTask});let o=u(e=>e.userManagement.taskData);const p=D.state.rowData;console.log("rowData",p),console.log("task",o),D.state.requestNumber,u(e=>e.payload);for(let e=0;e<y.length;e++)if(y[e].Description===p.description){y[e];break}const z=(e,l)=>{A(l)},H=()=>{var s,h;let e={};(o==null?void 0:o.processDesc)==="Mass Change"?e={massCreationId:"",massChangeId:(s=o==null?void 0:o.subject)==null?void 0:s.slice(3),screenName:"Change"}:(o==null?void 0:o.processDesc)==="Mass Create"&&(e={massCreationId:(h=o==null?void 0:o.subject)==null?void 0:h.slice(3),massChangeId:"",screenName:"Create"}),console.log(e);const l=i=>{F(i==null?void 0:i.body[0]);const T=i.body[0].viewData;R(i.body[0].IDs);const f=Object.keys(T);console.log("categorry",f),k(f);const O=f.map(B=>({category:B,data:T[B]}));_(O),console.log("materialDetails",C),w(j(i==null?void 0:i.body))},c=i=>{console.log(i)};ee(`/${te}/data/displayMassMaterial`,"post",l,c,e)};console.log("factorsArray",g),d.useEffect(()=>{H(),w(j(W))},[]);const b=g.map(e=>{var s;const l=(s=Object.entries(q).filter(h=>{var i;return((i=L[h[0]])==null?void 0:i.split(" ")[0])==(e==null?void 0:e.split(" ")[0])})[0])==null?void 0:s[1],c=C.filter(h=>{var i;return((i=h.category)==null?void 0:i.split(" ")[0])==(e==null?void 0:e.split(" ")[0])});return c.length!==0?{category:e==null?void 0:e.split(" ")[0],data:c[0].data}:{category:e==null?void 0:e.split(" ")[0],data:l}}).map((e,l)=>(console.log("categorydata",e),(e==null?void 0:e.category)=="Basic"?[t(n,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(e.data).map(c=>a(n,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...K},children:[t(r,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:c}),t(x,{sx:{width:"100%"},children:t(Z,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:t(n,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:e.data[c].map(s=>t(re,{label:s.fieldName,value:s.value,onSave:h=>handleFieldSave(s.fieldName,h),type:s.fieldType,field:s}))})})})]},c))},e.category)]:null));return console.log(b,"lololol"),a("div",{children:[t(n,{container:!0,style:{...G,backgroundColor:"#FAFCFF"},children:a(n,{sx:{width:"inherit"},children:[a(n,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[t(n,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:t(J,{color:"primary","aria-label":"upload picture",component:"label",sx:Q,children:t(U,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{v("/RequestBench")}})})}),a(n,{md:8,children:[t(r,{variant:"h3",children:a("strong",{children:["Multiple Material : ",p.description," "]})}),t(r,{variant:"body2",color:"#777",children:"This view displays details of uploaded material"})]})]}),a(n,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[a(x,{width:"70%",sx:{marginLeft:"40px"},children:[t(n,{item:!0,sx:{paddingTop:"2px !important"},children:a(m,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(r,{variant:"body2",color:"#777",children:"Material"})}),a(r,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",p.material]})]})}),t(n,{item:!0,sx:{paddingTop:"2px !important"},children:a(m,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(r,{variant:"body2",color:"#777",children:"Material Type"})}),a(r,{variant:"body2",fontWeight:"bold",children:[": ",p.materialType]})]})}),t(n,{item:!0,sx:{paddingTop:"2px !important"},children:a(m,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(r,{variant:"body2",color:"#777",children:"Description"})}),a(r,{variant:"body2",fontWeight:"bold",children:[": ",p.description]})]})}),t(n,{item:!0,sx:{paddingTop:"2px !important"},children:a(m,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(r,{variant:"body2",color:"#777",children:"Industry Sector"})}),a(r,{variant:"body2",fontWeight:"bold",children:[": ",p.industrySector]})]})})]}),t(x,{width:"30%",sx:{marginLeft:"40px"},children:t(n,{item:!0,children:a(m,{flexDirection:"row",children:[t(r,{variant:"body2",color:"#777",style:{width:"30%"}}),t(r,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"}}),t(r,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start"})]})})})]}),a(n,{container:!0,style:{padding:"16px"},children:[t(x,{sx:{borderBottom:1,borderColor:"divider"},children:t(X,{value:M,onChange:z,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:g.map((e,l)=>t(Y,{sx:{fontSize:"12px",fontWeight:"700"},label:e},l))})}),t(n,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:b&&((S=b[M])==null?void 0:S.map((e,l)=>t(x,{sx:{mb:2,width:"100%"},children:t(r,{variant:"body2",children:e})},l)))},b)]})]})}),N?t(ne,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:t(ae,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:I,onChange:e=>{E(e)},children:t(ie,{size:"small",variant:"contained",onClick:()=>{v("/masterDataCockpit/materialMaster/massMaterialTable")},children:"Save"})})}):""]})};export{he as default};
