import{ah as A,ai as I,aj as j,b as B,r as h,a as e,hj as m,af as L,B as _,i as D,e as O,w as P,hd as C,hl as U,j as c,$ as W,hq as $,T as R,Z as q,P as H,M as G,N as V,O as M,Q as a,S as w,g_ as z}from"./index-fdfa25a0.js";import{i as Q}from"./index-7377b8bc.js";import"./index-3f2e0745.js";import"./DialogContentText-8ac052ae.js";import"./ListItemButton-db9eb0d0.js";import"./StepButton-fdbf0590.js";import"./ToggleButtonGroup-c02e6027.js";import"./makeStyles-1dfd4db4.js";import"./Check-aa258898.js";import"./DeleteOutline-1f72afa8.js";import"./asyncToGenerator-88583e02.js";import"./FileUploadOutlined-bf833d2e.js";import"./DeleteOutlineOutlined-9e9a8646.js";import"./dayjs.min-774e293a.js";import"./CheckBox-6243e871.js";var x={},Z=I;Object.defineProperty(x,"__esModule",{value:!0});var y=x.default=void 0,J=Z(A()),K=j;y=x.default=(0,J.default)((0,K.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 5.63l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41"}),"ModeEditOutline");const X=t=>{var g;const l=B(),p=[{Description:"",Name:"WorkRulesServices",URL:m},{Description:"",Name:"CW_Worktext",URL:m},{Description:"",Name:"WorkRuleEngineServices",URL:m},{Description:"",Name:"WorkUtilsServices",URL:m}],u=()=>{var d,r;({...t.DTdetails},t.setShowDT(!1)),l((r=(d=L)==null?void 0:d.BUSINESS_RULES)==null?void 0:r.AUTHORING)},f={headerColors_C:"#DFEAFB",headerColors_A:"#FFF7E2",headerColors_P:"#EEEEEE",headerColors_S:"#EEEEEE",textColor:"rgb(66, 66, 66)",hoveredTextColor:"rgb(25, 118, 210)",fontFamily:"inherit"},b=h.useMemo(()=>e(Q.Authoring,{colors:f,translationDataObjects:[],Dtdetails:t==null?void 0:t.DTdetails,destinations:p,NavBackHandler:u,dateFormat:"dd-MMM-yyyy",disableExistingRows:!1}),[(g=t.DTdetails)==null?void 0:g.DTid]);return e(_,{children:b})},Y=X;function pe(){const[t,l]=h.useState({}),[p,u]=h.useState(!1),[f,b]=h.useState([]),g="",d="baaf87db-3f78-44c8-8c78-32bec835f6ff",r=D(i=>i==null?void 0:i.userManagement),n=r==null?void 0:r.userData,s=D(i=>i.appSettings),{t:o}=O(),E=s==null?void 0:s.dateFormat,T=s==null?void 0:s.timeFormat;h.useEffect(()=>{P(m+`/v1/application/hierarchy?app=${d}&isAuthoring=true&mode=DT`,"get",F,N)},[]);const F=i=>{b(i.data[0].childs[0].childs);let S=i.data[0],k=S.childs[0];l({...t,RMSid:S.id,RSid:k.id})};let N=i=>{console.log("Error Fetching Filter Data in this API",i)};const v=i=>{l({userDetails:{displayName:n==null?void 0:n.displayName,emailId:n==null?void 0:n.emailId},RMSid:t==null?void 0:t.RMSid,RSid:t==null?void 0:t.RSid,DTid:i.id,applicationId:d,ruleName:i.name,version:i.version,token:g.replace("Bearer ","")}),u(!0)};return console.log(C(1702891324188).format(`${E},${T}`)),e("div",{style:{...U,backgroundColor:"#FAFCFF"},children:c(z,{spacing:1,children:[c(W,{item:!0,md:5,sx:$,children:[e(R,{variant:"h3",children:e("strong",{children:o("Authoring")})}),e(R,{variant:"body2",color:"#777",children:o("This view displays the list of rules set for the application")})]}),e(_,{className:"content",sx:{margin:"25px"},children:p?t&&e(Y,{DTdetails:t,setDTdetails:l,setShowDT:u}):e(q,{component:H,sx:{boxShadow:"none",border:"1px solid #e0e0e0"},children:c(G,{sx:{minWidth:650},"aria-label":"simple table",children:[e(V,{sx:{background:"#f5f5f5"},children:c(M,{sx:{"& .MuiTableCell-root":{height:"52px",padding:"0px 16px",fontWeight:600}},children:[e(a,{children:o("Decision Table Name")}),e(a,{align:"right",children:o("Version")}),e(a,{align:"right",children:o("Modified By")}),e(a,{align:"right",children:o("Modified On")}),e(a,{align:"right",children:o("Status")}),e(a,{align:"right",children:o("Action")})]})}),e(w,{children:f.map(i=>c(M,{sx:{"&:last-child td, &:last-child th":{border:0},cursor:"pointer","& .MuiTableCell-root":{borderBottom:"1px solid #e0e0e0 !important",height:"52px",padding:"0px 16px"}},onClick:()=>v(i),children:[e(a,{component:"th",scope:"row",children:i.name}),e(a,{align:"right",children:i.version}),e(a,{align:"right",children:i.updatedBy}),e(a,{align:"right",children:C(i.updatedOn).format(`${E},${T}`)}),e(a,{align:"right",children:i.status}),e(a,{align:"right",children:e(y,{onClick:()=>{v(i)}})})]},i.id))})]})})})]})})}export{pe as default};
