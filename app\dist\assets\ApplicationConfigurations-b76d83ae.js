import{r as u,i as B,l as X,b as Z,w as P,hk as R,hh as L,j as o,hl as ee,a,hm as te,g_ as T,$ as m,ay as C,T as p,gO as i,gQ as x,h2 as F,h4 as n,R as ae,c9 as le,a1 as re,gW as ne,hn as oe,h8 as ie,ho as se,hp as ce,hq as de,hr as A}from"./index-fdfa25a0.js";const he=()=>{var V;const[W,ue]=u.useState(!1);u.useState(!0);const M=B(t=>t.appSettings);let Y=B(t=>t.userManagement.userData);const[v,_]=u.useState([]);let I=B(t=>t.masterData);(V=I==null?void 0:I.dropDown)==null||V["Application Settings"];const[l,g]=u.useState({dateFormat:M.dateFormat,range:M.range,timeFormat:M.timeFormat,defaultModule:M.defaultModule??"/",roleId:M.roleId??""}),[f,k]=u.useState([]),S=X(),$=t=>{g({...l,[t.target.name]:t.target.value})},b=t=>{g({...l,dateFormat:t.target.value})},Q=t=>{g({...l,roleId:t.target.value})},j=t=>{g({...l,range:t.target.value})},H=t=>{g({...l,defaultModule:t.target.value})},q=()=>{let t=e=>{if(!e)S(A());else if(e.dateFormat||e.dateRangeValue||e.timeFormat||e.landingPage){const c={dateFormat:e.dateFormat,range:e.dateRangeValue,timeFormat:e.timeFormat,defaultModule:e.landingPage};S(A(c)),g({...l,dateFormat:e.dateFormat??"DD MMM YYYY",range:e.dateRangeValue??7,timeFormat:e.timeFormat??"hh:mm A",defaultModule:e.landingPage??"/",mode:e.mode??"light"})}},r=()=>{};P(`/${R}/application/getByEmail/${Y==null?void 0:Y.emailId}`,"get",t,r)};Z();const G=()=>{const t=e=>{_(e)},r=e=>{console.error(e)};P(`/${R}/application/getRoles`,"get",t,r)};u.useEffect(()=>{q(),G()},[]),u.useEffect(()=>{if(l!=null&&l.roleId){console.log(l==null?void 0:l.roleId);let t=e=>{if(e&&(e.dateFormat||e.dateRangeValue||e.timeFormat||e.landingPage)){const c={dateFormat:e.dateFormat,range:e.dateRangeValue,timeFormat:e.timeFormat,defaultModule:e.landingPage};S(A(c)),g({...l,dateFormat:e.dateFormat??"DD MMM YYYY",range:e.dateRangeValue??7,timeFormat:e.timeFormat??"hh:mm A",defaultModule:e.landingPage??"/",mode:e.mode??"light"})}},r=e=>{console.error(e)};P(`/${R}/application/getByRoleId/${l==null?void 0:l.roleId}`,"get",t,r)}},[l==null?void 0:l.roleId]);const[h,O]=u.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""}),[w,z]=u.useState(""),E=["Last Week","Last Month","Current Month","Current Quarter","Year To Date"],s={handleClosePromptBox:()=>{O(t=>({open:!1,type:"",redirectOnClose:!1,message:"",title:"",severity:""})),z(""),k([])},handleOpenPromptBox:(t,r={})=>{let e={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okButtonText:"Ok",cancelText:"Cancel",type:"dialog"};t==="SUCCESS"&&(e.type="toast",e.dialogSeverity="success"),z(t),O({...e,...r})},handleCloseAndRedirect:()=>{s.handleClosePromptBox(),props==null||props.handleClose()},getCancelFunction:()=>{switch(w){default:return s.handleClosePromptBox}},getCloseFunction:()=>{switch(w){default:return s.handleClosePromptBox}},getCloseAndRedirectFunction:()=>h.redirectOnClose?s.handleCloseAndRedirect:s.handleClosePromptBox},U=B(t=>t.userManagement.entitiesAndActivities),N=()=>{let t={dateRangeValue:l.range,dateFormat:l.dateFormat,email:l.roleId?"":Y==null?void 0:Y.emailId,timeFormat:l.timeFormat,landingPage:l.defaultModule,roleId:l.roleId},r=c=>{const d={dateFormat:l.dateFormat,range:l.range,timeFormat:l.timeFormat,landingPage:l.defaultModule,mode:l.mode??"light"};S(A(d)),c.status==="Success"?s.handleOpenPromptBox("SUCCESS",{message:"Application Settings Updated successfully",redirectOnClose:!0}):s.handleOpenPromptBox("ERROR",{title:"Failed",message:"Application Settings Update Failed",severity:"danger",cancelButton:!1})},e=()=>{s.handleOpenPromptBox("ERROR",{title:"Failed",message:"Application Settings Update Failed",severity:"danger",cancelButton:!1})};P(`/${R}/application/create`,"post",r,e,t)},[D,J]=u.useState([]);u.useEffect(()=>{var c,d;let t=[],r=1;if(L){(d=(c=L)==null?void 0:c.accessItems)==null||d.map(y=>{y.isAccessible&&y.isSideOption&&Object.keys(U).includes(y.iwaName)&&(t.push({...y,id:r}),r=r+1)});var e=K(t);J(e)}},[U]);const K=t=>{var r=[];return t.map(e=>{if(e.isSideOption&&e.isAccessible){const c=e.childItems.filter(d=>d.isAccessible&&d.isSideOption);r.push({name:e.displayName,value:e.routePath,icon:e.icon,isMenu:!1}),c.length>0?c.map(d=>{r.push({name:d.displayName,value:d.routePath,icon:d.icon,isMenu:!0})}):r.push({name:e.displayName,value:e.routePath,icon:e.icon,isMenu:!0})}}),r};return o("div",{id:"printScreen",style:ee,children:[a(te,{type:h.type,promptState:h.open,setPromptState:s.handleClosePromptBox,onCloseAction:s.getCloseFunction(),promptMessage:h.message,dialogSeverity:h.severity,dialogTitleText:h.title,handleCancelButtonAction:s.getCancelFunction(),cancelButtonText:h.cancelText,showCancelButton:h.cancelButton,handleSnackBarPromptClose:s.getCloseAndRedirectFunction(),okButtonText:h.okButtonText,showOkButton:h.okButton}),a(se,{sx:{color:"#fff",zIndex:100},open:W,children:a(ie,{color:"primary"})}),o(T,{spacing:1,children:[a(m,{container:!0,sx:ce,children:o(m,{item:!0,md:5,sx:de,children:[a(p,{variant:"h3",children:a("strong",{children:"Application Configurations"})}),a(p,{variant:"body2",children:"This view displays the settings for configuring the application"})]})}),o(m,{container:!0,spacing:2,justifyContent:"left",children:[a(m,{item:!0,md:3,children:o(C,{children:[a(p,{sx:i,children:"User Role"}),a(x,{fullWidth:!0,size:"small",sx:{margin:".5em 0px"},children:o(F,{placeholder:"Select User Role",select:!0,sx:i,size:"small",value:l.roleId,name:"roleId",onChange:Q,displayEmpty:!0,error:f.includes("roleId"),children:[a(n,{sx:i,value:"",children:a("div",{style:{color:"#C1C1C1"},children:"Select User Role "})}),v&&(v==null?void 0:v.map(t=>a(n,{sx:i,value:t.id,children:t.name})))]})})]})}),a(m,{item:!0,md:4,children:o(m,{container:!0,rowSpacing:1,spacing:2,children:[a(m,{item:!0,md:12,children:o(C,{children:[a(p,{sx:i,children:"Date Format"}),a(x,{fullWidth:!0,size:"small",sx:{margin:".5em 0px"},children:o(F,{placeholder:"Select Date Format",select:!0,sx:i,size:"small",value:l.dateFormat,name:"dateFormat",onChange:b,displayEmpty:!0,error:f.includes("dateFormat"),children:[a(n,{sx:i,value:"",children:o("div",{style:{color:"#C1C1C1"},children:["Select Date Format"," "]})}),a(n,{value:"DD MMM YYYY",children:"DD MMM YYYY (01 Apr 2023)"}),a(n,{value:"MMM DD, YYYY",children:"MMM DD, YYYY (Apr 01, 2023)"}),a(n,{value:"YYYY MMM DD",children:"YYYY MMM DD (2023 Apr 01)"}),a(n,{value:"DD-MM-YYYY",children:"DD-MM-YYYY (01-04-2023)"}),a(n,{value:"MM-DD-YYYY",children:"MM-DD-YYYY (04-01-2023)"}),a(n,{value:"YYYY-MM-DD",children:"YYYY-MM-DD (2023-04-01)"}),a(n,{value:"DD/MM/YYYY",children:"DD/MM/YYYY (01/04/2023)"}),a(n,{value:"MM/DD/YYYY",children:"MM/DD/YYYY (04/01/2023)"}),a(n,{value:"YYYY/MM/DD",children:"YYYY/MM/DD (2023/04/01)"})]})})]})}),a(m,{item:!0,md:12,children:o(C,{children:[a(p,{sx:i,children:"Time Format"}),a(x,{fullWidth:!0,size:"small",sx:{margin:".5em 0px"},children:o(F,{placeholder:"Select Time Format",select:!0,size:"small",value:l.timeFormat,name:"timeFormat",onChange:$,displayEmpty:!0,sx:i,error:f.includes("timeFormat"),children:[a(n,{sx:i,value:"",children:o("div",{style:{color:"#C1C1C1"},children:["Select Time Format"," "]})}),a(n,{value:"hh:mm A",children:"12-hour (01:34 AM)"}),a(n,{value:"HH:mm",children:"24-hour (13:34)"})]})})]})}),a(m,{item:!0,md:12,children:o(C,{children:[a(p,{sx:i,children:"Default Date Range"}),a(x,{fullWidth:!0,size:"small",sx:{margin:".5em 0px"},children:o(F,{placeholder:"Select Default Date Range",select:!0,size:"small",value:l.range,name:"range",onChange:j,displayEmpty:!0,sx:i,error:f.includes("range"),children:[a(n,{sx:i,value:0,children:a("div",{style:{color:"#C1C1C1"},children:"Select Default Date Range"})}),E==null?void 0:E.map(t=>a(n,{value:t==="Last Week"?7:t==="Last Month"?50:t==="Current Month"?100:t==="Current Quarter"?150:t==="Year To Date"?200:0,children:t}))]})})]})}),D&&a(m,{item:!0,md:12,children:o(C,{children:[a(p,{sx:i,children:"Default Landing Page"}),a(x,{fullWidth:!0,size:"small",sx:{margin:".5em 0px"},children:a(F,{placeholder:"Select Default Module",select:!0,size:"small",value:l.defaultModule,name:"defaultModule",onChange:H,displayEmpty:!0,sx:i,error:f.includes("defaultModule"),children:D==null?void 0:D.map(t=>t.isMenu?a(n,{value:t.value,children:o(T,{spacing:1,direction:"row",alignItems:"center",justifyContent:"start",children:[a(ae,{iconName:t.icon}),a(p,{variant:"body1",children:t.name})]})},t.value):a(le,{children:a("i",{children:t.name})}))})})]})}),a(m,{item:!0,md:12,sx:{display:"flex",justifyContent:"right"},children:a(T,{spacing:1,direction:"row",children:a(re,{type:"save",variant:"contained",sx:{...ne},onClick:()=>{oe(l,["dateFormat","range","timeFormat"],k)&&N()},children:"Save"})})})]})})]})]})]})};export{he as default};
