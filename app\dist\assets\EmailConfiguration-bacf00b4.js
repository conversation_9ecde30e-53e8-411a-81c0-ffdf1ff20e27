import{ah as ti,ai as ii,aj as ai,t8 as Ti,fp as bi,t9 as wi,j as c,a as i,h1 as yt,fX as St,fH as At,a1 as ue,fV as Tt,$ as k,R as Zt,ta as Qt,W as $e,aB as ni,T as O,gQ as vi,a3 as De,F as de,pZ as li,i as ot,r as l,cK as T,l as Ht,h8 as Bt,ho as Pt,q2 as Ii,gK as M,B as re,aa as ri,ab as ht,g_ as E,ib as Ei,sG as ci,P as _e,h as Et,a0 as bt,gZ as oi,hi as Ai,V as Rt,jr as Le,hI as di,i8 as si,kX as Ut,hy as ct,tb as Ri,tc as ui,hE as $t,td as ki,te as Oi,tf as Bi,w as ft,G as kt,hg as Ot,U as <PERSON>,pF as <PERSON>,N as Mi,O as Jt,kV as <PERSON>,fF as <PERSON>,b as mi,iB as Li,iQ as _i,hW as Wi,h4 as pe,h2 as et,tg as Gi,e as Ui,hl as zi,hp as Hi,hq as $i,ku as ji,th as Gt,ti as Vi,ag as Xt}from"./index-fdfa25a0.js";import{D as Ye,r as jt,h as hi,d as fi,a as Ki,b as qi,T as Yi,c as Zi,e as pt,f as Qi,g as Ji}from"./Check-aa258898.js";import{D as Xi,C as zt}from"./DialogContentText-8ac052ae.js";import{m as pi}from"./makeStyles-1dfd4db4.js";var Vt={},ea=ii;Object.defineProperty(Vt,"__esModule",{value:!0});var gi=Vt.default=void 0,ta=ea(ti()),ia=ai;gi=Vt.default=(0,ta.default)((0,ia.jsx)("path",{d:"M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2zm-2 0-8 5-8-5zm0 12H4V8l8 5 8-5z"}),"EmailOutlined");const xi=Ti({name:"userReducer",initialState:{userToken:{},userData:{user_id:""},useWorkAccess:!1,useConfigServerDestination:!1,oDestinationUrlMap:{},aDestinationUrl:[],environment:"",applicationName:"",needHeading:!0,refreshTemplates:{},showManageGroups:!0,useCrud:!0,feature:{}},reducers:{setToken:(e,m)=>{e.userToken=m.payload},setUserData:(e,m)=>{e.userData=m.payload},setfeature:(e,m)=>{e.feature=m.payload},setAPIBaseUrl:(e,m)=>{var x;e.environment=m.payload.environment;let y={};for(let o=0;o<((x=m.payload)==null?void 0:x.destinations.length);o++)y[m.payload.destinations[o].Name]=m.payload.destinations[o].URL;e.oDestinationUrlMap=y,e.aDestinationUrl=m.payload.destinations},setConfigs:(e,m)=>{e.useWorkAccess=m.payload.useWorkAccess,e.useConfigServerDestination=m.payload.useConfigServerDestination,e.userData={user_id:m.payload.userId},e.applicationName=m.payload.applicationName,e.applicationId=m.payload.applicationId,e.needHeading=m.payload.needHeading,e.showManageGroups=m.payload.needHeading,e.useCrud=m.payload.useCrud},updateBusyLoader:(e,m)=>{e.showBusyLoader=m.payload},setRefreshTemplate:(e,m)=>{e.refreshTemplates={}}}}),{setToken:aa,setUserData:Ua,setAPIBaseUrl:na,setConfigs:la,setRefreshTemplate:za,setfeature:ra}=xi.actions,ca=xi.reducer,oa=bi({userReducer:ca}),Dt=wi({reducer:oa,middleware:e=>e({immutableCheck:!1,serializableCheck:!1})}),je=async(e,m,y,x,o,N)=>{const t=da(N);switch(e=sa(e,t),m.toLowerCase()){case"post":await fetch(e,{method:"POST",body:JSON.stringify(y),headers:t.headers,mode:"cors"}).then(s=>s.json()).then(s=>{x(s)}).catch(s=>{o(s)});break;case"postblobfile":await fetch(e,{method:"POST",body:y,headers:{authorization:t.headers.Authorization}}).then(s=>s.json()).then(s=>{x(s)}).catch(s=>{o(s)});break;case"get":await fetch(e,{method:"GET",headers:t.headers,mode:"cors"}).then(s=>s.json()).then(s=>{x(s)}).catch(s=>{o(s)});break;case"getblobfile":await fetch(e,{method:"GET",headers:t.headers,mode:"cors"}).then(s=>s.blob()).then(s=>{x(s)}).catch(s=>{o(s)});break;case"delete":await fetch(e,{method:"DELETE",headers:t.headers,mode:"cors"}).then(s=>s.json()).then(s=>{x(s)}).catch(s=>{o(s)});break;case"deletewithbody":await fetch(e,{method:"DELETE",headers:t.headers,body:JSON.stringify(y),mode:"cors"}).then(s=>s.json()).then(s=>{x(s)}).catch(s=>{o(s)});break;case"put":await fetch(e,{method:"PUT",headers:t.headers,mode:"cors"}).then(s=>s.json()).then(s=>{x(s)}).catch(s=>{o(s)});break;case"patch":await fetch(e,{method:"PATCH",body:JSON.stringify(y),headers:t.headers,mode:"cors"}).then(s=>s.json()).then(s=>{x(s)}).catch(s=>{o(s)});break}},Ve=async(e,m,y,x)=>{const o=Dt.getState().userReducer;let N="";o.useCrud?N="/CrudApiServices/crud/api/fetchQuery?converterName=map":N="/CrudApiServices/api/fetchQuery?converterName=map",je(N,"post",{query:e,args:m},function(t){y(t)},function(t){x(t)})},da=e=>{const m=Dt.getState().userReducer;let y={headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*"}};return m.useWorkAccess&&(y.headers={...y.headers,Authorization:`Bearer ${m.userToken}`,tenantId:"azure"}),e&&(e==="no-auth"?delete y.headers.Authorization:y.headers={...y.headers,...e}),y},sa=(e,m)=>{var x;const y=Dt.getState().userReducer;if(e.startsWith("/CrudApiServices")&&(m.headers={...m.headers,env:y!=null&&y.environment?y.environment:"itm"}),y.useConfigServerDestination){let o=e.split("/");e=e.replace("/"+o[1],(x=y.oDestinationUrlMap)==null?void 0:x[o[1]])}return e},ua="/assets/Logo-f785ba8f.png",Mt=e=>c(Tt,{open:e==null?void 0:e.open,children:[i(yt,{className:"styleMessageBox",sx:{height:"3rem",display:"flex",alignItems:"center",fontWeight:"500",borderRadius:"7px",background:"#F1F5FE",fontSize:"1.5rem",color:"black "},children:"Confirmation"}),i(St,{sx:{minWidth:"20rem",display:"flex",alignItems:"center"},children:i(Xi,{sx:{display:"flex",alignItems:"center"},children:e==null?void 0:e.message})}),c(At,{sx:{height:"3rem",borderTop:"2px solid #d9d9d9"},children:[(e==null?void 0:e.creationType)!=="Timeout"&&i(ue,{variant:"outlined",size:"small",sx:{color:"red",borderColor:"red !important"},onClick:()=>{e==null||e.onClose("Cancel")},children:"Cancel"}),i(ue,{variant:"contained",size:"small",onClick:()=>e==null?void 0:e.onClose(e==null?void 0:e.creationType),children:"Ok"})]})]});function ma({promptState:e,setPromptState:m,handlePromptClose:y,onCloseAction:x,promptFullWidth:o,promptMessage:N,promptMaxWidth:t,showInputText:s,inputText:H,setInputText:F,dialogInputPlaceholder:te,DialogMessageContent:I,dialogSeverity:r,dialogTitleText:Y,handleCancelButtonAction:$,cancelButtonText:ie,showCancelButton:ce,handleOkButtonAction:Ae,okButtonText:oe,showOkButton:Me,handleExtraButtonAction:u,extraButtonText:U,showExtraButton:X}){var Re,ye,L,me;const ae=()=>{m(!1)};return i(de,{children:c(Tt,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none",minWidth:450}},open:e,onClose:()=>{x?x():y?y():ae()},fullWidth:o,children:[c(k,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:[Y&&i(k,{item:!0,children:c(yt,{id:"alert-dialog-title",sx:{fontWeight:600,display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center",fontSize:"16px"},children:[r&&c("span",{style:{display:"flex",alignItems:"center"},children:[i(Zt,{iconName:((ye=(Re=Qt.severityIcons)==null?void 0:Re[r.toUpperCase()])==null?void 0:ye.iconName)??"",iconColor:((me=(L=Qt.severityIcons)==null?void 0:L[r.toUpperCase()])==null?void 0:me.iconColor)??""}),"  "]}),Y]})}),i(k,{item:!0,sx:{padding:"12px"},children:i($e,{onClick:se=>{se.stopPropagation(),y?y():ae()},children:i(Zt,{iconName:"Close"})})})]}),i(St,{sx:{paddingTop:0},children:c(ni,{children:[i(k,{container:!0,children:c(k,{item:!0,md:12,sx:{padding:"0px 20px 20px 0px",textAlign:"left"},children:[I&&i(I,{}),N&&i(O,{children:N})]})}),s&&i(vi,{sx:{height:"auto"},fullWidth:!0,children:i(De,{sx:{backgroundColor:"#F5F5F5"},value:H,onChange:se=>F(se.target.value),multiline:!0,placeholder:te})})]})}),(ce||Me||X)&&c(At,{sx:{paddingRight:"1.5rem"},children:[ce&&i(ue,{variant:"outlined",sx:{height:40,minWidth:"4rem",textTransform:"none",borderColor:"#3B30C8",color:"#3B30C8"},onClick:()=>{$?$():y?y():ae()},children:ie??"Cancel"}),Me&&i(ue,{variant:"contained",style:{height:40,minWidth:"4rem",backgroundColor:"#3B30C8",textTransform:"none"},onClick:()=>{Ae?Ae():y?y():ae()},children:oe??"Ok"}),X&&i(ue,{variant:"contained",style:{height:40,minWidth:"4rem",backgroundColor:"#3B30C8",textTransform:"none"},onClick:()=>{u&&u(),y?y():ae()},children:U??"Ok"})]})]})})}function ha({promptState:e,setPromptState:m,handleSnackBarPromptClose:y,promptMessage:x}){const o=()=>{m(!1)};return i(ni,{spacing:2,sx:{width:"100%"},children:i(li,{autoHideDuration:5e3,anchorOrigin:{vertical:"top",horizontal:"center"},open:e,onClose:()=>{y?y():o()},message:x,sx:{height:"150px"}})})}function Nt(e){switch(e==null?void 0:e.type){case"dialog":return i(ma,{...e});case"snackbar":return i(ha,{...e})}}const fa=({...e})=>{var w;const m=ot(b=>b.userReducer);l.useState(!1);const[y,x]=T.useState(!1);Ht();const[o,N]=T.useState(()=>Ye.EditorState.createEmpty()),[t,s]=T.useState(()=>Ye.EditorState.createEmpty()),[H,F]=T.useState("1"),[te,I]=l.useState(new Map),[r,Y]=l.useState(new Map),[$,ie]=T.useState(!1),[ce,Ae]=T.useState(""),[oe,Me]=T.useState("Cancel"),u=(b,Z)=>{F(Z)};l.useEffect(()=>{console.log(r.get(parseInt(e==null?void 0:e.cardData.ccParticipant)),"123")},[]);const U=b=>{b==="delete"&&X(e==null?void 0:e.data),ie(!1)},X=b=>{x(!0),me(Z=>({...Z,open:!1})),je("/WorkUtilsServices/v1/mail-definition/"+b.emailDefinitionId,"delete",null,function(Z){q.handleOpenPromptBox("SUCCESS",{message:"Email template deleted succesfully",redirectOnClose:!1}),x(!1)},function(Z){x(!1),q.handleOpenPromptBox("ERROR",{title:"Error",message:"Failed to create template",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"}),console.log("error")})},ae=b=>{const Z=hi(b),{contentBlocks:J,entityMap:ne}=Z,A=Ye.ContentState.createFromBlockArray(J,ne),V=Ye.EditorState.createWithContent(A);s(V),N(V)},Re=(b,Z)=>{var J=new FileReader;J.readAsDataURL(b),J.onload=()=>Z(J.result),J.onerror=ne=>{}},ye=b=>new Promise((Z,J)=>Re(b,ne=>Z({data:{link:ne}})));T.useEffect(()=>{var b;m.applicationName!==""&&ae((b=e==null?void 0:e.data)==null?void 0:b.content)},[e==null?void 0:e.data,m]),l.useEffect(()=>{m.applicationName!==""&&(e==null||e.userList.map(b=>(te.set(b.emailId,b.userName),{code:b.userName,description:b.emailId})),I(new Map(te)),e==null||e.allGroups.map(b=>(r.set(b.id,b.name),{code:b.name,description:b.id})),Y(new Map(r)))},[m,e==null?void 0:e.allGroups]);const[L,me]=l.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""}),[se,ke]=l.useState(""),q={handleClosePromptBox:()=>{me(b=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),ke("")},handleOpenPromptBox:(b,Z={})=>{let J={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};b==="SUCCESS"&&(J.type="snackbar"),ke(b),me({...J,...Z})},handleCloseAndRedirect:()=>{q.handleClosePromptBox(),navigate("")},getCancelFunction:()=>{switch(se){default:return()=>{q.handleClosePromptBox()}}},getCloseFunction:()=>{switch(se){case"COMMENTERROR":default:return b=>{q.handleClosePromptBox()}}},getOkFunction:()=>{switch(se){case"DELETE_TEMPLATE":return()=>U("delete");default:return()=>q.handleClosePromptBox()}},getCloseAndRedirectFunction:()=>L.redirectOnClose?q.handleCloseAndRedirect:q.handleClosePromptBox};return c("div",{children:[i(Pt,{className:"backdrop",sx:{zIndex:"9"},open:y,children:i(Bt,{color:"primary"})}),i(Nt,{type:L.type,promptState:L.open,setPromptState:q.handleClosePromptBox,onCloseAction:q.getCloseFunction(),promptMessage:L.message,dialogSeverity:L.severity,dialogTitleText:L.title,handleCancelButtonAction:q.getCancelFunction(),cancelButtonText:L.cancelText,showCancelButton:L.cancelButton,handleSnackBarPromptClose:q.getCloseAndRedirectFunction(),handleOkButtonAction:q.getOkFunction(),okButtonText:L.okButtonText,showOkButton:L.okButton}),c(Ii,{anchor:"right",onClose:e==null?void 0:e.onClose,open:e==null?void 0:e.open,PaperProps:{elevation:4,sx:{width:"45vw",minWidth:"600px",bgcolor:M.background.default,borderRadius:"12px 0 0 12px",padding:"24px"}},children:[c(re,{sx:{borderBottom:`1px solid ${M.border.main}`,pb:2,mb:3,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[c(ri,{value:H,onChange:u,sx:{"& .MuiTab-root":{textTransform:"none",fontSize:"14px",fontWeight:500,minWidth:"unset",px:3,color:M.text.secondary,"&.Mui-selected":{color:M.primary.main,fontWeight:600}},"& .MuiTabs-indicator":{backgroundColor:M.primary.main,height:"3px",borderRadius:"2px"}},children:[i(ht,{label:"Template Information",value:"1"}),i(ht,{label:"Preview Information",value:"2"})]}),c(E,{direction:"row",spacing:1.5,children:[i($e,{onClick:()=>{e==null||e.setOpenCreateTemplate(!0),e==null||e.setSelectedRow(e==null?void 0:e.data),e==null||e.setCreationType("edit"),e==null||e.setScenario("EDIT"),e==null||e.setIsEditing(e==null?void 0:e.index),e==null||e.setOpenTemplateDialog(!1)},sx:{color:M.primary.main,"&:hover":{backgroundColor:M.primary.lighter}},children:i(Ei,{fontSize:"small"})}),m.feature.EMAIL_CONFIG_DELETE==="True"&&i($e,{onClick:()=>{e==null||e.setSelectedRow(e==null?void 0:e.data),q.handleOpenPromptBox("DELETE_TEMPLATE",{title:"Confirm Delete",message:"Do you want to delete this record?",severity:"warning",cancelButton:!0,okButton:!0,okButtonText:"Ok"})},sx:{color:M.error.main,"&:hover":{backgroundColor:M.error.lighter}},children:i(ci,{fontSize:"small"})})]})]}),H==="1"&&i(E,{spacing:3,sx:{px:2,height:"100%",overflow:"auto"},children:[{label:(e==null?void 0:e.headers[3])||"Template Name",value:(w=e==null?void 0:e.data)==null?void 0:w.name},{label:"Identifier",value:e==null?void 0:e.data.identifierDesc},{label:(e==null?void 0:e.headers[1])||"Module",value:e==null?void 0:e.data.entityDesc},{label:(e==null?void 0:e.headers[2])||"Event",value:e==null?void 0:e.data.processDesc},{label:"Recipients",value:(e==null?void 0:e.cardData.toParticipantType)==="GROUP"?r.get(parseInt(e==null?void 0:e.cardData.toParticipant)):(e==null?void 0:e.cardData.toParticipantType)==="USER"?te.get(e==null?void 0:e.cardData.toParticipant):e==null?void 0:e.cardData.toParticipant},{label:"CC Recipients",value:(e==null?void 0:e.cardData.ccParticipantType)==="GROUP"?r.get(parseInt(e==null?void 0:e.cardData.ccParticipant)):((e==null?void 0:e.cardData.toParticipantType)==="USER",e==null?void 0:e.cardData.ccParticipant)}].map((b,Z)=>c(_e,{elevation:0,sx:{p:2,borderRadius:"8px",backgroundColor:M.background.paper,border:`1px solid ${M.border.light}`},children:[i(O,{variant:"body1",sx:{color:M.text.primary,fontWeight:600,display:"block",mb:1,fontSize:"0.95rem"},children:b.label}),i(Et,{title:b.value||"Not Available",children:i(O,{variant:"body2",sx:{fontWeight:500,color:M.text.secondary,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:b.value||"Not Available"})})]},Z))}),H==="2"&&c(_e,{elevation:0,sx:{width:"100%",flexGrow:1,bgcolor:M.background.paper,overflow:"auto",border:`1px solid ${M.border.light}`,borderRadius:"8px"},children:[i(re,{sx:{borderBottom:`1px solid ${M.border.light}`,p:2,bgcolor:M.background.neutral},children:i(O,{variant:"subtitle2",sx:{color:M.text.primary,fontWeight:600},children:"Email Preview"})}),i(E,{sx:{height:"100%",minHeight:"65vh",p:3},children:i(jt.Editor,{editorState:o,wrapperClassName:"Editor",editorClassName:"Editor",defaultEditorState:t,onEditorStateChange:N,toolbar:{inline:{inDropdown:!1},list:{inDropdown:!1},textAlign:{inDropdown:!1},link:{inDropdown:!1},history:{inDropdown:!1},image:{uploadCallback:ye,previewImage:!0,alignmentEnabled:!0}},toolbarHidden:!0,readOnly:!0})})]})]}),i(Mt,{message:ce,creationType:oe,open:$,onClose:b=>U(b)})]})},Kt=e=>{var N,t,s,H,F,te,I,r,Y;const[m,y]=l.useState({}),x=$=>{y({[$]:!0})},o=()=>{y({})};return c(de,{children:[i(k,{item:!0,xs:12,sm:6,md:4,lg:3,children:i(bt,{elevation:0,sx:{backgroundColor:M.primary.veryLight,borderRadius:"8px",border:`1px solid ${M.border.light}`,height:"160px",cursor:"pointer","&:hover":{backgroundColor:M.primary.light,borderColor:M.border.medium}},onClick:()=>{var $;return x(($=e==null?void 0:e.cardData)==null?void 0:$.emailDefinitionId)},children:i(zt,{sx:{height:"100%"},children:c(oi,{sx:{p:2,height:"100%",display:"flex",flexDirection:"column",backgroundColor:M.primary.veryLight,justifyContent:"space-between",gap:1},children:[c(re,{sx:{flex:"1 1 auto"},children:[i(Et,{title:(N=e==null?void 0:e.cardData)==null?void 0:N.name,placement:"top",children:i(O,{sx:{fontSize:"14px",color:M.text.primary,mb:.5,fontWeight:700,display:"-webkit-box",WebkitLineClamp:"1",WebkitBoxOrient:"vertical",overflow:"hidden",lineHeight:"1.2"},children:(t=e==null?void 0:e.cardData)==null?void 0:t.name})}),c(re,{sx:{display:"flex",alignItems:"center",mb:.5},children:[i(fi,{sx:{fontSize:12,color:((s=e==null?void 0:e.cardData)==null?void 0:s.status)==="Active"?M.success.vibrant:M.warning.amber,mr:.5}}),i(O,{sx:{fontSize:"12px",color:((H=e==null?void 0:e.cardData)==null?void 0:H.status)==="Active"?M.success.vibrant:M.warning.amber},children:(F=e==null?void 0:e.cardData)==null?void 0:F.status})]}),i(Ai,{sx:{my:.5}}),i(O,{sx:{fontSize:"12px",color:M.text.secondary,mb:.5,wordBreak:"break-word"},children:((te=e==null?void 0:e.cardData)==null?void 0:te.subject)||"Approval for Request"})]}),c(re,{sx:{display:"flex",gap:.5,flexWrap:"wrap",flexShrink:0},children:[((I=e==null?void 0:e.cardData)==null?void 0:I.entityDesc)&&i(Et,{title:e.cardData.entityDesc,placement:"top",children:i(Rt,{label:e.cardData.entityDesc,size:"small",sx:{backgroundColor:M.primary.light,color:M.primary.main,fontSize:"10px",height:"20px",maxWidth:"120px",border:`1px solid ${M.primary.border}`,"& .MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",px:1},"&:hover":{backgroundColor:M.primary.ultraLight}}})}),((r=e==null?void 0:e.cardData)==null?void 0:r.identifierDesc)&&i(Et,{title:e.cardData.identifierDesc,placement:"top",children:i(Rt,{label:e.cardData.identifierDesc,size:"small",sx:{backgroundColor:M.secondary.light,color:M.secondary.green,fontSize:"10px",height:"20px",maxWidth:"120px",border:`1px solid ${M.secondary.lightGreen}`,"& .MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",px:1},"&:hover":{backgroundColor:M.primary.chipBackground}}})})]})]})})})},e==null?void 0:e.index),i(fa,{open:m[(Y=e==null?void 0:e.cardData)==null?void 0:Y.emailDefinitionId],onClose:o,setCreationType:e==null?void 0:e.setCreationType,index:e==null?void 0:e.index,data:e==null?void 0:e.cardData,setSelectedRow:e==null?void 0:e.setSelectedRow,setOpenCreateTemplate:e==null?void 0:e.setOpenCreateTemplate,setOpenTemplateDialog:y,mailmappingData:e==null?void 0:e.mailmappingData,groupList:e==null?void 0:e.groupList,userList:e==null?void 0:e.userList,allGroups:e==null?void 0:e.allGroups,headers:e==null?void 0:e.headers,...e})]})};function ga(e){const[m,y]=l.useState({});return i(de,{children:e!=null&&e.isLoading?i(de,{children:i(k,{container:!0,rowSpacing:{xs:2,sm:2,md:3,lg:3},columnSpacing:{xs:2,sm:2,md:3},children:Array.from(Array(10)).map((x,o)=>i(k,{item:!0,xs:2,sm:3,md:3,xl:3,height:"10rem",children:i(_e,{width:"100%",sx:{padding:"1rem"},children:c(E,{direction:"column",spacing:1,height:"100%",width:"100%",justifyContent:"center",alignItems:"flex-start",children:[i(Le,{variant:"text",sx:{fontSize:"1rem"},width:200,p:2}),i(Le,{variant:"rounded",width:"80%",height:60,p:2}),c(E,{direction:"row",spacing:2,justifyContent:"flex-start",alignItems:"center",width:"100%",children:[i(Le,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}}),i(Le,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}})]})]})})},o))})}):i(k,{container:!0,rowSpacing:2,columnSpacing:{xs:2,sm:2,md:3},children:(e!=null&&e.filteredData.length||(e==null?void 0:e.searchParam)!==""?e==null?void 0:e.filteredData:e==null?void 0:e.emailTemplateData).map((x,o)=>i(de,{children:i(Kt,{cardData:x,index:o,setCreationType:e==null?void 0:e.setCreationType,setSelectedRow:e==null?void 0:e.setSelectedRow,setOpenCreateTemplate:e==null?void 0:e.setOpenCreateTemplate,setOpenTemplateDialog:y,mailmappingData:e==null?void 0:e.mailmappingData,groupList:e==null?void 0:e.groupList,userList:e==null?void 0:e.userList,setIsEditing:e==null?void 0:e.setIsEditing,allGroups:e==null?void 0:e.allGroups,headers:e==null?void 0:e.headers,setScenario:e==null?void 0:e.setScenario})}))})})}function xa(e){const[m,y]=l.useState({});return T.useState(null),i(de,{children:e.isLoading?i(de,{children:i(k,{container:!0,rowSpacing:{xs:2,sm:2,md:3,lg:3},columnSpacing:{xs:2,sm:2,md:3},children:Array.from(Array(10)).map((x,o)=>i(k,{item:!0,xs:2,sm:3,md:3,xl:3,height:"10rem",children:i(_e,{width:"100%",sx:{padding:"1rem"},children:c(E,{direction:"column",spacing:1,height:"100%",width:"100%",justifyContent:"center",alignItems:"flex-start",children:[i(Le,{variant:"text",sx:{fontSize:"1rem"},width:200,p:2}),i(Le,{variant:"rounded",width:"80%",height:60,p:2}),c(E,{direction:"row",spacing:2,justifyContent:"flex-start",alignItems:"center",width:"100%",children:[i(Le,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}}),i(Le,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}})]})]})})},o))})}):i(k,{container:!0,rowSpacing:2,columnSpacing:{xs:2,sm:2,md:3},children:(e.filteredData.length||e.searchParam!==""?e.filteredData:e.active).map((x,o)=>i(de,{children:i(Kt,{headers:e.headers,cardData:x,index:o,setCreationType:e.setCreationType,setSelectedRow:e.setSelectedRow,setOpenCreateTemplate:e.setOpenCreateTemplate,setOpenTemplateDialog:y,mailmappingData:e.mailmappingData,groupList:e.groupList,userList:e.userList,setIsEditing:e.setIsEditing,allGroups:e.allGroups,setScenario:e.setScenario})}))})})}function Ca(e){const[m,y]=l.useState({});return T.useState(null),i(de,{children:e!=null&&e.isLoading?i(k,{container:!0,rowSpacing:{xs:2,sm:2,md:3,lg:3},columnSpacing:{xs:2,sm:2,md:3},children:Array.from(Array(10)).map((x,o)=>i(k,{item:!0,xs:2,sm:3,md:3,xl:3,height:"10rem",children:i(_e,{width:"100%",sx:{padding:"1rem"},children:c(E,{direction:"column",spacing:1,height:"100%",width:"100%",justifyContent:"center",alignItems:"flex-start",children:[i(Le,{variant:"text",sx:{fontSize:"1rem"},width:200,p:2}),i(Le,{variant:"rounded",width:"80%",height:60,p:2}),c(E,{direction:"row",spacing:2,justifyContent:"flex-start",alignItems:"center",width:"100%",children:[i(Le,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}}),i(Le,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}})]})]})})},o))}):i(k,{container:!0,rowSpacing:2,columnSpacing:{xs:2,sm:2,md:3},children:(e!=null&&e.filteredData.length||(e==null?void 0:e.searchParam)!==""?e==null?void 0:e.filteredData:e==null?void 0:e.draft).map((x,o)=>i(de,{children:i(Kt,{cardData:x,index:o,setCreationType:e==null?void 0:e.setCreationType,setSelectedRow:e==null?void 0:e.setSelectedRow,setOpenCreateTemplate:e==null?void 0:e.setOpenCreateTemplate,setOpenTemplateDialog:y,mailmappingData:e==null?void 0:e.mailmappingData,groupList:e==null?void 0:e.groupList,userList:e==null?void 0:e.userList,allGroups:e==null?void 0:e.allGroups,setIsEditing:e==null?void 0:e.setIsEditing,headers:e==null?void 0:e.headers,setScenario:e==null?void 0:e.setScenario})}))})})}const ya=e=>{ot(s=>s.userReducer),T.useState(!1);const[m,y]=T.useState(()=>Ye.EditorState.createEmpty()),[x,o]=T.useState(()=>Ye.EditorState.createEmpty()),N=(s,H)=>{var F=new FileReader;F.readAsDataURL(s),F.onload=()=>H(F.result),F.onerror=te=>{}},t=s=>new Promise((H,F)=>N(s,te=>H({data:{link:te}})));return c(Tt,{open:e==null?void 0:e.openPreview,onClose:e==null?void 0:e.closePreview,children:[i(yt,{children:c(E,{direction:"row",spacing:2,alignItems:"center",justifyContent:"space-between",width:"100%",children:[i(O,{sx:{fontWeight:500,color:"#1D1D11",fontSize:"16px",fontFamily:'"Roboto", sans-serif !important'},children:"Preview"}),i(ue,{onClick:e==null?void 0:e.closePreview,startIcon:i(di,{})})]})}),i(St,{children:i(re,{children:c(_e,{elevation:3,width:"80%",height:"90%",children:[i(E,{backgroundColor:"#F0EFFF",children:i("img",{src:ua,style:{width:"100%",height:"2rem"}})}),c(E,{children:[c(E,{justifyContent:"space-around",children:[c(E,{direction:"row",justifyContent:"space-around",alignItems:"center",children:[c("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 0px 0px 2px ",width:"50%"},children:[i("span",{style:{width:"46.5%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important',whiteSpace:"nowrap"},children:"Template Name"}),i("span",{style:{fontWeight:400,width:"50%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"name"})]}),c("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 2px 0px 2px ",width:"50%"},children:[i("span",{style:{width:"50%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important',whiteSpace:"nowrap"},children:"Identifier"}),i("span",{style:{fontWeight:400,width:"70%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Identifier"})]})]}),c(E,{direction:"row",justifyContent:"space-around",children:[c("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 0px 0px 2px ",width:"50%"},children:[i("span",{style:{width:"50%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important',whiteSpace:"nowrap"},children:"Module"}),i("span",{style:{fontWeight:500,width:"50%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Module"})]}),c("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 2px 0px 2px ",width:"50%"},children:[i("span",{style:{width:"50%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important',whiteSpace:"nowrap"},children:"Event"}),i("span",{style:{fontWeight:500,width:"50%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Event"})]})]}),i(E,{direction:"row",children:c("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 0px 0px 2px ",width:"50%"},children:[i("span",{style:{width:"50%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Subject"}),i("span",{style:{fontWeight:500,width:"50%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Subject"})]})})]}),i(jt.Editor,{editorState:m,wrapperClassName:"Editor",editorClassName:"Editor",defaultEditorState:x,onEditorStateChange:y,toolbar:{inline:{inDropdown:!1},list:{inDropdown:!1},textAlign:{inDropdown:!1},link:{inDropdown:!1},history:{inDropdown:!1},image:{uploadCallback:t,previewImage:!0,alignmentEnabled:!0}},toolbarHidden:!0,readOnly:!0})]}),c(E,{backgroundColor:"#F5F5F5",width:"100%",children:[c(O,{sx:{fontWeight:400,color:"#757575",fontSize:"12px",fontFamily:'"Roboto", sans-serif !important'},children:["Send us your ",i("u",{children:"feedback!"})]}),i(O,{sx:{fontWeight:400,color:"#1D1D11",fontSize:"12px",fontFamily:'"Roboto", sans-serif !important'},children:"Reply Directly to this email to comment , and CC teamsmates to add them as collaborators."}),c(O,{sx:{fontWeight:400,color:"#757575",fontSize:"12px",fontFamily:'"Roboto", sans-serif !important'},children:["If you want to stop recieving notifications about this task , you can",i("u",{children:"remove yourself from it."})]})]})]})})})]})},Sa=e=>i("div",{children:i(()=>{switch(e==null?void 0:e.channel){case"Phone":return i("div",{});case"SMS":return i(Ta,{});case"WhatsApp":return i("div",{});case"Email":return i("div",{});case"InApp":return i("div",{});default:return i("div",{})}},{})}),Ta=()=>c(E,{diection:"column",justifyContent:"start",alignItems:"center",children:[i(O,{variant:"h2",children:"SMS Configuration"}),c(E,{spacing:1,direction:"row",justifyItems:"flex-start",alignItems:"center",children:[i(O,{variant:"body2",noWrap:!0,color:"text.primary",children:"Graph Title"}),i(O,{variant:"body2",noWrap:!0,color:"error.main",children:"*"})]})]});M.template.mentionBackground;M.primary.white,`${M.template.suggestionBorder}`,`${M.template.suggestionItem}`,M.template.suggestionFocused;const ba={Phone:{key:"Phone",label:"Phone",icon:i(ki,{})},SMS:{key:"SMS",label:"SMS",icon:i(Oi,{})},WhatsApp:{key:"WhatsApp",label:"WhatsApp",icon:i(Bi,{})},Email:{key:"Email",label:"Email",icon:i(Ut,{})},InApp:{key:"InApp",label:"In-App",icon:i(ui,{})}},ei=({scenario:e,open:m,onClose:y,userList:x=[],groupList:o=[],setScenario:N,...t})=>{var we,ve;const s=ot(a=>a.userReducer),[H,F]=T.useState(!1),[te,I]=T.useState(!1),[r,Y]=l.useState(!1),[$,ie]=T.useState([]),[ce,Ae]=T.useState([]),[oe,Me]=T.useState([]),[u,U]=T.useState({name:"",entity:null,process:null,identifier:null,subject:"",body:"",application:s.applicationName,emailDefinitionId:"",createdBy:s.userData.user_id,createdOn:new Date().toISOString(),updatedBy:s.userData.user_id,updatedOn:new Date().toISOString()}),[X,ae]=T.useState(()=>Ye.EditorState.createEmpty()),[Re,ye]=T.useState([]),[L,me]=T.useState(()=>Ye.EditorState.createEmpty()),[se,ke]=T.useState(!1),[q,w]=T.useState(""),[b,Z]=T.useState("Cancel");l.useState([]);const[J,ne]=l.useState([]),[A,V]=l.useState([]);l.useState([]),l.useState([]);const[We,Ie]=l.useState([]);l.useState([]),l.useState([]),l.useState("");const[tt,K]=l.useState(!1),[Ge,Ue]=l.useState("success"),[gt,he]=l.useState("");l.useState("");const[wt,xe]=l.useState("");l.useState(""),l.useState(!1),l.useState(null);const[ze,Q]=T.useState(!1);T.useState(!1),l.useState(null);const[fe,le]=l.useState(new Map),[Oe,He]=l.useState(new Map),[it,Se]=l.useState([]),[Ze,Ne]=l.useState({});l.useState({});const[Ce,S]=l.useState([]),[C,Te]=l.useState({receipentType:"",receipents:"",ccType:"",cc:""}),R=["INITIATOR","REVIEWER"],[dt,Ke]=l.useState([]);l.useState([]);const[at,nt]=l.useState("Email");l.useState(""),l.useEffect(()=>{let a=(t==null?void 0:t.contentHTML)??"";Xe(a)},[]);const Qe=(a,d)=>{if(Te(D=>({...D,[`${a}`]:d})),C.receipentType=="VARIABLE"&&d=="VARIABLE"&&Se(R),a==="receipentType"&&d=="GROUP"){var h=[],j={};for(let[D,G]of Oe.entries())j[G]=D,h.push(G);Ne({...j,"":""}),Se(h)}if(C.receipentType=="USER"){var h=[];for(let[,G]of fe.entries())h.push(G);Se(h)}if(a=="ccType"&&d=="VARIABLE"&&S(R),C.receipentType=="VARIABLE"&&d=="VARIABLE"&&Se(R),a==="ccType"&&d=="GROUP"){var h=[],j={};for(let[W,ee]of Oe.entries())j[ee]=W,h.push(ee);Ne({...j,"":""}),S(h)}if(a=="ccType"&&d=="VARIABLE"&&S(R),a=="ccType"&&d=="USER"){var h=[];for(let[,G]of fe.entries())h.push(G);S(h)}},lt=()=>{U({name:"",entity:null,process:null,identifier:null,subject:"",body:"",application:"",emailDefinitionId:"",createdBy:s.userData.user_id,createdOn:new Date().toISOString(),updatedBy:s.userData.user_id,updatedOn:new Date().toISOString()});let a=(t==null?void 0:t.contentHTML)??"";Xe(a),Te({receipentType:"",receipents:"",ccType:"",cc:""}),V([])},rt=a=>{Pe(d=>({...d,open:!1})),a!=="Cancel"&&a!=="Discard"&&a!=="Timeout"?u.entity&&u.process&&u.name&&u.identifier?(n(a),g(),(t==null?void 0:t.creationType)!=="edit"&&(t==null||t.setCreationType("new")),t==null||t.setSelectedRow(t==null?void 0:t.data),t==null||t.setIsEditing(null)):F(!1):a==="Discard"?(lt(),t==null||t.setSelectedRow(null),t==null||t.setCreationType("new"),t==null||t.setIsEditing(null)):a==="Timeout"&&window.location.reload(),ke(!1),w("")},n=a=>{var D,G;F(!0),I(!0);let d={application:s.applicationName,applicationDesc:s.applicationName,content:u.body,createdBy:u.createdBy,createdOn:u.createdOn,emailDefinitionId:u.emailDefinitionId,entity:u.entity.entity,entityDesc:u.entity.entityDesc,name:u.name,process:u.process.process,processDesc:u.process.processDesc,identifier:(D=u==null?void 0:u.identifier)==null?void 0:D.identifier,identifierDesc:(G=u==null?void 0:u.identifier)==null?void 0:G.identifierDesc,status:a,subject:u.subject.replaceAll("[","").replaceAll("]",""),updatedBy:s.userData.user_id,updatedOn:new Date().toISOString()},h,j;u.emailDefinitionId===""?(h="POST",j="/WorkUtilsServices/v2/mail-definition/"):(h="PATCH",j=`/WorkUtilsServices/v2/mail-definition/${u.emailDefinitionId}`),je(j,h,d,function(W){if(W.statusCode===401||W.statusCode==="401"){z.handleOpenPromptBox("TIMEOUT",{title:"Error",message:W.message,severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"});return}if(t==null||t.getMailDefinition(),je("/WorkUtilsServices/v2/mail-definition","get",null,function(ee){var Yt;(ee.statusCode===401||ee.statusCode==="401")&&z.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out.Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"});var Ee=(Yt=ee.data)==null?void 0:Yt.filter(Ct=>Ct.name===u.name),qe=Ee[0].emailDefinitionId;if(t!=null&&t.isRecepientData){var yi="/WorkUtilsServices/v1/mail-mapping";(t==null?void 0:t.creationType)!=="edit"||(h="PATCH");var Si={ccParticipant:C.ccType==="GROUP"?Ze[C.cc]:C.cc,ccParticipantType:C.ccType,createdBy:u.createdBy,createdOn:u.createdOn,fromDestination:null,id:"",application:s.applicationName,name:u.name,process:u.process.process,regionId:"",status:a,templateId:qe,toParticipant:C.receipentType==="GROUP"?Ze[C.receipents]:C.receipents,toParticipantType:C.receipentType,updatedBy:s.userData.user_id,updatedOn:new Date().toISOString()};je(yi,h,Si,Ct=>{Ct&&I(!1)},function(Ct){t==null||t.setAlert(!0),t==null||t.setAlertSeverity("error"),t==null||t.setAlertMessage(Ct)})}I(!1)},function(ee){t==null||t.setAlert(!0),t==null||t.setAlertSeverity("error"),t==null||t.setAlertMessage(ee)}),F(!1),t==null||t.getMailDefinition(),W.statusCode===200&&W.status){t==null||t.setAlertMessage(W.message),z.handleOpenPromptBox("SUCCESS",{message:`Email template ${e==="CREATE"?"created":"updated"} succesfully`,redirectOnClose:!1});return}if(W.statusCode!==200&&W.status){t==null||t.setAlertMessage(W.message),z.handleOpenPromptBox("ERROR",{title:"Error",message:W.message,severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"});return}},function(W){t==null||t.setAlert(!0),t==null||t.setAlertSeverity("error"),t==null||t.setAlertMessage(W),F(!1)})},f=(a,d)=>{F(!0);const h=G=>{if((G==null?void 0:G.statusCode)===401||(G==null?void 0:G.statusCode)==="401")z.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out. Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"});else if(G){const W=Array.isArray(G)?G:[];ie(W)}F(!1)},j=G=>{var W,ee,Ee;(W=t==null?void 0:t.setAlert)==null||W.call(t,!0),(ee=t==null?void 0:t.setAlertSeverity)==null||ee.call(t,"error"),(Ee=t==null?void 0:t.setAlertMessage)==null||Ee.call(t,G),F(!1)},D=`/${kt}${Ot.EMAIL_CONFIG.FETCH_NOTIFICATION_MODULES}?identifierId=${encodeURIComponent(d)}`;ft(D,"get",h,j)},g=()=>{Q(!0);let a="/WorkUtilsServices/v1/application/variables",d=[];d=A||[{}],je(a,"patch",d,function(h){(h.statusCode===401||h.statusCode==="401")&&z.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out.Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"}),Q(!1),he("Updated successfully"),Ue("success"),Je(),Q(!1)},function(){Q(!1),he("error"),Ue("error"),Je()})},B=(a,d,h,j)=>{F(!0),Ve("fetchidentifierVariablesHana",[j,d,h],function(D){(D.statusCode===401||D.statusCode==="401")&&z.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out.Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"}),D&&ye(D),F(!1)},function(D){t==null||t.setAlert(!0),t==null||t.setAlertSeverity("error"),t==null||t.setAlertMessage(D),F(!1)})},v=(a,d,h)=>{F(!0);const j=W=>{if((W==null?void 0:W.statusCode)===401||(W==null?void 0:W.statusCode)==="401")z.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out. Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"});else if(W){const ee=Array.isArray(W)?W:[];Ae(ee)}F(!1)},D=W=>{var ee,Ee,qe;(ee=t==null?void 0:t.setAlert)==null||ee.call(t,!0),(Ee=t==null?void 0:t.setAlertSeverity)==null||Ee.call(t,"error"),(qe=t==null?void 0:t.setAlertMessage)==null||qe.call(t,W),F(!1)},G=`/${kt}${Ot.EMAIL_CONFIG.FETCH_NOTIFICATION_EVENTS}?identifierId=${encodeURIComponent(h)}&notificationId=${encodeURIComponent(d)}`;ft(G,"get",j,D)},_=()=>{F(!0),Ve("populateAppIdentifiersHana",[],function(a){(a.statusCode===401||a.statusCode==="401")&&z.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out.Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"}),a&&Me(a),F(!1)},function(a){t==null||t.setAlert(!0),t==null||t.setAlertSeverity("error"),t==null||t.setAlertMessage(a),F(!1)})},p=()=>{if(Q(!0),s.aDestinationUrl&&s.aDestinationUrl){let a={application:s.applicationName,entity:u.entity.entity,identifier:u.identifier.identifier};je("/WorkUtilsServices/v2/application/process/Variable","post",a,function(d){var h,j;if((d.statusCode===401||d.statusCode==="401")&&z.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out.Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"}),d.data){ne(d.data);let D=(h=d.data)==null?void 0:h.filter(G=>G.processDesc===u.process.processDesc);V((j=D==null?void 0:D[0])==null?void 0:j.variables),Ie(d.data.map(Ft))}Q(!1)},function(){Q(!1),he("error"),Ue("error"),Je()})}};l.useEffect(()=>{s.applicationName!==""&&ge()},[s]);const ge=()=>{F(!0),Ve("fetchMailGroupingHana",[],function(a){if((a.statusCode===401||a.statusCode==="401")&&z.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out.Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"}),a){let d=a.map(D=>({id:D.id,name:D.groupName,userIdList:D.email}));Ke(d);let j=d.concat(o).map(D=>(Oe.set(D.id,D.name),D.name));He(new Map(Oe)),Se(j),S(j)}F(!1)},function(){Q(!1),he("error"),Ue("error"),Je()})},Be=()=>{let a=x==null?void 0:x.map(d=>(fe==null||fe.set(d.emailId,d.userName),d.emailId));le(new Map(fe)),Se(a),S(a)},Je=()=>{K(!0)},Ft=a=>({process:a==null?void 0:a.process,processDesc:a==null?void 0:a.processDesc,active:a==null?void 0:a.variables.filter(h=>(h==null?void 0:h.active)===!0),deactive:a==null?void 0:a.variables.filter(h=>(h==null?void 0:h.active)===!1)}),Lt=(a,d)=>{var h=new FileReader;h.readAsDataURL(a),h.onload=()=>d(h.result),h.onerror=()=>{}},vt=a=>new Promise(d=>Lt(a,h=>d({data:{link:h}}))),_t=a=>{let d=Ki(a);try{let h=document.createElement("div");h.style.display="flex",h.style.flexDirection="column",h.style.justifyContent="start",h.innerHTML=d,[...h.children].forEach(D=>{if(D.localName==="p")if(D.innerHTML)D.style.display="block !important",D.style.margin="0em",D.style.lineHeight="1.2em",D.style.width="100%";else{let G=document.createElement("br");D.replaceWith(G)}}),d=h.outerHTML}catch(h){console.log(h)}U({...u,body:d})},Xe=a=>{const d=hi(a),{contentBlocks:h,entityMap:j}=d,D=Ye.ContentState.createFromBlockArray(h,j),G=Ye.EditorState.createWithContent(D);me(G),ae(G)},Wt=a=>/^[A-Za-z][A-Za-z0-9_]*$/.test(a),It=["VARIABLE","GROUP","USER"];T.useEffect(()=>{var a,d,h,j,D,G,W;if(s.applicationName!=="")if(_(),(t==null?void 0:t.creationType)!=="new"&&(t!=null&&t.data)){let ee=t==null?void 0:t.data.subject;if(ee!==""){ee=ee.replaceAll("$","$[");let Ee=ee.split(" ");Ee=Ee.map(qe=>(qe[0]==="$"&&(qe+="]"),qe)),ee="",Ee.forEach(qe=>ee+=qe+" ")}U({name:(t==null?void 0:t.creationType)==="copy"?(t==null?void 0:t.data.name)+"_Copy":t==null?void 0:t.data.name,entity:{entity:(a=t==null?void 0:t.data)==null?void 0:a.entity,entityDesc:(d=t==null?void 0:t.data)==null?void 0:d.entityDesc},process:{process:t==null?void 0:t.data.process,processDesc:t==null?void 0:t.data.processDesc},identifier:{identifier:t==null?void 0:t.data.identifier,identifierDesc:t==null?void 0:t.data.identifierDesc},subject:ee,body:t==null?void 0:t.data.content,application:t==null?void 0:t.data.application,applicationDesc:t==null?void 0:t.data.applicationDesc,emailDefinitionId:(t==null?void 0:t.creationType)==="copy"?"":t==null?void 0:t.data.emailDefinitionId,createdBy:t==null?void 0:t.data.createdBy,createdOn:new Date(t==null?void 0:t.data.createdOn).toISOString(),updatedBy:(j=(h=t==null?void 0:t.data)==null?void 0:h.createdBy)==null?void 0:j.updatedBy,updatedOn:new Date(t==null?void 0:t.data.updatedOn).toISOString()}),Te({receipentType:t==null?void 0:t.data.toParticipantType,receipents:(t==null?void 0:t.data.toParticipantType)==="GROUP"?(D=Array.from(Oe).find(([Ee])=>Ee==(t==null?void 0:t.data.toParticipant)))==null?void 0:D[1]:t==null?void 0:t.data.toParticipant,ccType:t==null?void 0:t.data.ccParticipantType,cc:(t==null?void 0:t.data.ccParticipantType)==="GROUP"?(G=Array.from(Oe).find(([Ee])=>Ee==(t==null?void 0:t.data.ccParticipant)))==null?void 0:G[1]:t==null?void 0:t.data.ccParticipant}),Qe("receipentType",t==null?void 0:t.data.toParticipantType),Qe("ccType",t==null?void 0:t.data.ccParticipantType),f(t==null?void 0:t.data.application,t==null?void 0:t.data.identifier),v(t==null?void 0:t.data.application,t==null?void 0:t.data.entity,t==null?void 0:t.data.identifier),B(t==null?void 0:t.data.application,t==null?void 0:t.data.entity,t==null?void 0:t.data.process,t==null?void 0:t.data.identifier),Xe((W=t==null?void 0:t.data)==null?void 0:W.content)}else lt()},[t==null?void 0:t.data,t==null?void 0:t.creationType,s]);const st=(a,d)=>{let h=structuredClone(A);h[d].active=!a.active,V(h)};l.useEffect(()=>{u!=null&&u.identifier&&(u!=null&&u.process)&&(u!=null&&u.entity)&&p(u==null?void 0:u.entity)},[u==null?void 0:u.identifier,u==null?void 0:u.process,u==null?void 0:u.entity,s.applicationId]),l.useEffect(()=>{s.applicationId!==void 0&&Be()},[s.applicationId]);const[Fe,Pe]=l.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""}),[ut,mt]=l.useState(""),z={handleClosePromptBox:()=>{Pe(a=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),mt("")},handleOpenPromptBox:(a,d={})=>{let h={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};a==="SUCCESS"&&(h.type="snackbar"),mt(a),Pe({...h,...d})},handleCloseAndRedirect:()=>{z.handleClosePromptBox(),navigate("/purchaseManagement/purchaseOrder")},getCancelFunction:()=>{switch(ut){default:return()=>{z.handleClosePromptBox()}}},getCloseFunction:()=>{switch(ut){case"COMMENTERROR":default:return a=>{z.handleClosePromptBox()}}},getOkFunction:()=>{switch(ut){case"CONFIRM_DISCARD":return()=>{xt()};case"CONFIRM_SUBMIT":return()=>{rt("Active")};case"CONFIRM_SUBMIT_AS_DRAFT":return()=>{rt("Draft")};case"TIMEOUT":return()=>{rt("Timeout")};default:return()=>z.handleClosePromptBox()}},getCloseAndRedirectFunction:()=>Fe.redirectOnClose?z.handleCloseAndRedirect:z.handleClosePromptBox};let be=a=>{switch(a){case"CONFIRM_DISCARD":z.handleOpenPromptBox("CONFIRM_DISCARD",{title:"Confirm Discard",message:"Are you sure you want to proceed with discard? The entered data will be lost",severity:"warning",cancelButton:!0,okButton:!0,okButtonText:"Discard"});break;case"CONFIRM_SUBMIT":if(!Wt(u.name)){z.handleOpenPromptBox("WARNING",{title:"Error",message:"Please Enter a valid template name",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"});return}if(!u.entity|!u.process|!u.name|!u.identifier){z.handleOpenPromptBox("WARNING",{title:"Error",message:"Please fill in all the mandatory fields",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"});return}z.handleOpenPromptBox("CONFIRM_SUBMIT",{title:"Confirm Submit",message:"Are you sure you want to proceed with submission?",severity:"success",cancelButton:!0,okButton:!0,okButtonText:"Submit"});break;case"CONFIRM_SUBMIT_AS_DRAFT":z.handleOpenPromptBox("CONFIRM_SUBMIT_AS_DRAFT",{title:"Confirm Submit",message:"Are you sure you want to proceed with saving as draft?",severity:"success",cancelButton:!0,okButton:!0,okButtonText:"Save as draft"});break}};const xt=()=>{y(),(t==null?void 0:t.creationType)!=="edit"&&lt(),N("INITIAL"),ne([]),xe("new")},P=()=>{Y(!1)};return l.useEffect(()=>{var a;((a=u.subject)==null?void 0:a.length)>100&&z.handleOpenPromptBox("ERROR",{title:"Error",message:"Subject exceeded max length of 100",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"})},[u.subject]),c("div",{children:[i(Pt,{className:"backdrop",sx:{zIndex:"9999999"},open:te,children:i(Bt,{color:"primary"})}),i(Nt,{type:Fe.type,promptState:Fe.open,setPromptState:z.handleClosePromptBox,onCloseAction:z.getCloseFunction(),promptMessage:Fe.message,dialogSeverity:Fe.severity,dialogTitleText:Fe.title,handleCancelButtonAction:z.getCancelFunction(),cancelButtonText:Fe.cancelText,showCancelButton:Fe.cancelButton,handleSnackBarPromptClose:z.getCloseAndRedirectFunction(),handleOkButtonAction:z.getOkFunction(),okButtonText:Fe.okButtonText,showOkButton:Fe.okButton}),i(k,{container:!0,sx:{marginBottom:"1.5rem"},children:c(k,{item:!0,md:9,style:{display:"flex"},children:[i(k,{item:!0,sx:{maxWidth:"max-content"},children:i($e,{onClick:xt,color:"primary",component:"label",className:"iconButton-spacing-small",sx:{padding:"0.25rem",height:"max-content"},children:i(si,{sx:{fontSize:"25px",color:"#000000"}})})}),c(k,{item:!0,xs:!0,children:[i(O,{variant:"h3",children:c("strong",{children:[" ",(t==null?void 0:t.creationType)==="new"||(t==null?void 0:t.creationType)==="copy"?"Create":"Edit"," Template"]})}),c(O,{variant:"body2",color:"#777",children:["This view allows users to ",e==="CREATE"?"create":"edit"," Email Templates"]})]})]})}),c(k,{container:!0,alignItems:"center",justifyContent:"center",children:[c(re,{sx:{marginBottom:"64px",width:"100%"},className:"cwWorkUtilsScroll",children:[c(bt,{sx:{padding:"1.5rem",marginBottom:"1.5rem",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.08)",background:"linear-gradient(to bottom, #ffffff, #fafbff)"},children:[c(O,{variant:"h5",sx:{marginBottom:"1.5rem",fontWeight:600,color:"#1a3e72",display:"flex",alignItems:"center",gap:"0.5rem"},children:[i(Ut,{fontSize:"small"})," Email Template"]}),c(k,{container:!0,spacing:3,columns:24,children:[c(k,{item:!0,xs:6,children:[i(O,{variant:"body1",sx:{marginBottom:"0.5rem",fontWeight:500},children:t!=null&&t.headers[3]?t==null?void 0:t.headers[3]:"Template Name"}),i(De,{fullWidth:!0,size:"small",placeholder:"Enter Template Name",className:"CustomTextField",id:"outlined-basic",variant:"outlined",value:u.name,onChange:a=>U({...u,name:a.target.value}),sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px","&:hover fieldset":{borderColor:"#1976d2"}}}})]}),c(k,{item:!0,xs:6,children:[i(O,{variant:"body1",sx:{marginBottom:"0.5rem",fontWeight:500},children:t!=null&&t.headers[0]?t==null?void 0:t.headers[0]:"Identifier"}),i(ct,{disabled:(t==null?void 0:t.creationType)==="edit",variant:"outlined",disablePortal:!0,className:"CustomAutoComplete",size:"small",id:"combo-box-demo",placeholder:"Enter Identifier Name",options:oe,value:u.identifier,renderInput:a=>i(De,{...a,fullWidth:!0,placeholder:"Enter Identifier Name",sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px","&:hover fieldset":{borderColor:"#1976d2"}}}}),getOptionLabel:a=>a.identifierDesc?a.identifierDesc:"",onChange:(a,d)=>{U({...u,identifier:d,entity:null,process:null}),d&&f(s.applicationName,d.identifier)},isOptionEqualToValue:(a,d)=>a.identifier===d.identifier})]}),c(k,{item:!0,xs:6,children:[i(O,{variant:"body1",sx:{marginBottom:"0.5rem",fontWeight:500},children:t!=null&&t.headers[1]?t==null?void 0:t.headers[1]:"Module"}),i(ct,{disabled:(t==null?void 0:t.creationType)==="edit",disablePortal:!0,id:"combo-box-demo",size:"small",options:$,value:u.entity,renderInput:a=>i(De,{...a,fullWidth:!0,placeholder:"Enter Module Name",sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px","&:hover fieldset":{borderColor:"#1976d2"}}}}),getOptionLabel:a=>a.entityDesc?a.entityDesc:"",onChange:(a,d)=>{var h;U({...u,entity:d,process:null}),d&&v(s.applicationName,d.entity,(h=u==null?void 0:u.identifier)==null?void 0:h.identifier)},isOptionEqualToValue:(a,d)=>a.entity===d.entity})]}),c(k,{item:!0,xs:6,children:[i(O,{variant:"body1",sx:{marginBottom:"0.5rem",fontWeight:500},children:t!=null&&t.headers[2]?t==null?void 0:t.headers[2]:"Event"}),i(ct,{disabled:(t==null?void 0:t.creationType)==="edit",variant:"outlined",disablePortal:!0,size:"small",id:"combo-box-demo",options:ce,value:u.process,renderInput:a=>i(De,{placeholder:"Enter Event Name",fullWidth:!0,...a,sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px","&:hover fieldset":{borderColor:"#1976d2"}}}}),getOptionLabel:a=>a.processDesc?a.processDesc:"",onChange:(a,d)=>{var h;U({...u,process:d}),d&&B(s.applicationName,u.entity.entity,d.process,(h=u==null?void 0:u.identifier)==null?void 0:h.identifier)},isOptionEqualToValue:(a,d)=>a.processDesc===d.processDesc})]})]}),c(re,{sx:{marginTop:"2rem",padding:"1.5rem",backgroundColor:"#f8f9fa",borderRadius:"6px"},children:[i(O,{variant:"h5",sx:{marginBottom:"1rem",fontWeight:600,color:"#1a3e72"},children:"Event Variables"}),A!=null&&A.length?i(E,{direction:"row",rowGap:2,columnGap:1.5,alignItems:"start",justifyContent:"start",sx:{minHeight:"20vh",display:"flex",width:"100%",flexWrap:"wrap",padding:"0.5rem"},children:A==null?void 0:A.map((a,d)=>i(ue,{variant:a.active?"contained":"outlined",color:a.active?"primary":"secondary",sx:{marginBottom:"0.75rem",borderRadius:"20px",padding:"6px 16px",textTransform:"none",fontWeight:500,boxShadow:a.active?"0 2px 5px rgba(0,0,0,0.1)":"none"},onClick:()=>st(a,d),children:a.variable},d))}):i(re,{sx:{minHeight:"20vh",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"#fff",borderRadius:"6px",border:"1px dashed #d32f2f",padding:"1rem"},children:i(O,{sx:{color:"#d32f2f",fontWeight:500,fontSize:"0.95rem"},children:"No variables available. Please select an event first."})})]})]}),(t==null?void 0:t.useChannels)&&i(re,{sx:{padding:"1rem 1rem ",marginBottom:"1rem"},children:c(E,{container:!0,direction:"column",children:[i(O,{sx:{marginBottom:"1rem"},variant:"h5",children:"Channels"}),c(k,{spacing:2,direction:"row",children:[i(E,{justifyContent:"start",alignItems:"center",direction:"row",spacing:2,children:Object.values(ba).map(a=>i(ue,{onClick:()=>nt(a.key),variant:"contained",color:"action",startIcon:a.key===at?i(Ri,{}):a.icon,children:a.label}))}),i(Sa,{channel:at})]})]})}),c(bt,{sx:{padding:"1.5rem",marginBottom:"1.5rem",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.08)",background:"linear-gradient(to bottom, #ffffff, #fafbff)"},children:[c(O,{variant:"h5",sx:{marginBottom:"1.5rem",fontWeight:600,color:"#1a3e72",display:"flex",alignItems:"center",gap:"0.5rem"},children:[i(Ut,{fontSize:"small"})," Email Template"]}),c(k,{container:!0,direction:"column",spacing:3,children:[c(k,{item:!0,children:[c(re,{sx:{display:"flex",alignItems:"flex-start",gap:"1rem",width:"100%"},children:[i(O,{sx:{fontWeight:500,width:"80px",paddingTop:"8px",color:"#555"},children:"Subject"}),i(De,{fullWidth:!0,size:"small",placeholder:"Enter email subject line",inputProps:{maxLength:100},value:u.subject,onChange:a=>U({...u,subject:a.target.value}),sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px",backgroundColor:"#fff",transition:"all 0.2s","&:hover fieldset":{borderColor:"#1976d2"},"&.Mui-focused fieldset":{borderColor:"#3b30c8",borderWidth:"1px"}}}})]}),c(re,{sx:{mt:.5,textAlign:"right",color:((we=u.subject)==null?void 0:we.length)>80?"#d32f2f":"#666",fontSize:"0.75rem"},children:[((ve=u.subject)==null?void 0:ve.length)||0,"/100"]})]}),c(k,{item:!0,children:[i(O,{sx:{fontWeight:500,mb:1.5,color:"#555"},children:"Email Body"}),i(re,{sx:{border:"1px solid #e0e0e0",borderRadius:"8px",overflow:"hidden","& .rdw-editor-toolbar":{border:"none",borderBottom:"1px solid #e0e0e0",padding:"8px 16px",background:"#f5f7fa"},"& .rdw-option-wrapper":{border:"none",background:"transparent",height:"28px",width:"28px",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 2px",borderRadius:"4px","&:hover":{background:"#e3e8f0"},"&.rdw-option-active":{background:"#d8e0f0"}},"& .rdw-dropdown-wrapper":{border:"1px solid #e0e0e0",borderRadius:"4px",margin:"0 4px"},"& .Editor":{padding:"16px",minHeight:"250px",fontSize:"14px",lineHeight:"1.6",color:"#333"},"& .public-DraftStyleDefault-block":{margin:"0.5em 0"},"& .rdw-link-modal, & .rdw-image-modal":{boxShadow:"0 3px 10px rgba(0,0,0,0.15)",borderRadius:"8px"},"& .rdw-suggestion-dropdown":{borderRadius:"8px",boxShadow:"0 3px 10px rgba(0,0,0,0.15)",padding:"8px 0"},"& .rdw-suggestion-option":{padding:"8px 16px","&:hover":{background:"#f0f4fa"}}},children:i(jt.Editor,{editorState:X,wrapperClassName:"Editor",editorClassName:"Editor",defaultEditorState:L,onEditorStateChange:ae,onContentStateChange:_t,toolbar:{inline:{inDropdown:!1},list:{inDropdown:!1},textAlign:{inDropdown:!1},link:{inDropdown:!1},history:{inDropdown:!1},image:{uploadCallback:vt,previewImage:!0,alignmentEnabled:!0}},mention:{separator:" ",trigger:"$",suggestions:A==null?void 0:A.map(a=>({text:a.variable??"",value:a.variable??"",url:a.variable??""}))}})}),i(re,{sx:{mt:2,display:"flex",alignItems:"center",gap:1},children:c(re,{sx:{display:"inline-flex",alignItems:"center",gap:.5,backgroundColor:"#f0f4fa",padding:"4px 10px",borderRadius:"4px",fontSize:"0.75rem",color:"#3b30c8",fontWeight:500},children:[i(ui,{fontSize:"small",sx:{fontSize:"14px"}}),"Use $variable to insert dynamic content"]})})]})]})]}),(t==null?void 0:t.isRecepientData)&&i(k,{item:!0,xs:12,p:4,spacing:2,children:c(k,{container:!0,children:[i(O,{sx:{fontWeight:500,color:"black ",fontSize:"18px"},children:"Select The Participants"}),c(k,{container:!0,spacing:2,p:2,children:[c(k,{item:!0,xs:12,children:[i(O,{children:"Recipent Type"}),i(ct,{value:C==null?void 0:C.receipentType,onSelect:a=>{Qe("receipentType",a.target.value)},disablePortal:!0,id:"combo-box-demo",options:It,size:"small",renderInput:a=>i(De,{fullWidth:!0,...a,label:"Select Recipient Type"})})]}),c(k,{item:!0,xs:12,children:[i(O,{children:"Receipents"}),i(ct,{value:C==null?void 0:C.receipents,onSelect:a=>Qe("receipents",a.target.value),fullWidth:!0,disablePortal:!0,id:"combo-box-demo",options:it,size:"small",renderInput:a=>i(De,{fullWidth:!0,...a,label:"Select Recipients"})})]}),c(k,{item:!0,xs:12,children:[i(O,{children:"CC Type"}),i(ct,{value:C==null?void 0:C.ccType,onSelect:a=>Qe("ccType",a.target.value),fullWidth:!0,disablePortal:!0,id:"combo-box-demo",options:It,size:"small",renderInput:a=>i(De,{fullWidth:!0,...a,label:"Add CC recipient type"})})]}),c(k,{item:!0,xs:12,children:[i(O,{children:"CC"}),i(ct,{value:C==null?void 0:C.cc,onSelect:a=>Qe("cc",a.target.value),fullWidth:!0,disablePortal:!0,id:"combo-box-demo",options:Ce,size:"small",renderInput:a=>i(De,{fullWidth:!0,...a,label:"Add CC recipient "})})]})]})]})})]}),i(_e,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:"4"},elevation:2,children:i($t,{className:"container_BottomNav",children:c(E,{direction:"row",sx:{marginLeft:"auto"},spacing:2,alignItems:"center",children:[s.feature.EMAIL_CONFIG_DISCARD==="True"&&i(ue,{variant:"text",sx:{minWidth:"max-content",padding:"6px 12px",texttransform:"capitalize",height:"2rem"},onClick:()=>{be("CONFIRM_DISCARD")},children:"Discard"}),s.feature.EMAIL_CONFIG_SAVE==="True"&&i(ue,{variant:"outlined",sx:{minWidth:"max-content",padding:"6px 12px",texttransform:"capitalize",height:"2rem"},onClick:()=>{be("CONFIRM_SUBMIT_AS_DRAFT")},children:"Save As Draft"}),s.feature.EMAIL_CONFIG_SUBMIT==="True"&&i(ue,{variant:"contained",sx:{minWidth:"max-content",padding:"6px 12px",texttransform:"capitalize",height:"2rem"},onClick:()=>{be("CONFIRM_SUBMIT")},children:"Submit"})]})})})]}),i(Mt,{message:q,creationType:b,open:se,onClose:a=>rt(a)}),i(ya,{openPreview:r,closePreview:P})]})},wa=pi(e=>({avatar:{width:"26px",height:"26px",background:"#F1F5FE"}})),va=e=>{var m=[""];return e&&(m=e==null?void 0:e.trim().split(",")[0].split(" ")),m.length>1?`${m[0][0]}${m[(m==null?void 0:m.length)-1][0]}`:m[0].length>0?`${m[0][0]}`:""};function Ia(e){const m=wa();return i(Pi,{...e,src:(e==null?void 0:e.src)&&(e==null?void 0:e.src),className:`${m.avatar} ${e==null?void 0:e.className}`,sx:(e==null?void 0:e.sx)&&(e==null?void 0:e.sx),children:va(e==null?void 0:e.name)})}const Ea=({open:e,onClose:m,setScenario:y,...x})=>{const o=useSelector(S=>S.userReducer),[N,t]=T.useState("1"),[s,H]=T.useState(!1),[F,te]=T.useState(!1),[I,r]=T.useState(!1),[Y,$]=T.useState(""),[ie,ce]=T.useState("Cancel"),[Ae,oe]=l.useState("success"),[Me,u]=l.useState(""),[U,X]=l.useState(!1),[ae,Re]=l.useState([]);l.useState([]);const[ye,L]=T.useState(!1),[me,se]=l.useState(null),[ke,q]=l.useState(new Map),[w,b]=l.useState(new Map),[Z,J]=l.useState([]),[ne,A]=l.useState([]),[V,We]=l.useState([]),[Ie,tt]=l.useState([]),[K,Ge]=l.useState({id:null,groupName:"",email:"",createdBy:o.userData.user_id,createdOn:new Date().toISOString(),updatedBy:o.userData.user_id,updatedOn:new Date().toISOString()}),Ue=(S,C)=>{C!="clickaway"&&X(!1)},gt=()=>{H(!0)},he=()=>{H(!1)},wt=()=>{te(!0)},xe=()=>{te(!1)},ze=()=>{m(),y("INITIAL")},Q=(S,C)=>{t(C)};l.useEffect(()=>{if(o.applicationName!==""){x==null||x.userList.map(C=>(ke.set(C.emailId,C.userName),{code:C.userName,description:C.emailId})),q(new Map(ke));let S=x==null?void 0:x.groupList.map(C=>(w.set(C.id,C.name),{code:C.name,description:C.id}));b(new Map(w)),A(S)}},[o]);const fe=()=>{X(!0)},le=S=>{S==="Timeout"&&window.location.reload(),r(!1),$("")},Oe=S=>{let C;S.includes(",")?C=S.split(","):C=S.split(";");let Te=C.map(R=>({emailId:R,username:ke.get(R)===void 0?R:ke.get(R)}));J(Te)},He=S=>{Ve("fetchAssociatedTemplatesHana",[S,S],function(C){(C.statusCode===401||C.statusCode==="401")&&(r(!0),ce("Timeout"),$("Session Timed Out.Kindly Refresh")),C&&tt(C),console.log(C),L(!1)},function(C){x==null||x.setAlert(!0),x==null||x.setAlertSeverity("error"),x==null||x.setAlertMessage(C),L(!1)})},it=()=>{L(!0),Ve("fetchMailGroupingHana",[],function(S){if((S.statusCode===401||S.statusCode==="401")&&(r(!0),ce("Timeout"),$("Session Timed Out.Kindly Refresh")),S){let C=S.map(R=>({id:R.id,name:R.groupName,userIdList:R.email}));Re(C);let Te=C.concat(x==null?void 0:x.groupList);We(Te)}L(!1)},function(S){u("Error"),oe("error"),fe(),L(!1)})},Se=()=>{L(!0);let S=K.email.split(";"),C=K.groupName;if(Ze(S)&&Ne(C)){let Te="/WorkUtilsServices/v1/mail-group",R="POST",dt={id:K.id,createdBy:K.createdBy,createdOn:K.createdOn,email:K.email,groupName:K.groupName,updatedBy:o.userData.user_id,updatedOn:new Date().toISOString()};je(Te,R,dt,function(Ke){(Ke.statusCode===401||Ke.statusCode==="401")&&(r(!0),ce("Timeout"),$("Session Timed Out.Kindly Refresh")),it(),u("Saved successfully"),oe("success"),fe(),L(!1),H(!1)},function(Ke){u("Error"),oe("error"),fe(),L(!1)})}else u("Enter valid Data!"),oe("error"),fe(),L(!1)},Ze=S=>{const C=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return S.every(Te=>Te.match(C))},Ne=S=>/^[A-Za-z][A-Za-z0-9_]*$/.test(S),Ce=(S,C)=>{Ge(C==="groupName"?{...K,groupName:S.target.value}:{...K,email:S.target.value})};return l.useEffect(()=>{o.applicationName!==""&&(it(),console.log(o.applicationName))},[o]),c("div",{p:2,className:"cwntSetHeight100 cwntSetWidth100",children:[c(E,{direction:"row",spacing:2,alignItems:"center",justifyContent:"space-between",children:[c(E,{direction:"row",alignItems:"center",children:[i($e,{"aria-label":"delete",onClick:ze,children:i(qi,{})}),i(O,{sx:{fontWeight:600,color:"black !important",fontSize:"20px"},children:"Groups"})]}),i(E,{direction:"row",spacing:2,alignItems:"center",children:i(ue,{variant:"outlined",onClick:gt,children:"Create Group"})})]}),c(E,{height:"100%",width:"100%",direction:"row",justifyContent:"space-around",sx:{overflow:"hidden",background:"#fff"},children:[c(_e,{elevation:1,sx:{width:"35%",background:"#fff",borderRadius:"12px",margin:"1rem 0.5rem 1rem 1rem",height:"calc(100%-2rem)"},children:[i(E,{direction:"row",pl:1,pr:1,justifyContent:"space-between",alignItems:"center",width:"100%",height:"2.5rem",sx:{background:"#F1F5FE",borderRadius:"12px"},children:i("span",{style:{fontWeight:500,color:"black ",fontSize:"14px",whiteSpace:"nowrap",fontFamily:'"Roboto", sans-serif !important'},children:"Display Name"})}),i(E,{pt:1,sx:{height:"93%",width:"100%",borderRadius:"5px",overflowY:"scroll",backgroundColor:"white",position:"relative"},alignItems:"center",children:V==null?void 0:V.map((S,C)=>i(re,{sx:{paddingTop:1,paddingBottom:2,width:"90%"},children:i(bt,{sx:{minWidth:275,background:me===C?" linear-gradient(180.76deg, #C1DCFF -113.11%, rgba(255, 255, 255, 0) 198.03%)":""},onClick:()=>{Oe(S.userIdList),He(S.id),se(C)},children:i(zt,{children:i(Di,{avatar:i(Ia,{src:"",name:S.name,sx:{fontSize:"0.8rem",color:"black"}}),title:S.name})})},C)}))})]}),c(_e,{elevation:1,sx:{width:"64%",background:"#fff",borderRadius:"12px",margin:"1rem 1rem 1rem 0.5rem",height:"calc(100%-2rem)"},children:[c(E,{direction:"row",justifyContent:"space-between",children:[i(O,{children:"Group Details"}),o.feature.EMAIL_CONFIG_MANAGE_GROUPS_ADD_USER==="True"&&c(ue,{variant:"outlined",onClick:wt,children:[" ","Add User"]})]}),i(re,{sx:{width:"100%",typography:"body1"},children:c(Yi,{value:N,children:[i(re,{sx:{borderBottom:1,borderColor:"divider"},children:c(Zi,{onChange:Q,"aria-label":"lab API tabs example",children:[o.feature.EMAIL_CONFIG_MANAGE_GROUPS_MEMBER_INFO==="True"&&i(ht,{label:"Member Information",value:"1"}),o.feature.EMAIL_CONFIG_MANAGE_GROUPS_TEMPLATE_INFO==="True"&&i(ht,{label:"Associated Template",value:"2"})]})}),c(pt,{value:"1",children:[" ",i(TableContainer,{component:_e,children:c(Table,{sx:{minWidth:650},"aria-label":"simple table",children:[i(Mi,{children:c(Jt,{children:[i(TableCell,{children:"User Name"}),i(TableCell,{children:"Email ID"})]})}),i(TableBody,{children:Z==null?void 0:Z.map(S=>c(Jt,{sx:{"&:last-child td, &:last-child th":{border:0}},children:[i(TableCell,{component:"th",scope:"row",children:S.username}),i(TableCell,{children:S.emailId})]}))})]})})]}),i(pt,{value:"2",overflowY:"scroll",height:"100%",children:i(k,{container:!0,direction:"row",spacing:2,children:Ie==null?void 0:Ie.map((S,C)=>i(k,{item:!0,xs:6,sm:6,md:6,xl:3,children:i(bt,{sx:{backgroundColor:"#FFFFFF",minHeight:"7rem",maxWidth:"20rem"},children:i(zt,{sx:{height:"100%"},children:i(oi,{children:c(E,{direction:"column",spacing:1,children:[c(E,{direction:"row",width:"100%",justifyContent:"space-between",children:[i(O,{sx:{fontFamily:"Roboto, sans-serif !important",color:"#000000 !important",fontWeight:"500 !important",fontSize:"14px !important",height:"24px !important"},children:S.templateName}),c(E,{direction:"row",children:[i($e,{size:"small",children:i(fi,{variant:"outlined",sx:{color:S.status==="Active"?"#4CAF50":S.status==="Draft"?"#FFA500":"#647C90",fontSize:"0.8rem"}})}),i(O,{sx:{width:"34px !important",color:"#000000 !important ",fontWeight:"400 !important",fontSize:"12px !important",color:S.status==="Active"?"#4CAF50":S.status==="Draft"?"#FFA500":"#647C90",display:"flex !important",height:"24px !important",alignItems:"center!important"},children:S.status})]})]}),c(E,{direction:"row",spacing:2,children:[i(Rt,{label:S.entitydesc,sx:{width:"120px !important",color:"#072E3A !important ",fontWeight:"400 !important",fontSize:"14px !important",lineHeight:"16.41px !important",display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center",padding:"8px !important",background:"#E4F1FF !important",height:"24px !important",borderRadius:"9px !important"}}),i(Rt,{label:S.processName,variant:"filled",sx:{width:"120px !important",color:"#600E59 !important",fontWeight:"400 !important",fontSize:"14px !important",lineHeight:"16px !important",display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center",padding:"8px !important",background:"#FBEEFA !important",height:"24px !important",borderRadius:"9px !important"}})]})]})})})})}))})})]})})]})]}),c(Tt,{open:s,onClose:he,children:[i(yt,{children:"Create Group"}),i(St,{children:c(E,{children:[c(E,{justifyContent:"space-between",alignItems:"left",children:[i("span",{style:{fontWeight:500,color:"black ",fontSize:"1rem"},children:"Group Name:"}),i(De,{autoFocus:!0,margin:"dense",value:K.groupName,id:"groupName",sx:{width:"25rem","& .MuiInputBase-root":{height:40}},fullWidth:!0,variant:"outlined",onChange:S=>Ce(S,"groupName")})]}),c(E,{justifyContent:"space-between",alignItems:"left",children:[i("span",{style:{fontWeight:500,color:"black ",fontSize:"1rem"},children:"Email ID:"}),i(De,{autoFocus:!0,margin:"dense",id:"emailId",value:K==null?void 0:K.email,fullWidth:!0,variant:"outlined",sx:{width:"25rem","& .MuiInputBase-root":{height:40}},onChange:S=>Ce(S,"email")})]})]})}),c(At,{children:[i(ue,{onClick:he,children:"Cancel"}),c(ue,{variant:"contained",onClick:()=>Se(),children:["Save & Continue"," "]})]})]}),c(Tt,{open:F,onClose:xe,children:[i(yt,{children:"Add User"}),i(St,{children:c(E,{children:[i(O,{sx:{height:"29px"},children:"PO Group"}),c(E,{justifyContent:"space-between",alignItems:"left",children:[i("span",{style:{fontWeight:500,color:"black ",fontSize:"1rem"},children:"Select User"}),i(De,{autoFocus:!0,margin:"dense",id:"emailId",fullWidth:!0,variant:"outlined",sx:{width:"25rem","& .MuiInputBase-root":{height:40}}})]})]})}),c(At,{children:[i(ue,{onClick:xe,variant:"outlined",children:"Cancel"}),i(ue,{variant:"contained",children:"Save "})]})]}),i(Mt,{message:Y,creationType:ie,open:I,onClose:S=>le(S)}),i(li,{open:U,autoHideDuration:6e3,onClose:Ue,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:i(Ni,{onClose:Ue,severity:Ae,sx:{width:"100%"},children:Me})})]})},Aa=Fi("div")(({theme:e})=>({display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%"}));function Ra(){return i(Aa,{children:i(re,{sx:{mt:1},children:"No Data Available"})})}function ka({onCellEditCommit:e,field_name:m,url_onRowClick:y,stopPropagation_Column:x,redirecOnDoubleClick:o=null,status_onRowDoubleClick:N=!1,status_onRowSingleClick:t=!1,title:s,getRowIdValue:H,getRowHeight:F,rows:te,columns:I,hideFooter:r,checkboxSelection:Y,disableSelectionOnClick:$,onRowsSelectionHandler:ie=()=>{},showConfig:ce,setShowWork:Ae,fieldName_onCellClick:oe,onCellKeyDown:Me=()=>{},onEditCellPropsChange:u=()=>{},experimentalFeatures:U,isRowSelectable:X,module:ae,isLoading:Re,rowsPerPageOptions:ye,noOfColumns:L,callback_onRowDoubleClick:me=null,callback_onRowSingleClick:se=null,sortModel:ke,onSortModelChange:q,selectedRows:w,columnVisibility:b=!0}){Ht();const[Z,J]=l.useState(L??10);let ne=mi();return c("div",{className:"reusable-table",children:[i(O,{variant:"h6",sx:{marginBottom:".5rem"},children:s}),i(_i,{onCellKeyDown:Me,disableColumnSelector:b,autoHeight:!0,loading:Re,getRowId:A=>H?A[H]:"id",rows:te,columns:I,pageSize:Z,onPageSizeChange:A=>J(A),rowsPerPageOptions:ye??[10,25,50],disableExtendRowFullWidth:!1,hideFooter:r,sortModel:ke,onSortModelChange:q,checkboxSelection:Y,disableSelectionOnClick:$,experimentalFeatures:U,getRowClassName:A=>`custom-row--${m}-${A.row[m]}`,getRowHeight:F,onRowDoubleClick:!me&&N?(A,V)=>{V.stopPropagation(),o&&o(),A.row.id?ne(y+`${A.row.id}`):ne(y+`${A.id}`),Ae&&Ae(!0)}:me?A=>me(A):null,onRowClick:t?(A,V)=>{se(A)}:null,onCellClick:(A,V)=>{typeof x!="object"?x===A.field&&V.stopPropagation():x.includes(A.field)&&V.stopPropagation()},onCellEditCommit:e,onEditCellPropsChange:(A,V)=>u(A,V),onSelectionModelChange:A=>ie(A),sx:{"& .MuiDataGrid-row:hover":{backgroundColor:"#EAE9FF40",cursor:t?"pointer":"default"},backgroundColor:"#fff"},components:{LoadingOverlay:Li,NoRowsOverlay:Ra},isRowSelectable:X,selectionModel:w})]})}const Oa=({onClose:e,userList:m,groupList:y,setScenario:x,...o})=>{const N=ot(n=>n.userReducer),[t,s]=l.useState(new Map),[H,F]=l.useState(new Map);l.useState(!1);const[te,I]=T.useState(!1);l.useState(!0),l.useState({application:null,process:null,templateName:null,participant:null,ccParticipant:null,groupName:null,emailId:null}),l.useState(!1),l.useState([]),l.useState(!1),l.useState([]);const[r,Y]=l.useState([]),[$,ie]=l.useState([]),[ce,Ae]=l.useState([]),[oe,Me]=l.useState([]);l.useState([]),l.useState([]),l.useState({}),l.useState({}),l.useState({}),l.useState({});const[u,U]=l.useState([]);l.useState({});const[X,ae]=l.useState([]),[Re,ye]=l.useState([]),L=["GROUP","USER"],[me,se]=l.useState(!1);l.useState(null);const[ke,q]=l.useState("success"),[w,b]=l.useState([]),[Z,J]=T.useState(!1),[ne,A]=T.useState(""),[V,We]=T.useState("Cancel"),[Ie,tt]=l.useState([]),[K,Ge]=l.useState(""),[Ue,gt]=l.useState([]),he={ccParticipant:"",ccParticipantType:"",createdBy:N.userData.user_id,createdOn:new Date().toISOString(),updatedBy:N.userData.user_id,updatedOn:new Date().toISOString(),fromDestination:"",id:Wi(),application:"",name:"",process:"",regionId:"",status:"",templateId:null,toParticipant:"",toParticipantType:"",isRowEditable:!0,creationType:"new",applicationName:"",processName:"",entity:"",identifier:""};l.useState(he);const[wt,xe]=l.useState("");l.useEffect(()=>{N.applicationName!==""&&(ze(U),ze(ae),Q(U),Q(ae))},[N]);const ze=n=>{I(!1),Ve("fetchMailGroupingHana",[],function(f){if(console.log(f,"Odata"),(f.statusCode===401||f.statusCode==="401")&&I(!1),f){let g=f.map((_,p)=>({id:_.id,name:_.groupName,userIdList:_.email}));tt(g);let B=g.concat(y),v=B.map((_,p)=>(H.set(_.id,_.name),{id:_.id,name:_.name}));console.log(B,"result"),console.log(v,"transFormgroupData1"),F(new Map(H)),n(v)}},function(f){xe("error"),q("error"),it(),I(!1)})},Q=n=>{let f=m.map((g,B)=>(t.set(g.emailId,g.userName),{id:g.emailId,name:g.userName}));s(new Map(t)),n(f)},fe=(n,f,g,B)=>{I(!0),Ve("fetchTemplatesOnIdentifiersHana",[g,B,f],function(v){(v.statusCode===401||v.statusCode==="401")&&(J(!0),We("Timeout"),A("Session Timed Out.Kindly Refresh")),v&&Me(v),I(!1)},function(v){se(!0),xe("error"),q("error"),I(!1)})},le=(n,f,g,B)=>{let v="";for(let _=0;_<w.length;_++)if(w[_].id===f){v=_;break}w[v].isEdited=!0,B==="application"&&(w[v].applicationName=g.applicationName,w[v].application=g.application,b([...w]),nt(g.application)),B==="identifier"?(w[v].identifierDesc=g.identifierDesc,w[v].identifier=g.identifier,b([...w]),at(N.applicationName,g.identifier)):B==="entity"?(w[v].entitydesc=g.entitydesc,w[v].entity=g.entity,b([...w]),nt(N.applicationName,g.entity,w[v].identifier)):B==="process"?(w[v].processName=g.processName,w[v].process=g.process,b([...w]),fe(N.applicationName,g.process,w[v].identifier,w[v].entity)):B==="name"?(w[v].templateName=g.name,w[v].templateId=g.emailDefinitionId,b([...w])):B==="toParticipantType"?(w[v].toParticipantType=g,b([...w]),g==="VARIABLE"?U([{id:"INITIATOR",name:"INITIATOR"},{id:"REVIEWER",name:"REVIEWER"}]):g==="GROUP"?ze(U):Q(U)):B==="toParticipant"?(w[v].toParticipant=g.id,b([...w])):B==="ccParticipantType"?(w[v].ccParticipantType=g,g===""?(w[v].ccParticipant="",ae([])):g==="VARIABLE"?ae([]):g==="GROUP"?ze(ye):Q(ye),b([...w])):B==="ccParticipant"&&(w[v].ccParticipant=g.id,b([...w]))},Oe=()=>{I(!0),Ve("fetchMailMappingHana",[],function(n){(n.statusCode===401||n.statusCode==="401")&&(J(!0),We("Timeout"),A("Session Timed Out.Kindly Refresh")),n&&(n.forEach(f=>{f.isRowEditable=!1,f.isEdited=!1}),b(n)),I(!1)},function(n){se(!0),xe("error"),q("error"),I(!1)})},He=()=>{let n=structuredClone(w);n.unshift(he),b(n)},it=()=>{se(!0)},Se=n=>{let f="",g=[...w];for(let B=0;B<g.length;B++)if(g[B].id===n){f=B;break}if(w[f].hasOwnProperty("creationType")){const B=[...w];B.splice(f,1),b(B)}else Oe()},Ze=(n,f)=>{let g="";n.isRowEditable=!0;let B=[...w];for(let v=0;v<(B==null?void 0:B.length);v++)if(w[v].id===f){g=v;break}switch(B[g]=n,b(B),at(N.applicationName,n.identifier),nt(N.applicationName,n.entity,n.identifier),fe(N.applicationName,n.process,n.identifier,n.entity),n.ccParticipantType){case"USER":Q(ye);break;case"GROUP":ze(ye);break}n.toParticipantType==="GROUP"?ze(U):n.toParticipantType==="USER"?Q(U):n.toParticipantType==="VARIABLE"&&U([{id:"INITIATOR",name:"INITIATOR"},{id:"REVIEWER",name:"REVIEWER"}]),n.ccParticipantType==="GROUP"?ze(ae):n.ccParticipantType==="USER"?Q(ae):n.ccParticipantType==="VARIABLE"&&ae([{id:"INITIATOR",name:"INITIATOR"},{id:"REVIEWER",name:"REVIEWER"}])},Ne=n=>{S(f=>({...f,open:!1})),I(!0),je("/WorkUtilsServices/v1/mail-mapping?Id="+n,"delete",null,function(f){(f.statusCode===401||f.statusCode==="401")&&R.handleOpenPromptBox("ERROR",{title:"Error",message:"Session Timeout",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"}),Oe(),R.handleOpenPromptBox("SUCCESS",{title:"Success",message:"successfully updated the record",severity:"success"}),I(!1)},function(f){R.handleOpenPromptBox("ERROR",{title:"Error",message:"Failed to update the record",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"}),I(!1)})},[Ce,S]=l.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""}),[C,Te]=l.useState(""),R={handleClosePromptBox:()=>{S(n=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),Te("")},handleOpenPromptBox:(n,f={})=>{let g={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};n==="SUCCESS"&&(g.type="snackbar"),Te(n),S({...g,...f})},handleCloseAndRedirect:()=>{R.handleClosePromptBox(),navigate("")},getCancelFunction:()=>{switch(C){default:return()=>{R.handleClosePromptBox()}}},getCloseFunction:()=>{switch(C){case"COMMENTERROR":default:return n=>{R.handleClosePromptBox()}}},getOkFunction:()=>{switch(C){case"DISCARD_CHANGES":return()=>{S(n=>({...n,open:!1})),Se(K)};case"DELETE_MAPPING":return()=>{Ne(K)};case"SUBMIT_CHANGES":return()=>{dt(Ue,K)};default:return()=>R.handleClosePromptBox()}},getCloseAndRedirectFunction:()=>Ce.redirectOnClose?R.handleCloseAndRedirect:R.handleClosePromptBox},dt=(n,f)=>{I(!0),S(v=>({...v,open:!1}));let g="",B=[...w];for(let v=0;v<B.length;v++)if(w[v].id===f){g=v;break}if(w[g].isRowEditable=!1,n.isEdited){w[g].isEdited=!1;let v="/WorkUtilsServices/v1/mail-mapping",_="POST";n.id&&(_="PATCH");let p={ccParticipant:n.ccParticipant,ccParticipantType:n.ccParticipantType,createdBy:n.createdBy,createdOn:new Date(n.createdOn).toISOString(),fromDestination:null,id:n.id,application:N.applicationName,name:n.templateName,process:n.process,regionId:n.regionId,status:n.status,templateId:n.templateId,toParticipant:n.toParticipant,toParticipantType:n.toParticipantType,updatedBy:N.userData.user_id,updatedOn:new Date().toISOString()};je(v,_,p,function(ge){(ge.statusCode===401||ge.statusCode==="401")&&(J(!0),R.handleOpenPromptBox("ERROR",{title:"Error",message:"Session Timeout",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"})),Oe(),b([...w]),R.handleOpenPromptBox("SUCCESS",{title:"Success",message:"successfully updated the record",severity:"success"}),I(!1)},function(ge){R.handleOpenPromptBox("ERROR",{title:"Error",message:"Failed to update the record",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"}),I(!1)})}};l.useEffect(()=>{N.applicationName!==""&&(Ke(),Oe())},[N]);const Ke=()=>{I(!0),Ve("populateAppIdentifiersHana",[],function(n){(n.statusCode===401||n.statusCode==="401")&&(J(!0),We("Timeout"),A("Session Timed Out.Kindly Refresh")),n&&Y(n),I(!1)},function(n){se(!0),xe("error"),q("error"),I(!1)})},at=(n,f)=>{I(!0);const g=_=>{if((_==null?void 0:_.statusCode)===401||(_==null?void 0:_.statusCode)==="401")R.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out. Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"});else if(_){const p=Array.isArray(_)?_:[];ie(p)}I(!1)},B=_=>{var p,ge,Be;(p=o==null?void 0:o.setAlert)==null||p.call(o,!0),(ge=o==null?void 0:o.setAlertSeverity)==null||ge.call(o,"error"),(Be=o==null?void 0:o.setAlertMessage)==null||Be.call(o,_),I(!1)},v=`/${kt}${Ot.EMAIL_CONFIG.FETCH_NOTIFICATION_MODULES}?identifierId=${encodeURIComponent(f)}`;ft(v,"get",g,B)},nt=(n,f,g)=>{I(!0);const B=p=>{if((p==null?void 0:p.statusCode)===401||(p==null?void 0:p.statusCode)==="401")R.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out. Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"});else if(p){const ge=Array.isArray(p)?p:[];Ae(ge)}I(!1)},v=p=>{var ge,Be,Je;(ge=o==null?void 0:o.setAlert)==null||ge.call(o,!0),(Be=o==null?void 0:o.setAlertSeverity)==null||Be.call(o,"error"),(Je=o==null?void 0:o.setAlertMessage)==null||Je.call(o,p),I(!1)},_=`/${kt}${Ot.EMAIL_CONFIG.FETCH_NOTIFICATION_EVENTS}?identifierId=${encodeURIComponent(g)}&notificationId=${encodeURIComponent(f)}`;ft(_,"get",B,v)},Qe=n=>{n==="Timeout"&&window.location.reload(),J(!1),A("")},lt=()=>{e(),x("INITIAL")},rt=[{field:"id",headerName:"ID",hide:!0},{field:"masterDataCat",headerName:o!=null&&o.headers[0]?o==null?void 0:o.headers[0]:"Identifier",flex:1,headerAlign:"left",align:"left",renderCell:n=>n.row.isRowEditable?i(et,{value:n.row.identifier,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:r==null?void 0:r.map(f=>i(pe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:f.identifier,onClick:g=>le(g,n.row.id,f,"identifier"),children:f.identifierDesc}))}):n.row.identifierDesc},{field:"entity",headerName:o!=null&&o.headers[1]?o==null?void 0:o.headers[1]:"Module",flex:1,headerAlign:"left",align:"left",renderCell:n=>i(de,{children:n.row.isRowEditable?i(et,{value:n.row.entity,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:$==null?void 0:$.map(f=>i(pe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:f.entity,onClick:g=>le(g,n.row.id,f,"entity"),children:f.entityDesc}))}):n.row.entitydesc})},{field:"process",headerName:(o==null?void 0:o.headers[2])??"Event",type:"boolean",headerAlign:"left",align:"left",flex:1,renderCell:n=>i(de,{children:n.row.isRowEditable?i(et,{value:n.row.process,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:ce==null?void 0:ce.map(f=>i(pe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:f.process,onClick:g=>le(g,n.row.id,f,"process"),children:f.processDesc}))}):n.row.processName})},{field:"templateName",headerName:o!=null&&o.headers[3]?o==null?void 0:o.headers[3]:"Template Name",flex:1,headerAlign:"left",align:"left",renderCell:n=>{var f;return i(de,{children:n.row.isRowEditable?i(et,{value:(f=n.row)==null?void 0:f.templateName,sx:{width:"100%",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:oe==null?void 0:oe.map(g=>i(pe,{sx:{width:"inherit",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:g==null?void 0:g.name,onClick:B=>le(B,n.row.id,g,"name"),children:g==null?void 0:g.name}))}):n.row.templateName})}},{field:"toParticipantType",headerName:"Recipent Type",type:"boolean",hide:!0,flex:1,headerAlign:"left",align:"left",renderCell:n=>i(de,{children:n.row.isRowEditable?i(et,{name:"toParticipantType",value:n.row.toParticipantType,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:L==null?void 0:L.map(f=>i(pe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:f,onClick:g=>le(g,n.row.id,f,"toParticipantType"),children:f},f))}):n.row.toParticipantType})},{field:"toParticipant",headerName:"Recipent",sortable:!1,filterable:!1,width:"100",headerAlign:"left",align:"left",hide:!0,renderCell:n=>i(de,{children:n.row.isRowEditable?i(et,{value:n.row.toParticipant,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:u==null?void 0:u.map(f=>i(pe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:f.id,onClick:g=>le(g,n.row.id,f,"toParticipant"),children:f.name}))}):n.row.toParticipantType==="VARIABLE"?n.row.toParticipant:n.row.toParticipantType==="GROUP"?H.get(parseInt(n.row.toParticipant)):t.get(n.row.toParticipant)})},{field:"ccParticipantType",headerName:"CC Type",sortable:!1,filterable:!1,flex:1,hide:!1,headerAlign:"left",align:"left",renderCell:n=>i(de,{children:n.row.isRowEditable?c(et,{value:n.row.ccParticipantType,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:[i(pe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"grey !important"},value:"",onClick:f=>le(f,n.row.id,"","ccParticipantType"),children:"Select CC Type"},""),L==null?void 0:L.map(f=>i(pe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:f,onClick:g=>le(g,n.row.id,f,"ccParticipantType"),children:f},f))]}):n.row.ccParticipantType})},{field:"ccParticipant",headerName:"CC",sortable:!1,filterable:!1,flex:1,headerAlign:"left",align:"left",hide:!1,renderCell:n=>i(de,{children:n.row.isRowEditable?i(et,{value:n.row.ccParticipant,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:Re==null?void 0:Re.map(f=>i(pe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:f.id,onClick:g=>le(g,n.row.id,f,"ccParticipant"),children:f.name}))}):n.row.ccParticipantType==="VARIABLE"?n.row.ccParticipant:n.row.ccParticipantType==="GROUP"?H.get(parseInt(n.row.ccParticipant)):t.get(n.row.ccParticipant)})},{field:"action",headerName:"Action",sortable:!1,filterable:!1,width:"100",hide:!1,headerAlign:"center",align:"center",renderCell:n=>i(de,{children:n.row.isRowEditable?c(E,{direction:"row",children:[i($e,{size:"small","aria-label":"Edit",color:"error",onClick:()=>{gt(n.row),Ge(n.row.id),R.handleOpenPromptBox("SUBMIT_CHANGES",{title:"Confirm Submit",message:"Do you want to save this record?",severity:"success",cancelButton:!0,okButton:!0,okButtonText:"Submit"})},children:i(Qi,{sx:{color:"green"}})}),i($e,{size:"small","aria-label":"Edit",color:"error",onClick:()=>{Ge(n.row.id),R.handleOpenPromptBox("DISCARD_CHANGES",{title:"Confirm Discard",message:"Are you sure you want to proceed with discard? The entered data will be lost",severity:"warning",cancelButton:!0,okButton:!0,okButtonText:"Discard"})},children:i(di,{sx:{color:"red"}})})]}):c(E,{direction:"row",children:[i($e,{size:"small","aria-label":"Edit",color:"error",onClick:()=>Ze(n.row,n.row.id),children:i(Ji,{sx:{color:"blue"}})}),i($e,{size:"small","aria-label":"Edit",color:"error",onClick:()=>{Ge(n.row.id),R.handleOpenPromptBox("DELETE_MAPPING",{title:"Confirm Delete",message:"Do you want to delete this record?",severity:"warning",cancelButton:!0,okButton:!0,okButtonText:"Ok"})},children:i(ci,{color:"danger"})})]})})}];return c("div",{p:2,className:"cwntSetHeight100 cwntSetWidth100",children:[i(k,{container:!0,sx:{marginBottom:"1.5rem"},children:c(k,{item:!0,md:9,style:{display:"flex"},children:[i(k,{item:!0,sx:{maxWidth:"max-content"},children:i($e,{onClick:lt,color:"primary",component:"label",className:"iconButton-spacing-small",sx:{padding:"0.25rem",height:"max-content"},children:i(si,{sx:{fontSize:"25px",color:"#000000"}})})}),c(k,{item:!0,xs:!0,children:[i(O,{variant:"h3",children:i("strong",{children:"Associated Template"})}),i(O,{variant:"body2",color:"#777",children:"This view allows user to manage associated Email Templates"})]})]})}),i(Pt,{className:"backdrop",sx:{zIndex:"9"},open:te,children:i(Bt,{color:"primary"})}),i(Nt,{type:Ce.type,promptState:Ce.open,setPromptState:R.handleClosePromptBox,onCloseAction:R.getCloseFunction(),promptMessage:Ce.message,dialogSeverity:Ce.severity,dialogTitleText:Ce.title,handleCancelButtonAction:R.getCancelFunction(),cancelButtonText:Ce.cancelText,showCancelButton:Ce.cancelButton,handleSnackBarPromptClose:R.getCloseAndRedirectFunction(),handleOkButtonAction:R.getOkFunction(),okButtonText:Ce.okButtonText,showOkButton:Ce.okButton}),i(ka,{width:"100%",rows:w??[],columns:rt,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,isLoading:!1}),i(Mt,{message:ne,creationType:V,open:Z,onClose:n=>Qe(n)}),i(_e,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:"4"},elevation:2,children:i($t,{className:"container_BottomNav",children:i(E,{direction:"row",sx:{marginLeft:"auto"},spacing:2,alignItems:"center",children:i(ue,{variant:"contained",sx:{minWidth:"max-content",padding:"6px 12px",texttransform:"capitalize",height:"2rem"},onClick:He,children:"Create New Mapping"})})})})]})};var qt={},Ba=ii;Object.defineProperty(qt,"__esModule",{value:!0});var Ci=qt.default=void 0,Pa=Ba(ti()),Da=ai;Ci=qt.default=(0,Pa.default)((0,Da.jsx)("path",{d:"M3 10h11v2H3zm0-2h11V6H3zm0 8h7v-2H3zm15.01-3.13.71-.71c.39-.39 1.02-.39 1.41 0l.71.71c.39.39.39 1.02 0 1.41l-.71.71zm-.71.71-5.3 5.3V21h2.12l5.3-5.3z"}),"EditNote");const Ma=e=>i(Gi,{store:Dt,children:i(Fa,{...e})}),Na=T.memo(Ma);function Fa({token:e,destinations:m,feature:y,environment:x,useWorkAccess:o,useConfigServerDestination:N,userId:t,applicationName:s,applicationId:H,needHeading:F,showManageGroups:te,useCrud:I,...r}){const Y=ot(P=>P.userReducer),$=Ht(),ie=mi(),[ce,Ae]=T.useState(""),[oe,Me]=T.useState("1"),[u,U]=T.useState(!1),[X,ae]=T.useState([]),[Re,ye]=T.useState([]),[L,me]=T.useState([]),[se,ke]=T.useState([]),[q,w]=T.useState([]),[b,Z]=T.useState([]),[J,ne]=T.useState(!1),[A,V]=T.useState(null),[We,Ie]=T.useState("new"),[tt,K]=T.useState("INITIAL"),[Ge,Ue]=T.useState([]),[gt,he]=T.useState(!1),[wt,xe]=T.useState(""),[ze,Q]=T.useState("success"),[fe,le]=T.useState(null),[Oe,He]=T.useState(""),[it,Se]=T.useState("Cancel"),[Ze,Ne]=l.useState(!1);l.useState(!1);const[Ce,S]=l.useState(!1),[C,Te]=l.useState(!1),[R,dt]=l.useState([]);l.useState(new Map);const[Ke,at]=l.useState(new Map),[nt,Qe]=l.useState([]),[lt,rt]=l.useState([]),[n,f]=l.useState([]),[g,B]=l.useState([]),[v,_]=l.useState([]),[p,ge]=l.useState(!0),{t:Be}=Ui(),Je=()=>{Te(!1),ge(!0)},Ft=()=>{S(!1),ge(!0)},Lt=()=>{Ne(!0),ge(!1),Ie("new"),K("CREATE")},vt=()=>{Ne(!1),ge(!0)};l.useEffect(()=>{X.length&&R.length&&_t()},[X,R]);function _t(){var P=X,we=R;const ve=[];P.forEach(a=>{const d=we.find(j=>a.emailDefinitionId===j.templateId),h={...a,toList:(d==null?void 0:d.toList)??"",toParticipant:(d==null?void 0:d.toParticipant)??"",toParticipantType:(d==null?void 0:d.toParticipantType)??"",ccList:(d==null?void 0:d.ccList)??"",ccParticipant:(d==null?void 0:d.ccParticipant)??"",ccParticipantType:(d==null?void 0:d.ccParticipantType)??""};ve.push(h)}),f(ve),me(ve.filter(a=>a.status==="Active")),w(ve.filter(a=>a.status==="Draft"))}const Xe=()=>{if(ne(!0),!m){ne(!1);return}je("/WorkUtilsServices/v2/mail-definition","get",null,function(P){if(P.statusCode===401||P.statusCode==="401"){U(!0),Se("Timeout"),He("Session Timed Out. Kindly Refresh"),ne(!1);return}const ve=(P.data||[]).filter(h=>h.identifierDesc==="MDG_BATCH");ae(ve),ye(ve);const a=ve.filter(h=>h.status==="Active"),d=ve.filter(h=>h.status==="Draft");me(a),ke(a),w(d),Z(d),ne(!1)},function(P){he(!0),Q("error"),xe(P),ne(!1)})};T.useEffect(()=>{X!=null&&X.length&&st()},[X]),T.useEffect(()=>{$(la({useWorkAccess:o,useConfigServerDestination:N,userId:t,applicationName:s,applicationId:H,useCrud:I})),$(ra(y)),Promise.all([$(aa(e)),$(na({destinations:m,environment:x}))]).then(([])=>{Xe(),st()}).catch(P=>{})},[Y==null?void 0:Y.refreshTemplates]);const Wt=(P,we)=>{Me(we)},It=P=>{Ue(X.filter(we=>we.name.toLowerCase().includes(P.toLowerCase())||we.entityDesc.toLowerCase().includes(P.toLowerCase())||we.processDesc.toLowerCase().includes(P.toLowerCase())))},st=()=>{Ve("fetchMailMappingHana",[],function(P){(P.statusCode===401||P.statusCode==="401")&&(U(!0),Se("Timeout"),He("Session Timed Out.Kindly Refresh")),P&&dt(P)},function(P){he(!0),Q("error"),xe(P),ne(!1)})},Fe=()=>{switch(tt){case"EDIT":return i(ei,{headers:r==null?void 0:r.headers,isRecepientData:r==null?void 0:r.isRecepientData,open:Ze,onClose:vt,...r,getMailDefinition:Xe,data:A,creationType:We,setSelectedRow:V,setCreationType:Ie,setAlert:he,setAlertMessage:xe,setAlertSeverity:Q,setIsEditing:le,TemplateGroupData:st,userList:r==null?void 0:r.userList,groupList:r==null?void 0:r.groupList,contentHTML:r==null?void 0:r.contentHTML,setScenario:K});case"INITIAL":return c(E,{children:[i(E,{spacing:1,children:c(k,{container:!0,sx:{...Hi},children:[c(k,{item:!0,md:5,xs:12,sx:$i,children:[i(O,{variant:"h3",children:i("strong",{children:Be("Email Template Configurations")})}),i(O,{variant:"body2",color:"#777",children:Be("This view allows the user to create and display Email Templates")})]}),i(k,{item:!0,md:7,xs:12,sx:{display:"flex",justifyContent:"flex-end",alignItems:"center"},children:i(re,{sx:{maxWidth:200,marginLeft:"auto"},children:i(De,{fullWidth:!0,size:"small",placeholder:Be("Search Templates"),variant:"outlined",value:ce,onChange:P=>{Ae(P.target.value),It(P.target.value)},InputProps:{startAdornment:i(ji,{sx:{color:"action.active"}})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"10px",backgroundColor:"white","&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"primary.main"},"&.Mui-focused .MuiOutlinedInput-notchedOutline":{borderColor:"primary.main",borderWidth:"2px"}},"& .MuiOutlinedInput-input":{padding:"10px 14px"}}})})})]})}),i(_e,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:2},elevation:2,children:i($t,{className:"container_BottomNav",children:y.EMAIL_CONFIG_CREATE==="True"&&i(ue,{size:"small",variant:"contained",className:"btn-ml",onClick:Lt,children:Be("Create New Template")})})}),p&&c(de,{children:[y.EMAIL_CONFIG_SUMMARY==="True"&&i(re,{borderBottom:1,sx:{borderColor:"divider",marginBottom:"1rem"},children:c(ri,{value:oe,onChange:Wt,children:[y.EMAIL_CONFIG_SUMMARY_ACTIVE==="True"&&i(ht,{label:c(E,{direction:"row",sx:{alignItems:"center"},children:[i(gi,{sx:{fontSize:"15px"}}),i(O,{variant:"body1",ml:1,sx:{fontWeight:600,fontSize:"14px"},children:Be("Active Template")})]}),value:"1",sx:{fontWeight:"700",fontSize:"14px",textTransform:"none"}}),y.EMAIL_CONFIG_SUMMARY_DRAFT==="True"&&i(ht,{label:c(E,{direction:"row",sx:{alignItems:"center"},children:[i(Ci,{sx:{fontSize:"15px"}}),i(O,{variant:"body1",ml:1,sx:{fontWeight:600,fontSize:"14px"},children:Be("Draft")})]}),value:"2",sx:{fontWeight:"700",fontSize:"14px",textTransform:"none"}})]})}),i(E,{children:oe==="1"&&i(xa,{setScenario:K,destinations:m,setCreationType:Ie,applicationName:s,setSelectedRow:V,setShowConfirmation:U,setIsEditing:le,isEditing:fe,setButtonAction:Se,setConfirmationMessage:He,filteredData:Ge,active:L,mailDefination:X,mailmappingData:R,searchParam:ce,setOpenCreateTemplate:Ne,ccToParticipant:nt,toparticipant:lt,emailTemplateData:n,groupList:r==null?void 0:r.groupList,userList:r==null?void 0:r.userList,isLoading:J,allGroups:v,headers:r==null?void 0:r.headers})}),i(E,{children:oe==="2"&&i(Ca,{destinations:m,setCreationType:Ie,applicationName:s,setSelectedRow:V,setShowConfirmation:U,draft:q,setIsEditing:le,isEditing:fe,setButtonAction:Se,setConfirmationMessage:He,filteredData:Ge,mailDefination:X,searchParam:ce,setOpenCreateTemplate:Ne,mailmappingData:R,emailTemplateData:n,groupList:r==null?void 0:r.groupList,userList:r==null?void 0:r.userList,isLoading:J,allGroups:v,headers:r==null?void 0:r.headers,setScenario:K})}),i(E,{children:oe==="3"&&i(ga,{destinations:m,setCreationType:Ie,applicationName:s,setSelectedRow:V,setScenario:K,setShowConfirmation:U,setIsEditing:le,isEditing:fe,setButtonAction:Se,setConfirmationMessage:He,filteredData:Ge,mailDefination:X,searchParam:ce,setOpenCreateTemplate:Ne,mailmappingData:R,emailTemplateData:n,groupList:r==null?void 0:r.groupList,userList:r==null?void 0:r.userList,isLoading:J,allGroups:v,headers:r==null?void 0:r.headers})})]})]});case"CREATE":return i(ei,{scenario:tt,headers:r==null?void 0:r.headers,isRecepientData:r==null?void 0:r.isRecepientData,open:Ze,onClose:vt,...r,getMailDefinition:Xe,data:A,creationType:We,setSelectedRow:V,setCreationType:Ie,setAlert:he,setAlertMessage:xe,setAlertSeverity:Q,setIsEditing:le,TemplateGroupData:st,userList:r==null?void 0:r.userList,groupList:r==null?void 0:r.groupList,contentHTML:r==null?void 0:r.contentHTML,setScenario:K});case"MANAGE_GROUPS":return i(Ea,{headers:r==null?void 0:r.headers,open:Ce,onClose:Ft,...r,getMailDefinition:Xe,data:A,creationType:We,setSelectedRow:V,setCreationType:Ie,setAlert:he,setAlertMessage:xe,setAlertSeverity:Q,setIsEditing:le,setScenario:K,isAssociatedTemplate:r==null?void 0:r.isAssociatedTemplate});case"ASSOCIATED_TEMPLATE":return i(Oa,{headers:r==null?void 0:r.headers,open:C,onClose:Je,...r,getMailDefinition:Xe,data:A,creationType:We,setSelectedRow:V,setCreationType:Ie,setAlert:he,setAlertMessage:xe,setAlertSeverity:Q,setIsEditing:le,userList:r==null?void 0:r.userList,groupList:r==null?void 0:r.groupList,setScenario:K,promptAction_Functions:be})}},[Pe,ut]=l.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""}),[mt,z]=l.useState(""),be={handleClosePromptBox:()=>{ut(P=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),z("")},handleOpenPromptBox:(P,we={})=>{let ve={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};P==="SUCCESS"&&(ve.type="snackbar"),z(P),ut({...ve,...we})},handleCloseAndRedirect:()=>{be.handleClosePromptBox(),ie("/purchaseManagement/purchaseOrder")},getCancelFunction:()=>{switch(mt){default:return()=>{be.handleClosePromptBox()}}},getCloseFunction:()=>{switch(mt){case"COMMENTERROR":default:return P=>{be.handleClosePromptBox()}}},getOkFunction:()=>{switch(mt){case"CONFIRMDELETE_PROCESS":return()=>deleteProcess();case"CONFIRMDELETE_METADATA":return()=>deleteMetaData();default:return()=>be.handleClosePromptBox()}},getCloseAndRedirectFunction:()=>Pe.redirectOnClose?be.handleCloseAndRedirect:be.handleClosePromptBox},xt=()=>{ne(!0),Ve("fetchMailGroupingHana",[],function(P){if(console.log(P),P.statusCode===401||P.statusCode,P){let we=P.map(d=>({id:d.id,name:d.groupName,userIdList:d.email}));B(we),console.log(r==null?void 0:r.groupList,"props?.groupList");let a=we.concat(r==null?void 0:r.groupList).map((d,h)=>(Ke.set(d.id,d.name),{id:d.id,name:d.name}));at(new Map(Ke)),_(a)}ne(!1)},function(P){he(!0),Q("error"),xe(P)})};return l.useEffect(()=>{xt()},[r==null?void 0:r.groupList]),l.useEffect(()=>{xt(),Y.applicationName},[Y]),m===null?i(Pt,{className:"backdrop",open:!0,children:i(Bt,{color:"primary"})}):c(de,{children:[i(Nt,{type:Pe.type,promptState:Pe.open,setPromptState:be.handleClosePromptBox,onCloseAction:be.getCloseFunction(),promptMessage:Pe.message,dialogSeverity:Pe.severity,dialogTitleText:Pe.title,handleCancelButtonAction:be.getCancelFunction(),cancelButtonText:Pe.cancelText,showCancelButton:Pe.cancelButton,handleSnackBarPromptClose:be.getCloseAndRedirectFunction(),handleOkButtonAction:be.getOkFunction(),okButtonText:Pe.okButtonText,showOkButton:Pe.okButton}),i(E,{sx:{...zi,minHeight:"100vh",height:"max-content",backgroundColor:P=>P.background.default},children:Fe()})]})}const Ha=()=>{let e=ot(I=>I.userManagement.userData);const m=ot(I=>I.applicationConfig),[y,x]=l.useState([]),[o,N]=l.useState([]),t=m.environment==="localhost"?[{Description:"",Name:"CW_Worktext",URL:`${Gt}`},{Description:"",Name:"WorkUtilsServices",URL:`${Gt}`},{Description:"",Name:"WorkUtilsServicesHana",URL:`${Gt}`},{Description:"",Name:"CrudApiServices",URL:`${Vi}`}]:[];let s=m.environment==="localhost"?m.iwaToken:"Bearer ";const H={EMAIL_CONFIG_SUMMARY:"True",EMAIL_CONFIG_SUMMARY_ACTIVE:"True",EMAIL_CONFIG_SUMMARY_DRAFT:"True",EMAIL_CONFIG_SUMMARY_SORT:"True",EMAIL_CONFIG_SUMMARY_SEARCH:"True",EMAIL_CONFIG_CREATE:"True",EMAIL_CONFIG_SAVE:"True",EMAIL_CONFIG_DISCARD:"True",EMAIL_CONFIG_DELETE:"True",EMAIL_CONFIG_SUBMIT:"True",EMAIL_CONFIG_MANAGE_GROUPS:"True",EMAIL_CONFIG_MANAGE_GROUPS_CREATE:"True",EMAIL_CONFIG_MANAGE_GROUPS_ADD_USER:"True",EMAIL_CONFIG_MANAGE_GROUPS_MEMBER_INFO:"True",EMAIL_CONFIG_MANAGE_GROUPS_TEMPLATE_INFO:"false",EMAIL_CONFIG_GROUP_MAPPING:"True",EMAIL_CONFIG_GROUP_MAPPING_ADD:"True",EMAIL_CONFIG_GROUP_MAPPING_DELETE:"True",EMAIL_CONFIG_GROUP_MAPPING_FILTER:"True"},F=()=>{ft(`/${Xt}/api/v1/usersMDG/getUsersMDG`,"get",I=>{var r=I.data,Y=r==null?void 0:r.map(ie=>({...ie,userId:ie==null?void 0:ie.emailId})),$={...I,data:Y};x(Y)})},te=()=>{ft(`/${Xt}/api/v1/groupsMDG/getAllGroupsMDG`,"get",I=>{var r=I.data,Y=r==null?void 0:r.map(ie=>({...ie,groupName:ie==null?void 0:ie.name})),$={...I,data:Y};N(r)})};return l.useEffect(()=>{F(),te()},[]),i("div",{children:i(Na,{applicationName:"ITM",token:s,destinations:t,headers:[],useCrud:!0,environment:"itm",useWorkAccess:!1,useConfigServerDestination:!1,userId:e==null?void 0:e.emailId,applicationId:"1",groupList:o,userList:y,contentHTML:"",needHeading:!1,isAssociatedTemplate:!0,isRecepientData:!1,showManageGroups:!0,pathName:"/configCockpit/userManagement?component=groups",feature:H})})};export{Ha as default};
