import{fE as j,fD as U,r as u,fF as E,fS as H,fU as O,at as g,aE as W,as as P,sa as q,nE as _,aj as L,nG as D,fG as F,sb as I}from"./index-fdfa25a0.js";function J(t){return j("MuiToggleButton",t)}const K=U("MuiToggleButton",["root","disabled","selected","standard","primary","secondary","sizeSmall","sizeMedium","sizeLarge","fullWidth"]),x=K,Q=u.createContext({}),A=Q,X=u.createContext(void 0),V=X;function Y(t,o){return o===void 0||t===void 0?!1:Array.isArray(o)?o.indexOf(t)>=0:t===o}const Z=["value"],w=["children","className","color","disabled","disableFocusRipple","fullWidth","onChange","onClick","selected","size","value"],S=t=>{const{classes:o,fullWidth:s,selected:a,disabled:f,size:c,color:v}=t,B={root:["root",a&&"selected",f&&"disabled",s&&"fullWidth",`size${O(c)}`,v]};return F(B,J,o)},tt=E(H,{name:"MuiToggleButton",slot:"Root",overridesResolver:(t,o)=>{const{ownerState:s}=t;return[o.root,o[`size${O(s.size)}`]]}})(({theme:t,ownerState:o})=>{let s=o.color==="standard"?t.palette.text.primary:t.palette[o.color].main,a;return t.vars&&(s=o.color==="standard"?t.vars.palette.text.primary:t.vars.palette[o.color].main,a=o.color==="standard"?t.vars.palette.text.primaryChannel:t.vars.palette[o.color].mainChannel),g({},t.typography.button,{borderRadius:(t.vars||t).shape.borderRadius,padding:11,border:`1px solid ${(t.vars||t).palette.divider}`,color:(t.vars||t).palette.action.active},o.fullWidth&&{width:"100%"},{[`&.${x.disabled}`]:{color:(t.vars||t).palette.action.disabled,border:`1px solid ${(t.vars||t).palette.action.disabledBackground}`},"&:hover":{textDecoration:"none",backgroundColor:t.vars?`rgba(${t.vars.palette.text.primaryChannel} / ${t.vars.palette.action.hoverOpacity})`:W(t.palette.text.primary,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${x.selected}`]:{color:s,backgroundColor:t.vars?`rgba(${a} / ${t.vars.palette.action.selectedOpacity})`:W(s,t.palette.action.selectedOpacity),"&:hover":{backgroundColor:t.vars?`rgba(${a} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:W(s,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${a} / ${t.vars.palette.action.selectedOpacity})`:W(s,t.palette.action.selectedOpacity)}}}},o.size==="small"&&{padding:7,fontSize:t.typography.pxToRem(13)},o.size==="large"&&{padding:15,fontSize:t.typography.pxToRem(15)})}),ot=u.forwardRef(function(o,s){const a=u.useContext(A),{value:f}=a,c=P(a,Z),v=u.useContext(V),B=q(g({},c,{selected:Y(o.value,f)}),o),$=_({props:B,name:"MuiToggleButton"}),{children:h,className:p,color:k="standard",disabled:b=!1,disableFocusRipple:r=!1,fullWidth:N=!1,onChange:C,onClick:i,selected:R,size:m="medium",value:T}=$,y=P($,w),z=g({},$,{color:k,disabled:b,disableFocusRipple:r,fullWidth:N,size:m}),M=S(z),n=d=>{i&&(i(d,T),d.defaultPrevented)||C&&C(d,T)},l=v||"";return L.jsx(tt,g({className:D(c.className,M.root,p,l),disabled:b,focusRipple:!r,ref:s,onClick:n,onChange:C,value:T,ownerState:z,"aria-pressed":R},y,{children:h}))}),dt=ot;function et(t){return j("MuiToggleButtonGroup",t)}const st=U("MuiToggleButtonGroup",["root","selected","horizontal","vertical","disabled","grouped","groupedHorizontal","groupedVertical","fullWidth","firstButton","lastButton","middleButton"]),e=st,at=["children","className","color","disabled","exclusive","fullWidth","onChange","orientation","size","value"],rt=t=>{const{classes:o,orientation:s,fullWidth:a,disabled:f}=t,c={root:["root",s==="vertical"&&"vertical",a&&"fullWidth"],grouped:["grouped",`grouped${O(s)}`,f&&"disabled"],firstButton:["firstButton"],lastButton:["lastButton"],middleButton:["middleButton"]};return F(c,et,o)},lt=E("div",{name:"MuiToggleButtonGroup",slot:"Root",overridesResolver:(t,o)=>{const{ownerState:s}=t;return[{[`& .${e.grouped}`]:o.grouped},{[`& .${e.grouped}`]:o[`grouped${O(s.orientation)}`]},{[`& .${e.firstButton}`]:o.firstButton},{[`& .${e.lastButton}`]:o.lastButton},{[`& .${e.middleButton}`]:o.middleButton},o.root,s.orientation==="vertical"&&o.vertical,s.fullWidth&&o.fullWidth]}})(({ownerState:t,theme:o})=>g({display:"inline-flex",borderRadius:(o.vars||o).shape.borderRadius},t.orientation==="vertical"&&{flexDirection:"column"},t.fullWidth&&{width:"100%"},{[`& .${e.grouped}`]:g({},t.orientation==="horizontal"?{[`&.${e.selected} + .${e.grouped}.${e.selected}`]:{borderLeft:0,marginLeft:0}}:{[`&.${e.selected} + .${e.grouped}.${e.selected}`]:{borderTop:0,marginTop:0}})},t.orientation==="horizontal"?{[`& .${e.firstButton},& .${e.middleButton}`]:{borderTopRightRadius:0,borderBottomRightRadius:0},[`& .${e.lastButton},& .${e.middleButton}`]:{marginLeft:-1,borderLeft:"1px solid transparent",borderTopLeftRadius:0,borderBottomLeftRadius:0}}:{[`& .${e.firstButton},& .${e.middleButton}`]:{borderBottomLeftRadius:0,borderBottomRightRadius:0},[`& .${e.lastButton},& .${e.middleButton}`]:{marginTop:-1,borderTop:"1px solid transparent",borderTopLeftRadius:0,borderTopRightRadius:0}},t.orientation==="horizontal"?{[`& .${e.lastButton}.${x.disabled},& .${e.middleButton}.${x.disabled}`]:{borderLeft:"1px solid transparent"}}:{[`& .${e.lastButton}.${x.disabled},& .${e.middleButton}.${x.disabled}`]:{borderTop:"1px solid transparent"}})),nt=u.forwardRef(function(o,s){const a=_({props:o,name:"MuiToggleButtonGroup"}),{children:f,className:c,color:v="standard",disabled:B=!1,exclusive:$=!1,fullWidth:h=!1,onChange:p,orientation:k="horizontal",size:b="medium",value:r}=a,N=P(a,at),C=g({},a,{disabled:B,fullWidth:h,orientation:k,size:b}),i=rt(C),R=u.useCallback((n,l)=>{if(!p)return;const d=r&&r.indexOf(l);let G;r&&d>=0?(G=r.slice(),G.splice(d,1)):G=r?r.concat(l):[l],p(n,G)},[p,r]),m=u.useCallback((n,l)=>{p&&p(n,r===l?null:l)},[p,r]),T=u.useMemo(()=>({className:i.grouped,onChange:$?m:R,value:r,size:b,fullWidth:h,color:v,disabled:B}),[i.grouped,$,m,R,r,b,h,v,B]),y=I(f),z=y.length,M=n=>{const l=n===0,d=n===z-1;return l&&d?"":l?i.firstButton:d?i.lastButton:i.middleButton};return L.jsx(lt,g({role:"group",className:D(i.root,c),ref:s,ownerState:C},N,{children:L.jsx(A.Provider,{value:T,children:y.map((n,l)=>L.jsx(V.Provider,{value:M(l),children:n},l))})}))}),ut=nt;export{dt as T,ut as a,J as b,e as c,et as g,x as t};
