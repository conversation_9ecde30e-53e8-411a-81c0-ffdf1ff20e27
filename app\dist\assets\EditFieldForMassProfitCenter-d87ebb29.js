import{r as f,i as P,l as V,iz as ee,a as o,g_ as te,j as v,T as S,hy as re,a3 as _,gR as ae,gS as oe,hJ as se,$ as J,hA as K,F as N,hN as ce,is as z,sv as ne,w as W,hK as w,hP as H}from"./index-fdfa25a0.js";function le(r,n){return Array.isArray(n)&&n.find(F=>F.code===r)||""}const me=({label:r,value:n,length:O,units:F,onSave:de,fieldGroup:l,isEditMode:Q,activeTabIndex:T,visibility:d,isExtendMode:ie,pcTabs:q,selectedRowData:M,options:ge=[],type:m})=>{var I,L;const[h,A]=f.useState(n),[he,X]=f.useState(!1),D=P(e=>e.AllDropDown.dropDown),c=P(e=>e.profitCenter.MultipleProfitCenterData),u=V();le(h,D);const R=P(e=>e.appSettings);P(e=>e.edit.payload);let B={},k=-1;for(let e=0;e<(c==null?void 0:c.length);e++)if(c[e].profitCenter===M){B=c[e],k=e;break}console.log("selectedrowdata",M,c[0].profitCenter);let i=q[T];console.log("activerow",k,B,i);const p=(e,t)=>{const s=e==null?void 0:e.find(a=>(a==null?void 0:a.fieldName)===t);return s?s.value:""},y=c[k];let g=r.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");f.useEffect(()=>{A(n)},[n]),console.log(d,g,"visibility45"),f.useEffect(()=>{(d==="0"||d==="Required")&&u(ee(g))},[i]);const E={label:r,value:h,units:F,type:m,visibility:d};console.log("fieldData==========",E);const C=(e,t)=>{u(z({keyname:g.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:t}));let s=c==null?void 0:c.map((a,G)=>{let $=q[T];if(G===k){let b=a.viewData,U=a.viewData[$];console.log("temp",U);let x=a.viewData[$][l];return console.log("temp2",x),{...a,viewData:{...b,[$]:{...U,[l]:x==null?void 0:x.map(j=>j.fieldName===e?{...j,value:t}:j)}}}}else return a});console.log("changedData",s),u(ne(s))},Y=e=>{console.log("compcode",e);const t=a=>{console.log("value",a),u(z({keyname:"Currency",data:""})),u(H({keyName:"Currency",data:a.body}))},s=a=>{console.log(a,"error in dojax")};W(`/${w}/data/getCurrency?companyCode=${e==null?void 0:e.code}`,"get",t,s)},Z=e=>{console.log("countryyyyy",e);const t=a=>{console.log("value",a),u(z({keyname:"Region",data:""})),u(H({keyName:"Region",data:a.body}))},s=a=>{console.log(a,"error in dojax")};W(`/${w}/data/getRegionBasedOnCountry?country=${e==null?void 0:e.code}`,"get",t,s)};return console.log("chiranjit",h),f.useEffect(()=>{(r==="Analysis Period From"||r==="Analysis Period To"||r==="Created On")&&A(parseInt(n.replace("/Date(","").replace(")/","")))},[n]),console.log("editedValue[key] ",D[g]),console.log("editedValue[key] ",h),o(J,{item:!0,children:o(te,{children:Q?v(N,{children:[v(S,{variant:"body2",color:"#777",children:[r," ",d==="Required"||d==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),m==="Drop Down"?o(re,{options:D[g]??[],value:p(y.viewData[i][l],r)&&((I=D[g])==null?void 0:I.filter(e=>e.code===p(y.viewData[i][l],r)))&&((L=D[g])==null?void 0:L.filter(e=>e.code===p(y.viewData[i][l],r))[0])||"",onChange:(e,t)=>{r==="Comp Code"&&Y(t),r==="Country/Reg"&&Z(t),C(r,t==null?void 0:t.code),console.log("newValue",t),A(t.code),X(!0),console.log("keys",g)},getOptionLabel:e=>(console.log("optionn",e),e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??""),renderOption:(e,t)=>(console.log("option vakue",t),o("li",{...e,children:o(S,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})})),renderInput:e=>o(_,{...e,variant:"outlined",placeholder:`Select ${E.label}`,size:"small",label:null})}):m==="Input"?o(_,{variant:"outlined",size:"small",value:p(y.viewData[i][l],r).toUpperCase(),placeholder:`Enter ${E.label}`,inputProps:{maxLength:O},onChange:e=>{const t=e.target.value;if(t.length>0&&t[0]===" ")C(r,t.trimStart());else{let s=t.toUpperCase();C(r,s)}}}):m==="Calendar"?o(ae,{dateAdapter:oe,children:o(se,{slotProps:{textField:{size:"small"}},value:parseInt(p(y.viewData[i][l],r).replace("/Date(","").replace(")/","")),placeholder:"Select Date Range",maxDate:new Date(9999,12,31),onChange:e=>{C(e),A(e)}})}):m==="Radio Button"?o(J,{item:!0,md:2,children:o(K,{sx:{padding:0},checked:p(y.viewData[i][l],r)==!0,onChange:e=>{console.log("oncheckbox",r,e.target.checked),C(r,e.target.checked)}})}):""]}):o(N,{children:v(N,{children:[v(S,{variant:"body2",color:"#777",children:[r," ",d==="Required"||d==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),v(S,{variant:"body2",fontWeight:"bold",children:[r==="Analysis Period From"||r==="Analysis Period To"?ce(h).format(R==null?void 0:R.dateFormat):h,m==="Radio Button"?o(K,{sx:{padding:0},checked:h,disabled:!0}):""]})]})})})})};export{me as E};
